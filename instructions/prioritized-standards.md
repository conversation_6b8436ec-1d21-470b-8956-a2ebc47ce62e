# Prioritized Standard Services for Solution Design

This document outlines the standard services that should be prioritized and considered when designing solutions.

## Security and Access Management
- **Okta**
    - Identity and access management platform
    - Standard solution for authentication and authorization
    - Required for user management and SSO implementations
    - Okta Workforce Identity is used for employee authentication
    - Okta Customer Identity is used for customer authentication
- **HashiCorp Vault**
    - Secrets management and data protection
    - Required for storing application secrets
    - Standard for dynamic secrets and encryption
- **Imperva WAF**
    - Web Application Firewall for protecting web applications
    - Required for external-facing applications
    - Must be implemented before production deployment
- **Apigee API Gateway**
    - API management and security
    - Required for all external API endpoints
    - Must be implemented for API versioning and monitoring

## Monitoring and Observability
- **DataDog APM**
    - Application Performance Monitoring
    - Required for all production applications
    - Must be configured for service-level monitoring
- **Splunk**
    - Log aggregation and analysis
    - Required for centralized logging
    - Must be implemented for security and operational monitoring

## Infrastructure
- **Terraform**
    - Infrastructure as Code (IaC) platform
    - Required for all infrastructure provisioning
    - Standard format for infrastructure definitions
- **Aiven <PERSON>fka**
    - Managed event streaming platform
    - Standard solution for event-driven architectures
    - Required for asynchronous messaging patterns
- **AWS EKS/Azure AKS**
    - Managed Kubernetes services on AWS and Azure
    - Standard platform for containerized applications
    - Required for all new container deployments
- **Istio**
    - Service mesh and ingress controller for Kubernetes
    - Required for microservices traffic management
    - Standard for service-to-service communication
- **Artifactory**
    - Enterprise artifact repository manager
    - Required for storing and managing build artifacts
    - Standard solution for container images and dependencies
- **WEX Fabric**
    - Company's golden path development platform
    - Standard framework for application development
    - Required for new application initiatives
    - Standard platform for containerized applications
    - Required for all new container deployments

## Deployment and Configuration
- **ArgoCD Helm CI-CD**
    - GitOps continuous delivery tool
    - Standard for Kubernetes deployments
    - Required for automated deployment pipelines

## Implementation Notes
1. These services should be considered as default choices for their respective functions
2. Any deviation from these standards requires architectural review and approval
3. Integration with these services should follow established patterns and practices
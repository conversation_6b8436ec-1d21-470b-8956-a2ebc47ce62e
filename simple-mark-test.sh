#!/bin/bash

# Simple test using our pre-built Docker image with mark --compile-only

set -e

echo "🧪 Simple Mark Integration Test"
echo "==============================="

# Create test directory
TEST_DIR="simple-mark-test"
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR"

echo "📝 Creating simple test markdown file..."

# Create a simple test markdown file
cat > "$TEST_DIR/test.md" << 'EOF'
<!-- Title: TEST-001 Simple Test -->

# Simple Test Document

This document contains a simple Mermaid diagram.

```mermaid
graph TD
    A[Start] --> B[End]
```

That's it!
EOF

echo "✅ Test markdown file created"

echo "🐳 Testing with our pre-built Docker image..."

# Use our pre-built image that already has everything installed
if docker run --rm \
    -v "$PWD/$TEST_DIR:/work" \
    -w /work \
    -e MARK_SPACE="TEST" \
    -e MARK_BASE_URL="https://example.com" \
    my-custom-mark-renderer mark --compile-only \
        --mermaid-provider='python3 /app/custom_mermaid_renderer.py' \
        --mermaid-scale 2 \
        -f 'test.md' > "$TEST_DIR/output.html" 2> "$TEST_DIR/stderr.log"; then
    
    echo "✅ Mark executed successfully!"
    
    # Check the output
    if [ -s "$TEST_DIR/output.html" ]; then
        echo "✅ HTML output generated ($(wc -c < "$TEST_DIR/output.html") bytes)"
        
        # Check for images
        if grep -q "<img" "$TEST_DIR/output.html"; then
            echo "✅ Found image tags - Mermaid was processed!"
            echo "📊 Number of images: $(grep -c "<img" "$TEST_DIR/output.html")"
        else
            echo "⚠️  No image tags found"
        fi
        
        # Show a sample of the HTML
        echo "📋 HTML sample (first 20 lines):"
        head -20 "$TEST_DIR/output.html"
        
    else
        echo "❌ No HTML output generated"
    fi
    
else
    echo "❌ Mark execution failed"
fi

# Show any errors
if [ -s "$TEST_DIR/stderr.log" ]; then
    echo ""
    echo "📋 Error output:"
    echo "==============="
    cat "$TEST_DIR/stderr.log"
fi

echo ""
echo "📁 Files created in: $TEST_DIR/"
echo "🔍 To view HTML: open $TEST_DIR/output.html"
echo "🧹 To clean up: rm -rf $TEST_DIR"

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.http import MediaIoBaseDownload
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from urllib.parse import urlparse
from markdownify import markdownify as md
import re
import io
import os
import shutil


def gcp_get_credentials(refresh_token=None, client_id=None, client_secret=None):

    creds = Credentials(
        None,
        refresh_token=refresh_token,
        token_uri='https://www.googleapis.com/oauth2/v4/token',
        client_id=client_id,
        client_secret=client_secret)

    if creds:
        if creds.expired and creds.refresh_token:
            creds.refresh(Request())
    return creds

def check_file_access(service, file_id):
    try:
        file_metadata = service.files().get(fileId=file_id, supportsAllDrives=True).execute()
        print(f"File found: {file_metadata['name']}")
        return True
    except HttpError as error:
        print(f"Error accessing file: {error}")
        return False
    
def remove_whitespaces(text):
    trimmed_text =  re.sub(r'\s+', '', text) 
    return trimmed_text

def remove_characters(text):
    trimmed_text = text.strip()
    specific_char_trimmed_text = trimmed_text.strip('!@#$%^&*()_-+')
    return specific_char_trimmed_text

def extract_file_id_from_url(url):
    parsed_url = urlparse(url)
    path_segments = parsed_url.path.split('/')
    
    path_segments = [segment for segment in path_segments if segment]
    
    if len(path_segments) >= 3:
        return path_segments[2]
    else:
        return None

def convert_html_to_markdown(html_file_path, markdown_file_path):
  
    output = ""
    with(open(html_file_path, 'r', encoding='utf-8')) as html_file:
        html_content = html_file.read()
        output = md(html_content)

    # Save the Markdown content to a new file
    with open(markdown_file_path, 'w', encoding='utf-8') as markdown_file:
        markdown_file.write(output)
        
def download_file(token, clientid, clientsecret, file_id):

    creds = gcp_get_credentials(refresh_token=token, client_id=clientid, client_secret=clientsecret)
    service = build('drive', 'v3', credentials=creds)
    file_metadata = service.files().get(fileId=file_id, supportsAllDrives=True).execute()
    if check_file_access(service, file_id):
        try:
        
            if file_metadata['mimeType'] is not None: 
                export_mime_type = 'text/html'
                request = service.files().export_media(fileId=file_id, mimeType=export_mime_type)
            else:
                request = service.files().get_media(fileId=file_id)

            file = io.BytesIO()
            downloader = MediaIoBaseDownload(file, request)
            done = False
            while done is False:
                status, done = downloader.next_chunk()
                print(f"Download {int(status.progress() * 100)}.")

            exported_filename = file_metadata['name'] if file_metadata['name'] is not None else "downloaded_file.html"
            trimmed_filename = remove_characters(exported_filename)
            trimmed_filename = remove_whitespaces(trimmed_filename)
            extension = ".html"
            filename =  "".join([trimmed_filename, extension])
            output_filename = "".join([trimmed_filename, ".md"])
            with open(filename, 'wb') as f:
                f.write(file.getvalue())
            
        except HttpError as error:
            print(f"An error occurred: {error}")
            print("Error details:", error.content)

        return filename, output_filename

def create_rfc_folder(category_path, rfc_name):
    if not os.path.exists(os.path.join(category_path, rfc_name)):
        os.makedirs(os.path.join(category_path, rfc_name))

def move_file_to_category(src_file_path, dst_path):
    shutil.move(src_file_path, dst_path)

def check_for_header(file_path, pattern):
    result = False

    with open(file_path) as fp:
        for line in fp:
           if re.match(pattern, line):
               return True
           
def validate_headers(expressions_dict, rfc_file_path):

    return_message_list = []

    for k,v in expressions_dict.items():
                dict_return_msg = {}
                if check_for_header(rfc_file_path, v):
                    msg_text = "Info: {} header has been found.".format(k)
                    msg_code = 0
                    dict_return_msg.update({ msg_code: msg_text })
                    return_message_list.append(dict_return_msg)
                else:
                    msg_text = "Error: {} header is invalid. Please check!".format(k)
                    msg_code = 1
                    dict_return_msg.update({ msg_code: msg_text })
                    return_message_list.append(dict_return_msg)

    return dict_return_msg, return_message_list

if __name__ == '__main__':

    client_id = os.getenv('CLIENT_ID')
    client_secret = os.getenv('CLIENT_SECRET')
    refresh_token = os.getenv('REFRESH_TOKEN')
    file_url = os.getenv('FILE_URL')
    category_path = os.getenv('RFC_CATEGORY')
    rfc_name = os.getenv('RFC_NAME')

    file_id = extract_file_id_from_url(file_url)
    filename, output_filename = download_file(refresh_token, client_id, client_secret, file_id)
    convert_html_to_markdown(filename, output_filename)
    create_rfc_folder(category_path, rfc_name)
    dst_file_path = os.path.join(category_path, rfc_name, filename)
    move_file_to_category(filename, dst_file_path)
    print(f"Conversion has been finished. The file has been saved to {dst_file_path}")
#!/bin/bash

# Test script for custom Mermaid renderer
# This script tests the renderer independently using Docker

set -e

echo "🧪 Testing Custom Mermaid Renderer"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test cases
declare -A test_cases=(
    ["simple_flowchart"]="flowchart TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E"
    
    ["architecture_with_icons"]="architecture-beta
    group \"Cloud Functions\"
        api(logos:aws-lambda)[API Gateway]
        processor(logos:azure-functions)[Data Processor]
    end

    group \"Data Storage\"
        db(logos:aws-dynamodb)[NoSQL DB]
        blob(logos:azure-blob-storage)[Blob Storage]
    end

    api --> processor
    processor --> db
    processor --> blob"
    
    ["sequence_diagram"]="sequenceDiagram
    participant A as Alice
    participant B as Bob
    A->>B: Hello Bob, how are you?
    B-->>A: Great!"
)

# Create test directory
TEST_DIR="./mermaid-test"
mkdir -p "$TEST_DIR"

echo -e "${YELLOW}📦 Building test Docker image...${NC}"

# Create Dockerfile for testing
cat > "$TEST_DIR/Dockerfile" << 'EOF'
FROM kovetskiy/mark:latest

# Update package list and install Python
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    && rm -rf /var/lib/apt/lists/*

# Install Playwright (using --break-system-packages for Docker)
RUN pip3 install --break-system-packages playwright

# Install Chromium browser
RUN python3 -m playwright install chromium

# Install system dependencies for Chromium
RUN python3 -m playwright install-deps

# Create app directory
RUN mkdir -p /app/icons

# Copy the custom renderer
COPY custom_mermaid_renderer.py /app/custom_mermaid_renderer.py
RUN chmod +x /app/custom_mermaid_renderer.py

# Create dummy icon files for testing
RUN echo '{"prefix":"aws","icons":{"lambda":{"body":"<svg viewBox=\"0 0 24 24\"><polygon points=\"12 2 2 22 22 22\" fill=\"#FF4F8B\"/></svg>"},"dynamodb":{"body":"<svg viewBox=\"0 0 24 24\"><rect x=\"2\" y=\"6\" width=\"20\" height=\"12\" fill=\"#3F48CC\"/></svg>"}}}' > /app/icons/aws-icons.json
RUN echo '{"prefix":"azure","icons":{"functions":{"body":"<svg viewBox=\"0 0 24 24\"><path d=\"M12 2L2 7v10l10 5 10-5V7z\" fill=\"#0078D4\"/></svg>"},"blob-storage":{"body":"<svg viewBox=\"0 0 24 24\"><circle cx=\"8\" cy=\"8\" r=\"4\" fill=\"#0078D4\"/><circle cx=\"16\" cy=\"16\" r=\"4\" fill=\"#0078D4\"/></svg>"}}}' > /app/icons/azure-icons.json
RUN echo '{"prefix":"gcp","icons":{"cloud-functions":{"body":"<svg viewBox=\"0 0 24 24\"><path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z\" fill=\"#4285F4\"/></svg>"}}}' > /app/icons/gcp-icons.json

WORKDIR /app
EOF

# Copy the renderer to test directory
cp "actions/publish-mark/custom_mermaid_renderer.py" "$TEST_DIR/"

# Build the test image
docker build -t my-custom-mark-renderer "$TEST_DIR"

echo -e "${GREEN}✅ Docker image built successfully${NC}"

# Function to run a test case
run_test() {
    local test_name="$1"
    local mermaid_code="$2"
    
    echo -e "${YELLOW}🔍 Testing: $test_name${NC}"
    
    # Create output directory for this test
    local output_dir="$TEST_DIR/output/$test_name"
    mkdir -p "$output_dir"
    
    # Run the test
    if echo "$mermaid_code" | docker run --rm -i my-custom-mark-renderer python3 /app/custom_mermaid_renderer.py > "$output_dir/output.b64" 2> "$output_dir/stderr.log"; then
        
        # Check if we got base64 output
        if [ -s "$output_dir/output.b64" ]; then
            # Decode base64 to PNG
            if base64 -d "$output_dir/output.b64" > "$output_dir/output.png" 2>/dev/null; then
                # Check if it's a valid PNG
                if file "$output_dir/output.png" | grep -q "PNG image"; then
                    local file_size=$(stat -f%z "$output_dir/output.png" 2>/dev/null || stat -c%s "$output_dir/output.png" 2>/dev/null)
                    echo -e "${GREEN}  ✅ SUCCESS: Generated PNG ($file_size bytes)${NC}"
                    echo -e "     📁 Output: $output_dir/output.png"
                else
                    echo -e "${RED}  ❌ FAILED: Invalid PNG file${NC}"
                    return 1
                fi
            else
                echo -e "${RED}  ❌ FAILED: Invalid base64 output${NC}"
                return 1
            fi
        else
            echo -e "${RED}  ❌ FAILED: No output generated${NC}"
            return 1
        fi
        
        # Show stderr if there are any warnings/info
        if [ -s "$output_dir/stderr.log" ]; then
            echo -e "${YELLOW}  📋 Logs:${NC}"
            tail -5 "$output_dir/stderr.log" | sed 's/^/    /'
        fi
        
    else
        echo -e "${RED}  ❌ FAILED: Script execution failed${NC}"
        if [ -s "$output_dir/stderr.log" ]; then
            echo -e "${RED}  📋 Error logs:${NC}"
            cat "$output_dir/stderr.log" | sed 's/^/    /'
        fi
        return 1
    fi
}

# Run all test cases
echo -e "\n${YELLOW}🚀 Running test cases...${NC}"
echo "========================"

failed_tests=0
total_tests=${#test_cases[@]}

for test_name in "${!test_cases[@]}"; do
    if ! run_test "$test_name" "${test_cases[$test_name]}"; then
        ((failed_tests++))
    fi
    echo ""
done

# Summary
echo -e "${YELLOW}📊 Test Summary${NC}"
echo "==============="
echo "Total tests: $total_tests"
echo "Passed: $((total_tests - failed_tests))"
echo "Failed: $failed_tests"

if [ $failed_tests -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed!${NC}"
    echo -e "${GREEN}Your custom Mermaid renderer is working correctly.${NC}"
else
    echo -e "${RED}❌ Some tests failed. Check the output above for details.${NC}"
    exit 1
fi

# Interactive test option
echo -e "\n${YELLOW}🔧 Interactive Testing${NC}"
echo "====================="
echo "To test interactively, run:"
echo "  docker run -it --rm my-custom-mark-renderer bash"
echo ""
echo "Then inside the container:"
echo "  echo 'graph TD; A-->B' | python3 /app/custom_mermaid_renderer.py > test.b64"
echo "  base64 -d test.b64 > test.png"
echo ""
echo "Test outputs are saved in: $TEST_DIR/output/"

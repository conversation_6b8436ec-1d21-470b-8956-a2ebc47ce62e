#!/bin/bash

# Test custom provider by creating a mermaid-go wrapper

set -e

echo "🔧 Testing Custom Provider Integration"
echo "====================================="

echo "🐳 Creating mermaid-go wrapper script..."

# Create a test that replaces mermaid-go with our custom script
docker run --rm my-custom-mark-renderer sh -c "
# Create a wrapper script that mimics mermaid-go interface
cat > /usr/local/bin/mermaid-go << 'EOF'
#!/bin/bash
# Custom mermaid-go wrapper that calls our Python renderer

echo 'Custom mermaid-go wrapper called with args:' \$@ >&2

# Call our Python renderer
python3 /app/custom_mermaid_renderer.py
EOF

chmod +x /usr/local/bin/mermaid-go

echo '✅ Created mermaid-go wrapper'
echo '📋 Wrapper content:'
cat /usr/local/bin/mermaid-go

echo ''
echo '🧪 Testing wrapper directly:'
echo 'graph TD; A --> B' | /usr/local/bin/mermaid-go > test_wrapper.b64 2> test_wrapper.log

echo 'Exit code:' \$?
echo 'Output size:' \$(wc -c < test_wrapper.b64) 'bytes'
echo 'Logs:'
cat test_wrapper.log | tail -5
"

echo ""
echo "🔧 Custom provider test completed."

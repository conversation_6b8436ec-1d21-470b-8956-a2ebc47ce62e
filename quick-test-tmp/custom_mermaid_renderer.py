#!/usr/bin/env python3
"""
Custom Mermaid.js rendering provider for kovetskiy/mark.
Renders Mermaid diagrams to PNG images with custom Iconify icon pack support.
"""

import asyncio
import base64
import json
import logging
import sys
from pathlib import Path
from playwright.async_api import async_playwright

# Configure logging to stderr
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

# Dummy Iconify JSON data for fallback
DUMMY_ICON_PACKS = {
    'aws': {
        "prefix": "aws",
        "icons": {
            "s3": {"body": "<svg viewBox='0 0 24 24'><circle cx='12' cy='12' r='10' fill='#FF9900'/></svg>"},
            "lambda": {"body": "<svg viewBox='0 0 24 24'><polygon points='12 2 2 22 22 22' fill='#FF4F8B'/></svg>"},
            "dynamodb": {"body": "<svg viewBox='0 0 24 24'><rect x='2' y='6' width='20' height='12' fill='#3F48CC'/></svg>"}
        }
    },
    'azure': {
        "prefix": "azure",
        "icons": {
            "functions": {"body": "<svg viewBox='0 0 24 24'><path d='M12 2L2 7v10l10 5 10-5V7z' fill='#0078D4'/></svg>"},
            "blob-storage": {"body": "<svg viewBox='0 0 24 24'><circle cx='8' cy='8' r='4' fill='#0078D4'/><circle cx='16' cy='16' r='4' fill='#0078D4'/></svg>"}
        }
    },
    'gcp': {
        "prefix": "gcp",
        "icons": {
            "cloud-functions": {"body": "<svg viewBox='0 0 24 24'><path d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z' fill='#4285F4'/></svg>"},
            "cloud-storage": {"body": "<svg viewBox='0 0 24 24'><rect x='4' y='8' width='16' height='8' fill='#34A853'/></svg>"}
        }
    }
}

def load_icon_pack(pack_name: str) -> dict:
    """Load Iconify JSON file or return dummy data as fallback."""
    icon_file = Path(f"/app/icons/{pack_name}-icons.json")
    
    try:
        if icon_file.exists():
            with open(icon_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"Loaded {pack_name} icon pack from {icon_file}")
                return data
        else:
            logger.warning(f"Icon file not found: {icon_file}, using dummy data")
            return DUMMY_ICON_PACKS.get(pack_name, {})
    except (json.JSONDecodeError, IOError) as e:
        logger.error(f"Failed to load {pack_name} icon pack: {e}")
        return DUMMY_ICON_PACKS.get(pack_name, {})

def download_mermaid_js() -> str:
    """Download Mermaid.js locally and return the file path."""
    import subprocess
    import os

    mermaid_path = "/tmp/mermaid.min.js"

    # Check if already downloaded
    if os.path.exists(mermaid_path) and os.path.getsize(mermaid_path) > 1000000:  # > 1MB
        logger.info(f"Mermaid.js already exists at {mermaid_path}")
        return mermaid_path

    try:
        logger.info("Downloading Mermaid.js from CDN...")
        # Use wget with SSL verification disabled
        result = subprocess.run([
            'wget', '--no-check-certificate', '-q', '-O', mermaid_path,
            'https://unpkg.com/mermaid@10/dist/mermaid.min.js'
        ], timeout=30, capture_output=True, text=True)

        if result.returncode == 0 and os.path.exists(mermaid_path):
            file_size = os.path.getsize(mermaid_path)
            logger.info(f"Successfully downloaded Mermaid.js ({file_size} bytes)")
            return mermaid_path
        else:
            logger.error(f"wget failed: {result.stderr}")
            raise Exception("Failed to download Mermaid.js")

    except Exception as e:
        logger.error(f"Failed to download Mermaid.js: {e}")
        # Create a minimal fallback
        fallback_content = """
        window.mermaid = {
            initialize: function() { console.log('Mermaid fallback initialized'); },
            render: function() {
                return Promise.resolve({
                    svg: '<svg><text x="10" y="20">Mermaid.js failed to load</text></svg>'
                });
            }
        };
        """
        with open(mermaid_path, 'w') as f:
            f.write(fallback_content)
        logger.info("Created fallback Mermaid.js")
        return mermaid_path

def generate_html_template(mermaid_code: str) -> str:
    """Generate HTML template with Mermaid diagram code."""
    # Escape the mermaid code to prevent HTML injection
    import html
    escaped_code = html.escape(mermaid_code)

    return f"""<!DOCTYPE html>
<html>
<head>
    <title>Mermaid Renderer</title>
    <style>
        body {{
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f8f7f4;
            font-family: Arial, sans-serif;
        }}
        .mermaid {{
            width: auto;
            height: auto;
            max-width: 90vw;
            max-height: 90vh;
            border: 1px solid #ddd;
            padding: 10px;
        }}
        .error {{
            color: red;
            padding: 20px;
            border: 1px solid red;
            background: #ffe6e6;
        }}
    </style>
</head>
<body>
    <div class="mermaid" id="mermaid-container">{escaped_code}</div>

    <script id="mermaid-script">
        // This will be replaced with actual Mermaid.js content
        console.log('Mermaid script placeholder');
    </script>

    <script>
        console.log('Initializing Mermaid renderer...');

        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {{
            console.log('DOM ready, checking for Mermaid...');

            // Check if mermaid is available
            if (typeof mermaid === 'undefined') {{
                console.error('Mermaid is not available');
                document.body.innerHTML = '<div class="error">Mermaid.js failed to load</div>';
                return;
            }}

            console.log('Mermaid is available, version:', mermaid.version || 'unknown');

            try {{
                // Initialize Mermaid
                mermaid.initialize({{
                    startOnLoad: false,
                    securityLevel: 'loose',
                    theme: 'default'
                }});

                // Get the mermaid element
                const element = document.getElementById('mermaid-container');
                if (!element) {{
                    throw new Error('Mermaid container not found');
                }}

                const diagramText = element.textContent.trim();
                console.log('Rendering diagram:', diagramText.substring(0, 100) + '...');

                // Render the diagram
                mermaid.render('diagram-' + Date.now(), diagramText).then(function(result) {{
                    console.log('Diagram rendered successfully');
                    element.innerHTML = result.svg;

                    // Call bindFunctions if available
                    if (result.bindFunctions) {{
                        result.bindFunctions(element);
                    }}
                }}).catch(function(error) {{
                    console.error('Mermaid render error:', error);
                    element.innerHTML = '<div class="error">Failed to render diagram: ' + error.message + '</div>';
                }});

            }} catch (error) {{
                console.error('Mermaid initialization error:', error);
                document.body.innerHTML = '<div class="error">Error: ' + error.message + '</div>';
            }}
        }});
    </script>
</body>
</html>"""

async def render_mermaid(mermaid_code: str) -> str:
    """Render Mermaid diagram to base64 encoded PNG."""
    logger.info("Starting Mermaid rendering process")

    browser = None
    try:
        # Download Mermaid.js locally first
        mermaid_js_path = download_mermaid_js()

        # Read the Mermaid.js content
        with open(mermaid_js_path, 'r', encoding='utf-8') as f:
            mermaid_js_content = f.read()

        # Launch browser
        logger.info("Launching headless Chromium browser")
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=True)
        page = await browser.new_page()

        # Generate HTML template
        html_content = generate_html_template(mermaid_code)

        # Replace the placeholder script with actual Mermaid.js content
        html_content = html_content.replace(
            '<script id="mermaid-script">\n        // This will be replaced with actual Mermaid.js content\n        console.log(\'Mermaid script placeholder\');\n    </script>',
            f'<script id="mermaid-script">\n{mermaid_js_content}\n    </script>'
        )

        logger.info("Setting page content with local Mermaid.js")
        await page.set_content(html_content)
        
        # Load and register icon packs
        logger.info("Loading and registering icon packs")
        aws_icons = load_icon_pack('aws')
        azure_icons = load_icon_pack('azure')
        gcp_icons = load_icon_pack('gcp')
        
        # Register icon packs with Mermaid
        await page.evaluate(f"""
            if (typeof mermaid !== 'undefined' && mermaid.registerIconPacks) {{
                mermaid.registerIconPacks([
                    {json.dumps(aws_icons)},
                    {json.dumps(azure_icons)},
                    {json.dumps(gcp_icons)}
                ]);
                console.log('Icon packs registered successfully');
            }} else {{
                console.warn('mermaid.registerIconPacks not available');
            }}
        """)
        
        # Debug: Check what's in the page
        logger.info("Checking page content before waiting for SVG")
        page_content = await page.content()
        logger.info(f"Page HTML length: {len(page_content)}")

        # Debug: Check for any errors in console
        page.on("console", lambda msg: logger.info(f"Browser console: {msg.text}"))
        page.on("pageerror", lambda error: logger.error(f"Browser error: {error}"))

        # Wait a bit for Mermaid to initialize
        await asyncio.sleep(2)

        # Check if mermaid div exists
        mermaid_div = await page.query_selector('.mermaid')
        if mermaid_div:
            div_content = await mermaid_div.inner_html()
            logger.info(f"Mermaid div content: {div_content[:200]}...")
        else:
            logger.error("Mermaid div not found!")

        # Wait for SVG to be rendered
        logger.info("Waiting for SVG element to be attached")
        try:
            await page.wait_for_selector('.mermaid svg', state='attached', timeout=30000)
        except Exception as e:
            # If SVG not found, let's see what's in the mermaid div
            logger.error(f"SVG not found: {e}")
            final_content = await page.content()
            logger.info(f"Final page content: {final_content[:1000]}...")
            raise
        
        # Allow additional time for full visual rendering
        await asyncio.sleep(1)
        
        # Take screenshot of the mermaid element
        logger.info("Taking screenshot")
        mermaid_element = page.locator('.mermaid')
        screenshot_bytes = await mermaid_element.screenshot()
        
        # Encode to base64
        base64_image = base64.b64encode(screenshot_bytes).decode('utf-8')
        logger.info("Screenshot taken and encoded to base64")
        
        return base64_image
        
    except Exception as e:
        logger.error(f"Error during rendering: {e}", exc_info=True)
        raise
    finally:
        if browser:
            logger.info("Closing browser")
            await browser.close()
            await playwright.stop()

async def main():
    """Main execution function."""
    try:
        logger.info("Custom Mermaid renderer started")
        
        # Read input from stdin
        logger.info("Reading Mermaid code from stdin")
        mermaid_code = sys.stdin.read().strip()
        
        if not mermaid_code:
            logger.error("No input provided via stdin")
            sys.exit(1)
        
        logger.info(f"Received Mermaid code ({len(mermaid_code)} characters)")
        
        # Render the diagram
        base64_image = await render_mermaid(mermaid_code)
        
        # Output base64 image to stdout
        print(base64_image)
        sys.stdout.flush()
        
        logger.info("Rendering completed successfully")
        
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())

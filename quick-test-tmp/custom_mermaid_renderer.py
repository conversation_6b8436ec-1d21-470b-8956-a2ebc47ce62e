#!/usr/bin/env python3
"""
Custom Mermaid.js rendering provider for kovetskiy/mark.
Renders Mermaid diagrams to PNG images with custom Iconify icon pack support.
"""

import asyncio
import base64
import json
import logging
import sys
from pathlib import Path
from playwright.async_api import async_playwright

# Configure logging to stderr
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

# Dummy Iconify JSON data for fallback
DUMMY_ICON_PACKS = {
    'aws': {
        "prefix": "aws",
        "icons": {
            "s3": {"body": "<svg viewBox='0 0 24 24'><circle cx='12' cy='12' r='10' fill='#FF9900'/></svg>"},
            "lambda": {"body": "<svg viewBox='0 0 24 24'><polygon points='12 2 2 22 22 22' fill='#FF4F8B'/></svg>"},
            "dynamodb": {"body": "<svg viewBox='0 0 24 24'><rect x='2' y='6' width='20' height='12' fill='#3F48CC'/></svg>"}
        }
    },
    'azure': {
        "prefix": "azure",
        "icons": {
            "functions": {"body": "<svg viewBox='0 0 24 24'><path d='M12 2L2 7v10l10 5 10-5V7z' fill='#0078D4'/></svg>"},
            "blob-storage": {"body": "<svg viewBox='0 0 24 24'><circle cx='8' cy='8' r='4' fill='#0078D4'/><circle cx='16' cy='16' r='4' fill='#0078D4'/></svg>"}
        }
    },
    'gcp': {
        "prefix": "gcp",
        "icons": {
            "cloud-functions": {"body": "<svg viewBox='0 0 24 24'><path d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z' fill='#4285F4'/></svg>"},
            "cloud-storage": {"body": "<svg viewBox='0 0 24 24'><rect x='4' y='8' width='16' height='8' fill='#34A853'/></svg>"}
        }
    }
}

def load_icon_pack(pack_name: str) -> dict:
    """Load Iconify JSON file or return dummy data as fallback."""
    icon_file = Path(f"/app/icons/{pack_name}-icons.json")
    
    try:
        if icon_file.exists():
            with open(icon_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"Loaded {pack_name} icon pack from {icon_file}")
                return data
        else:
            logger.warning(f"Icon file not found: {icon_file}, using dummy data")
            return DUMMY_ICON_PACKS.get(pack_name, {})
    except (json.JSONDecodeError, IOError) as e:
        logger.error(f"Failed to load {pack_name} icon pack: {e}")
        return DUMMY_ICON_PACKS.get(pack_name, {})

def generate_html_template(mermaid_code: str) -> str:
    """Generate HTML template with Mermaid diagram code."""
    return f"""<!DOCTYPE html>
<html>
<head>
    <title>Mermaid Renderer</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        body {{
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f8f7f4;
        }}
        .mermaid {{
            width: auto;
            height: auto;
            max-width: 90vw;
            max-height: 90vh;
        }}
    </style>
</head>
<body>
    <div class="mermaid">{mermaid_code}</div>
    <script>
        (async function() {{
            try {{
                // Initialize Mermaid
                mermaid.initialize({{ 
                    startOnLoad: false, 
                    securityLevel: 'loose' 
                }});
                
                // Get the mermaid element
                const element = document.querySelector('.mermaid');
                if (!element) {{
                    throw new Error('Mermaid element not found');
                }}
                
                // Render the diagram
                const {{ svg, bindFunctions }} = await mermaid.render('uniqueDiagramId', element.textContent);
                element.innerHTML = svg;
                
                // Call bindFunctions if available
                if (bindFunctions) {{
                    bindFunctions(element);
                }}
                
                console.log('Mermaid diagram rendered successfully');
            }} catch (error) {{
                console.error('Error rendering Mermaid diagram:', error);
                document.body.innerHTML = '<div style="color: red; padding: 20px;">Error: ' + error.message + '</div>';
            }}
        }})();
    </script>
</body>
</html>"""

async def render_mermaid(mermaid_code: str) -> str:
    """Render Mermaid diagram to base64 encoded PNG."""
    logger.info("Starting Mermaid rendering process")
    
    browser = None
    try:
        # Launch browser
        logger.info("Launching headless Chromium browser")
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # Generate and load HTML content
        html_content = generate_html_template(mermaid_code)
        logger.info("Setting page content")
        await page.set_content(html_content)
        
        # Load and register icon packs
        logger.info("Loading and registering icon packs")
        aws_icons = load_icon_pack('aws')
        azure_icons = load_icon_pack('azure')
        gcp_icons = load_icon_pack('gcp')
        
        # Register icon packs with Mermaid
        await page.evaluate(f"""
            if (typeof mermaid !== 'undefined' && mermaid.registerIconPacks) {{
                mermaid.registerIconPacks([
                    {json.dumps(aws_icons)},
                    {json.dumps(azure_icons)},
                    {json.dumps(gcp_icons)}
                ]);
                console.log('Icon packs registered successfully');
            }} else {{
                console.warn('mermaid.registerIconPacks not available');
            }}
        """)
        
        # Wait for SVG to be rendered
        logger.info("Waiting for SVG element to be attached")
        await page.wait_for_selector('.mermaid svg', state='attached', timeout=30000)
        
        # Allow additional time for full visual rendering
        await asyncio.sleep(1)
        
        # Take screenshot of the mermaid element
        logger.info("Taking screenshot")
        mermaid_element = page.locator('.mermaid')
        screenshot_bytes = await mermaid_element.screenshot()
        
        # Encode to base64
        base64_image = base64.b64encode(screenshot_bytes).decode('utf-8')
        logger.info("Screenshot taken and encoded to base64")
        
        return base64_image
        
    except Exception as e:
        logger.error(f"Error during rendering: {e}", exc_info=True)
        raise
    finally:
        if browser:
            logger.info("Closing browser")
            await browser.close()
            await playwright.stop()

async def main():
    """Main execution function."""
    try:
        logger.info("Custom Mermaid renderer started")
        
        # Read input from stdin
        logger.info("Reading Mermaid code from stdin")
        mermaid_code = sys.stdin.read().strip()
        
        if not mermaid_code:
            logger.error("No input provided via stdin")
            sys.exit(1)
        
        logger.info(f"Received Mermaid code ({len(mermaid_code)} characters)")
        
        # Render the diagram
        base64_image = await render_mermaid(mermaid_code)
        
        # Output base64 image to stdout
        print(base64_image)
        sys.stdout.flush()
        
        logger.info("Rendering completed successfully")
        
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())

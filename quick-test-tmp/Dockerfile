FROM kovetskiy/mark:latest

# Update package list and install Python
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    && rm -rf /var/lib/apt/lists/*

# Install Playwright (using --break-system-packages for Docker)
RUN pip3 install --break-system-packages playwright

# Install Chromium browser
RUN python3 -m playwright install chromium

# Install system dependencies for Chromium
RUN python3 -m playwright install-deps

# Create app directory and copy script
RUN mkdir -p /app
COPY custom_mermaid_renderer.py /app/custom_mermaid_renderer.py
RUN chmod +x /app/custom_mermaid_renderer.py

WORKDIR /app

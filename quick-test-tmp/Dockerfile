FROM kovetskiy/mark:latest

# Update package list and install Python + network tools
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    curl \
    wget \
    ca-certificates \
    dnsutils \
    iputils-ping \
    iproute2 \
    && rm -rf /var/lib/apt/lists/* \
    && update-ca-certificates

# Install Playwright (using --break-system-packages for Docker)
RUN pip3 install --break-system-packages playwright

# Install Chromium browser
RUN python3 -m playwright install chromium

# Install system dependencies for Chromium
RUN python3 -m playwright install-deps

# Create app directory and copy script
RUN mkdir -p /app
COPY custom_mermaid_renderer.py /app/custom_mermaid_renderer.py
RUN chmod +x /app/custom_mermaid_renderer.py

# Create mermaid-go wrapper to integrate with mark
RUN echo '#!/bin/bash' > /usr/local/bin/mermaid-go && \
    echo '# Custom mermaid-go wrapper that calls our Python renderer' >> /usr/local/bin/mermaid-go && \
    echo 'python3 /app/custom_mermaid_renderer.py' >> /usr/local/bin/mermaid-go && \
    chmod +x /usr/local/bin/mermaid-go

WORKDIR /app

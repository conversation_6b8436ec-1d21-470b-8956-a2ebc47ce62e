# WEX RFC Platform Content

## Project Summary

The WEX RFC Platform is a tool that manages portable technical documents for collaboration across the entire WEX Development Community. It allows teams to effectively discover, inform, and collaborate on paved roads, guardrails, patterns, practices, and informational material.

The platform uses:

- The `arch-rfc-content` GitHub repository to manage content as markdown
- The WEX RFC Confluence space for comments, notifications, and interoperability
- The Finn AI Google Chat Bot to enable discovery of content

### Table of Contents

- [Installation](#installation)
- [Usage](#usage)
- [Contributions](#contributions)
- [RFC Reference Material](#rfc-reference-material)

## Installation

1. Clone this repository:

   ```text
   git clone https://github.com/wexinc/arch-rfc-content.git
   cd arch-rfc-content
   ```

2. Install dependencies:

   - A markdown editor (e.g. Visual Studio Code)
   - Markdown code analyzer (e.g. markdownlint)
   - Spelling and grammar analyzer (e.g. Code Spell Checker)

3. Configure your Confluence notification preferences:
   - Update your notification preferences to ensure you are only notified about the types of content and changes being published

## Usage

> Refer to [RFC1- Using RFCs](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154468680237/RFC1-Using+RFCs) for more detailed information on working with RFCs

### Content Categories and Sub-Types

Content is organized at two levels to facilitate a good experience with discovery and isolation.  

- The `Category` aligns with current WEX architecture methodologies based on how and when a technical document is leveraged.
- The `Sub-Types` within each category align to the type of technical document being published

If you feel that a new content category or sub-type is warranted, please comment on [RFC1- Using RFCs](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154468680237/RFC1-Using+RFCs) to facilitate that discussion.

#### Document Category Decision Tree

```mermaid
graph LR
   A[Document] --> B{Reusable, completed work ratified by WEX governance?};
   B -- Yes --> C[Paved Road];
   B -- No --> D{Provides controls/guidelines with flexibility within policies?};
   D -- Yes --> E[Guardrail];
   D -- No --> F{Describes ways of working for WEX IT objectives?};
   F -- Yes --> G[Practice];
   F -- No --> H{High-level design of a proposed solution?};
   H -- Yes --> I[Design];
   H -- No --> J{Shares information with WEX development community without ratification?};
   J -- Yes --> K[Informational];
```

#### Paved Roads

Paved roads are re-usable piece of completed work documented in an RFC and ratified by a WEX governance body.

- **Canonical Systems:** standard tools and services that are used across WEX
- **Reference Architectures:** battle-tested solutions that improve time-to-delivery, security, and quality
- **Golden Path:** an opinionated and interconnected collection of paved roads streamlining development and operations (i.e. WEX Fabric)

```mermaid
graph LR
   A[Paved Road] --> B{Canonical system used across WEX for engineering?};
   B -- Yes --> C[Canonical Systems];
   B -- No --> D{Battle-tested solution blueprint improving time-to-delivery, security, quality?};
   D -- Yes --> E[Reference Architecture];
   D -- No --> F{Collection of paved roads streamlining development & operations?};
   F -- Yes --> G[Golden Path];
```

#### Guardrails

Guardrails are controls that allow developers the flexibility to innovate within the boundaries of defined policies with minimal impact to velocity.

- **Design Patterns:** small detailed recipes for solving common problems
- **Design Specification:** outlines the architecture, components, methodologies, and requirements of a solution
- **WEX Governance:** includes policies, standards, and procedures that must be followed to protect WEX and its customers

```mermaid
graph LR
   A[Guardrail] --> B{Partial, detailed solution for common problem?};
    B -- Yes --> C[Design Pattern];
    B -- No --> D{Outlines architecture, components, methodologies, requirements?};
    D -- Yes --> E[Design Specification];
    D -- No --> F{Policies and procedures to protect WEX & customers?};
    F -- Yes --> G[WEX Governance];
```

#### Practices

Practices are "ways of working" that drive improvements on WEX IT strategic objectives.

- **Design:** practices used when conceptualizing, planning, and implementing solutions
- **Delivery:** practices used when updating solutions and making changes available to users
- **Support:** practices used when monitoring, operating, and administering solutions
- **Initiatives:** are projects which promote, enable, and assist teams with practices

```mermaid
graph LR
   A[Practices] --> B{Used for conceptualizing, planning, implementing solutions?};
   B -- Yes --> C[Design];
   B -- No --> D{Used for updating solutions & making changes available?};
   D -- Yes --> E[Delivery];
   D -- No --> F{Used for monitoring, operating, administering solutions?};
   F -- Yes --> G[Support];
   F -- No --> H{Project promoting, enabling, assisting teams with practices?};
   H -- Yes --> I[Initiative];
```

#### Designs

Contains high-level design documents and product profile summaries that outline architectural solutions and product strategies by WEX organizations.

> [!info]
> Designs are grouped by the organization sponsoring the solution.  Contact `<EMAIL>` if you do not find your organization.

#### Informational

Informational content is any kind of information that is intended to be shared with the entire WEX development community.  Informational content is never ratified.

- **Discussion:** summary of a conversation (mail/meeting/chat) about a topic
- **White Paper:** opinionated documents that explains a complex technology, product, or concept, providing in-depth analysis, research, and recommendations
- **Case Study:** detailed analysis of a specific software project or application
- **Instructions:** describe how to perform a particular task
- **General Information:** includes basic factual information about a topic.
- **Architecture Decision Records:** describe a design choice including the options considered and reason for the decision

```mermaid
graph LR
   A[Informational] --> B{Summary of a conversation about a topic?};
    B -- Yes --> C[Discussion];
    B -- No --> D{Opinionated document explaining complex technology/product/concept?};
    D -- Yes --> E[White Paper];
    D -- No --> F{Detailed analysis of a specific software project/application?};
    F -- Yes --> G[Case Study];
    F -- No --> H{Describes how to perform a particular task?};
    H -- Yes --> I[Instructions];
    H -- No --> J{Basic factual information about a topic?};
    J -- Yes --> K[General Information];
    J -- No --> L{Describes a design choice with options & reasons?};
    L -- Yes --> M[Architecture Decision Records];
```

### Publishing Content

```text
These standards (or lack of them) are stated explicitly for two reasons.
First, there is a tendency to view a written statement as ipso facto
authoritative, and we hope to promote the exchange and discussion of
considerably less than authoritative ideas.  Second, there is a natural
hesitancy to publish something unpolished, and we hope to ease this
inhibition.
  -- Stephen Crocker RFC3 (https://tools.ietf.org/html/rfc3)
```

All published content will be auto-assigned an RFC number after being committed.  Content may be updated as long as it has not been ratified by a WEX governance body.  New RFCs are required to extend or replace ratified standards.

You may opt to use a pre-built markdown content template found in this repo under: `/templates`.

> [!info]
> Drafting RFCs with Copilot is an easy way to get started.
> Refer to [RFC-454 Drafting RFCs with Copilot](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155223294084/RFC-454+Drafting+RFCs+with+Copilot) for more information

The [Markdown To Confluence Tools](https://markdown-confluence.com/introduction.html) used for publishing supports a number of formatting features you can leverage in your markdown. (e.g. images, mermaid, mentions, etc)

To publish content:

1. Clone the repository locally
2. Create and switch to a new topic branch for your RFC
3. Create a content subfolder under the appropriate category and subtype directory
4. Create your RFC in Markdown format with the name `index.md` in your folder and include the required title, header metadata, and at least one line of content
5. Add additional supporting files as necessary (e.g. images, additional markdown files, etc)
6. Commit the document to your branch and create a pull request
7. Complete the PR which will trigger the GitHub Actions workflow
8. The workflow will assign a RFC number, convert the Markdown document to Confluence format, and publish it to the WEX RFC Confluence space

> Published content will be discoverable by Finn AI within 4-6 hours.

Confluence should be used for community comments, however PR comments may be used by your team for refinement before bringing to the broader community.

### Standards Ratification

We are working with the governance bodies to design the ratification process so that every body's scope and requirements are incorporated.

At a high level, the ratification process will involve:

- RFC for a paved road, guardrail, or practice must include all required details for relevant governance bodies
- A period of public commentary on the RFC
- A pre-review to ensure that all ratification requirements are met
- A formal approval by relevant governance bodies for ratification
- Ratified content will be closed for modification but remain open for comments upon ratification
- New RFCs will be required to extend or replace current ratified RFCs

## Contributions

Contributions are welcome!

Planned features and capabilities leverage the following design principles:

- Low barrier for comments and discovery (including non-technical roles)
- Low barrier for publishing technical documents (by technical resources)
- All published content should have value to the broader WEX community (not a single team, product, or line-of-business)
- Encourage iterative evolution of content based on collaboration via comments
- Documents should be portable (re-usability) for ratification and community collaboration
- Built using existing WEX-approved tools, services, and technology
- Aligned with the WEX Knowledge Management strategy from TISO

If you'd like to improve the RFC Platform, follow these steps:

1. Review the open work items under Jira Epic [ARC-2463](https://wexinc.atlassian.net/browse/ARC-2463)
2. Clone the repository locally
3. Create and switch to a new topic branch for your enhancement or bug fix
4. Make your changes and submit a pull request.
5. Ensure your code passes tests and adheres to the project's guidelines.
6. Follow-up with Architecture for PR review and approval

> **Note:** Changes for RFC content under `/content/{category}/{subtype}/` do not require a PR review and approval.  As long as the PR workflow passses successfully the PR can be merged at the author's discretion.

Happy collaborating! 🚀

## RFC Reference Material

- [WEX RFC Content Repository](https://github.com/wexinc/arch-rfc-content)
- [WEX RFC Confluence Space](https://wexinc.atlassian.net/wiki/spaces/ITRFC/overview)
- [How We Build Software at Netflix (Blog)](http://techblog.netflix.com/2016/03/how-we-build-code-at-netflix.html)
- [Guardrails and Paved Roads with Jason Chan (YouTube)](https://youtu.be/c0Hbz0ZBv9U)
- [Introduction to Guardrails and Paved Roads - Chad Metcalf (Blog)](https://www.daytona.io/dotfiles/introduction-to-guardrails-and-paved-paths)
- [Paved versus golden paths in Platform Engineering - Steve Fenton (Blog)](https://octopus.com/blog/paved-versus-golden-paths-platform-engineering)
- [Maximizing Developer Effectiveness - Tim Cochran (Blog)](https://martinfowler.com/articles/developer-effectiveness.html)
- [Talk about Platforms - Evan Bottcher (Blog)](https://martinfowler.com/articles/talk-about-platforms.html)
- [IETF RFC 3 - Document Conventions](https://datatracker.ietf.org/doc/html/rfc3)
- [IETF RFC 2119 - RFC Requirement Levels](https://datatracker.ietf.org/doc/html/rfc2119)

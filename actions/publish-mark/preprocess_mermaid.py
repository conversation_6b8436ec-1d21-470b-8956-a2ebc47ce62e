#!/usr/bin/env python3
"""
Pre-process Mermaid diagrams in markdown files.

This script finds Mermaid code blocks, renders them to PNG using our custom renderer,
saves the PNGs as attachments, and replaces the Mermaid blocks with image references.

This works around <PERSON>'s limited provider support by pre-processing diagrams.
"""

import os
import sys
import re
import hashlib
import base64
import asyncio
import logging
from pathlib import Path
from werkzeug.utils import secure_filename

# Import our custom renderer
sys.path.append('/app')
from custom_mermaid_renderer import render_mermaid

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger(__name__)

def find_mermaid_blocks(content: str) -> list:
    """Find all Mermaid code blocks in markdown content."""
    # Pattern to match ```mermaid ... ``` blocks
    pattern = r'```mermaid\n(.*?)\n```'
    matches = re.findall(pattern, content, re.DOTALL)
    
    blocks = []
    for match in matches:
        # Clean up the mermaid code
        mermaid_code = match.strip()
        if mermaid_code:
            blocks.append(mermaid_code)
    
    return blocks

def validate_path(path: Path, base_path: Path) -> Path:
    """Validate that path is within base_path to prevent traversal attacks."""
    try:
        resolved_path = path.resolve()
        resolved_base = base_path.resolve()
        resolved_path.relative_to(resolved_base)
        return resolved_path
    except ValueError:
        raise ValueError(f"Path traversal detected: {path}")

def generate_image_filename(mermaid_code: str) -> str:
    """Generate a unique filename for the Mermaid diagram."""
    # Create hash of the mermaid code for unique filename
    hash_obj = hashlib.sha256(mermaid_code.encode('utf-8'))
    filename = f"mermaid-{hash_obj.hexdigest()[:16]}.png"
    return secure_filename(filename)

async def process_mermaid_block(mermaid_code: str, output_dir: Path) -> str:
    """Process a single Mermaid block and return the image filename."""
    logger.info(f"Processing Mermaid block ({len(mermaid_code)} chars)")

    # Generate filename
    filename = generate_image_filename(mermaid_code)
    output_path = output_dir / filename

    # Validate path to prevent traversal
    try:
        validated_path = validate_path(output_path, output_dir.parent)
    except ValueError as e:
        logger.error(f"Path validation failed: {e}")
        return None

    # Check if already processed
    if validated_path.exists():
        logger.info(f"Using existing image: {filename}")
        return filename
    
    try:
        # Render the diagram using our custom renderer
        base64_image = await render_mermaid(mermaid_code)
        
        # Decode base64 to binary PNG
        png_data = base64.b64decode(base64_image)
        
        # Save PNG file
        with open(validated_path, 'wb') as f:
            f.write(png_data)
        
        logger.info(f"Generated image: {filename} ({len(png_data)} bytes)")
        return filename
        
    except Exception as e:
        logger.error(f"Failed to render Mermaid diagram: {e}")
        # Return None to indicate failure
        return None

def replace_mermaid_blocks(content: str, image_filenames: list) -> str:
    """Replace Mermaid blocks with image references."""
    # Pattern to match ```mermaid ... ``` blocks
    pattern = r'```mermaid\n(.*?)\n```'
    
    def replace_block(match):
        if not image_filenames:
            logger.warning("No image filename available for replacement")
            return match.group(0)  # Return original block
        
        filename = image_filenames.pop(0)
        if filename is None:
            logger.warning("Image generation failed, keeping original block")
            return match.group(0)  # Return original block
        
        # Replace with image reference
        return f"![Mermaid Diagram]({filename})"
    
    return re.sub(pattern, replace_block, content, flags=re.DOTALL)

async def process_markdown_file(file_path: Path, base_path: Path) -> bool:
    """Process a single markdown file."""
    logger.info(f"Processing file: {file_path}")

    try:
        # Validate file path
        validated_file_path = validate_path(file_path, base_path)

        # Read the markdown file
        with open(validated_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find Mermaid blocks
        mermaid_blocks = find_mermaid_blocks(content)
        if not mermaid_blocks:
            logger.info(f"No Mermaid blocks found in {file_path}")
            return True
        
        logger.info(f"Found {len(mermaid_blocks)} Mermaid blocks")
        
        # Create images directory
        images_dir = file_path.parent / "images"
        images_dir.mkdir(exist_ok=True)
        
        # Process each Mermaid block
        image_filenames = []
        for mermaid_code in mermaid_blocks:
            filename = await process_mermaid_block(mermaid_code, images_dir)
            image_filenames.append(f"images/{filename}" if filename else None)
        
        # Replace Mermaid blocks with image references
        new_content = replace_mermaid_blocks(content, image_filenames.copy())
        
        # Write the modified content back
        with open(validated_file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        logger.info(f"Successfully processed {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to process {file_path}: {e}")
        return False

async def main():
    """Main function to process all markdown files."""
    if len(sys.argv) != 2:
        logger.error("Usage: python3 preprocess_mermaid.py <content_folder>")
        sys.exit(1)
    
    content_folder = Path(sys.argv[1])
    if not content_folder.exists():
        logger.error(f"Content folder does not exist: {content_folder}")
        sys.exit(1)
    
    logger.info(f"Pre-processing Mermaid diagrams in: {content_folder}")
    
    # Find all index.md files
    markdown_files = list(content_folder.glob("**/index.md"))
    logger.info(f"Found {len(markdown_files)} markdown files")
    
    # Process each file
    success_count = 0
    for file_path in markdown_files:
        if await process_markdown_file(file_path, content_folder):
            success_count += 1
    
    logger.info(f"Successfully processed {success_count}/{len(markdown_files)} files")
    
    if success_count == len(markdown_files):
        logger.info("All files processed successfully")
        sys.exit(0)
    else:
        logger.error("Some files failed to process")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())

name: "Publish using Mark"
description: "Publishes markdown to confluence using Mark"
inputs:
  content_root_folders:
    description: "List of folders and subfolders to import to Confluence"
    required: true
  filter_content_pattern:
    description: "Pattern to filter content folders"
    required: false
    default: "RFC"
  root_content_folder:
    description: "Root content folder to import to Confluence"
    required: true
  changed_files:
    description: "List of changed files from evaluate-changed-files action"
    required: false
    default: ""
  site_space_id:
    description: "Confluence space ID"
    required: true
  site_root_page:
    description: "Confluence page default root url"
    required: true
  include_path:
    description: "Include path for Mark templates"
    required: false
    default: "include"
  compile_only:
    description: "Show resulting HTML and don't update Confluence page content."
    required: false
    default: "false"
  deploy_all_content:
    description: "When set to true, deploys all content instead of only changed files"
    required: false
    default: "false"
  atlassian_user_name:
    description: "Atlassian username for authentication"
    required: true
  atlassian_api_token:
    description: "Atlassian API token for authentication"
    required: true

runs:
  using: "composite"
  steps:
    - name: Publish to Confluence
      shell: bash
      env:
        ID_PREFIX: ${{ inputs.filter_content_pattern}}
        FOLDERS: ${{ inputs.content_root_folders}}
        CHANGED_FILES: ${{ inputs.changed_files }}
      run: |
        if [[ '${{ inputs.compile_only }}' == "true" ]]; then
          compile="--compile-only"
          echo "DEBUG: compile only enabled"
        fi

        if [[ '${{ inputs.deploy_all_content }}' == "true" ]]; then
          DEPLOY_ALL_CONTENT=true
          # Use content folder directly when deploying all content
          DEPLOY_FOLDER="${{ github.workspace }}"
          
          # Remove files without content ID
          for folder in $FOLDERS; do
            find "$folder" -mindepth 3 -maxdepth 3 -type f -name "index.md" | while read -r file; do            
            if [ -z "$(grep -m 1 "<\!-- Title: ${ID_PREFIX}-*[0-9]" "$file")" ]; then
              parent_folder=$(dirname "$file")
              echo "Content ID not found. Deleting file: $file"
              rm -f "$file"
            fi
            done
          done
          HAS_FILES_TO_PUBLISH=true

        else
          HAS_FILES_TO_PUBLISH=false
          DEPLOY_ALL_CONTENT=false
          # Create temporary deploy folder for selective deploys
          DEPLOY_FOLDER="${{ github.workspace }}/deploy"
          mkdir -p "$DEPLOY_FOLDER"         
          # Copy macros.md to deploy folder
          mkdir -p "$DEPLOY_FOLDER/${{ inputs.include_path }}"
          cp -r "${{ github.workspace }}/${{ inputs.include_path }}"/* "$DEPLOY_FOLDER/${{ inputs.include_path }}/"

          if [ -n "$CHANGED_FILES" ]; then
            for file in $CHANGED_FILES; do
              if [ -f "$file" ] && [[ "$file" == *index.md ]]; then
                # Check if file has the required title tag
                if grep -q "<\!-- Title: ${ID_PREFIX}-*[0-9]" "$file"; then                  
                  # Create directory structure in deploy folder
                  parent_dir=$(dirname "$file")
                  target_dir="$DEPLOY_FOLDER/$parent_dir"
                  mkdir -p "$target_dir"
                  # Copy the file to deploy folder
                  cp -r "$parent_dir/"* "$target_dir/"
                  echo "Copied file with valid content ID: $file"
                  HAS_FILES_TO_PUBLISH=true
                else
                  echo "Ignoring file without required content ID: $file"
                fi
              fi
            done
          else
            echo "No changed files provided. Nothing to publish."
          fi
        fi

        # Only run docker if there are files to publish
        if [ "$HAS_FILES_TO_PUBLISH" = true ]; then
          echo "Found files to publish, running mark docker command"
          docker run --rm \
            -v "$DEPLOY_FOLDER:/work" \
            -v "${{ github.action_path }}/custom_mermaid_renderer.py:/app/custom_mermaid_renderer.py" \
            -w /work \
            -e MARK_SPACE="${{ inputs.site_space_id }}" \
            -e MARK_BASE_URL="${{ inputs.site_root_page }}" \
            -e MARK_USERNAME="${{ inputs.atlassian_user_name }}" \
            -e MARK_PASSWORD="${{ inputs.atlassian_api_token }}" \
            -e MARK_LOG_LEVEL="info" \
            -e MARK_INCLUDE_PATH="${{ inputs.include_path }}" \
            kovetskiy/mark:latest sh -c "
              # Install Python dependencies
              pip install --break-system-packages playwright && \
              python3 -m playwright install chromium && \
              python3 -m playwright install-deps && \
              # Make the custom renderer executable
              chmod +x /app/custom_mermaid_renderer.py && \
              # Run mark with custom mermaid provider
              mark ${compile:-} \
                --mermaid-provider='python3 /app/custom_mermaid_renderer.py' \
                --mermaid-scale 2 \
                --title-from-h1 \
                --edit-lock \
                --version-message='Updating Files GitHub Ref: ${{ github.event_name }}' \
                --drop-h1 \
                -f '${{ inputs.root_content_folder }}/**/index.md'
            " || exit 1;
        else
          echo "No files with valid content ID found to publish. Skipping mark docker command."
        fi
          
        # Clean up
        if [ "$DEPLOY_ALL_CONTENT" = false ]; then
          echo "Cleaning up temporary deploy folder"
          rm -rf "$DEPLOY_FOLDER"
        else
          echo "Skipping cleanup for all content deploy as we used the workspace directly"
        fi

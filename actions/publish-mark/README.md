# Publish Mark Action

This GitHub Action publishes markdown content to Confluence using the [kovetskiy/mark](https://github.com/kovetskiy/mark) tool with a custom Mermaid.js renderer.

## Custom Mermaid Renderer

The action includes a custom Python-based Mermaid renderer (`custom_mermaid_renderer.py`) that provides:

- **Custom Iconify Icon Pack Support**: Supports AWS, Azure, and GCP icon packs for architecture diagrams
- **High-Quality PNG Output**: Uses headless Chromium via Playwright for accurate rendering
- **Robust Error Handling**: Graceful fallbacks and comprehensive logging
- **Docker Integration**: Seamlessly integrates with the kovetskiy/mark Docker container

### Supported Icon Packs

The renderer supports the following Iconify icon packs:

- **AWS Icons** (`aws`): S3, Lambda, DynamoDB, etc.
- **Azure Icons** (`azure`): Functions, Blob Storage, etc.  
- **GCP Icons** (`gcp`): Cloud Functions, Cloud Storage, etc.

### Example Mermaid Diagram

```mermaid
architecture-beta
  group "Cloud Functions"
    api(logos:aws-lambda)[API Gateway]
    processor(logos:azure-functions)[Data Processor]
  end

  group "Data Storage"
    db(logos:aws-dynamodb)[NoSQL DB]
    blob(logos:azure-blob-storage)[Blob Storage]
  end

  api --> processor
  processor --> db
  processor --> blob
```

### Icon Pack Configuration

The renderer looks for Iconify JSON files at:
- `/app/icons/aws-icons.json`
- `/app/icons/azure-icons.json`
- `/app/icons/gcp-icons.json`

If these files are not found, the renderer uses built-in dummy icon data to ensure rendering continues without errors.

## Action Inputs

| Input | Description | Required | Default |
|-------|-------------|----------|---------|
| `content_root_folders` | List of folders and subfolders to import to Confluence | Yes | |
| `filter_content_pattern` | Pattern to filter content folders | No | `"RFC"` |
| `root_content_folder` | Root content folder to import to Confluence | Yes | |
| `changed_files` | List of changed files from evaluate-changed-files action | No | `""` |
| `site_space_id` | Confluence space ID | Yes | |
| `site_root_page` | Confluence page default root url | Yes | |
| `include_path` | Include path for Mark templates | No | `"include"` |
| `compile_only` | Show resulting HTML and don't update Confluence page content | No | `"false"` |
| `deploy_all_content` | When set to true, deploys all content instead of only changed files | No | `"false"` |
| `atlassian_user_name` | Atlassian username for authentication | Yes | |
| `atlassian_api_token` | Atlassian API token for authentication | Yes | |

## Usage

```yaml
- name: Publish to Confluence
  uses: ./actions/publish-mark
  with:
    content_root_folders: "content"
    root_content_folder: "content"
    site_space_id: ${{ secrets.CONFLUENCE_SPACE_ID }}
    site_root_page: ${{ secrets.CONFLUENCE_BASE_URL }}
    atlassian_user_name: ${{ secrets.ATLASSIAN_USERNAME }}
    atlassian_api_token: ${{ secrets.ATLASSIAN_API_TOKEN }}
```

## Technical Details

The action:

1. **Prepares Content**: Copies changed files or all content to a deployment folder
2. **Validates Content**: Ensures files have required content IDs
3. **Runs Docker Container**: Executes kovetskiy/mark with the custom Mermaid renderer
4. **Installs Dependencies**: Automatically installs Playwright and Chromium in the container
5. **Renders Diagrams**: Uses the custom Python renderer for Mermaid diagrams
6. **Publishes to Confluence**: Updates or creates pages in the specified Confluence space

The custom renderer is mounted into the Docker container and executed as the Mermaid provider, ensuring consistent and high-quality diagram rendering with custom icon support.

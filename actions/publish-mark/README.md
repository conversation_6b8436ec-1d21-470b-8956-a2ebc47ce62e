# Publish Mark Action

This GitHub Action publishes markdown content to Confluence using the [kovetskiy/mark](https://github.com/kovetskiy/mark) tool with a custom Mermaid.js renderer.

## Custom Mermaid Renderer

The action includes a custom Python-based Mermaid renderer (`custom_mermaid_renderer.py`) that provides:

- **Mermaid v11.8.1 Support**: Full support for `architecture-beta` diagrams and latest features
- **Dynamic Iconify Icon Support**: Automatically downloads any icon referenced in Mermaid code from [Icones.js.org](https://icones.js.org/)
- **SSL Certificate Handling**: Resolves Docker environment connectivity issues with `--no-check-certificate`
- **High-Quality PNG Output**: Uses headless Chromium via Playwright for accurate rendering
- **Robust Error Handling**: Graceful fallbacks and comprehensive logging
- **Docker Integration**: Seamlessly integrates with the kovetskiy/mark Docker container via mermaid-go wrapper

### Supported Icon Packs

The renderer supports the following Iconify icon packs:

- **AWS Icons** (`aws`): S3, Lambda, DynamoDB, etc.
- **Azure Icons** (`azure`): Functions, Blob Storage, etc.  
- **GCP Icons** (`gcp`): Cloud Functions, Cloud Storage, etc.

### Example Mermaid Diagram

```mermaid
architecture-beta
  group "Cloud Functions"
    api(logos:aws-lambda)[API Gateway]
    processor(logos:azure-functions)[Data Processor]
  end

  group "Data Storage"
    db(logos:aws-dynamodb)[NoSQL DB]
    blob(logos:azure-blob-storage)[Blob Storage]
  end

  api --> processor
  processor --> db
  processor --> blob
```

### Dynamic Icon Support

The renderer automatically detects and downloads icons referenced in your Mermaid code:

- **Supported Collections**: Any collection from [Iconify](https://icones.js.org/) (e.g., `simple-icons`, `logos`, `mdi`, etc.)
- **Icon Format**: Use `(collection:icon-name)` syntax in your Mermaid diagrams
- **Automatic Download**: Icons are downloaded on-demand during rendering
- **Fallback Icons**: Custom fallback icons are generated if downloads fail

### Example Usage with Dynamic Icons

```mermaid
architecture-beta
    group cloud_functions(cloud)[Cloud Functions]
        service api(logos:aws-lambda)[API Gateway] in cloud_functions
        service processor(simple-icons:azurefunctions)[Data Processor] in cloud_functions

    group storage(database)[Data Storage]
        service db(logos:aws-dynamodb)[NoSQL DB] in storage
        service blob(simple-icons:microsoftazure)[Blob Storage] in storage

    api:R --> L:processor
    processor:B --> T:db
    processor:B --> T:blob
```

The renderer will automatically:

1. Parse the Mermaid code for icon references
2. Download `logos:aws-lambda`, `simple-icons:azurefunctions`, `logos:aws-dynamodb`, and `simple-icons:microsoftazure`
3. Register them with Mermaid for rendering
4. Generate fallback icons if any downloads fail

## Action Inputs

| Input | Description | Required | Default |
|-------|-------------|----------|---------|
| `content_root_folders` | List of folders and subfolders to import to Confluence | Yes | |
| `filter_content_pattern` | Pattern to filter content folders | No | `"RFC"` |
| `root_content_folder` | Root content folder to import to Confluence | Yes | |
| `changed_files` | List of changed files from evaluate-changed-files action | No | `""` |
| `site_space_id` | Confluence space ID | Yes | |
| `site_root_page` | Confluence page default root url | Yes | |
| `include_path` | Include path for Mark templates | No | `"include"` |
| `compile_only` | Show resulting HTML and don't update Confluence page content | No | `"false"` |
| `deploy_all_content` | When set to true, deploys all content instead of only changed files | No | `"false"` |
| `atlassian_user_name` | Atlassian username for authentication | Yes | |
| `atlassian_api_token` | Atlassian API token for authentication | Yes | |

## Usage

```yaml
- name: Publish to Confluence
  uses: ./actions/publish-mark
  with:
    content_root_folders: "content"
    root_content_folder: "content"
    site_space_id: ${{ secrets.CONFLUENCE_SPACE_ID }}
    site_root_page: ${{ secrets.CONFLUENCE_BASE_URL }}
    atlassian_user_name: ${{ secrets.ATLASSIAN_USERNAME }}
    atlassian_api_token: ${{ secrets.ATLASSIAN_API_TOKEN }}
```

## Technical Details

The action:

1. **Prepares Content**: Copies changed files or all content to a deployment folder
2. **Validates Content**: Ensures files have required content IDs
3. **Builds Custom Docker Image**: Creates image with Mermaid renderer and all dependencies
4. **Renders Diagrams**: Uses the custom Python renderer for Mermaid diagrams with real icons
5. **Publishes to Confluence**: Updates or creates pages in the specified Confluence space

### Custom Docker Image

The action builds a custom Docker image on-demand that includes:

- **Base**: `kovetskiy/mark:latest` with mark publishing capabilities
- **Pre-installed Dependencies**: Playwright, Chromium, Python packages
- **Custom Mermaid Renderer**: Python script with Mermaid v11.8.1 support
- **mermaid-go Wrapper**: Seamless integration with mark's provider system
- **Build-time Testing**: Validates renderer during image build

### Key Implementation Details

- **Custom Docker Image**: Built from `kovetskiy/mark` with pre-installed dependencies
- **SSL Certificate Handling**: Uses `wget --no-check-certificate` to bypass Docker SSL issues
- **Icon Source**: Downloads from `simple-icons` collection via Iconify API (`https://api.iconify.design/`)
- **Fallback Strategy**: Provides custom SVG fallbacks if icon downloads fail
- **Integration Method**: Pre-installed `/usr/local/bin/mermaid-go` wrapper calls custom Python script
- **Mermaid Version**: Specifically uses v11.8.1 for `architecture-beta` diagram support
- **Performance**: No runtime dependency installation - everything is pre-built in the image

### Docker Image Build Process

The custom Docker image is built on-demand during action execution:

1. **Build Context**: Uses `actions/publish-mark/` directory as build context
2. **Dockerfile**: Builds from `actions/publish-mark/Dockerfile`
3. **Build-time Testing**: Validates Mermaid renderer during image build
4. **Caching**: Docker layer caching optimizes subsequent builds

The custom renderer ensures consistent and high-quality diagram rendering with authentic cloud provider icons.

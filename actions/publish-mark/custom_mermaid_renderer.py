#!/usr/bin/env python3
"""
Custom Mermaid.js rendering provider for kovetskiy/mark.
Renders Mermaid diagrams to PNG images with custom Iconify icon pack support.
"""

import asyncio
import base64
import json
import logging
import sys
from pathlib import Path
from playwright.async_api import async_playwright

# Configure logging to stderr
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

# Dummy icon loaders for Mermaid v11.8.1 format
DUMMY_ICON_LOADERS = [
    {
        "name": "aws",
        "loader": lambda: {
            "prefix": "aws",
            "icons": {
                "s3": {"body": "<svg viewBox='0 0 24 24'><circle cx='12' cy='12' r='10' fill='#FF9900'/></svg>"},
                "lambda": {"body": "<svg viewBox='0 0 24 24'><polygon points='12 2 2 22 22 22' fill='#FF4F8B'/></svg>"},
                "dynamodb": {"body": "<svg viewBox='0 0 24 24'><rect x='2' y='6' width='20' height='12' fill='#3F48CC'/></svg>"}
            }
        }
    },
    {
        "name": "azure",
        "loader": lambda: {
            "prefix": "azure",
            "icons": {
                "functions": {"body": "<svg viewBox='0 0 24 24'><path d='M12 2L2 7v10l10 5 10-5V7z' fill='#0078D4'/></svg>"},
                "blob-storage": {"body": "<svg viewBox='0 0 24 24'><circle cx='8' cy='8' r='4' fill='#0078D4'/><circle cx='16' cy='16' r='4' fill='#0078D4'/></svg>"}
            }
        }
    },
    {
        "name": "gcp",
        "loader": lambda: {
            "prefix": "gcp",
            "icons": {
                "cloud-functions": {"body": "<svg viewBox='0 0 24 24'><path d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z' fill='#4285F4'/></svg>"},
                "cloud-storage": {"body": "<svg viewBox='0 0 24 24'><rect x='4' y='8' width='16' height='8' fill='#34A853'/></svg>"}
            }
        }
    }
]

def load_icon_loader(pack_name: str) -> dict:
    """Load Iconify JSON file and return as icon loader format for Mermaid v11.8.1."""
    icon_file = Path(f"/app/icons/{pack_name}-icons.json")

    try:
        if icon_file.exists():
            with open(icon_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"Loaded {pack_name} icon pack from {icon_file}")
                # Return in icon loader format
                return {
                    "name": pack_name,
                    "loader": data  # The actual icon data
                }
        else:
            logger.warning(f"Icon file not found: {icon_file}, using dummy data")
            # Find dummy loader for this pack
            for loader in DUMMY_ICON_LOADERS:
                if loader["name"] == pack_name:
                    return {
                        "name": pack_name,
                        "loader": loader["loader"]()  # Call the lambda to get the data
                    }
            return {"name": pack_name, "loader": {}}
    except (json.JSONDecodeError, IOError) as e:
        logger.error(f"Failed to load {pack_name} icon pack: {e}")
        # Find dummy loader for this pack
        for loader in DUMMY_ICON_LOADERS:
            if loader["name"] == pack_name:
                return {
                    "name": pack_name,
                    "loader": loader["loader"]()  # Call the lambda to get the data
                }
        return {"name": pack_name, "loader": {}}

def download_icon_from_iconify(collection: str, icon_name: str) -> str:
    """Download an icon from Iconify API and return SVG content."""
    import subprocess

    try:
        # Download icon from Iconify API
        url = f"https://api.iconify.design/{collection}/{icon_name}.svg"
        result = subprocess.run([
            'wget', '--no-check-certificate', '-q', '-O', '-', url
        ], timeout=10, capture_output=True, text=True)

        if result.returncode == 0 and result.stdout.strip():
            logger.info(f"Downloaded icon {collection}:{icon_name}")
            return result.stdout.strip()
        else:
            logger.warning(f"Failed to download icon {collection}:{icon_name}")
            return None

    except Exception as e:
        logger.error(f"Error downloading icon {collection}:{icon_name}: {e}")
        return None

def download_mermaid_js() -> str:
    """Download Mermaid.js locally and return the file path."""
    import subprocess
    import os

    mermaid_path = "/tmp/mermaid.min.js"

    # Check if already downloaded
    if os.path.exists(mermaid_path) and os.path.getsize(mermaid_path) > 1000000:  # > 1MB
        logger.info(f"Mermaid.js already exists at {mermaid_path}")
        return mermaid_path

    try:
        logger.info("Downloading Mermaid.js v11.8.1 from CDN...")
        # Use wget with SSL verification disabled (required for Docker environments)
        result = subprocess.run([
            'wget', '--no-check-certificate', '-q', '-O', mermaid_path,
            'https://unpkg.com/mermaid@11.8.1/dist/mermaid.min.js'
        ], timeout=30, capture_output=True, text=True)

        if result.returncode == 0 and os.path.exists(mermaid_path):
            file_size = os.path.getsize(mermaid_path)
            logger.info(f"Successfully downloaded Mermaid.js ({file_size} bytes)")
            return mermaid_path
        else:
            logger.error(f"wget failed: {result.stderr}")
            raise Exception("Failed to download Mermaid.js")

    except Exception as e:
        logger.error(f"Failed to download Mermaid.js: {e}")
        # Create a minimal fallback
        fallback_content = """
        window.mermaid = {
            initialize: function() { console.log('Mermaid fallback initialized'); },
            render: function() {
                return Promise.resolve({
                    svg: '<svg><text x="10" y="20">Mermaid.js failed to load</text></svg>'
                });
            }
        };
        """
        with open(mermaid_path, 'w') as f:
            f.write(fallback_content)
        logger.info("Created fallback Mermaid.js")
        return mermaid_path

def generate_html_template(mermaid_code: str) -> str:
    """Generate HTML template with Mermaid diagram code."""
    # Escape the mermaid code to prevent HTML injection
    import html
    escaped_code = html.escape(mermaid_code)

    return f"""<!DOCTYPE html>
<html>
<head>
    <title>Mermaid Renderer</title>
    <style>
        body {{
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f8f7f4;
            font-family: Arial, sans-serif;
        }}
        .mermaid {{
            width: auto;
            height: auto;
            max-width: 90vw;
            max-height: 90vh;
            border: 1px solid #ddd;
            padding: 10px;
        }}
        .error {{
            color: red;
            padding: 20px;
            border: 1px solid red;
            background: #ffe6e6;
        }}
    </style>
</head>
<body>
    <div class="mermaid" id="mermaid-container">{escaped_code}</div>

    <script id="mermaid-script">
        // This will be replaced with actual Mermaid.js content
        console.log('Mermaid script placeholder');
    </script>

    <script>
        console.log('Initializing Mermaid renderer...');

        // Register icons immediately when script loads
        function registerIcons() {{
            if (typeof mermaid !== 'undefined' && mermaid.registerIconPacks) {{
                try {{
                    const iconLoaders = [{{
                        name: 'logos',
                        loader: () => Promise.resolve({{
                            prefix: 'logos',
                            icons: {{
                                'aws-lambda': {{
                                    body: '<svg viewBox="0 0 48 48"><rect width="48" height="48" fill="#FF9900" rx="4"/><text x="24" y="30" text-anchor="middle" fill="white" font-size="16" font-weight="bold">λ</text></svg>'
                                }},
                                'azure-functions': {{
                                    body: '<svg viewBox="0 0 48 48"><rect width="48" height="48" fill="#0078D4" rx="4"/><text x="24" y="30" text-anchor="middle" fill="white" font-size="16" font-weight="bold">f()</text></svg>'
                                }},
                                'aws-dynamodb': {{
                                    body: '<svg viewBox="0 0 48 48"><rect width="48" height="48" fill="#3F48CC" rx="4"/><text x="24" y="30" text-anchor="middle" fill="white" font-size="12" font-weight="bold">DDB</text></svg>'
                                }},
                                'azure-blob-storage': {{
                                    body: '<svg viewBox="0 0 48 48"><rect width="48" height="48" fill="#0078D4" rx="4"/><circle cx="18" cy="18" r="6" fill="white"/><circle cx="30" cy="30" r="6" fill="white"/></svg>'
                                }}
                            }}
                        }})
                    }}];

                    mermaid.registerIconPacks(iconLoaders);
                    console.log('Icons registered successfully before initialization');
                    return true;
                }} catch (error) {{
                    console.error('Error registering icons:', error);
                    return false;
                }}
            }}
            return false;
        }}

        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {{
            console.log('DOM ready, checking for Mermaid...');

            // Check if mermaid is available
            if (typeof mermaid === 'undefined') {{
                console.error('Mermaid is not available');
                document.body.innerHTML = '<div class="error">Mermaid.js failed to load</div>';
                return;
            }}

            console.log('Mermaid is available, version:', mermaid.version || 'unknown');

            // Register icons first
            registerIcons();

            try {{
                // Initialize Mermaid
                mermaid.initialize({{
                    startOnLoad: false,
                    securityLevel: 'loose',
                    theme: 'default'
                }});

                // Get the mermaid element
                const element = document.getElementById('mermaid-container');
                if (!element) {{
                    throw new Error('Mermaid container not found');
                }}

                const diagramText = element.textContent.trim();
                console.log('Rendering diagram:', diagramText.substring(0, 100) + '...');

                // Render the diagram
                mermaid.render('diagram-' + Date.now(), diagramText).then(function(result) {{
                    console.log('Diagram rendered successfully');
                    element.innerHTML = result.svg;

                    // Call bindFunctions if available
                    if (result.bindFunctions) {{
                        result.bindFunctions(element);
                    }}
                }}).catch(function(error) {{
                    console.error('Mermaid render error:', error);
                    element.innerHTML = '<div class="error">Failed to render diagram: ' + error.message + '</div>';
                }});

            }} catch (error) {{
                console.error('Mermaid initialization error:', error);
                document.body.innerHTML = '<div class="error">Error: ' + error.message + '</div>';
            }}
        }});
    </script>
</body>
</html>"""

async def render_mermaid(mermaid_code: str) -> str:
    """Render Mermaid diagram to base64 encoded PNG."""
    logger.info("Starting Mermaid rendering process")

    browser = None
    try:
        # Download Mermaid.js locally first
        mermaid_js_path = download_mermaid_js()

        # Read the Mermaid.js content
        with open(mermaid_js_path, 'r', encoding='utf-8') as f:
            mermaid_js_content = f.read()

        # Launch browser
        logger.info("Launching headless Chromium browser")
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=True)
        page = await browser.new_page()

        # Capture console logs
        page.on("console", lambda msg: logger.info(f"Browser console: {msg.text}"))
        page.on("pageerror", lambda error: logger.error(f"Browser error: {error}"))

        # Generate HTML template
        html_content = generate_html_template(mermaid_code)

        # Replace the placeholder script with actual Mermaid.js content
        html_content = html_content.replace(
            '<script id="mermaid-script">\n        // This will be replaced with actual Mermaid.js content\n        console.log(\'Mermaid script placeholder\');\n    </script>',
            f'<script id="mermaid-script">\n{mermaid_js_content}\n    </script>'
        )

        logger.info("Setting page content with local Mermaid.js")
        await page.set_content(html_content)

        # Wait for page to load
        await page.wait_for_load_state('domcontentloaded')

        # Load pre-downloaded icons from build time (avoids runtime SSL issues)
        logger.info("Loading pre-downloaded Iconify icons...")

        def load_predownloaded_icon(icon_path: str) -> str:
            """Load pre-downloaded icon from file system."""
            try:
                if os.path.exists(icon_path):
                    with open(icon_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        logger.info(f"Loaded pre-downloaded icon: {icon_path}")
                        return content
                else:
                    logger.warning(f"Pre-downloaded icon not found: {icon_path}")
                    return None
            except Exception as e:
                logger.error(f"Error loading pre-downloaded icon {icon_path}: {e}")
                return None

        # Load pre-downloaded icons (downloaded at Docker build time)
        aws_lambda_svg = load_predownloaded_icon('/app/icons/aws-lambda.svg')
        aws_dynamodb_svg = load_predownloaded_icon('/app/icons/aws-dynamodb.svg')
        azure_functions_svg = load_predownloaded_icon('/app/icons/azure-functions.svg')
        azure_blob_svg = load_predownloaded_icon('/app/icons/azure-blob.svg')

        # Fallback icons if pre-downloaded icons are missing (distinctive red color to identify fallbacks)
        fallback_icons = {
            'aws-lambda': '<svg viewBox="0 0 24 24" fill="#FF0000"><rect x="2" y="2" width="20" height="20" rx="2"/><text x="12" y="14" text-anchor="middle" fill="white" font-size="10" font-weight="bold">λ</text></svg>',
            'aws-dynamodb': '<svg viewBox="0 0 24 24" fill="#FF0000"><rect x="2" y="2" width="20" height="20" rx="2"/><text x="12" y="14" text-anchor="middle" fill="white" font-size="8" font-weight="bold">DDB</text></svg>',
            'azure-functions': '<svg viewBox="0 0 24 24" fill="#FF0000"><rect x="2" y="2" width="20" height="20" rx="2"/><text x="12" y="14" text-anchor="middle" fill="white" font-size="10" font-weight="bold">f()</text></svg>',
            'azure-blob-storage': '<svg viewBox="0 0 24 24" fill="#FF0000"><rect x="2" y="2" width="20" height="20" rx="2"/><circle cx="8" cy="8" r="2" fill="white"/><circle cx="16" cy="16" r="2" fill="white"/></svg>'
        }

        # Use downloaded icons or fallbacks
        final_icons = {
            'aws-lambda': aws_lambda_svg or fallback_icons['aws-lambda'],
            'aws-dynamodb': aws_dynamodb_svg or fallback_icons['aws-dynamodb'],
            'azure-functions': azure_functions_svg or fallback_icons['azure-functions'],
            'azure-blob-storage': azure_blob_svg or fallback_icons['azure-blob-storage']
        }

        # Register icon loaders with Mermaid v11.8.1
        await page.evaluate(f"""
            if (typeof mermaid !== 'undefined') {{
                console.log('Mermaid version:', mermaid.version || 'unknown');

                // Check what icon registration methods are available
                console.log('Available methods:', Object.keys(mermaid).filter(k => k.includes('icon') || k.includes('Icon')));

                if (mermaid.registerIconPacks) {{
                    try {{
                        // Create icon loaders in the format expected by Mermaid v11.8.1
                        const iconLoaders = [
                            {{
                                name: 'logos',
                                loader: () => Promise.resolve({{
                                    prefix: 'logos',
                                    icons: {{
                                        'aws-lambda': {{
                                            body: {json.dumps(final_icons['aws-lambda'])}
                                        }},
                                        'azure-functions': {{
                                            body: {json.dumps(final_icons['azure-functions'])}
                                        }},
                                        'aws-dynamodb': {{
                                            body: {json.dumps(final_icons['aws-dynamodb'])}
                                        }},
                                        'azure-blob-storage': {{
                                            body: {json.dumps(final_icons['azure-blob-storage'])}
                                        }}
                                    }}
                                }})
                            }}
                        ];

                        mermaid.registerIconPacks(iconLoaders);
                        console.log('Real Iconify icons registered successfully for Mermaid v11.8.1');
                    }} catch (error) {{
                        console.error('Error registering icon packs:', error);
                    }}
                }} else {{
                    console.warn('mermaid.registerIconPacks not available');
                    console.log('Trying alternative icon registration...');

                    // Try alternative registration if available
                    if (mermaid.setConfig) {{
                        mermaid.setConfig({{
                            iconPacks: [{{
                                name: 'logos',
                                loader: () => Promise.resolve({{
                                    prefix: 'logos',
                                    icons: {{
                                        'aws-lambda': {{
                                            body: '<svg viewBox="0 0 24 24"><polygon points="12 2 2 22 22 22" fill="#FF4F8B"/><text x="12" y="14" text-anchor="middle" fill="white" font-size="8">λ</text></svg>'
                                        }},
                                        'azure-functions': {{
                                            body: '<svg viewBox="0 0 24 24"><path d="M12 2L2 7v10l10 5 10-5V7z" fill="#0078D4"/><text x="12" y="14" text-anchor="middle" fill="white" font-size="8">f</text></svg>'
                                        }},
                                        'aws-dynamodb': {{
                                            body: '<svg viewBox="0 0 24 24"><rect x="2" y="6" width="20" height="12" rx="2" fill="#3F48CC"/><text x="12" y="14" text-anchor="middle" fill="white" font-size="8">DB</text></svg>'
                                        }},
                                        'azure-blob-storage': {{
                                            body: '<svg viewBox="0 0 24 24"><circle cx="8" cy="8" r="4" fill="#0078D4"/><circle cx="16" cy="16" r="4" fill="#0078D4"/></svg>'
                                        }}
                                    }}
                                }})
                            }}]
                        }});
                        console.log('Icon packs set via config');
                    }}
                }}
            }} else {{
                console.error('Mermaid not available');
            }}
        """)
        
        # Debug: Check what's in the page
        logger.info("Checking page content before waiting for SVG")
        page_content = await page.content()
        logger.info(f"Page HTML length: {len(page_content)}")

        # Debug: Check for any errors in console
        page.on("console", lambda msg: logger.info(f"Browser console: {msg.text}"))
        page.on("pageerror", lambda error: logger.error(f"Browser error: {error}"))

        # Wait a bit for Mermaid to initialize
        await asyncio.sleep(2)

        # Check if mermaid div exists
        mermaid_div = await page.query_selector('.mermaid')
        if mermaid_div:
            div_content = await mermaid_div.inner_html()
            logger.info(f"Mermaid div content: {div_content[:200]}...")
        else:
            logger.error("Mermaid div not found!")

        # Wait for SVG to be rendered
        logger.info("Waiting for SVG element to be attached")
        try:
            await page.wait_for_selector('.mermaid svg', state='attached', timeout=30000)
        except Exception as e:
            # If SVG not found, let's see what's in the mermaid div
            logger.error(f"SVG not found: {e}")
            final_content = await page.content()
            logger.info(f"Final page content: {final_content[:1000]}...")
            raise
        
        # Allow additional time for full visual rendering
        await asyncio.sleep(1)
        
        # Take screenshot of the mermaid element
        logger.info("Taking screenshot")
        mermaid_element = page.locator('.mermaid')
        screenshot_bytes = await mermaid_element.screenshot()
        
        # Encode to base64
        base64_image = base64.b64encode(screenshot_bytes).decode('utf-8')
        logger.info("Screenshot taken and encoded to base64")
        
        return base64_image
        
    except Exception as e:
        logger.error(f"Error during rendering: {e}", exc_info=True)
        raise
    finally:
        if browser:
            logger.info("Closing browser")
            await browser.close()
            await playwright.stop()

async def main():
    """Main execution function."""
    try:
        logger.info("Custom Mermaid renderer started")
        
        # Read input from stdin
        logger.info("Reading Mermaid code from stdin")
        mermaid_code = sys.stdin.read().strip()
        
        if not mermaid_code:
            logger.error("No input provided via stdin")
            sys.exit(1)
        
        logger.info(f"Received Mermaid code ({len(mermaid_code)} characters)")
        
        # Render the diagram
        base64_image = await render_mermaid(mermaid_code)
        
        # Output base64 image to stdout
        print(base64_image)
        sys.stdout.flush()
        
        logger.info("Rendering completed successfully")
        
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())

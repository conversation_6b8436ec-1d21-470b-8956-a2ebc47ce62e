# Fast build approach: Skip heavy dependencies and use runtime installation
FROM kovetskiy/mark:latest

# Install minimal system dependencies only
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    curl \
    wget \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Skip Playwright installation at build time - install at runtime for faster builds

# Create app directory and copy renderer (separate layer for caching)
RUN mkdir -p /app
COPY custom_mermaid_renderer.py /app/custom_mermaid_renderer.py
COPY preprocess_mermaid.py /app/preprocess_mermaid.py
RUN chmod +x /app/custom_mermaid_renderer.py /app/preprocess_mermaid.py

# Pre-install playwright to avoid runtime installation and stdout contamination
RUN pip3 install --break-system-packages --no-cache-dir playwright && \
    python3 -m playwright install chromium --with-deps

# Create mermaid-go wrapper (separate layer)
RUN echo '#!/bin/bash' > /usr/local/bin/mermaid-go && \
    echo '# Custom mermaid-go wrapper that calls our Python renderer' >> /usr/local/bin/mermaid-go && \
    echo '# Mark expects: stdin -> mermaid code, stdout -> PNG binary data' >> /usr/local/bin/mermaid-go && \
    echo '' >> /usr/local/bin/mermaid-go && \
    echo '# Handle version/help flags that Mark might use to test the binary' >> /usr/local/bin/mermaid-go && \
    echo 'if [[ "$1" == "--version" || "$1" == "-v" ]]; then' >> /usr/local/bin/mermaid-go && \
    echo '    echo "mermaid-go custom renderer v1.0.0" >&2' >> /usr/local/bin/mermaid-go && \
    echo '    exit 0' >> /usr/local/bin/mermaid-go && \
    echo 'fi' >> /usr/local/bin/mermaid-go && \
    echo '' >> /usr/local/bin/mermaid-go && \
    echo 'if [[ "$1" == "--help" || "$1" == "-h" ]]; then' >> /usr/local/bin/mermaid-go && \
    echo '    echo "Custom mermaid-go renderer for Mark" >&2' >> /usr/local/bin/mermaid-go && \
    echo '    exit 0' >> /usr/local/bin/mermaid-go && \
    echo 'fi' >> /usr/local/bin/mermaid-go && \
    echo '' >> /usr/local/bin/mermaid-go && \
    echo '# DEBUG: Log every call to this wrapper' >> /usr/local/bin/mermaid-go && \
    echo 'echo "DEBUG: mermaid-go wrapper called at $(date) with args: $*" >&2' >> /usr/local/bin/mermaid-go && \
    echo '# Read stdin to temp file to preserve it for both logging and processing' >> /usr/local/bin/mermaid-go && \
    echo 'TEMP_INPUT=$(mktemp)' >> /usr/local/bin/mermaid-go && \
    echo 'cat > "$TEMP_INPUT"' >> /usr/local/bin/mermaid-go && \
    echo 'echo "DEBUG: Input length: $(wc -c < "$TEMP_INPUT")" >&2' >> /usr/local/bin/mermaid-go && \
    echo '# Playwright is pre-installed, so just call renderer and decode base64' >> /usr/local/bin/mermaid-go && \
    echo 'cat "$TEMP_INPUT" | python3 /app/custom_mermaid_renderer.py | base64 -d' >> /usr/local/bin/mermaid-go && \
    echo 'rm -f "$TEMP_INPUT"' >> /usr/local/bin/mermaid-go && \
    chmod +x /usr/local/bin/mermaid-go

# Ensure mermaid-go is in PATH and verify it works
ENV PATH="/usr/local/bin:${PATH}"

# Set working directory
WORKDIR /work

# Skip build test for faster builds (test in CI instead)
# RUN echo 'graph TD; A[Build Test] --> B[Success]' | python3 /app/custom_mermaid_renderer.py > /dev/null

# Default command (can be overridden)
CMD ["mark", "--help"]

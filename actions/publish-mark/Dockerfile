# Fast build approach: Skip heavy dependencies and use runtime installation
FROM kovetskiy/mark:latest

# Install minimal system dependencies only
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    curl \
    wget \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Skip Playwright installation at build time - install at runtime for faster builds

# Create app directory and copy renderer (separate layer for caching)
RUN mkdir -p /app
COPY custom_mermaid_renderer.py /app/custom_mermaid_renderer.py
RUN chmod +x /app/custom_mermaid_renderer.py

# Create mermaid-go wrapper (separate layer)
RUN echo '#!/bin/bash' > /usr/local/bin/mermaid-go && \
    echo '# Custom mermaid-go wrapper that calls our Python renderer' >> /usr/local/bin/mermaid-go && \
    echo '# Mark expects: stdin -> mermaid code, stdout -> PNG binary data' >> /usr/local/bin/mermaid-go && \
    echo 'python3 /app/custom_mermaid_renderer.py | base64 -d' >> /usr/local/bin/mermaid-go && \
    chmod +x /usr/local/bin/mermaid-go

# Set working directory
WORKDIR /work

# Skip build test for faster builds (test in CI instead)
# RUN echo 'graph TD; A[Build Test] --> B[Success]' | python3 /app/custom_mermaid_renderer.py > /dev/null

# Default command (can be overridden)
CMD ["mark", "--help"]

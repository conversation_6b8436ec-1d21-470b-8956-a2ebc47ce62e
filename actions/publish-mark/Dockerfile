# Custom Mermaid Renderer Docker Image
# Based on kovetskiy/mark with Mermaid v11.8.1 and Iconify icon support

FROM kovetskiy/mark:latest

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    curl \
    wget \
    ca-certificates \
    fonts-liberation \
    fonts-dejavu-core \
    && update-ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies for Mermaid rendering
RUN pip3 install --break-system-packages playwright

# Install Playwright browsers and dependencies
RUN python3 -m playwright install chromium
RUN python3 -m playwright install-deps

# Create app directory
RUN mkdir -p /app

# Copy the custom Mermaid renderer
COPY custom_mermaid_renderer.py /app/custom_mermaid_renderer.py
RUN chmod +x /app/custom_mermaid_renderer.py

# Create mermaid-go wrapper to integrate with mark
RUN echo '#!/bin/bash' > /usr/local/bin/mermaid-go && \
    echo '# Custom mermaid-go wrapper that calls our Python renderer' >> /usr/local/bin/mermaid-go && \
    echo 'python3 /app/custom_mermaid_renderer.py' >> /usr/local/bin/mermaid-go && \
    chmod +x /usr/local/bin/mermaid-go

# Set working directory
WORKDIR /work

# Test the custom renderer during build
RUN echo 'graph TD; A[Build Test] --> B[Success]' | python3 /app/custom_mermaid_renderer.py > /dev/null && \
    echo "Custom Mermaid renderer build test passed!"

# Default command (can be overridden)
CMD ["mark", "--help"]

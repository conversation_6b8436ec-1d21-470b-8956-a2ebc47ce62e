name: "Ratification Scanning"
description: "Scan for changes to ratified files"
inputs:
  changed_content:
    description: "List of changed content files"
    required: true
runs:
  using: "composite"
  steps:
    - name: Scan for ratified content
      id: scan-markdown
      shell: bash
      env:
        CONTENT_FILES: ${{ inputs.changed_content }}
        PR_NUMBER: ${{ github.event.pull_request.number }}
        GH_TOKEN: ${{ github.token }}
      run: |
        REGEX_RATIFY=":isarb:"
        changed_md_files=$(echo "$CONTENT_FILES" | tr ' ' '\n' | grep -E "index\.md$") || echo " "

        if [ -z "$changed_md_files" ]; then
            echo "No markdown files found in the PR"
            exit 0
        fi

        for file in $changed_md_files; do
            # Skip informational category files (exempt from ratification validation)
            if [[ "$file" == *"content/informational/"* ]]; then
                echo "File $file is in informational category - exempt from ratification checks"
                continue
            fi
            
            if grep -q "$REGEX_RATIFY" "$file"; then
              echo "Notice: File $file contains ratified content."
              gh pr review $PR_NUMBER --body "Notice: File $file contains ratified content. Ratified RFCs require manual approval for changes." --request-changes
              exit 1
            fi
        done

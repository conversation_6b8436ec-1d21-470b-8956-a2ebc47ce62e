name: "Assign Content Identifiers"
description: "Updates folders and titles with content identifiers"
inputs:
  changed_files:
    description: "Content changed files"
    required: true
  filter_content_pattern:
    description: "Pattern to filter content folders"
    required: false
    default: "RFC"
  tag_name:
    description: "The tag name to search for"
    required: false
    default: "content-id"
outputs:
  has_new_assigned_ids:
    description: "Indicates if new content identifiers were assigned"
    value: ${{ steps.id-assign.outputs.assigned_any }}
runs:
  using: "composite"
  steps:
    - name: Get Next Identifier
      id: id-lookup
      uses: wexinc/arch-rfc-shared/actions/get-next-number@main
      with:
        tag_name: ${{ inputs.tag_name }}

    - name: Assigning Content Identifiers
      id: id-assign
      shell: bash
      env:
        ALL_CHANGED_FILES: ${{ inputs.changed_files }}
        ID_PREFIX: ${{ inputs.filter_content_pattern}}
        TAG_NAME: ${{ inputs.tag_name }}
        NEXT_NUMBER: ${{ steps.id-lookup.outputs.next_number }}
      run: |
        assigned_count=0

        for file_path in $ALL_CHANGED_FILES; do
          echo "Processing file: $file_path"
          
          if [ ! -f "$file_path" ]; then
            echo "File not found: $file_path"
            continue
          fi

          # Search for ID_PREFIX in Title tag - pattern matches RFC IDs with or without hyphens
          if grep -q -E "<!-- Title: ${ID_PREFIX}(-?)[0-9]+" "$file_path"; then
            echo "File already has a content ID: $file_path"
            continue
          elif [ -z "$title" ]; then
            echo "Assigning ID to: $file_path"
            assigned_count=$((assigned_count + 1))
            sed -i "s/<!-- Title:/<!-- Title: ${ID_PREFIX}-${NEXT_NUMBER}/" "$file_path"
            content_tag="${TAG_NAME}-${NEXT_NUMBER}"
            git tag ${content_tag}
            git push origin tag ${content_tag}
            NEXT_NUMBER=$((NEXT_NUMBER + 1))
          fi
        done

        echo "assigned_any=$([ $assigned_count -gt 0 ] && echo "true" || echo "false")" >> $GITHUB_OUTPUT

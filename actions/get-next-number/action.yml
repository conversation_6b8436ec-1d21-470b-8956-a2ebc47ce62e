name: Get Next Content ID
description: Retrieves the latest content ID from the repository
inputs:
  tag_name:
    description: "The tag name to search for"
    required: false
    default: "content-id"
outputs:
  next_number:
    description: "Next Content ID"
    value: ${{ steps.parse-latest-tag.outputs.next_number }}
runs:
  using: "composite"
  steps:
    - name: Get the next available content ID
      id: parse-latest-tag
      shell: bash
      env:
        TAG_NAME: ${{ inputs.tag_name }}
      run: |
        git fetch --tags
        LATEST_TAG=$(git tag --list "${TAG_NAME}-*" | sort -V | tail -n 1)
        if [ -z "$LATEST_TAG" ]; then
          NEXT_NUMBER=1
        else
          # Extract the number from the last tag
          LAST_NUMBER=$(echo $LATEST_TAG | sed 's/[^0-9]*//g')
          NEXT_NUMBER=$((LAST_NUMBER + 1))
        fi
        echo "next_number=$NEXT_NUMBER" >> $GITHUB_OUTPUT

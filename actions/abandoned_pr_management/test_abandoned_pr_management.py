import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone, timedelta
import abandoned_pr_management as apm

class TestAbandonedPRManagement(unittest.TestCase):
    def setUp(self):
        self.repo = "testorg/testrepo"
        self.pr_number = 42
        self.branch = "feature-branch"
        self.default_branch = "main"
        self.author = {"login": "janedoe"}
        self.now = datetime(2025, 6, 4, tzinfo=timezone.utc)
        self.created_at = (self.now - timedelta(days=31)).strftime("%Y-%m-%dT%H:%M:%SZ")
        self.updated_at = (self.now - timedelta(days=31)).strftime("%Y-%m-%dT%H:%M:%SZ")
        self.pr = {
            "number": self.pr_number,
            "createdAt": self.created_at,
            "updatedAt": self.updated_at,
            "headRefName": self.branch,
            "author": self.author,
            "labels": [],
        }
        self.comments = []
        apm.GITHUB_REPOSITORY = self.repo

    @patch("abandoned_pr_management.run_gh_command")
    def test_get_open_prs(self, mock_run_gh_command):
        mock_run_gh_command.return_value = '[{"number": 1}]'
        prs = apm.get_open_prs(self.repo)
        self.assertEqual(len(prs), 1)
        self.assertEqual(prs[0]["number"], 1)

    @patch("abandoned_pr_management.run_gh_command")
    def test_get_pr_comments(self, mock_run_gh_command):
        mock_run_gh_command.return_value = '{"comments": [{"body": "test comment"}]}'
        comments = apm.get_pr_comments(self.repo, self.pr_number)
        self.assertEqual(comments[0]["body"], "test comment")

    @patch("abandoned_pr_management.run_gh_command")
    def test_get_branch_info(self, mock_run_gh_command):
        mock_run_gh_command.return_value = '{"protected": true}'
        info = apm.get_branch_info(self.repo, self.branch)
        self.assertTrue(info["protected"])

    @patch("abandoned_pr_management.run_gh_command")
    def test_get_default_branch(self, mock_run_gh_command):
        mock_run_gh_command.return_value = '{"defaultBranchRef": {"name": "main"}}'
        branch = apm.get_default_branch(self.repo)
        self.assertEqual(branch, "main")

    def test_has_comment(self):
        comments = [{"body": "something :x: here"}]
        self.assertTrue(apm.has_comment(comments, ":x:"))
        self.assertFalse(apm.has_comment(comments, ":warning:"))

    def test_has_label(self):
        pr = {"labels": [{"name": "foo"}]}
        self.assertTrue(apm.has_label(pr, "foo"))
        self.assertFalse(apm.has_label(pr, "bar"))

    def test_get_last_activity_with_updated_and_comments(self):
        pr = self.pr.copy()
        comments = [{"createdAt": (self.now - timedelta(days=20)).strftime("%Y-%m-%dT%H:%M:%SZ")}]
        last = apm.get_last_activity(pr, comments)
        self.assertEqual(last, self.now - timedelta(days=20))

    def test_get_last_activity_with_only_created(self):
        pr = self.pr.copy()
        last = apm.get_last_activity(pr, [])
        self.assertEqual(last, datetime.strptime(self.created_at, "%Y-%m-%dT%H:%M:%SZ").replace(tzinfo=timezone.utc))

    @patch("abandoned_pr_management.get_branch_info")
    def test_can_delete_branch_protected(self, mock_get_branch_info):
        mock_get_branch_info.return_value = {"protected": True}
        self.assertFalse(apm.can_delete_branch(self.repo, self.branch, "other"))

    @patch("abandoned_pr_management.get_branch_info")
    def test_can_delete_branch_not_protected(self, mock_get_branch_info):
        mock_get_branch_info.return_value = {"protected": False}
        self.assertTrue(apm.can_delete_branch(self.repo, self.branch, "other"))

    def test_can_delete_branch_is_default(self):
        self.assertFalse(apm.can_delete_branch(self.repo, self.default_branch, self.default_branch))

    @patch("abandoned_pr_management.get_pr_comments")
    @patch("abandoned_pr_management.get_last_activity")
    @patch("abandoned_pr_management.post_pr_comment")
    @patch("abandoned_pr_management.can_delete_branch")
    @patch("abandoned_pr_management.close_pr")
    @patch("abandoned_pr_management.close_pr_without_deleting")
    @patch("abandoned_pr_management.add_label")
    def test_process_pr_close_and_delete(
        self, mock_add_label, mock_close_pr_without_deleting, mock_close_pr,
        mock_can_delete, mock_post_pr_comment, mock_get_last_activity, mock_get_pr_comments
    ):
        pr = self.pr.copy()
        mock_get_pr_comments.return_value = []
        mock_get_last_activity.return_value = self.now - timedelta(days=31)
        mock_can_delete.return_value = True

        apm.process_pr(pr, self.now, self.default_branch)
        mock_post_pr_comment.assert_called()
        mock_close_pr.assert_called()
        mock_add_label.assert_called()
        mock_close_pr_without_deleting.assert_not_called()

    @patch("abandoned_pr_management.get_pr_comments")
    @patch("abandoned_pr_management.get_last_activity")
    @patch("abandoned_pr_management.post_pr_comment")
    @patch("abandoned_pr_management.can_delete_branch")
    @patch("abandoned_pr_management.close_pr")
    @patch("abandoned_pr_management.close_pr_without_deleting")
    @patch("abandoned_pr_management.add_label")
    def test_process_pr_close_no_delete(
        self, mock_add_label, mock_close_pr_without_deleting, mock_close_pr,
        mock_can_delete, mock_post_pr_comment, mock_get_last_activity, mock_get_pr_comments
    ):
        pr = self.pr.copy()
        mock_get_pr_comments.return_value = []
        mock_get_last_activity.return_value = self.now - timedelta(days=31)
        mock_can_delete.return_value = False

        apm.process_pr(pr, self.now, self.default_branch)
        mock_post_pr_comment.assert_called()
        mock_close_pr_without_deleting.assert_called()
        mock_close_pr.assert_not_called()
        mock_add_label.assert_called()

if __name__ == "__main__":
    unittest.main()

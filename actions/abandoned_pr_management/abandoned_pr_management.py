#!/usr/bin/env python3

import os
import json
import subprocess
from datetime import datetime, timezone
import logging
import sys
import traceback

# --- Robust global exception hook ---
def global_exception_hook(exc_type, exc_value, exc_traceback):
    logging.error("UNCAUGHT EXCEPTION: %s %s", exc_type, exc_value)
    logging.error("Traceback:\n%s", "".join(traceback.format_tb(exc_traceback)))
    print("UNCAUGHT EXCEPTION:", exc_type, exc_value, file=sys.stderr, flush=True)
    traceback.print_tb(exc_traceback, file=sys.stderr)

sys.excepthook = global_exception_hook

# --- Unbuffered output for github log visibility ---
for stream in (sys.stdout, sys.stderr):
    try:
        stream.reconfigure(line_buffering=True)
    except Exception:
        pass  # Only available in Python 3.7+

# --- Logging configuration ---
logging.basicConfig(
    stream=sys.stdout,
    level=logging.INFO,  # Change to logging.DEBUG for more detail
    format='%(asctime)s %(levelname)s %(message)s',
    force=True
)

logging.info("[INIT] Logging initialized with level: %s", logging.getLevelName(logging.INFO))
logging.info("[INIT] Starting abandoned PR management script")

GITHUB_REPOSITORY = os.getenv("GH_REPOSITORY", "")

# Configurable via environment variables or defaults
WARN_AFTER_DAYS = int(os.getenv("PR_WARN_AFTER_DAYS", 25))
CLOSE_AFTER_DAYS = int(os.getenv("CLOSE_AFTER_DAYS", 30))
EXCLUSION_LABEL = os.getenv("PR_EXCLUSION_LABEL", "do-not-close")
# RFC_LABEL = os.getenv("PR_RFC_LABEL", "rfc")
DEFAULT_BRANCH = os.getenv("PR_DEFAULT_BRANCH", "")

WARNING_MSG_TEMPLATE = os.getenv(
    "PR_WARNING_MSG",
    ":warning: {mention} @wexinc/arch-evo-admin - Attention: This PR is open for {days} days. "
    "Please take a moment to review or update this PR. If there’s no further activity in the next {left_days} days, we may close it to help keep things tidy. Thank you!"
)
CLOSING_MSG_TEMPLATE = os.getenv(
    "PR_CLOSING_MSG",
    ":x: {mention} @wexinc/arch-evo-admin - This PR has been open for {days} days and will now be closed. "
    "Thank you for your contribution! If you’d like to continue the discussion or make updates, feel free to reopen this PR or start a new one anytime."
)

WARNING_SNIPPET = ":warning:"
CLOSING_SNIPPET = ":x:"

def run_gh_command(args):
    logging.info("[CMD] Running command: %s", " ".join(args))
    try:
        result = subprocess.run(
            args,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            encoding='utf-8',
            check=True
        )
        if result.stdout:
            logging.debug("[CMD] STDOUT: %s", result.stdout)
        if result.stderr:
            logging.debug("[CMD] STDERR: %s", result.stderr)
        return result.stdout
    except subprocess.CalledProcessError as e:
        logging.error("[CMD] COMMAND FAILED! Return code: %s", e.returncode)
        logging.error("[CMD] CMD: %s", e.cmd)
        logging.error("[CMD] OUTPUT: %s", e.output)
        logging.error("[CMD] STDERR: %s", e.stderr)
        raise

def get_open_prs(repo):
    logging.info("[FUNC] get_open_prs(repo=%s)", repo)
    output = run_gh_command([
        "gh", "pr", "list",
        "--repo", repo,
        "--state", "open",
        "--json", "number,createdAt,updatedAt,headRefName,author,labels",
        "--limit", "100"
    ])
    prs = json.loads(output)
    logging.info("[FUNC] Found %d open PRs", len(prs))
    return prs

def get_pr_comments(repo, pr_number):
    logging.info("[FUNC] get_pr_comments(repo=%s, pr_number=%s)", repo, pr_number)
    output = run_gh_command([
        "gh", "pr", "view",
        str(pr_number),
        "--repo", repo,
        "--json", "comments"
    ])
    data = json.loads(output)
    comments = data.get("comments", [])
    logging.info("[FUNC] Found %d comments in PR #%s", len(comments), pr_number)
    return comments

def get_branch_info(repo, branch_name):
    logging.info("[FUNC] get_branch_info(repo=%s, branch_name=%s)", repo, branch_name)
    output = run_gh_command([
        "gh", "api",
        f"repos/{repo}/branches/{branch_name}"
    ])
    info = json.loads(output)
    logging.debug("[FUNC] Branch info: %s", info)
    return info

def get_default_branch(repo):
    logging.info("[FUNC] get_default_branch(repo=%s)", repo)
    output = run_gh_command([
        "gh", "repo", "view",
        repo,
        "--json", "defaultBranchRef"
    ])
    repo_info = json.loads(output)
    default_branch = repo_info.get("defaultBranchRef", {}).get("name", "")
    logging.info("[FUNC] Default branch is '%s'", default_branch)
    return default_branch

def post_pr_comment(repo, pr_number, message):
    logging.info("[FUNC] post_pr_comment(repo=%s, pr_number=%s, message=%s)", repo, pr_number, message[:60]+"...")
    run_gh_command([
        "gh", "pr", "comment",
        str(pr_number),
        "--repo", repo,
        "--body", message
    ])
    logging.info("[FUNC] Comment posted to PR #%s", pr_number)

def close_pr(repo, pr_number):
    logging.info("[FUNC] close_pr(repo=%s, pr_number=%s)", repo, pr_number)
    run_gh_command([
        "gh", "pr", "close",
        str(pr_number),
        "--repo", repo,
        "--delete-branch"
    ])
    logging.info("[FUNC] PR #%s closed and branch deleted", pr_number)

def close_pr_without_deleting(repo, pr_number):
    logging.info("[FUNC] close_pr_without_deleting(repo=%s, pr_number=%s)", repo, pr_number)
    run_gh_command([
        "gh", "pr", "close",
        str(pr_number),
        "--repo", repo
    ])
    logging.info("[FUNC] PR #%s closed (branch NOT deleted)", pr_number)

def add_label(repo, pr_number, label):
    logging.info("[FUNC] add_label(repo=%s, pr_number=%s, label=%s)", repo, pr_number, label)
    run_gh_command([
        "gh", "pr", "edit",
        str(pr_number),
        "--repo", repo,
        "--add-label", label
    ])
    logging.info("[FUNC] Label '%s' added to PR #%s", label, pr_number)

def has_comment(comments, message_snippet):
    logging.debug("[FUNC] has_comment: Looking for snippet '%s'", message_snippet)
    found = any(message_snippet in c.get("body", "") for c in comments)
    logging.info("[FUNC] has_comment: snippet found? %s", found)
    return found

def has_label(pr, label):
    labels = [lab.get("name", "") for lab in pr.get("labels", [])]
    found = label in labels
    logging.info("[FUNC] has_label: Looking for label '%s' in %s: %s", label, labels, found)
    return found

def get_last_activity(pr, comments):
    logging.info("[FUNC] get_last_activity(pr_number=%s)", pr.get("number"))
    timestamps = []
    if "updatedAt" in pr and pr["updatedAt"]:
        timestamps.append(datetime.strptime(pr["updatedAt"], "%Y-%m-%dT%H:%M:%SZ").replace(tzinfo=timezone.utc))
    if comments:
        for c in comments:
            if "createdAt" in c and c["createdAt"]:
                timestamps.append(datetime.strptime(c["createdAt"], "%Y-%m-%dT%H:%M:%SZ").replace(tzinfo=timezone.utc))
    if timestamps:
        last = max(timestamps)
        logging.info("[FUNC] Last activity at %s", last)
        return last
    created = datetime.strptime(pr["createdAt"], "%Y-%m-%dT%H:%M:%SZ").replace(tzinfo=timezone.utc)
    logging.info("[FUNC] Only createdAt found: %s", created)
    return created

# def is_rfc_pr(pr):
#     result = has_label(pr, RFC_LABEL)
#     logging.info("[FUNC] is_rfc_pr(%s): %s", pr.get("number"), result)
#     return result

def is_excluded(pr):
    result = has_label(pr, EXCLUSION_LABEL)
    logging.info("[FUNC] is_excluded(%s): %s", pr.get("number"), result)
    return result

def can_delete_branch(repo, branch_name, default_branch):
    logging.info("[FUNC] can_delete_branch(repo=%s, branch_name=%s, default_branch=%s)", repo, branch_name, default_branch)
    if branch_name == default_branch:
        logging.info("[FUNC] Branch '%s' is default branch -- cannot delete", branch_name)
        return False
    try:
        branch_info = get_branch_info(repo, branch_name)
        protected = branch_info.get("protected", False)
        logging.info("[FUNC] Branch '%s' protected: %s", branch_name, protected)
        return not protected
    except Exception as e:
        logging.warning("[FUNC] Could not check branch protection for '%s': %s", branch_name, e)
        return False

def process_pr(pr, now, default_branch):
    pr_number = pr.get("number")
    branch_name = pr.get("headRefName")
    author_login = pr.get("author", {}).get("login", None)
    mention = f"@{author_login}" if author_login else ""

    logging.info("[PROCESS] Processing PR #%s (branch: %s, author: %s)", pr_number, branch_name, author_login)

    comments = get_pr_comments(GITHUB_REPOSITORY, pr_number)
    last_activity_dt = get_last_activity(pr, comments)
    diff_days = (now - last_activity_dt).days

    warning_msg = WARNING_MSG_TEMPLATE.format(
        mention=mention, days=WARN_AFTER_DAYS, left_days=(CLOSE_AFTER_DAYS - WARN_AFTER_DAYS)
    )
    closing_msg = CLOSING_MSG_TEMPLATE.format(
        mention=mention, days=CLOSE_AFTER_DAYS
    )

    logging.info("[PROCESS] PR #%s: last activity %d days ago", pr_number, diff_days)

    if diff_days >= CLOSE_AFTER_DAYS and not has_comment(comments, CLOSING_SNIPPET):
        logging.info("[PROCESS] PR #%s: Over CLOSE_AFTER_DAYS threshold (%d). Closing.", pr_number, CLOSE_AFTER_DAYS)
        post_pr_comment(GITHUB_REPOSITORY, pr_number, closing_msg)
        if can_delete_branch(GITHUB_REPOSITORY, branch_name, default_branch):
            close_pr(GITHUB_REPOSITORY, pr_number)
            logging.info("[PROCESS] Closed PR #%s and deleted branch '%s'", pr_number, branch_name)
        else:
            close_pr_without_deleting(GITHUB_REPOSITORY, pr_number)
            logging.info("[PROCESS] Closed PR #%s but branch '%s' was not deleted (protected or default)", pr_number, branch_name)
        add_label(GITHUB_REPOSITORY, pr_number, "archived")
        logging.info("[PROCESS] PR #%s: Added 'archived' label", pr_number)
    elif diff_days >= WARN_AFTER_DAYS and not has_comment(comments, WARNING_SNIPPET):
        logging.info("[PROCESS] PR #%s: Over WARN_AFTER_DAYS threshold (%d). Posting warning.", pr_number, WARN_AFTER_DAYS)
        post_pr_comment(GITHUB_REPOSITORY, pr_number, warning_msg)
        logging.info("[PROCESS] PR #%s: Warning posted", pr_number)
    else:
        logging.info("[PROCESS] PR #%s: No new action needed (last activity %d days ago)", pr_number, diff_days)

def main():
    logging.info("[MAIN] Starting main()")
    prs = get_open_prs(GITHUB_REPOSITORY)
    now = datetime.now(timezone.utc)
    default_branch = DEFAULT_BRANCH or get_default_branch(GITHUB_REPOSITORY)
    for pr in prs:
        # if not is_rfc_pr(pr):
        #    logging.info("[MAIN] Skipping PR #%s: does not have label '%s'", pr.get("number"), RFC_LABEL)
        #    continue
        if is_excluded(pr):
           logging.info("[MAIN] Skipping PR #%s: has exclusion label '%s'", pr.get("number"), EXCLUSION_LABEL)
           continue
        process_pr(pr, now, default_branch)
    logging.info("[MAIN] Finished main()")

if __name__ == "__main__":
    logging.info("[ENTRY] __main__ entrypoint")
    try:
        main()
    except Exception as exc:
        logging.error("[FATAL] FATAL ERROR in main(): %s", exc)
        traceback.print_exc()
        sys.exit(1)

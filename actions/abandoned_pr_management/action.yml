name: 'Abandoned PR Management'
description: 'Scans for abandoned PRs, notifies, and closes if necessary.'
inputs:
  warn_after_days:
    description: "Warn after days"
    required: true
  close_after_days:
    description: "Close after days"
    required: true
runs:
  using: "composite"
  steps:
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'

    - name: Run Abandoned PR Check
      id: scan-abandoned-pr
      env:
        GH_TOKEN: ${{ github.token }}
        GH_REPOSITORY: ${{ github.repository }}
        WARN_AFTER_DAYS: ${{ inputs.warn_after_days }}
        CLOSE_AFTER_DAYS: ${{ inputs.close_after_days }}
      shell: bash
      run: python -u ${{ github.action_path }}/abandoned_pr_management.py

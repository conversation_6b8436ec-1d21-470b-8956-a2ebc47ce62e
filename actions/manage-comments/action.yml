name: "Manage Confluence Comments"
description: "Preserve or restore Confluence inline comments during content updates"

inputs:
  operation:
    description: "Operation to perform: 'preserve' to save comments before updates, 'restore' to fix comment markers after updates"
    required: true
  confluence_url:
    description: "Confluence base URL (e.g., https://company.atlassian.net/wiki)"
    required: true
  username:
    description: "Atlassian username for authentication"
    required: true
  api_token:
    description: "Atlassian API token for authentication"
    required: true
  space_key:
    description: "Confluence space key (required for preserve operation)"
    required: false
  changed_files:
    description: "Space-separated list of changed files (required for preserve operation)"
    required: false
  page_ids:
    description: "Comma-separated list of Confluence page IDs (required for restore operation)"
    required: false

outputs:
  page_ids:
    description: "Comma-separated list of page IDs that were processed (preserve operation only)"
    value: ${{ steps.manage.outputs.page_ids }}

runs:
  using: "composite"
  steps:
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.x"

    - name: Install dependencies
      shell: bash
      run: |
        python -m pip install --upgrade pip
        pip install requests>=2.25.1
        pip install werkzeug

    - name: Manage Comments
      id: manage
      shell: bash
      working-directory: ${{ github.action_path }}
      env:
        OPERATION: ${{ inputs.operation }}
        CONFLUENCE_URL: ${{ inputs.confluence_url }}
        USERNAME: ${{ inputs.username }}
        API_TOKEN: ${{ inputs.api_token }}
        SPACE_KEY: ${{ inputs.space_key }}
        CHANGED_FILES: ${{ inputs.changed_files }}
        PAGE_IDS: ${{ inputs.page_ids }}
        PYTHONUNBUFFERED: "1"
        GITHUB_WORKSPACE: ${{ github.workspace }}
      run: |
        echo "🔧 Managing Confluence comments - Operation: $OPERATION"

        # Run the script with operation-specific arguments
        if [ "$OPERATION" = "preserve" ]; then
          if [ -z "$SPACE_KEY" ] || [ -z "$CHANGED_FILES" ]; then
            echo "::warning ❌ Error: preserve operation requires space_key and changed_files inputs"
            exit 0
          fi
          python manage_comments.py \
            --operation preserve \
            --confluence-url "$CONFLUENCE_URL" \
            --username "$USERNAME" \
            --api-token "$API_TOKEN" \
            --space-key "$SPACE_KEY" \
            --changed-files "$CHANGED_FILES" 2>&1 | tee manage_output.log
        elif [ "$OPERATION" = "restore" ]; then
          if [ -z "$PAGE_IDS" ]; then
            echo "::warning ⚠️ Restore operation requires page_ids input"
            exit 0
          fi
          python manage_comments.py \
            --operation restore \
            --confluence-url "$CONFLUENCE_URL" \
            --username "$USERNAME" \
            --api-token "$API_TOKEN" \
            --page-ids "$PAGE_IDS" 2>&1 | tee manage_output.log
        else
          echo "::warning ❌ Error: operation must be 'preserve' or 'restore'"
          exit 0
        fi

        SCRIPT_EXIT_CODE=${PIPESTATUS[0]}

        # Extract page IDs for preserve operation
        if [ "$OPERATION" = "preserve" ]; then
          PAGE_IDS=$(grep "Page IDs:" manage_output.log | sed 's/.*Page IDs: //' || echo "")
          echo "page_ids=$PAGE_IDS" >> $GITHUB_OUTPUT
        fi

        exit $SCRIPT_EXIT_CODE

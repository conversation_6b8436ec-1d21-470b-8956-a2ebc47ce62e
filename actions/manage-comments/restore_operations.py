#!/usr/bin/env python3
"""Restore operations for Confluence inline comments."""

import re
from pathlib import Path
from typing import Dict, List, Any, Tuple

from shared_utils import (
    logger, make_confluence_request, get_page_data, load_preserved_data,
    create_operation_result, process_multiple_pages, handle_operation_error,
    log_operation_summary, parse_page_ids, get_page_comments
)


def validate_and_find_text_in_content(text_selection: str, content: str) -> tuple[bool, str | None]:
    text_selection_sample = text_selection[:250]

    match = re.search(re.escape(text_selection_sample), content, re.IGNORECASE | re.DOTALL)

    if match:
        return True, match.group()
    else:
        return False, None


def restore_page_comments(page_id: str, data_dir: Path) -> Dict[str, Any]:
    """Restore comments for a specific page by ensuring page content has proper marker elements."""
    logger.info(f"Restoring comment markers for page ID: {page_id}")

    preserved_data = load_preserved_data(data_dir, page_id)
    if not preserved_data:
        return create_operation_result(
            page_id, 'no_data',
            markers_restored=0,
            markers_failed=0,
            comments_validated=0
        )

    try:
        page_data = get_page_data(page_id)
        current_comments = get_page_comments(page_id, 'all')
        current_comments_by_id = {c.get('id'): c for c in current_comments}

        logger.info(f"Page: {page_data['title']} (v{page_data['version']}, {len(preserved_data['comments'])} preserved, {len(current_comments)} current)")

        return restore_page_content_markers(
            page_id, page_data['title'], page_data['storage_content'], page_data['version'],
            preserved_data, current_comments_by_id
        )

    except Exception as e:
        return handle_operation_error('restore', page_id, e)

def restore_page_content_markers(page_id: str, page_title: str, current_storage_content: str,
                               current_version: int, preserved_data: Dict[str, Any],
                               current_comments_by_id: Dict[str, Any]) -> Dict[str, Any]:
    """Restore inline comment markers in page content."""
    try:
        existing_markers = re.findall(
            r'<ac:inline-comment-marker[^>]*ac:ref="([^"]*)"[^>]*>([^<]*)</ac:inline-comment-marker>',
            current_storage_content
        )
        existing_marker_refs = {marker_ref for marker_ref, _ in existing_markers}

        required_markers = []
        validated_comments = 0
        orphan_comments_processed = 0

        for comment_data in preserved_data['comments']:
            comment_id = comment_data.get('id')
            marker_ref = comment_data.get('marker_ref', '')
            text_selection = comment_data.get('text_selection', '')

            if comment_id in current_comments_by_id and marker_ref and text_selection:
                text_selection_valid, _ = validate_and_find_text_in_content(text_selection, current_storage_content)

                if text_selection_valid:
                    required_markers.append({
                        'comment_id': comment_id,
                        'marker_ref': marker_ref,
                        'text_selection': text_selection
                    })
                    validated_comments += 1
                else:
                    logger.info(f"Processing orphan comment {comment_id} - text selection no longer valid")
                    orphan_result = process_orphan_inline_comment(comment_id, comment_data, page_id)
                    if orphan_result['success']:
                        orphan_comments_processed += 1

        required_marker_refs = {m['marker_ref'] for m in required_markers}
        missing_markers = required_marker_refs - existing_marker_refs
        orphaned_markers = existing_marker_refs - required_marker_refs

        if len(missing_markers) == 0 and len(orphaned_markers) == 0:
            return create_operation_result(
                page_id, 'success',
                markers_restored=0,
                markers_failed=0,
                comments_validated=validated_comments,
                orphan_comments_processed=orphan_comments_processed,
                message='All markers already correct'
            )

        new_storage_content = update_page_markers(
            current_storage_content, required_markers, existing_markers
        )

        update_result = update_page_content(
            page_id, page_title, new_storage_content, current_version + 1
        )

        if update_result['success']:
            return create_operation_result(
                page_id, 'success',
                markers_restored=len(missing_markers),
                markers_failed=0,
                comments_validated=validated_comments,
                orphan_comments_processed=orphan_comments_processed,
                page_updated=True,
                new_version=current_version + 1
            )
        else:
            return create_operation_result(
                page_id, 'error',
                error=update_result['error'],
                markers_restored=0,
                markers_failed=len(missing_markers),
                comments_validated=validated_comments,
                orphan_comments_processed=orphan_comments_processed
            )

    except Exception as e:
        return handle_operation_error('restore_markers', page_id, e)

def update_page_markers(current_content: str, required_markers: List[Dict[str, str]],
                       existing_markers: List[Tuple[str, str]]) -> str:
    """Update page content with correct inline comment markers."""
    new_content = current_content

    # Remove all existing markers first
    for marker_ref, text in existing_markers:
        old_marker = f'<ac:inline-comment-marker ac:ref="{marker_ref}">{text}</ac:inline-comment-marker>'
        new_content = new_content.replace(old_marker, text)

    # Add required markers
    for marker_data in required_markers:
        marker_ref = marker_data['marker_ref']
        text_selection = marker_data['text_selection']

        
        text_found, matched_text = validate_and_find_text_in_content(text_selection, new_content)
        if text_found and matched_text:
            marker_element = f'<ac:inline-comment-marker ac:ref="{marker_ref}">{matched_text}</ac:inline-comment-marker>'
            new_content = new_content.replace(matched_text, marker_element, 1)

    return new_content

def update_page_content(page_id: str, page_title: str, new_content: str, new_version: int) -> Dict[str, Any]:
    """Update page content via Confluence API."""
    try:
        update_payload = {
            "id": page_id,
            "status": "current",
            "title": page_title,
            "body": {
                "representation": "storage",
                "value": new_content
            },
            "version": {
                "number": new_version,
                "message": "Restored inline comment marker references"
            }
        }

        response = make_confluence_request('PUT', f'pages/{page_id}', json=update_payload)
        return {
            'success': True,
            'new_version': response.get('version', {}).get('number', new_version)
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def restore_multiple_pages(page_ids: List[str], data_dir: Path) -> Dict[str, Any]:
    """Restore comment markers for multiple pages."""
    results = process_multiple_pages(page_ids, restore_page_comments, data_dir)

    # Add restore-specific metrics
    total_markers_restored = 0
    total_markers_failed = 0
    total_comments_validated = 0
    total_orphan_comments_processed = 0

    for page_result in results['page_results'].values():
        total_markers_restored += page_result.get('markers_restored', 0)
        total_markers_failed += page_result.get('markers_failed', 0)
        total_comments_validated += page_result.get('comments_validated', 0)
        total_orphan_comments_processed += page_result.get('orphan_comments_processed', 0)

    results.update({
        'total_markers_restored': total_markers_restored,
        'total_markers_failed': total_markers_failed,
        'total_comments_validated': total_comments_validated,
        'total_orphan_comments_processed': total_orphan_comments_processed
    })

    return results

def process_orphan_inline_comment(comment_id: str, comment_data: Dict[str, Any], page_id: str) -> Dict[str, Any]:
    """Convert orphaned inline comment to footer comment and resolve original."""
    try:
        footer_response = create_footer_comment(comment_data, page_id)
        resolve_inline_comment(comment_id, comment_data)

        logger.info(f"Successfully processed orphan comment {comment_id}: created footer comment {footer_response.get('id', 'unknown')}")
        return {
            'success': True,
            'comment_id': comment_id,
            'footer_comment_id': footer_response.get('id')
        }

    except Exception as e:
        logger.error(f"Failed to process orphan comment {comment_id}: {e}")
        return {'success': False, 'comment_id': comment_id, 'error': str(e)}

def resolve_inline_comment(comment_id, comment_data):
    preserved_version = comment_data.get('version', 1)
    resolve_payload = {
            'version': {
                'number': preserved_version + 1,
                'message': 'Resolved due to missing anchor text'
            },
            'body': {
                'representation': 'storage',
                'value': comment_data.get('content', '')
            },
            'resolved': True 
        }

    make_confluence_request(
            'PUT', f'inline-comments/{comment_id}',
            json=resolve_payload
        )

def create_footer_comment(comment_data, page_id):
    comment_body = (
            f"<p>Inline comment recreated because anchor text was not found in page.</p>"
            f"<p>{comment_data['content']}</p>"
            f"<p>Original Anchored Text: {comment_data['text_selection']}</p>"
            f"<p>Original Author: {comment_data['author']}</p>"
        )
   
    footer_comment_payload = {
            'pageId': page_id,
            'body': {
                'representation': 'storage',
                'value': comment_body
            }
        }
    
    footer_response = make_confluence_request(
            'POST', 'footer-comments',
            json=footer_comment_payload
        )
    
    return footer_response

def run_restore_operation(args, data_dir: Path) -> int:
    """Execute the restore operation."""
    try:
        page_ids = parse_page_ids(args.page_ids)
        results = restore_multiple_pages(page_ids, data_dir)
        log_operation_summary('restore', results)

        return 0 if results['failed_pages'] == 0 else 1

    except Exception as e:
        logger.error(f"Restore operation failed: {e}")
        return 1

#!/usr/bin/env python3
"""Preserve operations for Confluence inline comments."""

import os
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
from werkzeug.security import safe_join

from shared_utils import (
    logger, ConfluenceAPIError, make_confluence_request, get_page_data,
    save_preserved_data, create_operation_result, process_multiple_pages,
    handle_operation_error, log_operation_summary, parse_file_list,
    get_page_comments, get_timestamp, get_space_id, find_pages_by_titles
)


def preserve_page_comments(page_id: str, output_dir: Path) -> Dict[str, Any]:
    """Preserve all inline comments for a specific page."""
    logger.info(f"Preserving comments for page ID: {page_id}")
    
    try:
        page_data = get_page_data(page_id)
        page_markers = extract_page_markers(page_data['storage_content'])
        comments = get_page_comments(page_id, 'open')
        
        logger.info(f"Processing page: {page_data['title']} ({len(page_markers)} markers, {len(comments)} comments)")

        preserved_data = {
            'page_id': page_id,
            'page_title': page_data['title'],
            'preservation_timestamp': get_timestamp(),
            'original_content_length': len(page_data['plain_text']),
            'page_storage_content': page_data['storage_content'],
            'page_markers': page_markers,
            'comments': []
        }
        
        for comment in comments:
            preserved_comment = extract_comment_data(comment)
            if preserved_comment:
                preserved_data['comments'].append(preserved_comment)
        
        if save_preserved_data(output_dir, page_id, preserved_data):
            logger.info(f"Preserved {len(preserved_data['comments'])} comments")
            return create_operation_result(
                page_id, 'success', 
                comments_count=len(preserved_data['comments']),
                preserved_data=preserved_data
            )
        else:
            return create_operation_result(page_id, 'error', error='Failed to save preserved data')
        
    except ConfluenceAPIError:
        raise
    except Exception as e:
        return handle_operation_error('preserve', page_id, e)

def extract_comment_data(comment: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Extract relevant data from a comment for preservation."""
    try:
        comment_id = comment.get('id')
        if not comment_id:
            return None
        status = comment.get('resolutionStatus', 'open')

        if status != 'open':
            return None

        properties = comment.get('properties', {})
        inline_props = comment.get('inlineCommentProperties', {})

        text_selection = (
            properties.get('inlineOriginalSelection') or
            inline_props.get('textSelection', '')
        )
        marker_ref = properties.get('inlineMarkerRef', '')

        if not text_selection or not marker_ref:
            return None

        body = comment.get('body', {})
        comment_content = body.get('storage', {}).get('value', '') or body.get('view', {}).get('value', '')

        version = comment.get('version', {})
        authorId = version.get('authorId', comment.get('authorId', 'unknown'))
        try:
            # Get author name
            response = make_confluence_request('POST', f'users-bulk', json={'accountIds': [authorId]})
            author = response.get('results', [])[0].get('displayName', authorId) if response.get('results') else authorId
        except:
            author = authorId

        return {
            'id': comment_id,
            'author': author,
            'content': comment_content,
            'text_selection': text_selection,
            'text_selection_match_count': inline_props.get('textSelectionMatchCount', 1),
            'text_selection_match_index': inline_props.get('textSelectionMatchIndex', 0),
            'marker_ref': marker_ref,
            'created_at': version.get('createdAt', comment.get('createdAt', '')),
            'version': version.get('number', 1),
            'resolution_status': status,
            'page_id': comment.get('pageId', '')
        }

    except Exception as e:
        logger.error(f"Error extracting comment {comment.get('id', 'unknown')}: {e}")
        return None

def extract_page_markers(page_content: str) -> List[Dict[str, str]]:
    """Extract inline comment markers from page storage content."""
    try:
        marker_pattern = r'<ac:inline-comment-marker[^>]*ac:ref="([^"]*)"[^>]*>([^<]*)</ac:inline-comment-marker>'
        matches = re.findall(marker_pattern, page_content)
        return [{
            'marker_ref': marker_ref,
            'text_content': text_content,
            'marker_element': f'<ac:inline-comment-marker ac:ref="{marker_ref}">{text_content}</ac:inline-comment-marker>'
        } for marker_ref, text_content in matches]
    except Exception as e:
        logger.error(f"Error extracting page markers: {e}")
        return []

def preserve_multiple_pages(page_ids: List[str], output_dir: Path) -> Dict[str, Any]:
    """Preserve comments for multiple pages."""
    results = process_multiple_pages(page_ids, preserve_page_comments, output_dir)
    
    # Add preserve-specific metrics
    total_comments_preserved = 0
    for page_result in results['page_results'].values():
        if page_result['status'] == 'success':
            total_comments_preserved += page_result.get('comments_count', 0)
    
    results['total_comments_preserved'] = total_comments_preserved
    return results

def extract_page_titles_from_files(file_paths: List[str]) -> List[str]:
    """Extract page titles from markdown files."""
    page_titles = []

    for file_path in file_paths:
        file_path = file_path.strip()
        if not file_path or not file_path.endswith('.md'):
            continue
        
        full_path = safe_join(os.environ.get('GITHUB_WORKSPACE', os.getcwd()), file_path)
        
        title = extract_title_from_file(full_path)
        if title:
            page_titles.append(title)

    return page_titles

def extract_title_from_file(md_file_path: str) -> Optional[str]:
    """Extract title from a markdown file."""
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return extract_title_from_content(content)
    except Exception:
        return None

def extract_title_from_content(content: str) -> Optional[str]:
    """Extract title from markdown content."""
    # HTML comment with title
    title_match = re.search(r'<!--\s*Title:\s*(.+?)\s*-->', content, re.IGNORECASE)
    if title_match:
        return title_match.group(1).strip()

    # First H1 heading
    h1_match = re.search(r'^#\s+(.+)$', content, re.MULTILINE)
    if h1_match:
        return h1_match.group(1).strip()

    # Title in frontmatter
    frontmatter_match = re.search(r'^---\s*\n(.*?)\n---', content, re.DOTALL)
    if frontmatter_match:
        title_match = re.search(r'^title:\s*(.+)$', frontmatter_match.group(1), re.MULTILINE | re.IGNORECASE)
        if title_match:
            return title_match.group(1).strip().strip('"\'')

    return None

def run_preserve_operation(args, data_dir: Path) -> int:
    """Execute the preserve operation."""
    try:
        changed_files = parse_file_list(args.changed_files)
        titles = extract_page_titles_from_files(changed_files)

        if not titles:
            print("No page titles found in changed files")
            return 0

        print(f"Extracted titles: {', '.join(titles)}")

        space_id = get_space_id(args.space_key)
        if not space_id:
            print(f"Space not found: {args.space_key}")
            return 1

        page_ids = find_pages_by_titles(space_id, titles)
        if not page_ids:
            print("No valid page IDs found to process")
            return 0

        print(f"Page IDs: {','.join(page_ids)}")
        results = preserve_multiple_pages(page_ids, data_dir)
        log_operation_summary('preserve', results)

        return 0 if results['failed_pages'] == 0 else 1

    except Exception as e:
        logger.error(f"Preserve operation failed: {e}")
        return 1

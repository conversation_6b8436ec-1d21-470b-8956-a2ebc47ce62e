#!/usr/bin/env python3
"""Manage Confluence inline comments - preserve before updates, restore after updates."""

import sys
import argparse
from pathlib import Path

from shared_utils import setup_confluence_client, logger
from preserve_operations import run_preserve_operation
from restore_operations import run_restore_operation


def main():
    """Main function with operation routing."""
    parser = argparse.ArgumentParser(description='Manage Confluence inline comments - preserve or restore')
    parser.add_argument('--operation', required=True, choices=['preserve', 'restore'], 
                       help='Operation to perform: preserve or restore')
    parser.add_argument('--confluence-url', required=True, help='Confluence base URL')
    parser.add_argument('--username', required=True, help='Atlassian username')
    parser.add_argument('--api-token', required=True, help='Atlassian API token')
    
    # Preserve operation arguments
    parser.add_argument('--space-key', help='Confluence space key (required for preserve)')
    parser.add_argument('--changed-files', help='Space-separated list of changed files (required for preserve)')
    
    # Restore operation arguments
    parser.add_argument('--page-ids', help='Comma-separated list of page IDs (required for restore)')

    args = parser.parse_args()

    # Validate operation-specific arguments
    if args.operation == 'preserve':
        if not args.space_key or not args.changed_files:
            parser.error("preserve operation requires --space-key and --changed-files")
    elif args.operation == 'restore':
        if not args.page_ids:
            parser.error("restore operation requires --page-ids")

    try:
        # Setup global configuration
        setup_confluence_client(args.confluence_url, args.username, args.api_token)
        data_dir = Path('/tmp/preserved_comments')
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # Route to appropriate operation
        if args.operation == 'preserve':
            return run_preserve_operation(args, data_dir)
        elif args.operation == 'restore':
            return run_restore_operation(args, data_dir)
        else:
            logger.error(f"Unknown operation: {args.operation}")
            return 1
        
    except Exception as e:
        logger.error(f"Script execution failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

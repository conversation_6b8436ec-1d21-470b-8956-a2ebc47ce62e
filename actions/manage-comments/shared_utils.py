#!/usr/bin/env python3
"""Shared utilities for Confluence comment management operations."""

import json
import logging
import re
import html
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


class ConfluenceAPIError(Exception):
    pass


# Global configuration for script execution
config = {}

def setup_confluence_client(base_url: str, username: str, api_token: str):
    """Initialize global Confluence configuration."""
    global config
    session = requests.Session()
    session.auth = (username, api_token)
    session.headers.update({
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    })
    
    config.update({
        'base_url': base_url.rstrip('/'),
        'username': username,
        'api_token': api_token,
        'session': session
    })

def make_confluence_request(method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
    """Make HTTP request to Confluence API v2."""
    url = urljoin(f"{config['base_url']}/api/v2/", endpoint.lstrip('/'))
    
    try:
        response = config['session'].request(method, url, **kwargs)
        response.raise_for_status()
        return response.json() if response.content else {}
    except requests.exceptions.RequestException as e:
        logger.error(f"API request failed: {method} {url} - {e}")
        raise ConfluenceAPIError(f"API request failed: {e}")

def extract_text_from_storage(storage_content: str) -> str:
    """Extract plain text from Confluence storage format."""
    text = re.sub(r'<[^>]+>', '', storage_content)
    text = html.unescape(text)
    return re.sub(r'\s+', ' ', text).strip()

def get_page_data(page_id: str) -> Dict[str, Any]:
    """Get page data including title, content, and version."""
    page_data = make_confluence_request('GET', f'pages/{page_id}', params={'body-format': 'storage'})
    return {
        'title': page_data.get('title', 'Unknown'),
        'storage_content': page_data.get('body', {}).get('storage', {}).get('value', ''),
        'version': page_data.get('version', {}).get('number', 1),
        'plain_text': extract_text_from_storage(page_data.get('body', {}).get('storage', {}).get('value', ''))
    }

def load_preserved_data(data_dir: Path, page_id: str) -> Optional[Dict[str, Any]]:
    """Load preserved comment data from file."""
    preserved_file = data_dir / f"comments_{page_id}.json"
    if not preserved_file.exists():
        return None
    
    try:
        with open(preserved_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading preserved data for page {page_id}: {e}")
        return None

def save_preserved_data(data_dir: Path, page_id: str, data: Dict[str, Any]) -> bool:
    """Save preserved comment data to file."""
    try:
        output_file = data_dir / f"comments_{page_id}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved data to: {output_file}")
        logger.info(f"Data: {data}")
        return True
    except Exception as e:
        logger.error(f"Error saving data for page {page_id}: {e}")
        return False

def create_operation_result(page_id: str, status: str, **kwargs) -> Dict[str, Any]:
    """Create standardized operation result dictionary."""
    result = {
        'page_id': page_id,
        'status': status
    }
    result.update(kwargs)
    return result

def process_multiple_pages(page_ids: List[str], operation_func, *args, **kwargs) -> Dict[str, Any]:
    """Generic function to process multiple pages with any operation."""
    results = {
        'total_pages': len(page_ids),
        'successful_pages': 0,
        'failed_pages': 0,
        'page_results': {}
    }
    
    for page_id in page_ids:
        try:
            page_result = operation_func(page_id, *args, **kwargs)
            results['page_results'][page_id] = page_result
            
            if page_result['status'] in ['success', 'no_data']:
                results['successful_pages'] += 1
            else:
                results['failed_pages'] += 1
                
        except Exception as e:
            results['failed_pages'] += 1
            results['page_results'][page_id] = create_operation_result(
                page_id, 'failed', error=str(e)
            )
            
    return results

def handle_operation_error(operation: str, page_id: str, error: Exception) -> Dict[str, Any]:
    """Standardized error handling for operations."""
    logger.error(f"Error in {operation} operation for page {page_id}: {error}")
    return create_operation_result(page_id, 'error', error=str(error))

def log_operation_summary(operation: str, results: Dict[str, Any]) -> None:
    """Log standardized operation summary."""
    if operation == 'preserve':
        logger.info(f"Preserve complete: {results['successful_pages']}/{results['total_pages']} pages, "
                   f"{results['total_comments_preserved']} comments preserved")
    elif operation == 'restore':
        orphan_count = results.get('total_orphan_comments_processed', 0)
        orphan_msg = f", {orphan_count} orphan comments processed" if orphan_count > 0 else ""
        logger.info(f"Restore complete: {results['successful_pages']}/{results['total_pages']} pages, "
                   f"{results['total_markers_restored']} markers restored, "
                   f"{results['total_comments_validated']} comments validated{orphan_msg}")

def parse_file_list(file_string: str) -> List[str]:
    """Parse space-separated file list and filter valid entries."""
    return [f.strip() for f in file_string.split() if f.strip()]

def parse_page_ids(page_ids_string: str) -> List[str]:
    """Parse comma-separated page IDs and filter valid entries."""
    return [pid.strip() for pid in page_ids_string.split(',') if pid.strip()]

def get_page_comments(page_id: str, resolution_status: str = 'open') -> List[Dict[str, Any]]:
    """Get inline comments for a page using Confluence API v2."""
    params = {'body-format': 'storage'}
    if resolution_status != 'all':
        params['resolution-status'] = resolution_status

    response = make_confluence_request('GET', f'pages/{page_id}/inline-comments', params=params)
    return response.get('results', [])

def get_timestamp() -> str:
    """Get current timestamp in ISO format."""
    from datetime import datetime, timezone
    return datetime.now(timezone.utc).isoformat()

def get_space_id(space_key: str) -> str:
    """Get space ID from space key."""
    if space_key:
        params = {'keys': space_key}
        response = make_confluence_request('GET', 'spaces', params=params)
        results = response.get('results', [])
        if results:
            return results[0]['id']
    return ''

def find_pages_by_titles(space_id: str, titles: List[str]) -> List[str]:
    """Find page IDs by titles in a space."""
    page_ids = []
    for title in titles:
        try:
            page = find_page_by_title(space_id, title)
            if page:
                page_ids.append(page['id'])
        except Exception as e:
            logger.error(f"Error finding page '{title}': {e}")
    return page_ids

def find_page_by_title(space_id: str, title: str) -> Optional[Dict[str, Any]]:
    """Find page by title in a specific space."""
    params = {'space-id': space_id, 'title': title}
    response = make_confluence_request('GET', 'pages', params=params)
    results = response.get('results', [])
    return results[0] if results else None

name: "Evaluate Changed Files"
description: "Evaluates changed files as content or other files"
inputs:
  content_root_folders:
    description: "Root folders for content files"
    required: true
  common_root:
    description: "Common parent shared by all content files"
    required: true
outputs:
  content:
    description: "List of changed content files"
    value: ${{ steps.parse-files.outputs.content }}
  root_files:
    description: "List of changed root files"
    value: ${{ steps.parse-files.outputs.root }}
  other_files:
    description: "List of changed files not in a content directory"
    value: ${{ steps.parse-files.outputs.other }}
  deleted_files:
    description: "List of deleted files"
    value: ${{ steps.parse-files.outputs.deleted }}
  malformed_files:
    description: "List of malformed files"
    value: ${{ steps.parse-files.outputs.malformed_changed_files }}

runs:
  using: "composite"
  steps:
    - name: Get RAW changed files
      id: get-raw-changed-files
      env:
        GH_TOKEN: ${{ github.token }}
      shell: bash
      run: |
        set -euo pipefail

        # Check for required commands
        for cmd in gh git base64; do
          if ! command -v $cmd &>/dev/null; then
            echo "Error: Required command '$cmd' not found in PATH."
            exit 1
          fi
        done

        CHANGED_FILES=""

        if [ "${{ github.event_name }}" = 'pull_request' ]; then
          CHANGED_FILES=$(gh pr diff ${{ github.event.pull_request.number }} --name-only || true)
          echo "Debug: CHANGED_FILES=$CHANGED_FILES"
        elif [ "${{ github.event_name }}" != 'workflow_dispatch' ]; then 
          # Get the changed files since the last commit
          CHANGED_FILES=$(git diff --name-only --unified=0 -r HEAD^1 HEAD)
          echo "Debug: CHANGED_FILES=$CHANGED_FILES"
        else
          CHANGED_FILES=$(find . -path "./.git" -prune -o -type f -print)
          echo "Debug: workflow_dispatch mode"
          echo "Debug: CHANGED_FILES count: $(echo "$CHANGED_FILES" | wc -l)"
        fi

        # Handle empty results
        if [ -z "$CHANGED_FILES" ]; then
          echo "Warning: No changed files found."
        fi

        # Encode both outputs using printf for consistent handling
        ENCODED_OUTPUT=$(printf "%s" "$CHANGED_FILES" | base64 -w 0)

        # Output changed files
        echo "changed_files<<EOF" >> $GITHUB_OUTPUT
        echo "${ENCODED_OUTPUT}" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

        # Final debug info
        echo "Info: Changed files (base64, first 60 chars): ${ENCODED_OUTPUT:0:60}"

    - name: Parse changed files
      id: parse-files
      shell: bash
      env:
        ENCODED_CHANGED_FILES: ${{ steps.get-raw-changed-files.outputs.changed_files}}
        ROOT_FOLDERS: ${{ inputs.content_root_folders }}
        COMMON_ROOT: ${{ inputs.common_root }}
        GH_TOKEN: ${{ github.token }}
      run: |
        #!/bin/bash
        set -euo pipefail

        # Function to check if path belongs to any category root
        is_under_categories() {
            local check_path="$1"
            for category in $ROOT_FOLDERS; do
                if [[ "$check_path" == "$category"* ]]; then
                    return 0
                fi
            done
            return 1
        }

        # Check if a path is in categories
        is_category() {
            local check_path="$1"
            for category in $ROOT_FOLDERS; do
                if [ "$check_path" = "$category" ] ; then
                    return 0
                fi
            done
            return 1
        }

        # Initialize all variables
        CONTENT_FILES=""
        ROOT_FILES=""
        OTHER_FILES=""
        DELETED_FILES=""
        MALFORMED_CHANGED_FILES=""
        
        # Decode the changed files
        CHANGED_FILES=$(echo "$ENCODED_CHANGED_FILES" | base64 --decode)
        echo "Debug: ENCODED_CHANGED_FILES=$ENCODED_CHANGED_FILES"
        echo "Debug: CHANGED_FILES=$CHANGED_FILES"
        if [ -z "$CHANGED_FILES" ]; then
            echo "No changed files found."
            exit 0
        fi

        # Process files line by line to handle spaces in filenames
        while IFS= read -r file; do
            # Skip empty lines
            [ -z "$file" ] && continue
            
            # Check for spaces or special characters in file name
            if [[ "$file" =~ [^a-zA-Z0-9._/-] ]]; then
                echo "Debug: Found malformed file: $file"
                MALFORMED_CHANGED_FILES+="${file}"$'\n'
                continue
            fi

            # Check if file exists or has been deleted
            if [ ! -f "$file" ]; then
                echo "Detected deleted file: $file"
                DELETED_FILES+="${file}"$'\n'
                continue
            fi
            
            dir=$(dirname "$file")
            if ! is_under_categories "$dir"; then
                OTHER_FILES+="${file}"$'\n'
            elif is_category "$dir"; then
                ROOT_FILES+="${file}"$'\n'
            else
                CONTENT_FILES+="${file}"$'\n'
            fi
        done <<< "$CHANGED_FILES"

        # Output the results using EOF delimiter for multiline values
        echo "content<<EOF" >> $GITHUB_OUTPUT
        echo "${CONTENT_FILES}" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

        echo "root<<EOF" >> $GITHUB_OUTPUT
        echo "${ROOT_FILES}" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

        echo "other<<EOF" >> $GITHUB_OUTPUT
        echo "${OTHER_FILES}" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

        echo "deleted<<EOF" >> $GITHUB_OUTPUT
        echo "${DELETED_FILES}" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

        echo "malformed_changed_files<<EOF" >> $GITHUB_OUTPUT
        echo "${MALFORMED_CHANGED_FILES}" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

        if [ -n "$CONTENT_FILES" ]; then
          echo "Content files found."
          echo "$CONTENT_FILES"
        else
          echo "No content files found."
        fi
        if [ -n "$ROOT_FILES" ]; then
          echo "Root files found."
          echo "$ROOT_FILES"
        else
          echo "No root files found."
        fi
        if [ -n "$OTHER_FILES" ]; then
          echo "Other files found."
          echo "$OTHER_FILES"
        else
          echo "No other files found."
        fi
        if [ -n "$DELETED_FILES" ]; then
          echo "Deleted files found."
          echo "$DELETED_FILES"
        else
          echo "No deleted files found."
        fi
        if [ -n "$MALFORMED_CHANGED_FILES" ]; then
          echo "Malformed files found."
          echo "$MALFORMED_CHANGED_FILES"
        else
          echo "No malformed files found."
        fi

    - name: Set outputs
      shell: bash
      run: |
        # Helper function to format multiline output
        format_output() {
          if [ -n "$1" ]; then
            echo "$1"
          fi
        }
        
        echo "content_files<<EOF" >> $GITHUB_OUTPUT
        format_output "$CONTENT_FILES" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT
        
        echo "root_files<<EOF" >> $GITHUB_OUTPUT
        format_output "$ROOT_FILES" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT
        
        echo "other_files<<EOF" >> $GITHUB_OUTPUT
        format_output "$OTHER_FILES" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT
        
        echo "deleted_files<<EOF" >> $GITHUB_OUTPUT
        format_output "$DELETED_FILES" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT
        
        echo "malformed_files<<EOF" >> $GITHUB_OUTPUT
        format_output "$MALFORMED_CHANGED_FILES" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Update PR Title and Description
      id: update-pr
      if: github.event_name == 'pull_request'
      env:
        GH_TOKEN: ${{ github.token }}
      shell: bash
      run: |
        # Build a comprehensive validation summary with proper formatting
        PR_BODY="## 📋 File Analysis Summary
        
        This pull request has been automatically analyzed. Here are the results:
        
        ### 📁 **File Categories**
        "
        
        # Add content files section
        if [ -n "${{ steps.parse-files.outputs.content }}" ]; then
          PR_BODY+="
        #### ✅ Content Files
        \`\`\`
        ${{ steps.parse-files.outputs.content }}
        \`\`\`
        "
        else
          PR_BODY+="
        #### ⚪ Content Files
        > No content files detected
        "
        fi
        
        # Add root files section
        if [ -n "${{ steps.parse-files.outputs.root }}" ]; then
          PR_BODY+="
        #### 📂 Root Files
        \`\`\`
        ${{ steps.parse-files.outputs.root }}
        \`\`\`
        "
        else
          PR_BODY+="
        #### ⚪ Root Files
        > No root files detected
        "
        fi
        
        # Add other files section
        if [ -n "${{ steps.parse-files.outputs.other }}" ]; then
          PR_BODY+="
        #### 📄 Other Files
        \`\`\`
        ${{ steps.parse-files.outputs.other }}
        \`\`\`
        "
        else
          PR_BODY+="
        #### ⚪ Other Files
        > No other files detected
        "
        fi
        
        # Add deleted files section
        if [ -n "${{ steps.parse-files.outputs.deleted }}" ]; then
          PR_BODY+="
        #### 🗑️ Deleted Files
        \`\`\`
        ${{ steps.parse-files.outputs.deleted }}
        \`\`\`
        "
        else
          PR_BODY+="
        #### ⚪ Deleted Files
        > No deleted files detected
        "
        fi
        
        # Add malformed files section with warning styling
        if [ -n "${{ steps.parse-files.outputs.malformed_changed_files }}" ]; then
          PR_BODY+="
        ### ⚠️ **Issues Detected**
        
        #### 🚨 Malformed File Names
        The following files contain spaces or special characters that may cause issues:
        
        \`\`\`
        ${{ steps.parse-files.outputs.malformed_changed_files }}
        \`\`\`
        
        **Recommendation:** Consider renaming these files to use only alphanumeric characters, dots, hyphens, and underscores.
        "
        fi
        
        # Add footer with timestamp
        CURRENT_TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S UTC')
        PR_BODY+="
        
        ---
        
        🤖 **Automated Analysis** | Last updated: ${CURRENT_TIMESTAMP}
        
        > This comment was automatically generated by the file evaluation workflow."
        
        # Update the PR description
        gh pr edit ${{ github.event.pull_request.number }} \
          --body "$PR_BODY"
        
        echo "✅ PR description updated successfully"
name: "Linting"
description: "Lint markdown files and auto fix fixable errors"
inputs:
  changed_content:
    description: "List of changed content files"
    required: true
  config_file_path:
    description: "Path to the config file in the calling repository"
    required: true
    default: "/linter-config.yml"
runs:
  using: "composite"
  steps:
    - name: Filter for content markdown
      id: filter-markdown
      shell: bash
      env:
        CONTENT_FILES: ${{ inputs.changed_content }}
      run: |
        SEPARATOR=";"
        MARKDOWN_CONTENT=""

        for file in $CONTENT_FILES; do
          if [[ "$file" == *.md ]]; then
            MARKDOWN_CONTENT+="$file$SEPARATOR"
          fi       
        done
        # Trim trailing separator
        MARKDOWN_CONTENT=${MARKDOWN_CONTENT%%SEPARATOR}
        echo "content=$MARKDOWN_CONTENT" >> $GITHUB_OUTPUT
    - uses: DavidAnson/markdownlint-cli2-action@v20
      continue-on-error: true
      id: markdownlint
      if: ${{ steps.filter-markdown.outputs.content != '' }}
      with:
        config: ${{ inputs.config_file_path }}
        globs: ${{ steps.filter-markdown.outputs.content }}
        separator: ";"
    - name: Check for errors
      shell: bash
      env:
        PR_NUMBER: ${{ github.event.pull_request.number }}
        GH_TOKEN: ${{ github.token }}
        CONTENT_FILES: ${{ inputs.changed_content }}
      if: steps.markdownlint.outcome == 'failure'
      run: |
        gh pr review $PR_NUMBER --body "Notice: Linting autofix not possible. Please fix the file(s) manually: $CONTENT_FILES" --comment
        exit 1

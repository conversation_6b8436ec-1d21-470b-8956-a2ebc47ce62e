#!/bin/bash

set -e

if [ "$#" -lt 2 ]; then
    echo "Usage: $0 <file_paths> <pr_number>"
    exit 1
fi

FILES=$1
PR_NUMBER=$2
has_errors=false

changed_md_files=$(echo "$FILES" | tr ' ' '\n' | grep -E "index\.md$") || echo " "

if [ -z "$changed_md_files" ]; then
    echo "No markdown files found in the PR"
fi

if ! [[ "$PR_NUMBER" =~ ^[0-9]+$ ]]; then
    # Check if the PR number is valid
    if ! gh pr view "$PR_NUMBER" &>/dev/null; then
        echo "Error: Invalid PR number: $PR_NUMBER"
        exit 1
    fi
fi

for FILE_PATH in $changed_md_files; do
    # Validate file structure (must be Category/Subtype/index.md)
    if [[ ! "$FILE_PATH" =~ .*/[^/]+/[^/]+/index\.md$ ]]; then
        echo "Error: File must be in Category/Subtype/index.md format: $FILE_PATH"
        gh pr review $PR_NUMBER --body "File must be in Category/Subtype/index.md format." --request-changes
        has_errors=true
    fi

    # check metadata header
    if ! grep -E -q '^<!-- (Parent:[^ ]|Title:)' "$FILE_PATH"; then
        echo "Error: Required metadata (Parent or Title) not found or invalid in $FILE_PATH"
        gh pr review $PR_NUMBER --body "Required metadata (Parent or Title) not found or invalid in $FILE_PATH." --request-changes
        has_errors=true
        continue
    fi

    # Extract metadata (Parent, Title) from a file while ignoring formatting
    extract_metadata() {
        # Extract all Parent and Title tags
        parents=$(grep -o -E '<!-- *Parent: *[^>]+ *-->' "$1" | sed -E 's/<!-- *Parent: *([^>]+) *-->/Parent: \1/g' | sort)
        titles=$(grep -o -E '<!-- *Title: *[^>]+ *-->' "$1" | sed -E 's/<!-- *Title: *([^>]+) *-->/Title: \1/g')
        
        echo "$parents"
        echo "$titles"
    }
       
    # Get metadata from the previous version in the target branch
    target_branch=$(gh pr view $PR_NUMBER --json baseRefName --jq .baseRefName)
    previous_file_content=$(git show "origin/$target_branch":"$FILE_PATH" 2>/dev/null) || true
    
    if [ -z "$previous_file_content" ]; then
        # New file, no previous version to compare
        continue
    fi
    
    # Create a temporary file with the previous content
    temp_file=$(mktemp)
    echo "$previous_file_content" > "$temp_file"
    previous_metadata=$(extract_metadata "$temp_file")
    rm "$temp_file"
    
    # Get the current metadata
    current_metadata=$(extract_metadata "$FILE_PATH")
    
    if [ "$current_metadata" != "$previous_metadata" ]; then
        echo "Error: Metadata header has been modified in $FILE_PATH"
        echo "Current metadata:"
        echo "$current_metadata"
        echo "Previous metadata:"
        echo "$previous_metadata"
        gh pr review $PR_NUMBER --body "Metadata header has been modified. Changes to Parent or Title metadata are not allowed." --comment
        has_errors=true
    fi
done

if [ "$has_errors" = true ]; then
    echo "Notice: Errors found in markdown files. Please fix them before merging."
    exit 1
fi

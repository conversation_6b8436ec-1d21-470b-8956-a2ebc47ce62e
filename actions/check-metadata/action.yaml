name: "Check RFC Metadata"
description: "Checks and adds required metadata to RFC documents"
inputs:
  file_path:
    description: "Path to the index.md files (space separated)"
    required: true
runs:
  using: "composite"
  steps:
    - name: Check metadata
      id: check-metadata
      shell: bash
      env:
        GH_TOKEN: ${{ github.token }}
        CHANGED_MD_FILES: ${{ inputs.file_path }}
        PR_NUMBER: ${{ github.event.pull_request.number }}
        ACTION_PATH: ${{ github.action_path }}
      run: bash ${ACTION_PATH}/check-metadata.sh "${CHANGED_MD_FILES}" "${PR_NUMBER}"

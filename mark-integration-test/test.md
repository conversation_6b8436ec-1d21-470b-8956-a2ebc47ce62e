<!-- Title: TEST-001 Test Document -->

# Test Document

This is a test document with a Mermaid diagram.

## Simple Flowchart

```mermaid
flowchart TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E
```

## Architecture Diagram with Icons

```mermaid
architecture-beta
    group "Cloud Functions"
        api(logos:aws-lambda)[API Gateway]
        processor(logos:azure-functions)[Data Processor]
    end

    group "Data Storage"
        db(logos:aws-dynamodb)[NoSQL DB]
        blob(logos:azure-blob-storage)[Blob Storage]
    end

    api --> processor
    processor --> db
    processor --> blob
```

## Simple Graph

```mermaid
graph TD
    A --> B
    B --> C
    C --> A
```

End of test document.

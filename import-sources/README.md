# RFC Working Directory

This directory serves as a staging area for unstructured documentation and work-in-progress content related to RFCs (Request for Comments).

## Purpose

- Provides a space to load existing documentation for reference in Copilot
- Allows for collaborative brainstorming and note-taking
- Supports gathering requirements and feedback in an informal format

## Usage

Files in this directory are intentionally excluded from version control (via `.gitignore`). This means:

- You can freely create, modify, and delete files without affecting the repository
- Changes won't be tracked in git history
- Other team members won't see your working files

#!/bin/bash

# Test the custom Mermaid renderer with mark --compile-only
# This tests the full integration without connecting to Confluence

set -e

echo "🧪 Testing Mark Integration with Custom Mermaid Renderer"
echo "========================================================"

# Create test directory structure
TEST_DIR="mark-integration-test"
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR"

echo "📝 Creating test markdown file with Mermaid diagram..."

# Create a test markdown file with Mermaid content
cat > "$TEST_DIR/test.md" << 'EOF'
<!-- Title: TEST-001 Test Document -->

# Test Document

This is a test document with a Mermaid diagram.

## Simple Flowchart

```mermaid
flowchart TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E
```

## Architecture Diagram with Icons

```mermaid
architecture-beta
    group "Cloud Functions"
        api(logos:aws-lambda)[API Gateway]
        processor(logos:azure-functions)[Data Processor]
    end

    group "Data Storage"
        db(logos:aws-dynamodb)[NoSQL DB]
        blob(logos:azure-blob-storage)[Blob Storage]
    end

    api --> processor
    processor --> db
    processor --> blob
```

## Simple Graph

```mermaid
graph TD
    A --> B
    B --> C
    C --> A
```

End of test document.
EOF

echo "✅ Test markdown file created"

echo "🐳 Running mark with custom Mermaid renderer..."

# Run mark with --compile-only using our custom renderer
docker run --rm \
    -v "$PWD/$TEST_DIR:/work" \
    -v "$PWD/actions/publish-mark/custom_mermaid_renderer.py:/app/custom_mermaid_renderer.py" \
    -w /work \
    kovetskiy/mark:latest sh -c "
        # Install Python and pip first (redirect to stderr)
        apt-get update >&2 && apt-get install -y python3 python3-pip python3-venv >&2 && \
        # Install Python dependencies (redirect to stderr)
        pip3 install --break-system-packages playwright >&2 && \
        python3 -m playwright install chromium >&2 && \
        python3 -m playwright install-deps >&2 && \
        # Make the custom renderer executable
        chmod +x /app/custom_mermaid_renderer.py >&2 && \
        # Run mark with compile-only and custom mermaid provider
        mark --compile-only \
            --mermaid-provider='python3 /app/custom_mermaid_renderer.py' \
            --mermaid-scale 2 \
            test.md
    " > "$TEST_DIR/output.html" 2> "$TEST_DIR/mark_stderr.log"

echo "Exit code: $?"

# Check if HTML was generated
if [ -s "$TEST_DIR/output.html" ]; then
    echo "✅ HTML output generated ($(wc -c < "$TEST_DIR/output.html") bytes)"
    
    # Check if the HTML contains image tags (indicating Mermaid was processed)
    if grep -q "<img" "$TEST_DIR/output.html"; then
        echo "✅ Found image tags in HTML - Mermaid diagrams were processed!"
        
        # Count how many images were generated
        img_count=$(grep -c "<img" "$TEST_DIR/output.html")
        echo "📊 Number of images generated: $img_count"
        
        # Show a snippet of the HTML around the first image
        echo "📋 Sample image tag:"
        grep -A 2 -B 2 "<img" "$TEST_DIR/output.html" | head -10
        
    else
        echo "⚠️  No image tags found - Mermaid diagrams may not have been processed"
    fi
    
    # Check for any base64 encoded images
    if grep -q "data:image" "$TEST_DIR/output.html"; then
        echo "✅ Found base64 encoded images in HTML"
    else
        echo "ℹ️  No base64 encoded images found (images may be saved as separate files)"
    fi
    
else
    echo "❌ No HTML output generated"
fi

# Show any errors from mark
if [ -s "$TEST_DIR/mark_stderr.log" ]; then
    echo ""
    echo "📋 Mark stderr output:"
    echo "====================="
    cat "$TEST_DIR/mark_stderr.log"
fi

echo ""
echo "📁 Test files created in: $TEST_DIR/"
echo "   - test.md (input markdown)"
echo "   - output.html (generated HTML)"
echo "   - mark_stderr.log (mark error output)"
echo ""
echo "🔍 To inspect the results:"
echo "   cat $TEST_DIR/output.html"
echo "   open $TEST_DIR/output.html  # (on macOS)"
echo ""
echo "🧹 To clean up:"
echo "   rm -rf $TEST_DIR"

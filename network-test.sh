#!/bin/bash

# Network connectivity test for the Docker container
# This will help diagnose why Mermaid.js CDN is not loading

set -e

echo "🌐 Network Connectivity Test"
echo "============================"

echo "🐳 Testing network connectivity in Docker container..."

# Test basic network connectivity and CDN access
docker run --rm my-custom-mark-renderer sh -c "
echo '📡 Basic Network Tests'
echo '====================='

echo '1. Testing DNS resolution:'
nslookup google.com || echo 'DNS resolution failed'

echo ''
echo '2. Testing basic internet connectivity:'
ping -c 3 8.8.8.8 || echo 'Ping to 8.8.8.8 failed'

echo ''
echo '3. Testing HTTPS connectivity:'
curl -I https://www.google.com --connect-timeout 10 || echo 'HTTPS to google.com failed'

echo ''
echo '4. Testing CDN accessibility:'
echo 'Testing jsdelivr CDN:'
curl -I https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js --connect-timeout 10 || echo 'jsdelivr <PERSON>N failed'

echo ''
echo 'Testing unpkg CDN:'
curl -I https://unpkg.com/mermaid@10/dist/mermaid.min.js --connect-timeout 10 || echo 'unpkg CDN failed'

echo ''
echo 'Testing cdnjs CDN:'
curl -I https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.js --connect-timeout 10 || echo 'cdnjs CDN failed'

echo ''
echo '5. Testing if we can download Mermaid.js:'
echo 'Attempting to download from jsdelivr:'
curl -s -o /tmp/mermaid-jsdelivr.js https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js --connect-timeout 10 && echo 'jsdelivr download: SUCCESS' || echo 'jsdelivr download: FAILED'

echo 'Attempting to download from unpkg:'
curl -s -o /tmp/mermaid-unpkg.js https://unpkg.com/mermaid@10/dist/mermaid.min.js --connect-timeout 10 && echo 'unpkg download: SUCCESS' || echo 'unpkg download: FAILED'

echo ''
echo '6. Checking downloaded file sizes:'
ls -la /tmp/mermaid-*.js 2>/dev/null || echo 'No Mermaid files downloaded'

echo ''
echo '7. Testing if files are valid JavaScript:'
if [ -f /tmp/mermaid-jsdelivr.js ]; then
    echo 'jsdelivr file first 100 chars:'
    head -c 100 /tmp/mermaid-jsdelivr.js
    echo ''
fi

if [ -f /tmp/mermaid-unpkg.js ]; then
    echo 'unpkg file first 100 chars:'
    head -c 100 /tmp/mermaid-unpkg.js
    echo ''
fi

echo ''
echo '8. Testing browser network access with Playwright:'
python3 -c \"
import asyncio
from playwright.async_api import async_playwright

async def test_network():
    try:
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=True)
        page = await browser.new_page()
        
        print('Testing page navigation to google.com...')
        await page.goto('https://www.google.com', timeout=10000)
        title = await page.title()
        print(f'Page title: {title}')
        
        print('Testing CDN script loading...')
        await page.goto('data:text/html,<html><head><script src=\\\"https://unpkg.com/mermaid@10/dist/mermaid.min.js\\\"></script></head><body><div id=\\\"test\\\">Test</div></body></html>')
        
        # Wait a bit for script to load
        await asyncio.sleep(3)
        
        # Check if mermaid is available
        mermaid_available = await page.evaluate('typeof mermaid !== \\\"undefined\\\"')
        print(f'Mermaid available in browser: {mermaid_available}')
        
        if mermaid_available:
            version = await page.evaluate('mermaid.version || \\\"unknown\\\"')
            print(f'Mermaid version: {version}')
        
        await browser.close()
        await playwright.stop()
        
    except Exception as e:
        print(f'Browser test failed: {e}')

asyncio.run(test_network())
\"

echo ''
echo '9. Environment information:'
echo 'Container OS:'
cat /etc/os-release | head -5

echo ''
echo 'Network interfaces:'
ip addr show || ifconfig

echo ''
echo 'DNS configuration:'
cat /etc/resolv.conf

echo ''
echo 'Installed packages:'
dpkg -l | grep -E '(curl|wget|ca-certificates)' || echo 'Package info not available'

echo ''
echo '✅ Network test completed'
"

echo ""
echo "🔍 Network test completed. Check the output above for connectivity issues."

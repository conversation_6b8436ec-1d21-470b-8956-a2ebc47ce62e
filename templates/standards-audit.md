<!-- Parent: Design -->
<!-- Parent: [Organization] -->
<!-- Title: Compliance Audit Report for {repository_name} -->

<!-- Include: macros.md -->
# Compliance Audit Report for {repository_name}

```text
Author: [Name]
Publish Date: [YYYY-MM-DD]
Category: Design
Subtype: [Organization]
```

## Summary of Compliance Scores

### Application Logging Standard

**Compliance Score:** {compliance_score_app_logging}  
**Justification:**  
{justification_app_logging}

### REST API Design Standard

**Compliance Score:** {compliance_score_rest_api}  
**Justification:**  
{justification_rest_api}

---

## Application Logging - Violations & Recommendations

1. **Requirement Violated:** `{requirement_id_1}`  
   - **Details:** {description_of_violation_1}
   - **Example:** {violation_example_citation}
   - **Recommendation:** {recommendation_1}

2. **Requirement Violated:** `{requirement_id_2}`  
   - **Details:** {description_of_violation_2}
   - **Example:** {violation_example_citation}
   - **Recommendation:** {recommendation_2}

{repeat_for_all_violations_in_logging}

---

## REST API Design Standard - Violations and Recommendations

1. **Requirement Violated:** `{requirement_id_1}`  
   - **Details:** {description_of_violation_1}
   - **Example:** {violation_example_citation}
   - **Recommendation:** {recommendation_1}

2. **Requirement Violated:** `{requirement_id_2}`  
   - **Details:** {description_of_violation_2}
   - **Example:** {violation_example_citation}
   - **Recommendation:** {recommendation_2}

{repeat_for_all_violations_in_rest_api}

---

## Application Logging Standard - Advanced Recommendations

- **{Advanced Capability 1}:** {description_and_recommendation_1}
- **{Advanced Capability 2}:** {description_and_recommendation_2}

---

## REST API Design Standard - Advanced Recommendations

- **{Advanced Capability 1}:** {description_and_recommendation_1}
- **{Advanced Capability 2}:** {description_and_recommendation_2}

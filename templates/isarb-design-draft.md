<!-- Parent: Designs -->
<!-- Parent: [Sponsor Organization]-->
<!-- Title: [Title] -->

<!-- Include: macros.md -->
# [Title]

:draft:

```text
Author: [Name]
Publish Date: [YYYY-MM-DD]
Category: Designs
Subtype: [Sponsor Organization]
```

## Executive Summary

[2-3 sentences describing what is being proposed and why it matters]

This solution drives improvements towards the following Tech Transformation Vectors:

| Vector | Impact |
|--------|--------|
| 🔲 🛡️ Product Security | [Brief impact description if selected] |
| 🔲 🔄 Reliability | [Brief impact description if selected] |
| 🔲 ⚡ Product Innovation Velocity (PiV) | [Brief impact description if selected] |
| 🔲 ☁️ SaaS Maturity | [Brief impact description if selected] |
| 🔲 🤖 AI/ML Maturity | [Brief impact description if selected] |

:toc:

## 💼 Change Context

* **Organization:**: [Sponsoring organization]
* **Purpose:** [One sentence on value proposition]
* **Users:** [Key user groups]
* **Integration Points:** [Key systems]
* **Impacted Products:**
  * [Product Name 1]
  * [Product Name 2]
* **Impacted Teams:**
  * [Team Name 1]
  * [Team Name 2]
* **New Third-Party Software:** [PoC/Pilot/Approved Usage/None]
* **Design Qualities:** (select all that apply)
  * 🔲 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * 🔲 🟡 New Pattern Adoption
  * 🔲 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Phase 1 | YYYY-MM | [List dependencies] |
| Phase 2 | YYYY-MM | Phase 1 |
| Phase 3 | YYYY-MM | Phase 2 |

## 📊 Diagrams

### System Context Diagram

> Include a high-level system context diagram showing how users, systems, and external entities interact. Focus on showing boundaries and relationships.

```mermaid
graph TB
    User[WEX User]
    Customer[Customer]
    System[New System]
    Existing[Existing WEX System]
    Edge[Edge Device]
    Security[Security Component]

    User -->|Uses| System
    Customer -->|Interacts| System
    System -->|Integrates| Existing
    System -->|Secured by| Security
    System -->|Connected to| Edge

    %% Add annotations for key components
    classDef secure fill:#e6ffe6,stroke:#006600
    classDef external fill:#f9f9f9,stroke:#666666
    class Security secure
    class Customer,Edge external
```

### Cloud Architecture Diagram

> Show the cloud infrastructure components, their relationships, and security boundaries. Include compliance-related annotations.

```mermaid
flowchart TB
    subgraph "WEX Cloud Infrastructure"
        direction TB
        WAF[Imperva WAF]
        subgraph "Security Zone"
            App[Application Servers]
            DB[(Database)]
            Queue[[Message Queue]]
        end
    end

    subgraph "External Systems"
        Partner[Partner System]
    end

    User[User] -->|HTTPS/TLS 1.2+| WAF
    WAF -->|Internal HTTPS| App
    App -->|Encrypted Connection| DB
    App -->|Events| Queue
    App <-->|API/OAuth2| Partner

    %% Add annotations for data classification
    classDef class1data fill:#ffebeb,stroke:#990000
    classDef secure fill:#e6ffe6,stroke:#006600
    class DB class1data
    class WAF secure
```

## ⚖️ Evaluation Criteria

### 🛡️ Product Security

#### Risk Assessment

{this section can be omitted if the design does not include new third-party software}
The current status of risk assessments for this vendor and product:

* 🔲 Not Requested
* 🔲 In Progress
* 🔲 Completed without concerns
* 🔲 Completed with concerns: `Explain`

#### Data Compliance

The following types of Class 1 Protected Data is transmitted, processed, or stored in this solution:

* 🔲 PCI
* 🔲 PII
* 🔲 PHI
* 🔲 Other: `Explain`

Data Protection Controls:

* 🔲 Data is protected at rest, in transit, and in use per WEX IT policy
* 🔲 HTTPS using TLS 1.2 or higher
* 🔲 Message encryption
* 🔲 Database Connections using TLS 1.2 or higher
* 🔲 Row-level data encryption
* 🔲 Transparent data encryption
* 🔲 De-identification (tokenization, hashing, masking, redaction)
* 🔲 Digital Signatures
* 🔲 Access control (permission, role, policy)

Data Retention:

* Data archived after: `X days`
* Data purged after: `Y days`
* Logs purged after: `Z days`

#### Data Ingress / Egress

This solution uses the following data flow patterns:

* 🔲 Customer-to-WEX traffic
* 🔲 WEX-to-Customer traffic
* 🔲 Vendor-to-WEX traffic
* 🔲 WEX-to-Vendor traffic
* 🔲 Internal system-to-system traffic

For each data flow, provide:

| Origin System | Destination System | Protocol | Authentication | Data Classification | Encryption Methods |
|---------------|-------------------|----------|---------------|-------------------|-------------------|
| [System 1] | [System A] | [HTTPS/SFTP/etc.] | [OAuth/API Key/etc.] | [Class 1/Class 2/etc.] | [TLS 1.2+/AES-256/etc.] |
| [System 2] | [System B] | [HTTPS/SFTP/etc.] | [OAuth/API Key/etc.] | [Class 1/Class 2/etc.] | [TLS 1.2+/AES-256/etc.] |
| [System 3] | [System C] | [HTTPS/SFTP/etc.] | [OAuth/API Key/etc.] | [Class 1/Class 2/etc.] | [TLS 1.2+/AES-256/etc.] |

```mermaid
flowchart LR
  External[External Systems] <-->|Ingress/Egress| Gateway[API Gateway/WAF]
  Gateway --> |Ingress| WEX[WEX Systems]
  WEX --> |Egress| Gateway

  subgraph "Data Protection Controls"
    Gateway --> |TLS 1.2+| WEX
    Gateway --> |Authentication| WEX
    Gateway --> |Authorization| WEX
  end
```

#### Authentication & Authorization

WEX Employee Authentication Components:

* 🔲 IdentityIQ SAML
* 🔲 Okta Workforce Identity
* 🔲 Active Directory: `WEXPRODR`
* 🔲 Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* 🔲 Other: `Explain`

WEX Employee automated provisioning & deprovisioning:

* 🔲 Yes
* 🔲 No

WEX Customer Authentication Components:

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory: `DOMAIN FQDN`
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX customers do not authenticate
* 🔲 Other: `Explain`

WEX Service Principal Types:

* 🔲 Amazon Managed Identity
* 🔲 Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA: `DOMAIN FQDN`
* 🔲 Active Directory User Service Account: `DOMAIN FQDN`
* 🔲 OAuth / OIDC Token
* 🔲 Local user
* 🔲 Digital certificates
* 🔲 Local administrator
* 🔲 Other: `Explain`

All employee, customer, and service principal credentials require periodic rotation:

* 🔲 Yes
* 🔲 No

Service principal credentials are automatically rotated:

* 🔲 Yes
* 🔲 No

#### Network Traffic Patterns

**Inbound Connectivity:**

* 🔲 Protected by Imperva WAF
* 🔲 Direct inbound (not WAF-protected)
* 🔲 Customer/vendor traffic
* 🔲 WEX business user traffic
* 🔲 WEX support user traffic

**Outbound Connectivity:**

* 🔲 To customer/vendor systems
* 🔲 From WEX network (support)
* 🔲 Class 1 data transmission
* 🔲 Class 2 data transmission

**Security Controls:**

* 🔲 Bot protection enabled
* 🔲 Automated certificate renewal
* 🔲 TLS 1.2+ enforcement
* 🔲 Network traffic encryption

### 🤖 AI/ML

Indicate if this design includes any new AI/ML components:

* 🔲 Yes
* 🔲 No

Indicate if all AI/ML components have been reviewed for AI/ML compliance:

* 🔲 Yes
* 🔲 No

### 🔄 Reliability

#### High Availability Controls

* 🔲 Availability Zone Redundancy
* 🔲 Regional Failover
* 🔲 Automated Failover
* 🔲 Multi-Region Active-Active
* 🔲 Load Balancing
* 🔲 Content Delivery Network
* 🔲 Monitoring & Alerting
* 🔲 Other: `Explain`

#### Outage Impact Assessment

Business Impact:

* 🔲 Critical customer business impact
* 🔲 Partial loss of non-critical functionality
* 🔲 Customer recovery assistance required
* 🔲 Multiple lines of business affected
* 🔲 Multiple products in LOB affected
* 🔲 Multiple customers affected
* 🔲 External SLA impact
* 🔲 Internal SLA impact
* 🔲 Engineering support required

Recovery Actions:

* 🔲 Infrastructure deployment/patching
* 🔲 Application deployment
* 🔲 Configuration updates (App/LB/DNS/Firewall/Gateway)
* 🔲 Data recovery operations
* 🔲 Other: `Explain`

#### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | `[AWS / Azure / GCP]` |
| Primary Region | `[Region Name]` |
| Failover Region | `[Region Name]` |
| Production VNet | `[Network Name]` |
| WEX Fabric ID | `[UUID]` |

#### Vendor Cloud Infrastructure (if applicable)

| Component | Value |
|-----------|--------|
| Vendor Name | `[Name]` |
| Cloud Provider | `[AWS / Azure / GCP]` |
| Primary Region | `[Region Name]` |
| Failover Region | `[Region Name]` |

#### Network Connectivity Services

* 🔲 SDWAN
* 🔲 AWS Private Link
* 🔲 Azure Private Link
* 🔲 Netskope Private Access (NPA)
* 🔲 Point-to-Point VPN
* 🔲 Network Peering
* 🔲 Client VPN
* 🔲 DMZ

### ☁️ SaaS

#### Key Components

| Component | Description | Technology | Purpose |
|-----------|-------------|------------|---------|
| [Component 1] | [Brief description] | [Tech stack] | [Business function] |
| [Component 2] | [Brief description] | [Tech stack] | [Business function] |
| [Component 3] | [Brief description] | [Tech stack] | [Business function] |

#### Design Impact Scope

* 🔲 Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* 🔲 Vendor & Product Risk Assessment completed

#### Logging & APM

* 🔲 Splunk Forwarding
* 🔲 DataDog Forwarding
* 🔲 Security Event Logging
* 🔲 Synthetic Testing
* 🔲 Log Archival
* 🔲 Log Purging
* 🔲 Class 1 Data Logging Controls

#### Design Patterns

Design, delivery, or support patterns incorporated in this design:

* **Pattern 1:** [Pattern Name](documentation url)
* **Pattern 2:** [Pattern Name](documentation url)
* **Pattern 3:** [Pattern Name](documentation url)

#### Standards Adoption

Deviations from standard tools, services, and reference architectures are:

* 🔲 **WEX Fabric:** `Justification`
* 🔲 **SCM (GitHub Enterprise):** `Justification`
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `Justification`
* 🔲 **DFS (Panzura):** `Justification`
* 🔲 **Outbound Email (PowerMTA/Sendgrid):** `Justification`
* 🔲 **SFTP (GoAnywhere):** `Justification`
* 🔲 **WAF (Imperva):** `Justification`
* 🔲 **CDN (Imperva):** `Justification`
* 🔲 **Logging (Splunk):** `Justification`
* 🔲 **APM (DataDog):** `Justification`
* 🔲 **Auth Gateway:** `Justification`
* 🔲 **AI Gateway:** `Justification`
* 🔲 **Eventing Platform:** `Justification`
* 🔲 **WEX Data Platform:** `Justification`
* 🔲 **Tokenizer:** `Justification`
* 🔲 **Notification Hub:** `Justification`

### 📋 Relevant Context

{The following optional sections are used to provide relevant context when relevant to the proposed solution. They may be omitted when not relevant.}

#### Secure Coding

### Technology Stack:

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | `[java/dotnet/node/python/etc]` |
| Database Technologies | `[postgres/mysql/mssql/nosql/etc]` |
| Storage Solutions | `[S3/Azure Blob/DFS/etc]` |
| Operating Systems | `[linux/windows/containers/etc]` |
| Client Platforms | `[web/desktop/mobile/api/etc]` |

### Security Controls:

Code quality scanning services that are active for all development repositories:

* 🔲 Snyk
* 🔲 Cycode
* 🔲 Mend Renovate
* 🔲 GitHub Copilot
* 🔲 Dependabot
* 🔲 Other: `Service Name`

#### Procurement Details

Third-Party Software Type:

* 🔲 SaaS
* 🔲 IaaS
* 🔲 AWS PaaS
* 🔲 Azure PaaS
* 🔲 Code Dependency Package
* 🔲 Desktop
* 🔲 Mobile App
* 🔲 Browser - Web Browser

Additional Considerations:

* 🔲 Software installation & configuration is automated
* 🔲 Cost exceeds $25,000
* 🔲 Solution is part of Target State Architecture
* 🔲 Temporary solution planned for replacement

Alternatives Considered:

* **Product 1:** Summary of deciding factors
* **Product 2:** Summary of deciding factors
* **Product 3:** Summary of deciding factors

Build / Partner Analysis:

[2-3 sentences explaining why building in-house or partnering with another WEX team or vendor to build a solution was not pursued.]

## 🧩 Risk Analysis & Dependencies

### 🚨 Key Risks

> These are key risks that could impact delivery and potential mitigation strategies.

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| [Risk 1] | High/Medium/Low | High/Medium/Low | [Mitigation strategy] |
| [Risk 2] | High/Medium/Low | High/Medium/Low | [Mitigation strategy] |
| [Risk 3] | High/Medium/Low | High/Medium/Low | [Mitigation strategy] |

### 🔍 Critical Assumptions

> These are the conditions presumed to be true for the success of this solution.

* **Assumption 1:** [Description and impact if invalid]
* **Assumption 2:** [Description and impact if invalid]
* **Assumption 3:** [Description and impact if invalid]

### ⚠️ Known Issues

> Document any known issues or limitations with the current design.

* **Issue 1:** [Description, workaround if available]
* **Issue 2:** [Description, workaround if available]
* **Issue 3:** [Description, workaround if available]

### 📦 System Dependencies

> Document all external systems that the solution is dependent upon.

| Dependency | Type | Criticality | Contact Team |
|------------|------|-------------|--------------|
| [Dependency 1] | [System/Data/API] | High/Medium/Low | [Team name] |
| [Dependency 2] | [System/Data/API] | High/Medium/Low | [Team name] |
| [Dependency 3] | [System/Data/API] | High/Medium/Low | [Team name] |

## 🔗 Reference Links

* **Epic/Feature:** `[[Jira #] | [Azure Boards #]]`
* **Support Tickets:** `JSM-1234`, `JSM-5678`
* **Documentation:**
  * `[Title](url)`
  * `[Title](url)`
* **Repositories:**
  * `[Name](url)`
  * `[Name](url)`

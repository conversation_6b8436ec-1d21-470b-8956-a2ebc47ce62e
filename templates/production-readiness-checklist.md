# Production Readiness Checklist Template

```text
Author: [Name]
Publish Date: [YYYY-MM-DD]
Product Name: ProductX
LOB: LOBX
```

## Code Quality & Deployment

### Code Reviews

> _Confirm that all code changes have undergone code reviews by at least two team members and identify any outstanding pull requests or unreviewed code submissions._

{Response Here}

### Unit Tests

> _Provide evidence of unit test execution and results demonstrating adequate coverage of all critical paths and functions, and identify any remaining gaps._

{Response Here}

### Integration Tests

> _Provide an integration testing plan and results that show coverage of all critical interactions between components and validate overall system functionality and reliability._

{Response Here}

### End-To-End Tests

> _Provide an end-to-end test plan and results that validate system functionality and user experience, covering common flows, edge cases, and potential failures._

{Response Here}

### CI/CD Pipelines

> _Describe how your CI/CD pipelines automate the build, testing, and deployment of both applications and infrastructure-as-code (IaC), ensuring consistent quality checks and deployments across all environments._

{Response Here}

## Performance & Scalability

### Load Testing

> _Provide a test plan and the results showing that the system has undergone load testing in order to demonstrate that the system meets the defined performance criteria under normal and expected peak load conditions._

{Response Here}

### Stress Testing

> _Provide a test plan and the results showing that comprehensive stress testing has been completed to understand the limits of all system components and any breaking points._

{Response Here}

### Scalability Testing

> _Provide a test plan and the results showing the evaluation of the ability for the system to scale up and down based on changes in demand while maintaining optimal performance._

{Response Here}

## Security

### Security Architecture Review

> _Provide evidence demonstrating that a Security Architecture review has been conducted._

{Response Here}

### Security Scanning

> Provide evidence that the [required security scanning tools](https://wexinc.atlassian.net/wiki/spaces/ESA/pages/154923565420) have been implemented.

{Response Here}

### Vulnerability Remediation

> _Are application and infrastructure vulnerabilities being effectively identified, assessed, prioritized, and remediated in accordance with the [WEX Vulnerability Management policy](https://wexinc.policytech.com/dotNet/documents/?docid=2150&app=pt&source=search)? Please provide a link to the latest vulnerability testing results and remediation plans._

{Response Here}

### Data At Rest & In Transit

> _Provide details on the specific controls implemented to ensure data is encrypted at rest, in transit, and in use, as required by the [WEX Encryption Policy](https://wexinc.policytech.com/dotNet/documents/?app=pt&source=unspecified&docid=2340)._

{Response Here}

## Observability

### Application Performance Monitoring

> _Is DataDog being used to monitor the performance of critical system components? If so, provide links showing the specific metrics collected, dashboards used for visualization, and alerting mechanisms in place for detecting performance anomalies._

{Response Here}

### Logging

> _Confirm that logging has been implemented in accordance with the [WEX Logging Standards](https://docs.google.com/document/d/1hCkHdKisiHUzkS51pD9PoSvOg0c8kIyr/edit?usp=sharing&ouid=102572783890649579823&rtpof=true&sd=true) and logs are being sent to Splunk._

{Response Here}

### Alert Escalation

> _Provide evidence showing that monitors are integrated with an alert escalation tool, such as XMatters or PagerDuty, which helps to ensure timely receipt and action._

{Response Here}

## Reliability

### Reliability Targets

> _Provide documentation outlining agreed upon reliability targets (i.e. SLAs, SLOs, [RTO/RPO](https://aws.amazon.com/blogs/mt/establishing-rpo-and-rto-targets-for-cloud-applications/), [MTTR/MTBF](https://community.ibm.com/community/user/ai-datascience/blogs/emiley-edward/2022/04/05/what-is-mttr-in-cloud-computing))._

{Response Here}

### High Availability

> _Provide documentation that shows effective mechanisms implemented to ensure that the system can recover from failures and continue to operate with minimal downtime or interruption to customers._

{Response Here}

### Recovery

> _In accordance with the [Global Resilience Planning Guidelines](https://wexinc.policytech.com/dotNet/documents/?docid=2407&app=pt&source=search), provide documentation outlining disaster recovery procedures that are used to ensure business continuity in the case of a system failure._

{Response Here}

### Dependency Reliability

> _Provide documentation that outlines agreements with dependent systems, potential risks, and mitigation strategies in the event of an integration failure._

{Response Here}

## Infrastructure & Environment

### Infrastructure as Code (IaC)

> _Provide evidence demonstrating the use of Infrastructure as Code (IaC) for tracking changes, enabling collaboration, and ensuring consistent deployments across all environments._

{Response Here}

### Environment Segregation

> _Provide evidence of separate development, testing, and production environments with implemented safeguards that prevent cross-contamination (i.e. separate databases, access controls, and deployment pipelines)._

{Response Here}

### Resource Monitoring

> _Provide evidence demonstrating the established resource limits and quotas for critical system components, including the monitoring and alerting mechanisms used to proactively track their utilization._

{Response Here}

### Network Connectivity

> _Provide evidence that a network connectivity review has been conducted, including test results and diagrams that demonstrate how firewall rules and routing configurations permit the expected traffic flow between integrated components._

{Response Here}

## Operational Readiness

### Operational Run Book

> _Provide documentation that outlines common maintenance tasks, incident response procedures, and troubleshooting steps in the event of a system failure._

{Response Here}

### On-Call & Escalation

> _Provide documentation covering the on-call and escalation policy, which includes all upstream/downstream dependencies, and outlines clear roles, responsibilities, and escalation paths for incidents._

{Response Here}

### Post-mortem Process

> _Provide evidence of a documented incident post-mortem process, which focuses on identifying root cause, learning from incidents, and implementing corrective actions._

{Response Here}

## Regulatory & Compliance Checks

### Data Retention & Deletion

> _In accordance to the [WEX Record Retention Policy](https://wexinc.policytech.com/dotNet/documents/?docid=1667&app=pt&source=search), provide documentation that outlines the handling, retention, and deletion of data as it pertains to this system._

{Reponse Here}

### Data Privacy

> _In accordance with the [WEX Data Protection and Governance Policy](https://wexinc.policytech.com/dotNet/documents/?docid=1991&app=pt&source=search), have reviews been conducted to ensure the overall system is in compliance with GDPR, HIPAA, or other applicable regulations?_

{Reponse Here}

### Accessibility Compliance

> _If the system includes user interface (UI) components, provide evidence that accessibility reviews been completed and necessary adjustments have been made to ensure compliance with WCAG guidelines._

{Reponse Here}

### Licensing

> _Provide evidence that a comprehensive licensing audit been completed to identify and address potential licensing issues for any components (i.e. libraries, off-the-shelf software)._

{Reponse Here}

## User Documentation & Training

### User Documentation

> _Provide documentation or informative materials that guide users through the system’s features and functionality, and that equip support teams with the knowledge and tools to provide effective assistance._

{Reponse Here}

### API Documentation

> _If APIs are made available as part of this system, provide updated API documentation, showing clear descriptions of all enabled functionalities, along with practical usage examples and tutorials that promote self-service._

{Reponse Here}

### Feedback

> _Outline the established channels for gathering user feedback and user concerns to drive continuous improvement._

{Reponse Here}

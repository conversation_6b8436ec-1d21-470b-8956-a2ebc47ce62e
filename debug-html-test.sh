#!/bin/bash

# Debug the HTML template and Mermaid loading

set -e

echo "🔍 Debug HTML Template and Mermaid Loading"
echo "=========================================="

echo "🐳 Testing HTML template generation..."

# Test the HTML template generation
docker run --rm my-custom-mark-renderer python3 -c "
import sys
sys.path.append('/app')

# Test simple diagram
mermaid_code = 'graph TD; A --> B'

# Download Mermaid.js
import subprocess
import os

mermaid_path = '/tmp/mermaid.min.js'
result = subprocess.run([
    'wget', '--no-check-certificate', '-q', '-O', mermaid_path,
    'https://unpkg.com/mermaid@10/dist/mermaid.min.js'
], timeout=30)

if os.path.exists(mermaid_path):
    print(f'✅ Downloaded Mermaid.js: {os.path.getsize(mermaid_path)} bytes')
    
    # Read first 1000 chars to check content
    with open(mermaid_path, 'r') as f:
        content = f.read(1000)
        print(f'📋 Mermaid.js starts with: {content[:200]}...')
        
        # Check if it contains key Mermaid functions
        with open(mermaid_path, 'r') as f:
            full_content = f.read()
            if 'mermaid' in full_content.lower():
                print('✅ Contains \"mermaid\" references')
            if 'initialize' in full_content:
                print('✅ Contains \"initialize\" function')
            if 'render' in full_content:
                print('✅ Contains \"render\" function')
else:
    print('❌ Failed to download Mermaid.js')
"

echo ""
echo "🧪 Testing simple HTML with local Mermaid..."

# Create a simple test HTML file
docker run --rm my-custom-mark-renderer sh -c "
# Download Mermaid.js
wget --no-check-certificate -q -O /tmp/mermaid.min.js https://unpkg.com/mermaid@10/dist/mermaid.min.js

# Create simple test HTML
cat > /tmp/test.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Simple Mermaid Test</title>
    <style>
        body { margin: 20px; font-family: Arial; }
        .mermaid { border: 2px solid red; padding: 10px; }
    </style>
</head>
<body>
    <h1>Mermaid Test</h1>
    <div class=\"mermaid\">graph TD; A[Start] --> B[End]</div>
    
    <script>
        console.log('Script starting...');
        
        // Check if we have the mermaid div
        const div = document.querySelector('.mermaid');
        console.log('Mermaid div found:', div ? 'YES' : 'NO');
        if (div) {
            console.log('Div content:', div.textContent);
        }
        
        // Try to load mermaid from file
        fetch('/tmp/mermaid.min.js')
            .then(response => response.text())
            .then(script => {
                console.log('Loaded script, length:', script.length);
                eval(script);
                console.log('Mermaid available:', typeof mermaid !== 'undefined');
            })
            .catch(err => console.error('Failed to load mermaid:', err));
    </script>
</body>
</html>
EOF

echo '✅ Created test HTML file'
echo '📋 HTML content:'
cat /tmp/test.html
"

echo ""
echo "🔍 HTML debug test completed."

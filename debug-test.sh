#!/bin/bash

# Debug test for the custom Mermaid renderer
# This will help us understand what's happening in the browser

set -e

echo "🔍 Debug Test: Custom Mermaid Renderer"
echo "======================================"

echo "🧪 Testing with simple diagrams..."

# Test 1: Simple graph
echo ""
echo "Testing: Simple Graph"
DIAGRAM="graph TD
A --> B"
echo "Diagram: $DIAGRAM"

echo "$DIAGRAM" | docker run --rm -i my-custom-mark-renderer python3 /app/custom_mermaid_renderer.py > debug_output.b64 2> debug_stderr.log

echo "Exit code: $?"
echo "Stderr output:"
cat debug_stderr.log
echo ""

if [ -s debug_output.b64 ]; then
    echo "✅ Got base64 output ($(wc -c < debug_output.b64) bytes)"

    # Try to decode and check if it's a valid PNG
    if base64 -d debug_output.b64 > debug_output.png 2>/dev/null; then
        if file debug_output.png | grep -q "PNG image"; then
            echo "✅ Valid PNG generated!"
            echo "PNG info: $(file debug_output.png)"
        else
            echo "❌ Invalid PNG file"
        fi
    else
        echo "❌ Failed to decode base64"
    fi
else
    echo "❌ No base64 output"
fi

echo "----------------------------------------"

echo ""
echo "🔧 Interactive debugging:"
echo "Run: docker run -it --rm my-custom-mark-renderer bash"
echo "Then try:"
echo "  echo 'graph TD; A-->B' | python3 /app/custom_mermaid_renderer.py"
echo ""
echo "To see browser console output, modify the script to add:"
echo "  await page.evaluate('console.log(\"Debug:\", document.body.innerHTML)')"

# Clean up
rm -f debug_output.b64 debug_stderr.log debug_output.png

#!/bin/bash

# Test SSL bypass and local Mermaid.js download

set -e

echo "🔧 Testing SSL Bypass and Local Mermaid Download"
echo "================================================"

echo "🐳 Testing with SSL verification disabled..."

# Test downloading Mermaid.js with SSL verification disabled
docker run --rm my-custom-mark-renderer sh -c "
echo '🔧 Testing SSL bypass methods:'

echo '1. Testing curl with --insecure flag:'
curl --insecure -I https://unpkg.com/mermaid@10/dist/mermaid.min.js --connect-timeout 10 || echo 'Still failed'

echo ''
echo '2. Testing curl with --cacert disabled:'
curl -k -I https://unpkg.com/mermaid@10/dist/mermaid.min.js --connect-timeout 10 || echo 'Still failed'

echo ''
echo '3. Attempting to download Mermaid.js with --insecure:'
curl --insecure -s -o /tmp/mermaid.js https://unpkg.com/mermaid@10/dist/mermaid.min.js --connect-timeout 10 && echo 'Download SUCCESS!' || echo 'Download FAILED'

echo ''
echo '4. Checking downloaded file:'
if [ -f /tmp/mermaid.js ]; then
    echo \"File size: \$(wc -c < /tmp/mermaid.js) bytes\"
    echo \"First 200 characters:\"
    head -c 200 /tmp/mermaid.js
    echo \"\"
    echo \"\"
    echo \"Checking if it contains 'mermaid':\"
    grep -o 'mermaid' /tmp/mermaid.js | head -5 || echo 'No mermaid found'
else
    echo 'No file downloaded'
fi

echo ''
echo '5. Testing with wget:'
wget --no-check-certificate -q -O /tmp/mermaid-wget.js https://unpkg.com/mermaid@10/dist/mermaid.min.js --timeout=10 && echo 'wget SUCCESS!' || echo 'wget FAILED'

if [ -f /tmp/mermaid-wget.js ]; then
    echo \"wget file size: \$(wc -c < /tmp/mermaid-wget.js) bytes\"
fi
"

echo ""
echo "🔍 SSL bypass test completed."

.idea
*.egg-info

# macOS
.DS_Store

# Logs
logs
*.log

# Coverage directory generated when running tests with coverage
coverage

# Dependencies
.venv
**/__pypackages__/*
!.gitkeep

# dotenv environment variables file
.env
.env.test

# Build output
dist
dist-types

# Temporary change files created by Vim
*.swp

# Local Cache
__pycache__
.pytest_cache

tmp

# Super-linter
.mypy_cache

# Cookiecutter output
output/
fabric-workflows/

# vscode
.vscode

# disallow file names with spaces
**\ **
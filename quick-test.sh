#!/bin/bash

# Quick manual test for the custom Mermaid renderer
# This follows the exact steps you outlined

set -e

echo "🧪 Quick Test: Custom Mermaid Renderer"
echo "======================================"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Sample Mermaid diagram using correct architecture syntax
SAMPLE_DIAGRAM="architecture-beta
    group cloud_functions(cloud)[Cloud Functions]
        service api(logos:aws-lambda)[API Gateway] in cloud_functions
        service processor(logos:azure-functions)[Data Processor] in cloud_functions

    group storage(database)[Data Storage]
        service db(logos:aws-dynamodb)[NoSQL DB] in storage
        service blob(logos:azure-blob-storage)[Blob Storage] in storage

    api:R --> L:processor
    processor:B --> T:db
    processor:B --> T:blob"

echo -e "${YELLOW}Step 1: Building Docker image...${NC}"

# Create minimal test setup
mkdir -p quick-test-tmp
cp actions/publish-mark/custom_mermaid_renderer.py quick-test-tmp/

# Create simple Dockerfile
cat > quick-test-tmp/Dockerfile << 'EOF'
FROM kovetskiy/mark:latest

# Update package list and install Python + network tools
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    curl \
    wget \
    ca-certificates \
    dnsutils \
    iputils-ping \
    iproute2 \
    && rm -rf /var/lib/apt/lists/* \
    && update-ca-certificates

# Install Playwright (using --break-system-packages for Docker)
RUN pip3 install --break-system-packages playwright

# Install Chromium browser
RUN python3 -m playwright install chromium

# Install system dependencies for Chromium
RUN python3 -m playwright install-deps

# Create app directory and copy script
RUN mkdir -p /app
COPY custom_mermaid_renderer.py /app/custom_mermaid_renderer.py
RUN chmod +x /app/custom_mermaid_renderer.py

# Create mermaid-go wrapper to integrate with mark
RUN echo '#!/bin/bash' > /usr/local/bin/mermaid-go && \
    echo '# Custom mermaid-go wrapper that calls our Python renderer' >> /usr/local/bin/mermaid-go && \
    echo 'python3 /app/custom_mermaid_renderer.py' >> /usr/local/bin/mermaid-go && \
    chmod +x /usr/local/bin/mermaid-go

WORKDIR /app
EOF

# Build the image
docker build -t my-custom-mark-renderer quick-test-tmp/

echo -e "${GREEN}✅ Docker image built${NC}"

echo -e "${YELLOW}Step 2: Running container interactively...${NC}"
echo "You can now test manually with:"
echo ""
echo "docker run -it --rm my-custom-mark-renderer bash"
echo ""
echo "Inside the container, run:"
echo "echo 'architecture-beta"
echo "  service A[Service A]"
echo "  service B[Service B]"
echo "  A --> B' | python /app/custom_mermaid_renderer.py > output.b64"
echo ""

echo -e "${YELLOW}Step 3: Automated test...${NC}"

# Run automated test
echo "Testing with sample architecture diagram..."
if echo "$SAMPLE_DIAGRAM" | docker run --rm -i my-custom-mark-renderer python3 /app/custom_mermaid_renderer.py > quick-test-tmp/output.b64 2> quick-test-tmp/stderr.log; then
    
    if [ -s quick-test-tmp/output.b64 ]; then
        # Try to decode base64
        if base64 -d quick-test-tmp/output.b64 > quick-test-tmp/output.png 2>/dev/null; then
            # Check if it's a valid PNG
            if file quick-test-tmp/output.png | grep -q "PNG image"; then
                file_size=$(stat -f%z quick-test-tmp/output.png 2>/dev/null || stat -c%s quick-test-tmp/output.png 2>/dev/null)
                echo -e "${GREEN}✅ SUCCESS: Generated valid PNG ($file_size bytes)${NC}"
                echo -e "${GREEN}📁 Output saved to: quick-test-tmp/output.png${NC}"
                
                # Show first few lines of base64 output
                echo -e "${YELLOW}📋 Base64 output (first 100 chars):${NC}"
                head -c 100 quick-test-tmp/output.b64
                echo "..."
                
                # Show logs
                if [ -s quick-test-tmp/stderr.log ]; then
                    echo -e "${YELLOW}📋 Renderer logs:${NC}"
                    tail -10 quick-test-tmp/stderr.log
                fi
                
            else
                echo -e "${RED}❌ FAILED: Generated file is not a valid PNG${NC}"
                exit 1
            fi
        else
            echo -e "${RED}❌ FAILED: Could not decode base64 output${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ FAILED: No base64 output generated${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ FAILED: Script execution failed${NC}"
    if [ -s quick-test-tmp/stderr.log ]; then
        echo -e "${RED}Error logs:${NC}"
        cat quick-test-tmp/stderr.log
    fi
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Quick test completed successfully!${NC}"
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Check the generated PNG: quick-test-tmp/output.png"
echo "2. Run interactive test: docker run -it --rm my-custom-mark-renderer bash"
echo "3. Clean up: rm -rf quick-test-tmp/"

---
# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: check-added-large-files # prevents giant files from being committed.
      - id: check-case-conflict # checks for files that would conflict in case-insensitive filesystems.
      - id: check-merge-conflict # checks for files that contain merge conflict strings.
      - id: check-yaml # checks yaml files for parseable syntax.
      - id: detect-private-key # detects the presence of private keys.
      - id: end-of-file-fixer # ensures that a file is either empty, or ends with one newline.
      - id: fix-byte-order-marker # removes utf-8 byte order marker.
      - id: mixed-line-ending # replaces or checks mixed line ending.
      - id: requirements-txt-fixer # sorts entries in requirements.txt.
      - id: trailing-whitespace # trims trailing whitespace.
      - id: check-json # if you have JSON files in your repo
      - id: check-merge-conflict # useful if you often rebase/merge
      - id: check-symlinks # very helpful if there’s symlinks checked in to the index
      - id: destroyed-symlinks # very helpful if there’s symlinks checked in to the index
      - id: check-vcs-permalinks # particularly useful if there’s a lot of documentation files tracked
      - id: file-contents-sorter # useful for sorting files that have a specific format

  # GO LANG SECTION
  - repo: https://github.com/dnephin/pre-commit-golang
    rev: v0.5.1
    hooks:
      - id: go-fmt

  # YAML SECTION
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.35.1
    hooks:
      - id: yamllint
        verbose: true  # create awareness of linter findings
        args: ["-d", "{extends: relaxed, rules: {line-length: {max: 160}}}"]

  # TERRAFORM SECTION
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.89.0
    hooks:
      - id: terraform_fmt
        args:
          - --args=-diff
          - --args=-write=true
        stages: [ commit ]
      - id: terraform_docs
        stages: [ commit ]
      - id: terraform_tflint
        files: \.tf$
        args:
          - --args=--config=__GIT_WORKING_DIR__/.github/linters/.tflint.hcl
        stages: [ commit ]

  # GITHUB SECTION
  - repo: https://github.com/sirosen/check-jsonschema
    rev: 0.28.2
    hooks:
      - id: check-github-actions
      - id: check-github-workflows

  # DOCKER SECTION
  - repo: https://github.com/iamthefij/docker-pre-commit
    rev: v3.0.1
    hooks:
      - id: docker-compose-check

  # MARKDOWN SECTION
#  - repo: https://github.com/igorshubovych/markdownlint-cli
#    rev: v0.41.0
#    hooks:
#      - id: markdownlint-fix-docker
#        args:
#          - "--config ./.markdownlint.json"

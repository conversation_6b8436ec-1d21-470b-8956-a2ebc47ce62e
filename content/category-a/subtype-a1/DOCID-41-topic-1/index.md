<!-- Parent: Category A -->
<!-- Parent: Subtype A1 -->
<!-- Title: DOCID-41 Topic-1test -->

<!-- Include: macros.md -->
# Topic 1test

This is sample content for technical documentation written in Markdown.

**Testing Custom Mermaid Renderer**: This diagram should now show real AWS and Azure icons instead of blue question marks!

Mermaid Test

Architecture Beta

```mermaid
architecture-beta
    group cloud_functions(cloud)[Cloud Functions]
        service api(logos:aws-lambda)[API Gateway] in cloud_functions
        service processor(simple-icons:azurefunctions)[Data Processor] in cloud_functions

    group storage(database)[Data Storage]
        service db(logos:aws-dynamodb)[NoSQL DB] in storage
        service blob(simple-icons:microsoftazure)[Blob Storage] in storage

    api:R --> L:processor
    processor:B --> T:db
    processor:B --> T:blob
```

Radar

```mermaid
---
title: "Grades"
---
radar-beta
  axis m["Math"], s["Science"], e["English"]
  axis h["History"], g["Geography"], a["Art"]
  curve a["<PERSON>"]{85, 90, 80, 70, 75, 90}
  curve b["<PERSON>"]{70, 75, 85, 80, 90, 85}

  max 100
  min 0
```
<!-- Include: disclaimer.md -->
<!-- Parent: Category A -->
<!-- Parent: Subtype A1 -->
<!-- Title: DOCID-41 Topic-1test -->

<!-- Include: macros.md -->
# Topic 1test

This is sample content for technical documentation written in Markdown.

Mermaid Test

Architecture Beta

```mermaid
architecture-beta
    group api(logos:aws-lambda)[API]

    service db(logos:aws-aurora)[Database] in api
    service disk1(logos:aws-glacier)[Storage] in api
    service disk2(logos:aws-s3)[Storage] in api
    service server(logos:aws-ec2)[Server] in api

    db:L -- R:server
    disk1:T -- B:server
    disk2:T -- B:db
```

Radar

```mermaid
---
title: "Grades"
---
radar-beta
  axis m["Math"], s["Science"], e["English"]
  axis h["History"], g["Geography"], a["Art"]
  curve a["Alice"]{85, 90, 80, 70, 75, 90}
  curve b["<PERSON>"]{70, 75, 85, 80, 90, 85}

  max 100
  min 0
```
<!-- Include: disclaimer.md -->
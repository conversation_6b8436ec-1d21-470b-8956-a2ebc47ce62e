<!-- Parent: Paved Roads -->
<!-- Parent: Reference Architecture -->
<!-- Title: RFC-484 OpenTelemetry Observability Architecture -->
# OpenTelemetry Observability Architecture

```text
Author: <PERSON>
Publish Date: 2025-03-11
Category: Paved Roads
Subtype: Reference Architecture
```

> <!-- Info -->
> Draft!

## Introduction

This reference architecture defines a standardized approach for implementing distributed tracing, metrics collection, and logging using OpenTelemetry.

It will be demonstrating using DataDog, WEX's monitoring standard, as the primary backend service.

> <!-- Info -->
> While you can implement monitoring using DataDog tools and libraries directly, you are encouraged to use OpenTelemetry tools and libraries, as it avoids vendor lock-in. DataDog is fully compatible with OpenTelemetry tooling, as you'll see in this RFC.

This RFC contains the following main sections:

- What is observability and why should I care?
- Architecture overview
- Setting up agents and collectors in your infrastructure
- Emitting metrics and APM data from your application containers

It covers each of these sections with examples for many systems and tools, such as AWS ECS Fargate, AWS EKS, and WEX Fabric.

## What is observability and why should I care?

From OpenTelemetry:

> Observability lets you understand a system from the outside by letting you ask questions about that system without knowing its inner workings. Furthermore, it allows you to easily troubleshoot and handle novel problems, that is, “unknown unknowns”. It also helps you answer the question “Why is this happening?”
>
> To ask those questions about your system, your application must be properly instrumented. That is, the application code must emit signals such as traces, metrics, and logs. An application is properly instrumented when developers don’t need to add more instrumentation to troubleshoot an issue, because they have all of the information they need.

You are highly encouraged to read this page: [Observability Primer](https://opentelemetry.io/docs/concepts/observability-primer/)

> <!-- Info -->
> The stuff below was mostly generated by Copilot and I'm still working on it

### Business Benefits

- Reduced Mean Time To Resolution (MTTR) through enhanced system visibility
- Improved customer experience through proactive monitoring
- Lower operational costs through standardized observability practices
- Faster incident response and root cause analysis

### Technical Benefits of using OpenTelemetry

- Unified observability data collection across polyglot environments
- Automated instrumentation reducing developer overhead
- Consistent metric and trace correlation
- Cloud-native scalability and reliability
- Vendor-agnostic telemetry collection

## Architecture Overview

OpenTelemetry observability architecture consists of three main components working together to provide comprehensive system visibility:

### Components

#### 1. OpenTelemetry Collector

The OpenTelemetry Collector acts as a central aggregation and processing point for all telemetry data. It:

- Receives data from multiple applications and services
- Processes and batches telemetry data
- Exports data to backend systems (DataDog but not for long (TODO))
- Provides data transformation and filtering capabilities

#### 2. Application Instrumentation

Applications can be instrumented in two ways:

##### Automatic Instrumentation

This is the recommended starting point for most applications. It provides:

- Zero-code instrumentation for common frameworks and libraries
- Automatic collection of standard metrics and traces
- Minimal configuration requirements

Supported Language SDKs:

| Language | Documentation | Auto-Instrumentation Support |
|----------|--------------|----------------------------|
| Java | [OpenTelemetry Java](https://opentelemetry.io/docs/zero-code/java/agent/) | Extensive |
| .NET | [OpenTelemetry .NET](https://opentelemetry.io/docs/zero-code/dotnet/) | Extensive |
| Go | [OpenTelemetry Go](https://opentelemetry.io/docs/zero-code/go/) | Limited |
| Python | [OpenTelemetry Python](https://opentelemetry.io/docs/zero-code/python/) | Good |
| JavaScript | [OpenTelemetry JS](https://opentelemetry.io/docs/zero-code/js/) | Good |

For Kubernetes environments:
- Use the [OpenTelemetry Operator](https://opentelemetry.io/docs/kubernetes/operator/automatic/) for automatic instrumentation injection
- Simplifies instrumentation management across clusters

##### Manual Instrumentation

Use manual instrumentation when you need:
- Custom metrics or traces
- Fine-grained control over telemetry data
- Specific business-level metrics

Implementation steps:

1. Add the OpenTelemetry SDK to your application
2. Configure the SDK with collector endpoint information
3. Initialize OpenTelemetry components
4. Add specific instrumentation code where needed

Reference: [OpenTelemetry Language Implementation Guides](https://opentelemetry.io/docs/languages/)

> [!tip]
> Start with automatic instrumentation and add manual instrumentation only when needed for specific use cases.

#### 3. Backend System (BLANK)

BLANK serves as our observability backend, providing:

- Metric visualization and dashboards
- Trace aggregation and analysis
- Log management and correlation
- Alerting and monitoring capabilities

### Architecture Diagram
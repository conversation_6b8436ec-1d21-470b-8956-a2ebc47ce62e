<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by Microsoft Visio, SVG Export aks-baseline-architecture.svg secure-baseline-architecture -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="9.625in" height="8.125in" viewBox="0 0 693 585"
		xml:space="preserve" color-interpolation-filters="sRGB" class="st78">
	<v:documentProperties v:langID="1033" v:viewMarkup="false">
		<v:userDefs>
			<v:ud v:nameU="msvNoAutoConnect" v:val="VT0(1):26"/>
			<v:ud v:nameU="msvWarnOnPersonalInfo" v:val="VT0(1):5"/>
		</v:userDefs>
	</v:documentProperties>

	<style type="text/css">
	<![CDATA[
		.st1 {fill:#ffffff;stroke:none;stroke-width:0.25}
		.st2 {fill:none;stroke:none;stroke-width:0.25}
		.st3 {fill:#000000;font-family:Segoe UI;font-size:0.833336em}
		.st4 {font-size:1em}
		.st5 {fill:#ecf3fb;stroke:#d8d8d8;stroke-width:0.25}
		.st6 {fill:#000000;font-family:Segoe UI;font-size:0.75em}
		.st7 {fill:#ffffff;stroke:#7f7f7f;stroke-dasharray:2.25,2.25;stroke-width:0.75}
		.st8 {fill:#326ce5}
		.st9 {fill:#ffffff}
		.st10 {fill:#ffffff;stroke:none;stroke-width:0.75}
		.st11 {fill:#000000;font-family:Segoe UI Semibold;font-size:0.75em}
		.st12 {marker-end:url(#mrkr4-131);marker-start:url(#mrkr4-129);stroke:#000000;stroke-dasharray:1.5,3;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.5}
		.st13 {fill:#000000;fill-opacity:1;stroke:#000000;stroke-opacity:1;stroke-width:0.37313432835821}
		.st14 {marker-end:url(#mrkr4-131);stroke:#000000;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.5}
		.st15 {fill:#f2f2f2;stroke:#d8d8d8;stroke-width:0.25}
		.st16 {fill:#3f5765;stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.75}
		.st17 {fill:url(#grad0-159);stroke:none;stroke-linecap:butt;stroke-width:12.75}
		.st18 {fill:url(#grad4-169);stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st19 {fill:#ffffff;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st20 {fill:url(#grad7-175);stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st21 {fill:#ff9300;fill-opacity:0.75;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st22 {fill:url(#grad0-188)}
		.st23 {stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st24 {fill:#767676;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st25 {fill:#999999}
		.st26 {fill:#a3a3a3;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st27 {fill:#b3b3b3;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st28 {fill:url(#grad0-202);stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st29 {fill:#999999;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st30 {fill:#4672c4;stroke:none;stroke-width:6.375}
		.st31 {fill:none;stroke:#4472c4;stroke-dasharray:2.25,2.25;stroke-width:0.75}
		.st32 {fill:#feffff;stroke:none;stroke-width:0.25}
		.st33 {fill:url(#grad0-237);stroke:none;stroke-linecap:butt;stroke-width:34.0001}
		.st34 {fill:#50e6ff;stroke:none;stroke-linecap:butt;stroke-width:34.0001}
		.st35 {fill:#1490df;stroke:none;stroke-linecap:butt;stroke-width:34.0001}
		.st36 {fill:url(#grad0-280);stroke:none;stroke-linecap:butt;stroke-width:34}
		.st37 {fill:#821010;stroke:none;stroke-linecap:butt;stroke-width:34}
		.st38 {fill:#e62323;stroke:none;stroke-linecap:butt;stroke-width:34}
		.st39 {fill:#ff7381;stroke:none;stroke-linecap:butt;stroke-width:34}
		.st40 {fill:#0072c6;stroke:none;stroke-linecap:butt;stroke-width:12.24}
		.st41 {fill:#0078d4;stroke:none;stroke-linecap:butt;stroke-width:34}
		.st42 {fill:#6bb9f2;stroke:none;stroke-linecap:butt;stroke-width:34}
		.st43 {fill:url(#grad0-313);stroke:none;stroke-linecap:butt;stroke-width:34}
		.st44 {fill:#50e6ff;stroke:none;stroke-linecap:butt;stroke-width:34}
		.st45 {fill:#00b050;stroke:none;stroke-linecap:butt;stroke-width:12.24}
		.st46 {fill:#ffffff;stroke:none;stroke-linecap:butt;stroke-width:12.24}
		.st47 {fill:url(#grad0-341);stroke:none;stroke-width:6.375}
		.st48 {fill:url(#grad0-352);stroke:none;stroke-linecap:butt;stroke-width:31.8899}
		.st49 {fill:none;stroke:none;stroke-linecap:butt;stroke-width:31.8899}
		.st50 {fill:#341a6e;stroke:none;stroke-linecap:butt;stroke-width:31.8899}
		.st51 {fill:url(#grad0-412);stroke:none;stroke-linecap:butt;stroke-width:34}
		.st52 {fill:#ffffff;stroke:none;stroke-linecap:butt;stroke-width:34}
		.st53 {fill:#0078d4}
		.st54 {stroke:none;stroke-linecap:butt;stroke-width:34}
		.st55 {fill:url(#grad0-426);stroke:none;stroke-linecap:butt;stroke-width:34}
		.st56 {fill:#f2f2f2;fill-opacity:0.55;stroke:none;stroke-linecap:butt;stroke-width:34}
		.st57 {fill:none;stroke:none;stroke-linecap:butt;stroke-width:34}
		.st58 {fill:#f2f2f2;stroke:none;stroke-linecap:butt;stroke-width:34}
		.st59 {fill:#7a7a7a;stroke:none;stroke-linecap:butt;stroke-width:12.24}
		.st60 {fill:#1490df;stroke:none;stroke-linecap:butt;stroke-width:12.24}
		.st61 {fill:url(#grad0-483);stroke:none;stroke-linecap:butt;stroke-width:12.24}
		.st62 {fill:url(#grad0-487);stroke:none;stroke-linecap:butt;stroke-width:12.24}
		.st63 {fill:#50e6ff;stroke:none;stroke-linecap:butt;stroke-width:12.24}
		.st64 {fill:none;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st65 {fill:#000000;font-family:Segoe UI;font-size:0.666664em}
		.st66 {fill:url(#grad1-585);stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st67 {fill:#9cebff;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st68 {fill:#32bedd;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st69 {fill:#50e6ff;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st70 {fill:#f04049;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st71 {fill:url(#grad7-599);stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st72 {fill:#b796f9;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st73 {fill:url(#grad0-618);stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st74 {fill:#0078d4;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st75 {fill:#005ba1;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st76 {fill:#5ea0ef;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st77 {fill:#e6522c;stroke:none;stroke-linecap:butt;stroke-width:0.75}
		.st78 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<defs id="Patterns_And_Gradients">
		<linearGradient id="grad0-159" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(-90 0.5 0.5)">
			<stop offset="0" stop-color="#0078d4" stop-opacity="1"/>
			<stop offset="0.155" stop-color="#1380da" stop-opacity="1"/>
			<stop offset="0.53" stop-color="#3c91e5" stop-opacity="1"/>
			<stop offset="0.82" stop-color="#559cec" stop-opacity="1"/>
			<stop offset="1" stop-color="#5ea0ef" stop-opacity="1"/>
		</linearGradient>
		<radialGradient id="grad4-169" cx="0.5" cy="0" r="1.1">
			<stop offset="0.18" stop-color="#5ea0ef" stop-opacity="1"/>
			<stop offset="0.56" stop-color="#5c9fee" stop-opacity="1"/>
			<stop offset="0.69" stop-color="#559ced" stop-opacity="1"/>
			<stop offset="0.78" stop-color="#4a97e9" stop-opacity="1"/>
			<stop offset="0.86" stop-color="#3990e4" stop-opacity="1"/>
			<stop offset="0.93" stop-color="#2387de" stop-opacity="1"/>
			<stop offset="0.99" stop-color="#087bd6" stop-opacity="1"/>
			<stop offset="1" stop-color="#0078d4" stop-opacity="1"/>
		</radialGradient>
		<radialGradient id="grad7-175" cx="0" cy="0" r="1.4">
			<stop offset="0.27" stop-color="#ffd70f" stop-opacity="1"/>
			<stop offset="0.49" stop-color="#ffcb12" stop-opacity="1"/>
			<stop offset="0.88" stop-color="#feac19" stop-opacity="1"/>
			<stop offset="1" stop-color="#fea11b" stop-opacity="1"/>
		</radialGradient>
		<linearGradient id="grad0-188" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(-90 0.5 0.5)">
			<stop offset="0" stop-color="#0078d4" stop-opacity="1"/>
			<stop offset="0.16" stop-color="#1380da" stop-opacity="1"/>
			<stop offset="0.53" stop-color="#3c91e5" stop-opacity="1"/>
			<stop offset="0.82" stop-color="#559cec" stop-opacity="1"/>
			<stop offset="1" stop-color="#5ea0ef" stop-opacity="1"/>
		</linearGradient>
		<linearGradient id="grad0-202" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(90 0.5 0.5)">
			<stop offset="0" stop-color="#333132" stop-opacity="1"/>
			<stop offset="1" stop-color="#5b5a5c" stop-opacity="1"/>
		</linearGradient>
		<linearGradient id="grad0-237" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(-90 0.5 0.5)">
			<stop offset="0" stop-color="#5e9624" stop-opacity="1"/>
			<stop offset="0.24" stop-color="#6fb02a" stop-opacity="1"/>
			<stop offset="0.5" stop-color="#7cc52f" stop-opacity="1"/>
			<stop offset="0.755" stop-color="#83d232" stop-opacity="1"/>
			<stop offset="1" stop-color="#86d633" stop-opacity="1"/>
		</linearGradient>
		<linearGradient id="grad0-280" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(-90 0.5 0.5)">
			<stop offset="0" stop-color="#0078d4" stop-opacity="1"/>
			<stop offset="0.82" stop-color="#5ea0ef" stop-opacity="1"/>
		</linearGradient>
		<linearGradient id="grad0-313" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(90 0.5 0.5)">
			<stop offset="0" stop-color="#5ea0ef" stop-opacity="1"/>
			<stop offset="0.18" stop-color="#559cec" stop-opacity="1"/>
			<stop offset="0.47" stop-color="#3c91e5" stop-opacity="1"/>
			<stop offset="0.84" stop-color="#1380da" stop-opacity="1"/>
			<stop offset="1" stop-color="#0078d4" stop-opacity="1"/>
		</linearGradient>
		<linearGradient id="grad0-341" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(270 0.5 0.5)">
			<stop offset="0" stop-color="#0078d4" stop-opacity="1"/>
			<stop offset="0.155" stop-color="#1380da" stop-opacity="1"/>
			<stop offset="0.53" stop-color="#3c91e5" stop-opacity="1"/>
			<stop offset="0.82" stop-color="#559cec" stop-opacity="1"/>
			<stop offset="1" stop-color="#5ea0ef" stop-opacity="1"/>
		</linearGradient>
		<linearGradient id="grad0-352" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(0 0.5 0.5)">
			<stop offset="0" stop-color="#b77af4" stop-opacity="1"/>
			<stop offset="1" stop-color="#773adc" stop-opacity="1"/>
		</linearGradient>
		<linearGradient id="grad0-412" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(-45 0.5 0.5)">
			<stop offset="0" stop-color="#5e9624" stop-opacity="1"/>
			<stop offset="0.55" stop-color="#6dad2a" stop-opacity="1"/>
			<stop offset="1" stop-color="#76bc2d" stop-opacity="1"/>
		</linearGradient>
		<linearGradient id="grad0-426" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(-90.454718861693 0.5 0.5)">
			<stop offset="0" stop-color="#cccccc" stop-opacity="1"/>
			<stop offset="0.12" stop-color="#d7d7d7" stop-opacity="1"/>
			<stop offset="0.42" stop-color="#ebebeb" stop-opacity="1"/>
			<stop offset="0.72" stop-color="#f8f8f8" stop-opacity="1"/>
			<stop offset="1" stop-color="#fcfcfc" stop-opacity="1"/>
		</linearGradient>
		<linearGradient id="grad0-483" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(90 0.5 0.5)">
			<stop offset="0.23" stop-color="#5ea0ef" stop-opacity="1"/>
			<stop offset="0.32" stop-color="#5b9fee" stop-opacity="1"/>
			<stop offset="0.48" stop-color="#509aeb" stop-opacity="1"/>
			<stop offset="0.57" stop-color="#3f92e6" stop-opacity="1"/>
			<stop offset="0.75" stop-color="#2688df" stop-opacity="1"/>
			<stop offset="0.93" stop-color="#127fd9" stop-opacity="1"/>
		</linearGradient>
		<linearGradient id="grad0-487" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(90 0.5 0.5)">
			<stop offset="0.02" stop-color="#5ea0ef" stop-opacity="1"/>
			<stop offset="0.14" stop-color="#5b9fee" stop-opacity="1"/>
			<stop offset="0.23" stop-color="#5b9fee" stop-opacity="1"/>
			<stop offset="0.34" stop-color="#509aeb" stop-opacity="1"/>
			<stop offset="0.44" stop-color="#3f92e6" stop-opacity="1"/>
			<stop offset="0.63" stop-color="#2688df" stop-opacity="1"/>
			<stop offset="0.93" stop-color="#127fd9" stop-opacity="1"/>
		</linearGradient>
		<radialGradient id="grad1-585" cx="1" cy="1" r="1.4">
			<stop offset="0.18" stop-color="#5ea0ef" stop-opacity="1"/>
			<stop offset="0.56" stop-color="#5c9fee" stop-opacity="1"/>
			<stop offset="0.69" stop-color="#559ced" stop-opacity="1"/>
			<stop offset="0.78" stop-color="#4a97e9" stop-opacity="1"/>
			<stop offset="0.86" stop-color="#3990e4" stop-opacity="1"/>
			<stop offset="0.93" stop-color="#2387de" stop-opacity="1"/>
			<stop offset="0.99" stop-color="#087bd6" stop-opacity="1"/>
			<stop offset="1" stop-color="#0078d4" stop-opacity="1"/>
		</radialGradient>
		<radialGradient id="grad7-599" cx="0" cy="0" r="1.4">
			<stop offset="0.19" stop-color="#8c8e90" stop-opacity="1"/>
			<stop offset="0.35" stop-color="#848688" stop-opacity="1"/>
			<stop offset="0.6" stop-color="#6e7071" stop-opacity="1"/>
			<stop offset="0.91" stop-color="#4a4b4c" stop-opacity="1"/>
			<stop offset="1" stop-color="#3e3f3f" stop-opacity="1"/>
		</radialGradient>
		<linearGradient id="grad0-618" x1="0" y1="0" x2="1" y2="0" gradientTransform="rotate(-90 0.5 0.5)">
			<stop offset="0" stop-color="#773adc" stop-opacity="1"/>
			<stop offset="1" stop-color="#b77af4" stop-opacity="1"/>
		</linearGradient>
	</defs>
	<defs id="Markers">
		<g id="lend4">
			<path d="M 2 1 L 0 0 L 2 -1 L 2 1 " style="stroke:none"/>
		</g>
		<marker id="mrkr4-129" class="st13" v:arrowType="4" v:arrowSize="2" v:setback="5.12" refX="5.12" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend4" transform="scale(2.68) "/>
		</marker>
		<marker id="mrkr4-131" class="st13" v:arrowType="4" v:arrowSize="2" v:setback="5.36" refX="-5.36" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend4" transform="scale(-2.68,-2.68) "/>
		</marker>
	</defs>
	<g v:mID="16" v:index="1" v:groupContext="backgroundPage">
		<v:userDefs>
			<v:ud v:nameU="msvThemeOrder" v:val="VT0(0):26"/>
			<v:ud v:nameU="msvVisioCreated" v:prompt="" v:val="VT0(0):26"/>
		</v:userDefs>
		<title>VBackground-1</title>
		<v:pageProperties width="9.625" height="8.125" v:drawingScale="1" v:pageScale="1" v:drawingUnits="19" v:shadowOffsetX="9"
				v:shadowOffsetY="-9"/>
		<g id="shape1000-1" v:mID="1000" v:groupContext="shape">
			<title>Solid.1000</title>
			<v:userDefs>
				<v:ud v:nameU="Background" v:val="VT0(0):26"/>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				<v:ud v:nameU="msvShapeCategories" v:prompt="" v:val="VT4(DoNotContain)"/>
				<v:ud v:nameU="msvVisioCreated" v:prompt="" v:val="VT0(0):26"/>
			</v:userDefs>
			<rect x="0" y="0" width="693" height="585" class="st1"/>
		</g>
	</g>
	<g v:mID="12" v:index="2" v:groupContext="foregroundPage">
		<v:userDefs>
			<v:ud v:nameU="msvThemeOrder" v:val="VT0(0):26"/>
		</v:userDefs>
		<title>secure-baseline-architecture</title>
		<v:pageProperties v:drawingScale="1" v:pageScale="1" v:drawingUnits="19" v:shadowOffsetX="9" v:shadowOffsetY="-9"/>
		<v:layer v:name="Common Icons" v:index="0"/>
		<v:layer v:name="Connector" v:index="1"/>
		<g id="shape4-3" v:mID="4" v:groupContext="shape" transform="translate(199.324,-330.353)">
			<title>Sheet.4</title>
			<desc>Private Link endpoints subnet</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="52.1802" cy="569.25" width="104.37" height="31.5"/>
			<rect x="0" y="553.5" width="104.36" height="31.5" class="st2"/>
			<text x="26.93" y="566.25" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Private Link <tspan
						x="13.67" dy="1.2em" class="st4">endpoints subnet</tspan></text>		</g>
		<g id="shape17-7" v:mID="17" v:groupContext="shape" transform="translate(299.25,-330.353)">
			<title>Sheet.17</title>
			<desc>Ingress resources subnet</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="55.125" cy="569.25" width="110.26" height="31.5"/>
			<rect x="0" y="553.5" width="110.25" height="31.5" class="st2"/>
			<text x="17.04" y="566.25" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Ingress resources <tspan
						x="40.1" dy="1.2em" class="st4">subnet</tspan></text>		</g>
		<g id="shape44-11" v:mID="44" v:groupContext="shape" transform="translate(396.794,-330.353)">
			<title>Sheet.44</title>
			<desc>Azure Application Gateway subnet</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="55.125" cy="569.25" width="110.26" height="31.5"/>
			<rect x="0" y="553.5" width="110.25" height="31.5" class="st2"/>
			<text x="16.04" y="566.25" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Azure Application <tspan
						x="19.87" dy="1.2em" class="st4">Gateway subnet</tspan></text>		</g>
		<g id="shape47-15" v:mID="47" v:groupContext="shape" transform="translate(218.25,-43.875)">
			<title>Sheet.47</title>
			<rect x="0" y="410.474" width="273.375" height="174.526" class="st5"/>
		</g>
		<g id="shape58-17" v:mID="58" v:groupContext="shape" transform="translate(284.963,-218.25)">
			<title>Sheet.58</title>
			<desc>Cluster nodes subnet</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="69.975" cy="572.962" width="139.96" height="24.075"/>
			<rect x="0" y="560.925" width="139.95" height="24.075" class="st2"/>
			<text x="23.46" y="575.96" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Cluster nodes subnet</text>		</g>
		<g id="shape59-20" v:mID="59" v:groupContext="shape" transform="translate(229.5,-148.95)">
			<title>Sheet.59</title>
			<desc>System node pool</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="43.65" cy="572.962" width="87.31" height="24.075"/>
			<rect x="0" y="560.925" width="87.3" height="24.075" class="st2"/>
			<text x="7.76" y="575.66" class="st6" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>System node pool</text>		</g>
		<g id="shape110-23" v:mID="110" v:groupContext="shape" transform="translate(269.1,-192.15)">
			<title>Sheet.110</title>
			<desc>Azure Kubernetes Service (AKS) cluster</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="57.2625" cy="572.962" width="114.53" height="24.075"/>
			<rect x="0" y="560.925" width="114.525" height="24.075" class="st2"/>
			<text x="18.33" y="569.96" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Azure Kubernetes <tspan
						x="12.67" dy="1.2em" class="st4">Service (AKS) cluster</tspan></text>		</g>
		<g id="shape111-27" v:mID="111" v:groupContext="shape" transform="translate(228.375,-51.75)">
			<title>Sheet.111</title>
			<path d="M0 585 L83.25 585 L83.25 466.79 L0 466.79 L0 585 Z" class="st7"/>
		</g>
		<g id="shape112-29" v:mID="112" v:groupContext="shape" transform="translate(318.15,-51.1875)">
			<title>Sheet.112</title>
			<path d="M0 585 L163.91 585 L163.91 466.23 L0 466.23 L0 585 Z" class="st7"/>
		</g>
		<g id="shape113-31" v:mID="113" v:groupContext="shape" transform="translate(314.55,-149.962)">
			<title>Sheet.113</title>
			<desc>User node pool</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="43.65" cy="572.962" width="87.31" height="24.075"/>
			<rect x="0" y="560.925" width="87.3" height="24.075" class="st2"/>
			<text x="13.07" y="575.66" class="st6" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>User node pool</text>		</g>
		<g id="group1006-34" transform="translate(241.989,-106.516)" v:mID="1006" v:groupContext="group">
			<title>Sheet.1006</title>
			<g id="group1007-35" v:mID="1007" v:groupContext="group">
				<title>layer1</title>
				<g id="group1008-36" v:mID="1008" v:groupContext="group">
					<title>g70</title>
					<g id="shape1009-37" v:mID="1009" v:groupContext="shape" transform="translate(0.812106,-0.790028)">
						<title>path3055</title>
						<path d="M14.5 556.61 A1.94497 1.92905 -0 0 0 13.75 556.8 L3.58 561.66 A1.94497 1.92905 -0 0 0 2.53 562.97
									 L0.02 573.89 A1.94497 1.92905 -0 0 0 0.28 575.37 A1.94497 1.92905 -0 0 0 0.39 575.52 L7.43 584.27
									 A1.94497 1.92905 -0 0 0 8.95 585 L20.24 585 A1.94497 1.92905 -0 0 0 21.76 584.27 L28.8 575.52
									 A1.94497 1.92905 -0 0 0 29.18 573.89 L26.66 562.97 A1.94497 1.92905 -0 0 0 25.61 561.66 L15.44
									 556.8 A1.94497 1.92905 -0 0 0 14.5 556.61 Z" class="st8"/>
					</g>
					<g id="shape1010-40" v:mID="1010" v:groupContext="shape">
						<title>path3054-2-9</title>
						<path d="M15.3 555.03 A2.05325 2.03644 -0 0 0 14.52 555.23 L3.78 560.36 A2.05325 2.03644 -0 0 0 2.67 561.75
									 L0.02 573.27 A2.05325 2.03644 -0 0 0 0.3 574.83 A2.05325 2.03644 -0 0 0 0.42 574.99 L7.85 584.23
									 A2.05325 2.03644 -0 0 0 9.45 585 L21.37 585 A2.05325 2.03644 -0 0 0 22.98 584.23 L30.4 574.99
									 A2.05325 2.03644 -0 0 0 30.8 573.27 L28.15 561.74 A2.05325 2.03644 -0 0 0 27.04 560.36 L16.3
									 555.23 A2.05325 2.03644 -0 0 0 15.3 555.03 ZM15.31 555.83 A1.94497 1.92905 180 0 1 16.25 556.01
									 L26.42 560.87 A1.94497 1.92905 180 0 1 27.48 562.18 L29.99 573.1 A1.94497 1.92905 180 0 1 29.61
									 574.73 L22.58 583.48 A1.94497 1.92905 180 0 1 21.06 584.21 L9.77 584.21 A1.94497 1.92905 180
									 0 1 8.25 583.48 L1.21 574.73 A1.94497 1.92905 180 0 1 1.09 574.58 A1.94497 1.92905 180 0 1 0.83
									 573.1 L3.34 562.18 A1.94497 1.92905 180 0 1 4.39 560.87 L14.56 556.01 A1.94497 1.92905 180 0
									 1 15.31 555.83 L15.31 555.83 Z" class="st9"/>
					</g>
				</g>
				<g id="group1011-43" transform="translate(9.2104,-8.84197)" v:mID="1011" v:groupContext="group">
					<title>g3341</title>
					<g id="shape1012-44" v:mID="1012" v:groupContext="shape" transform="translate(0,-8.68824)">
						<title>path910</title>
						<path d="M0 583.2 L6.2 581.4 L12.4 583.2 L6.2 585 L0 583.2 Z" class="st10"/>
					</g>
					<g id="shape1013-46" v:mID="1013" v:groupContext="shape">
						<title>path912</title>
						<path d="M0 575.2 L0 581.8 L5.78 585 L5.81 576.93 L0 575.2 Z" class="st10"/>
					</g>
					<g id="shape1014-48" v:mID="1014" v:groupContext="shape" transform="translate(6.59521,0)">
						<title>path914</title>
						<path d="M5.81 575.2 L5.81 581.8 L0.03 585 L0 576.93 L5.81 575.2 Z" class="st10"/>
					</g>
				</g>
			</g>
		</g>
		<g id="group1015-50" transform="translate(266.4,-99)" v:mID="1015" v:groupContext="group">
			<title>Sheet.1015</title>
			<g id="group1016-51" v:mID="1016" v:groupContext="group">
				<title>layer1</title>
				<g id="group1017-52" v:mID="1017" v:groupContext="group">
					<title>g70</title>
					<g id="shape1018-53" v:mID="1018" v:groupContext="shape" transform="translate(0.812106,-0.790028)">
						<title>path3055</title>
						<path d="M14.5 556.61 A1.94497 1.92905 -0 0 0 13.75 556.8 L3.58 561.66 A1.94497 1.92905 -0 0 0 2.53 562.97
									 L0.02 573.89 A1.94497 1.92905 -0 0 0 0.28 575.37 A1.94497 1.92905 -0 0 0 0.39 575.52 L7.43 584.27
									 A1.94497 1.92905 -0 0 0 8.95 585 L20.24 585 A1.94497 1.92905 -0 0 0 21.76 584.27 L28.8 575.52
									 A1.94497 1.92905 -0 0 0 29.18 573.89 L26.66 562.97 A1.94497 1.92905 -0 0 0 25.61 561.66 L15.44
									 556.8 A1.94497 1.92905 -0 0 0 14.5 556.61 Z" class="st8"/>
					</g>
					<g id="shape1019-56" v:mID="1019" v:groupContext="shape">
						<title>path3054-2-9</title>
						<path d="M15.3 555.03 A2.05325 2.03644 -0 0 0 14.52 555.23 L3.78 560.36 A2.05325 2.03644 -0 0 0 2.67 561.75
									 L0.02 573.27 A2.05325 2.03644 -0 0 0 0.3 574.83 A2.05325 2.03644 -0 0 0 0.42 574.99 L7.85 584.23
									 A2.05325 2.03644 -0 0 0 9.45 585 L21.37 585 A2.05325 2.03644 -0 0 0 22.98 584.23 L30.4 574.99
									 A2.05325 2.03644 -0 0 0 30.8 573.27 L28.15 561.74 A2.05325 2.03644 -0 0 0 27.04 560.36 L16.3
									 555.23 A2.05325 2.03644 -0 0 0 15.3 555.03 ZM15.31 555.83 A1.94497 1.92905 180 0 1 16.25 556.01
									 L26.42 560.87 A1.94497 1.92905 180 0 1 27.48 562.18 L29.99 573.1 A1.94497 1.92905 180 0 1 29.61
									 574.73 L22.58 583.48 A1.94497 1.92905 180 0 1 21.06 584.21 L9.77 584.21 A1.94497 1.92905 180
									 0 1 8.25 583.48 L1.21 574.73 A1.94497 1.92905 180 0 1 1.09 574.58 A1.94497 1.92905 180 0 1 0.83
									 573.1 L3.34 562.18 A1.94497 1.92905 180 0 1 4.39 560.87 L14.56 556.01 A1.94497 1.92905 180 0
									 1 15.31 555.83 L15.31 555.83 Z" class="st9"/>
					</g>
				</g>
				<g id="group1020-59" transform="translate(9.2104,-8.84197)" v:mID="1020" v:groupContext="group">
					<title>g3341</title>
					<g id="shape1021-60" v:mID="1021" v:groupContext="shape" transform="translate(0,-8.68824)">
						<title>path910</title>
						<path d="M0 583.2 L6.2 581.4 L12.4 583.2 L6.2 585 L0 583.2 Z" class="st10"/>
					</g>
					<g id="shape1022-62" v:mID="1022" v:groupContext="shape">
						<title>path912</title>
						<path d="M0 575.2 L0 581.8 L5.78 585 L5.81 576.93 L0 575.2 Z" class="st10"/>
					</g>
					<g id="shape1023-64" v:mID="1023" v:groupContext="shape" transform="translate(6.59521,0)">
						<title>path914</title>
						<path d="M5.81 575.2 L5.81 581.8 L0.03 585 L0 576.93 L5.81 575.2 Z" class="st10"/>
					</g>
				</g>
			</g>
		</g>
		<g id="group1024-66" transform="translate(434.7,-117)" v:mID="1024" v:groupContext="group">
			<title>Sheet.1024</title>
			<g id="group1025-67" v:mID="1025" v:groupContext="group">
				<title>layer1</title>
				<g id="group1026-68" v:mID="1026" v:groupContext="group">
					<title>g70</title>
					<g id="shape1027-69" v:mID="1027" v:groupContext="shape" transform="translate(0.812106,-0.790028)">
						<title>path3055</title>
						<path d="M14.5 556.61 A1.94497 1.92905 -0 0 0 13.75 556.8 L3.58 561.66 A1.94497 1.92905 -0 0 0 2.53 562.97
									 L0.02 573.89 A1.94497 1.92905 -0 0 0 0.28 575.37 A1.94497 1.92905 -0 0 0 0.39 575.52 L7.43 584.27
									 A1.94497 1.92905 -0 0 0 8.95 585 L20.24 585 A1.94497 1.92905 -0 0 0 21.76 584.27 L28.8 575.52
									 A1.94497 1.92905 -0 0 0 29.18 573.89 L26.66 562.97 A1.94497 1.92905 -0 0 0 25.61 561.66 L15.44
									 556.8 A1.94497 1.92905 -0 0 0 14.5 556.61 Z" class="st8"/>
					</g>
					<g id="shape1028-72" v:mID="1028" v:groupContext="shape">
						<title>path3054-2-9</title>
						<path d="M15.3 555.03 A2.05325 2.03644 -0 0 0 14.52 555.23 L3.78 560.36 A2.05325 2.03644 -0 0 0 2.67 561.75
									 L0.02 573.27 A2.05325 2.03644 -0 0 0 0.3 574.83 A2.05325 2.03644 -0 0 0 0.42 574.99 L7.85 584.23
									 A2.05325 2.03644 -0 0 0 9.45 585 L21.37 585 A2.05325 2.03644 -0 0 0 22.98 584.23 L30.4 574.99
									 A2.05325 2.03644 -0 0 0 30.8 573.27 L28.15 561.74 A2.05325 2.03644 -0 0 0 27.04 560.36 L16.3
									 555.23 A2.05325 2.03644 -0 0 0 15.3 555.03 ZM15.31 555.83 A1.94497 1.92905 180 0 1 16.25 556.01
									 L26.42 560.87 A1.94497 1.92905 180 0 1 27.48 562.18 L29.99 573.1 A1.94497 1.92905 180 0 1 29.61
									 574.73 L22.58 583.48 A1.94497 1.92905 180 0 1 21.06 584.21 L9.77 584.21 A1.94497 1.92905 180
									 0 1 8.25 583.48 L1.21 574.73 A1.94497 1.92905 180 0 1 1.09 574.58 A1.94497 1.92905 180 0 1 0.83
									 573.1 L3.34 562.18 A1.94497 1.92905 180 0 1 4.39 560.87 L14.56 556.01 A1.94497 1.92905 180 0
									 1 15.31 555.83 L15.31 555.83 Z" class="st9"/>
					</g>
				</g>
				<g id="group1029-75" transform="translate(9.2104,-8.84197)" v:mID="1029" v:groupContext="group">
					<title>g3341</title>
					<g id="shape1030-76" v:mID="1030" v:groupContext="shape" transform="translate(0,-8.68824)">
						<title>path910</title>
						<path d="M0 583.2 L6.2 581.4 L12.4 583.2 L6.2 585 L0 583.2 Z" class="st10"/>
					</g>
					<g id="shape1031-78" v:mID="1031" v:groupContext="shape">
						<title>path912</title>
						<path d="M0 575.2 L0 581.8 L5.78 585 L5.81 576.93 L0 575.2 Z" class="st10"/>
					</g>
					<g id="shape1032-80" v:mID="1032" v:groupContext="shape" transform="translate(6.59521,0)">
						<title>path914</title>
						<path d="M5.81 575.2 L5.81 581.8 L0.03 585 L0 576.93 L5.81 575.2 Z" class="st10"/>
					</g>
				</g>
			</g>
		</g>
		<g id="shape1033-82" v:mID="1033" v:groupContext="shape" transform="translate(361.324,-117.529)">
			<title>Sheet.1033</title>
			<desc>Traefik ingress controller</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="35.3382" cy="572.94" width="70.68" height="24.12"/>
			<rect x="0" y="560.88" width="70.6765" height="24.12" class="st2"/>
			<text x="9.57" y="570.24" class="st6" v:langID="1033"><v:paragraph v:horizAlign="2"/><v:tabList/>Traefik ingress <tspan
						x="28.51" dy="1.2em" class="st4">controller</tspan></text>		</g>
		<g id="group1034-86" transform="translate(436.05,-81)" v:mID="1034" v:groupContext="group">
			<title>Sheet.1034</title>
			<g id="group1035-87" v:mID="1035" v:groupContext="group">
				<title>layer1</title>
				<g id="group1036-88" v:mID="1036" v:groupContext="group">
					<title>g70</title>
					<g id="shape1037-89" v:mID="1037" v:groupContext="shape" transform="translate(0.812106,-0.790028)">
						<title>path3055</title>
						<path d="M14.5 556.61 A1.94497 1.92905 -0 0 0 13.75 556.8 L3.58 561.66 A1.94497 1.92905 -0 0 0 2.53 562.97
									 L0.02 573.89 A1.94497 1.92905 -0 0 0 0.28 575.37 A1.94497 1.92905 -0 0 0 0.39 575.52 L7.43 584.27
									 A1.94497 1.92905 -0 0 0 8.95 585 L20.24 585 A1.94497 1.92905 -0 0 0 21.76 584.27 L28.8 575.52
									 A1.94497 1.92905 -0 0 0 29.18 573.89 L26.66 562.97 A1.94497 1.92905 -0 0 0 25.61 561.66 L15.44
									 556.8 A1.94497 1.92905 -0 0 0 14.5 556.61 Z" class="st8"/>
					</g>
					<g id="shape1038-92" v:mID="1038" v:groupContext="shape">
						<title>path3054-2-9</title>
						<path d="M15.3 555.03 A2.05325 2.03644 -0 0 0 14.52 555.23 L3.78 560.36 A2.05325 2.03644 -0 0 0 2.67 561.75
									 L0.02 573.27 A2.05325 2.03644 -0 0 0 0.3 574.83 A2.05325 2.03644 -0 0 0 0.42 574.99 L7.85 584.23
									 A2.05325 2.03644 -0 0 0 9.45 585 L21.37 585 A2.05325 2.03644 -0 0 0 22.98 584.23 L30.4 574.99
									 A2.05325 2.03644 -0 0 0 30.8 573.27 L28.15 561.74 A2.05325 2.03644 -0 0 0 27.04 560.36 L16.3
									 555.23 A2.05325 2.03644 -0 0 0 15.3 555.03 ZM15.31 555.83 A1.94497 1.92905 180 0 1 16.25 556.01
									 L26.42 560.87 A1.94497 1.92905 180 0 1 27.48 562.18 L29.99 573.1 A1.94497 1.92905 180 0 1 29.61
									 574.73 L22.58 583.48 A1.94497 1.92905 180 0 1 21.06 584.21 L9.77 584.21 A1.94497 1.92905 180
									 0 1 8.25 583.48 L1.21 574.73 A1.94497 1.92905 180 0 1 1.09 574.58 A1.94497 1.92905 180 0 1 0.83
									 573.1 L3.34 562.18 A1.94497 1.92905 180 0 1 4.39 560.87 L14.56 556.01 A1.94497 1.92905 180 0
									 1 15.31 555.83 L15.31 555.83 Z" class="st9"/>
					</g>
				</g>
				<g id="group1039-95" transform="translate(9.2104,-8.84197)" v:mID="1039" v:groupContext="group">
					<title>g3341</title>
					<g id="shape1040-96" v:mID="1040" v:groupContext="shape" transform="translate(0,-8.68824)">
						<title>path910</title>
						<path d="M0 583.2 L6.2 581.4 L12.4 583.2 L6.2 585 L0 583.2 Z" class="st10"/>
					</g>
					<g id="shape1041-98" v:mID="1041" v:groupContext="shape">
						<title>path912</title>
						<path d="M0 575.2 L0 581.8 L5.78 585 L5.81 576.93 L0 575.2 Z" class="st10"/>
					</g>
					<g id="shape1042-100" v:mID="1042" v:groupContext="shape" transform="translate(6.59521,0)">
						<title>path914</title>
						<path d="M5.81 575.2 L5.81 581.8 L0.03 585 L0 576.93 L5.81 575.2 Z" class="st10"/>
					</g>
				</g>
			</g>
		</g>
		<g id="shape1043-102" v:mID="1043" v:groupContext="shape" transform="translate(371.25,-81)">
			<title>Sheet.1043</title>
			<desc>Workload</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="30.375" cy="572.94" width="60.76" height="24.12"/>
			<rect x="0" y="560.88" width="60.75" height="24.12" class="st2"/>
			<text x="18.14" y="575.64" class="st6" v:langID="1033"><v:paragraph v:horizAlign="2"/><v:tabList/>Workload</text>		</g>
		<g id="shape1077-105" v:mID="1077" v:groupContext="shape" transform="translate(30.9706,-508.765)">
			<title>Sheet.1077</title>
			<desc>Azure Firewall subnet (Outbound)</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="55.125" cy="569.25" width="110.26" height="31.5"/>
			<rect x="0" y="553.5" width="110.25" height="31.5" class="st2"/>
			<text x="8.1" y="566.25" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Azure Firewall subnet<v:newlineChar/><tspan
						x="29.34" dy="1.2em" class="st4">(Outbound)</tspan></text>		</g>
		<g id="shape1082-109" v:mID="1082" v:groupContext="shape" transform="translate(135.45,-509.85)">
			<title>Sheet.1082</title>
			<desc>Azure Bastion subnet (Management)</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="55.125" cy="569.25" width="110.26" height="31.5"/>
			<rect x="0" y="553.5" width="110.25" height="31.5" class="st2"/>
			<text x="8.49" y="566.25" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Azure Bastion subnet<v:newlineChar/><tspan
						x="22.7" dy="1.2em" class="st4">(Management) </tspan> </text>		</g>
		<g id="shape1085-113" v:mID="1085" v:groupContext="shape" transform="translate(234.9,-509.4)">
			<title>Sheet.1085</title>
			<desc>Gateway subnet (To on-premises)</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="55.125" cy="569.25" width="110.26" height="31.5"/>
			<rect x="0" y="553.5" width="110.25" height="31.5" class="st2"/>
			<text x="19.87" y="566.25" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Gateway subnet<v:newlineChar/><tspan
						x="17.76" dy="1.2em" class="st4">(To on</tspan>-premises)</text>		</g>
		<g id="shape1133-117" v:mID="1133" v:groupContext="shape" transform="translate(380.382,-369.529)">
			<title>Sheet.1133</title>
			<desc>Spoke virtual network</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="53.3713" cy="577.406" width="106.75" height="15.1875"/>
			<rect x="0" y="569.812" width="106.743" height="15.1875" class="st2"/>
			<text x="8.63" y="580.11" class="st11" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Spoke virtual network</text>		</g>
		<g id="shape1122-120" v:mID="1122" v:groupContext="shape" transform="translate(218.382,-545.824)">
			<title>Sheet.1122</title>
			<desc>Hub virtual network</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="47.5312" cy="577.406" width="95.07" height="15.1875"/>
			<rect x="0" y="569.812" width="95.0625" height="15.1875" class="st2"/>
			<text x="6.75" y="580.11" class="st11" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Hub virtual network</text>		</g>
		<g id="shape1145-123" v:mID="1145" v:groupContext="shape" transform="translate(850.5,168.882) rotate(90)">
			<title>Sheet.1145</title>
			<path d="M7.68 585 L8.04 585 L37.49 585" class="st12"/>
		</g>
		<g id="shape1151-132" v:mID="1151" v:groupContext="shape" transform="translate(565.5,880.875) rotate(180)">
			<title>Sheet.1151</title>
			<path d="M0 585 L68.02 585" class="st14"/>
		</g>
		<g id="shape1157-137" v:mID="1157" v:groupContext="shape" transform="translate(477.45,-520.517)">
			<title>Sheet.1157</title>
			<desc>On-premises network</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="36.9" cy="573.666" width="73.8" height="22.6687"/>
			<rect x="0" y="562.331" width="73.8" height="22.6687" class="st2"/>
			<text x="8.63" y="570.67" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>On-premises<v:newlineChar/><tspan
						x="18.99" dy="1.2em" class="st4">network</tspan></text>		</g>
		<g id="shape1159-141" v:mID="1159" v:groupContext="shape" transform="translate(486.794,686.25) rotate(180)">
			<title>Sheet.1159</title>
			<path d="M0 585 L154.49 585" class="st14"/>
		</g>
		<g id="shape1154-146" v:mID="1154" v:groupContext="shape" transform="translate(486.79,-449.55)">
			<title>Sheet.1154</title>
			<rect x="0" y="514.248" width="58.1657" height="70.7522" class="st15"/>
		</g>
		<g id="group1253-148" transform="translate(562.5,-371.7)" v:mID="1253" v:groupContext="group">
			<title>Sheet.1253</title>
			<g id="group1251-149" transform="translate(8.1,0)" v:mID="1251" v:groupContext="group">
				<title>Sheet.1251</title>
				<g id="group1248-150" v:mID="1248" v:groupContext="group">
					<title>Sheet.1248</title>
					<g id="shape1247-151" v:mID="1247" v:groupContext="shape">
						<title>Sheet.1247</title>
						<rect x="0" y="514.248" width="58.1657" height="70.7522" class="st15"/>
					</g>
					<g id="shape1245-153" v:mID="1245" v:groupContext="shape" transform="translate(11.303,-14.6761)">
						<title>On-premises #1.1245</title>
						<path d="M7.47 561.22 L4.95 561.22 L4.95 558.75 L7.47 558.75 L7.47 561.22 L7.47 561.22 ZM12.27 561.22 L14.8
									 561.22 L14.8 558.75 L12.27 558.75 L12.27 561.22 L12.27 561.22 ZM7.47 565.89 L4.95 565.89 L4.95
									 568.37 L7.47 568.37 L7.47 565.89 L7.47 565.89 ZM12.27 568.37 L14.8 568.37 L14.8 565.89 L12.27
									 565.89 L12.27 568.37 L12.27 568.37 ZM7.47 551.69 L4.95 551.69 L4.95 554.08 L7.47 554.08 L7.47
									 551.69 L7.47 551.69 ZM12.27 554.08 L14.8 554.08 L14.8 551.69 L12.27 551.69 L12.27 554.08 L12.27
									 554.08 ZM19.7 554.08 L22.12 554.08 L22.12 551.69 L19.7 551.69 L19.7 554.08 L19.7 554.08 ZM7.47
									 573.04 L4.95 573.04 L4.95 575.38 L7.47 575.38 L7.47 573.04 L7.47 573.04 ZM12.27 575.38 L14.8
									 575.38 L14.8 573.04 L12.27 573.04 L12.27 575.38 L12.27 575.38 ZM35.84 558.75 L35.84 585 L27.02
									 585 L18.72 585 L0 585 L0 546.88 L27.02 546.88 L27.02 558.75 L35.84 558.75 L35.84 558.75 ZM18.72
									 582.57 L18.72 558.75 L24.64 558.75 L24.64 549.26 L2.57 549.26 L2.57 582.57 L18.72 582.57 L18.72
									 582.57 ZM33.7 560.85 L20.86 560.85 L20.86 582.94 L33.7 582.94 L33.7 560.85 L33.7 560.85 ZM25.16
									 568.88 L23.01 568.88 L23.01 570.94 L25.16 570.94 L25.16 568.88 L25.16 568.88 ZM31.32 568.88
									 L29.45 568.88 L29.45 570.94 L31.32 570.94 L31.32 568.88 L31.32 568.88 ZM25.16 574.91 L23.01
									 574.91 L23.01 577.01 L25.16 577.01 L25.16 574.91 L25.16 574.91 ZM31.32 574.91 L29.45 574.91
									 L29.45 577.01 L31.32 577.01 L31.32 574.91 L31.32 574.91 ZM25.16 562.67 L23.01 562.67 L23.01
									 564.77 L25.16 564.77 L25.16 562.67 L25.16 562.67 ZM31.32 562.67 L29.45 562.67 L29.45 564.77
									 L31.32 564.77 L31.32 562.67 L31.32 562.67 Z" class="st16"/>
					</g>
				</g>
				<g id="group1249-155" transform="translate(22.553,-4.32609)" v:mID="1249" v:groupContext="group">
					<title>Sheet.1249</title>
					<g id="shape1250-156" v:mID="1250" v:groupContext="shape">
						<title>Sheet.1250</title>
						<path d="M29.3 578.42 C29.25 576.86 28.65 575.37 27.61 574.21 C26.56 573.05 25.13 572.3 23.59 572.09 C23.49
									 569.91 22.54 567.86 20.94 566.37 C19.34 564.89 17.22 564.09 15.04 564.16 C13.28 564.13 11.55
									 564.64 10.09 565.64 C8.63 566.63 7.52 568.05 6.91 569.7 C5.04 569.93 3.32 570.82 2.05 572.21
									 C0.78 573.6 0.06 575.39 0 577.27 C0.08 579.38 1 581.37 2.55 582.8 C4.09 584.24 6.14 585 8.25
									 584.92 C8.5 584.92 8.75 584.91 8.98 584.89 L22.34 584.89 C22.46 584.89 22.58 584.87 22.69 584.84
									 C24.41 584.83 26.05 584.15 27.28 582.96 C28.52 581.76 29.24 580.14 29.3 578.42 Z" class="st17"/>
					</g>
				</g>
			</g>
			<g id="shape1252-160" v:mID="1252" v:groupContext="shape" transform="translate(-8.99997,-72.3656)">
				<title>Sheet.1252</title>
				<desc>Spoke (Remote office)</desc>
				<v:textBlock v:margins="rect(4,4,4,4)"/>
				<v:textRect cx="43.3125" cy="573.666" width="86.63" height="22.6687"/>
				<rect x="0" y="562.331" width="86.625" height="22.6687" class="st2"/>
				<text x="29.69" y="570.67" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Spoke<v:newlineChar/><tspan
							x="9.58" dy="1.2em" class="st4">(Remote office)</tspan></text>			</g>
		</g>
		<g id="group1265-164" transform="translate(112.5,-309.6)" v:mID="1265" v:groupContext="group">
			<title>Key Vaults.1054</title>
			<desc>Azure Key Vault</desc>
			<g id="group1266-165" v:mID="1266" v:groupContext="group">
				<v:userDefs>
					<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
				</v:userDefs>
				<title>Sheet.1266</title>
				<g id="shape1267-166" v:mID="1267" v:groupContext="shape">
					<title>Sheet.1267</title>
					<v:userDefs>
						<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
					</v:userDefs>
					<path d="M17.28 550.44 C7.74 550.44 0 558.18 0 567.72 C0 577.26 7.74 585 17.28 585 C26.82 585 34.56 577.26 34.56
								 567.72 C34.55 558.18 26.82 550.45 17.28 550.44 ZM17.28 582.64 C9.04 582.64 2.36 575.96 2.36 567.72
								 C2.36 559.48 9.04 552.8 17.28 552.8 C25.52 552.8 32.2 559.48 32.2 567.72 C32.2 575.96 25.52 582.64
								 17.28 582.64 Z" class="st18"/>
				</g>
				<g id="shape1268-170" v:mID="1268" v:groupContext="shape" transform="translate(2.35821,-2.35821)">
					<title>Sheet.1268</title>
					<v:userDefs>
						<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
					</v:userDefs>
					<ellipse cx="14.9218" cy="570.078" rx="14.9218" ry="14.9218" class="st19"/>
				</g>
				<g id="shape1269-172" v:mID="1269" v:groupContext="shape" transform="translate(6.81035,-0.939219)">
					<title>Sheet.1269</title>
					<v:userDefs>
						<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
					</v:userDefs>
					<path d="M19.49 565.26 C20.94 563.81 20.94 561.46 19.49 560 L13.09 553.57 C11.64 552.13 9.3 552.13 7.85 553.57
								 L1.44 560 C0 561.46 0 563.81 1.44 565.26 L6.77 570.69 C6.96 570.89 7.07 571.15 7.07 571.42 L7.07
								 581.34 C7.07 581.68 7.21 582 7.44 582.24 L9.88 584.67 C10.21 585 10.73 585 11.06 584.68 C11.06 584.67
								 11.06 584.67 11.06 584.67 L13.42 582.32 L14.8 580.93 C14.98 580.74 14.98 580.44 14.8 580.24 L13.81
								 579.25 C13.6 579.04 13.6 578.7 13.81 578.49 L14.8 577.5 C14.98 577.3 14.98 577 14.8 576.81 L13.81
								 575.81 C13.6 575.6 13.6 575.27 13.81 575.06 L14.8 574.07 C14.98 573.87 14.98 573.57 14.8 573.38
								 L13.42 571.97 L13.42 571.46 L19.49 565.26 ZM10.47 555.14 C11.63 555.14 12.57 556.08 12.57 557.24
								 C12.57 558.41 11.63 559.35 10.47 559.35 C9.31 559.35 8.37 558.41 8.37 557.24 C8.36 556.08 9.31 555.14
								 10.47 555.14 Z" class="st20"/>
				</g>
				<g id="shape1270-176" v:mID="1270" v:groupContext="shape" transform="translate(15.4626,-4.30374)">
					<title>Sheet.1270</title>
					<v:userDefs>
						<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
					</v:userDefs>
					<path d="M0.15 584.83 C0.35 585 0.64 584.98 0.81 584.78 C0.88 584.7 0.92 584.6 0.92 584.49 L0.92 576.35 C0.92
								 576.19 0.84 576.04 0.7 575.95 C0.49 575.82 0.22 575.87 0.08 576.08 C0.03 576.16 0 576.26 0.01 576.35
								 L0.01 584.49 C0.01 584.61 0.07 584.74 0.15 584.83 Z" class="st21"/>
				</g>
				<g id="shape1271-178" v:mID="1271" v:groupContext="shape" transform="translate(12.155,-22.5656)">
					<title>Sheet.1271</title>
					<v:userDefs>
						<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
					</v:userDefs>
					<path d="M0.57 583.76 L9.94 583.76 C10.25 583.76 10.51 584.01 10.51 584.33 L10.51 584.43 C10.51 584.74 10.25
								 585 9.94 585 L0.57 585 C0.26 585 0 584.75 0 584.43 L0 584.33 C0 584.02 0.26 583.76 0.57 583.76 Z"
							class="st21"/>
				</g>
				<g id="shape1272-180" v:mID="1272" v:groupContext="shape" transform="translate(12.155,-20.553)">
					<title>Sheet.1272</title>
					<v:userDefs>
						<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
					</v:userDefs>
					<path d="M0.57 583.76 L9.94 583.76 C10.25 583.76 10.51 584.01 10.51 584.33 L10.51 584.43 C10.51 584.75 10.25
								 585 9.94 585 L0.57 585 C0.26 585 0 584.75 0 584.43 L0 584.33 C0 584.01 0.26 583.76 0.57 583.76 Z"
							class="st21"/>
				</g>
			</g>
			<g id="shape1265-182" v:mID="1265" v:groupContext="groupContent">
				<v:textBlock v:margins="rect(0,0,0,0)"/>
				<v:textRect cx="17.28" cy="591.002" width="72" height="12.0036"/>
				<text x="-17.45" y="594" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Azure Key Vault</text>			</g>
		</g>
		<g id="group1273-184" transform="translate(111.15,-251.1)" v:mID="1273" v:groupContext="group">
			<title>Container Registries.1147</title>
			<desc>Azure Container Registry</desc>
			<g id="shape1274-185" v:mID="1274" v:groupContext="shape" transform="translate(0,-6.26182)">
				<title>Sheet.1274</title>
				<v:userDefs>
					<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
				</v:userDefs>
				<path d="M32.46 570.84 A8.38466 9.14182 -180 0 0 28.02 568.4 A9.82546 10.7127 -180 0 0 17.81 558.14 A10.1456 11.0618
							 -180 0 0 8.12 565.32 A9.24514 10.08 -180 0 0 0 575.09 A9.46526 10.32 -180 0 0 9.79 585 L26.91 585 A8.00445
							 8.72727 -180 0 0 34.76 576.69 A7.34408 8.00727 -180 0 0 32.48 570.95 L32.46 570.84 Z" class="st22"/>
				<path d="M32.46 570.84 A8.38466 9.14182 -180 0 0 28.02 568.4 A9.82546 10.7127 -180 0 0 17.81 558.14 A10.1456 11.0618
							 -180 0 0 8.12 565.32 A9.24514 10.08 -180 0 0 0 575.09 A9.46526 10.32 -180 0 0 9.79 585 L26.91 585 A8.00445
							 8.72727 -180 0 0 34.76 576.69 A7.34408 8.00727 -180 0 0 32.48 570.95" class="st23"/>
			</g>
			<g id="shape1275-190" v:mID="1275" v:groupContext="shape" transform="translate(20.1512,-10.08)">
				<title>Sheet.1275</title>
				<v:userDefs>
					<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
				</v:userDefs>
				<path d="M0 572 L0.02 581.33 L15.85 585 L15.85 578.5 L0 572 Z" class="st24"/>
			</g>
			<g id="shape1276-192" v:mID="1276" v:groupContext="shape" transform="translate(15.8688,-11.5636)">
				<title>Sheet.1276</title>
				<v:userDefs>
					<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
				</v:userDefs>
				<path d="M4.28 573.48 L0 576.32 L0 585 L4.3 582.82 L4.28 573.48 Z" class="st25"/>
				<path d="M4.28 573.48 L0 576.32 L0 585 L4.3 582.82" class="st23"/>
			</g>
			<g id="shape1277-195" v:mID="1277" v:groupContext="shape" transform="translate(21.632,-11.4982)">
				<title>Sheet.1277</title>
				<v:userDefs>
					<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
				</v:userDefs>
				<path d="M5.74 582.88 L7.18 583.32 L7.18 578.24 L5.74 577.69 L5.74 582.88 ZM4.3 577.19 L2.86 576.62 L2.86 582.05
							 L4.3 582.49 L4.3 577.19 ZM8.62 583.73 L10.03 584.15 L10.07 579.31 L8.62 578.78 L8.62 583.73 ZM0 581.25
							 L1.44 581.64 L1.44 576.12 L0 575.6 L0 581.25 ZM12.93 580.37 L11.49 579.83 L11.49 584.56 L12.93 585 L12.93
							 580.37 Z" class="st26"/>
			</g>
			<g id="shape1278-197" v:mID="1278" v:groupContext="shape" transform="translate(16.6092,-13.44)">
				<title>Sheet.1278</title>
				<v:userDefs>
					<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
				</v:userDefs>
				<path d="M0.72 584.54 L0 585 L0 578.83 L0.72 578.41 L0.72 584.54 ZM2.14 577.52 L1.4 578.04 L1.4 584.04 L2.14 583.6
							 L2.14 577.52 Z" class="st27"/>
			</g>
			<g id="shape1279-199" v:mID="1279" v:groupContext="shape" transform="translate(15.8688,-8.46545)">
				<title>Sheet.1279</title>
				<v:userDefs>
					<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
				</v:userDefs>
				<path d="M20.13 583.39 L15.81 585 L0 581.9 L4.3 579.72 L20.13 583.39 Z" class="st28"/>
			</g>
			<g id="shape1280-203" v:mID="1280" v:groupContext="shape" transform="translate(20.0912,-0.130909)">
				<title>Sheet.1280</title>
				<v:userDefs>
					<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
				</v:userDefs>
				<path d="M15.91 581.71 L0 585 L0.08 572.8 L15.91 576.01 L15.91 581.71 Z" class="st24"/>
			</g>
			<g id="shape1281-205" v:mID="1281" v:groupContext="shape" transform="translate(21.632,-2.13818)">
				<title>Sheet.1281</title>
				<v:userDefs>
					<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
				</v:userDefs>
				<path d="M0 585 L0 577.25 L1.44 577.45 L1.44 584.74 L0 585 ZM4.3 584.21 L2.86 584.5 L2.86 577.65 L4.3 577.89 L4.3
							 584.21 ZM5.74 583.97 L5.74 578.04 L7.18 578.24 L7.18 583.69 L5.74 583.97 ZM10.05 583.15 L8.62 583.43
							 L8.62 578.43 L10.05 578.67 L10.05 583.15 ZM12.97 582.64 L11.49 582.93 L11.49 578.83 L12.87 579.02 L12.97
							 582.64 Z" class="st26"/>
			</g>
			<g id="shape1282-207" v:mID="1282" v:groupContext="shape" transform="translate(15.8688,0)">
				<title>Sheet.1282</title>
				<v:userDefs>
					<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
				</v:userDefs>
				<path d="M0 582.08 L0 574.96 L4.32 572.78 L4.32 585 L0 582.08 Z" class="st29"/>
			</g>
			<g id="shape1283-209" v:mID="1283" v:groupContext="shape" transform="translate(16.5892,-2.05091)">
				<title>Sheet.1283</title>
				<v:userDefs>
					<v:ud v:nameU="msvDisableCustomConnectionPoints" v:prompt="" v:val="VT0(1):5"/>
				</v:userDefs>
				<path d="M0.64 584.11 L0 583.76 L0 577.73 L0.64 577.41 L0.64 584.11 ZM2.18 576.58 L1.42 576.99 L1.42 584.59 L2.16
							 585 L2.16 576.58 L2.18 576.58 Z" class="st27"/>
			</g>
			<g id="shape1273-211" v:mID="1273" v:groupContext="groupContent">
				<v:textBlock v:margins="rect(0,0,0,0)"/>
				<v:textRect cx="18" cy="597.002" width="72" height="24.0037"/>
				<text x="-17.53" y="594" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Azure Container <tspan
							x="0.27" dy="1.2em" class="st4">Registry</tspan></text>			</g>
		</g>
		<g id="shape1284-214" v:mID="1284" v:groupContext="shape" transform="translate(392.77,-275.103) rotate(24.2446)">
			<title>Sheet.1284</title>
			<path d="M0 585 L61.55 585" class="st14"/>
		</g>
		<g id="shape1285-219" v:mID="1285" v:groupContext="shape" transform="translate(-52.3468,-230.862) rotate(-20.4556)">
			<title>Sheet.1285</title>
			<path d="M0 585 L59.56 585" class="st14"/>
		</g>
		<g id="group1299-224" transform="translate(501.618,-466.412)" v:mID="1299" v:groupContext="group">
			<title>Building.1111</title>
			<g id="shape1300-225" v:mID="1300" v:groupContext="shape" transform="translate(0,-0.655016)">
				<title>Sheet.1300</title>
				<path d="M25.95 581.51 L27.95 581.51 L27.95 585 L0 585 L0 581.51 L2 581.51 L2 548.07 L3.49 548.07 L3.49 545.08 L4.99
							 545.08 L4.99 543.08 L22.96 543.08 L22.96 545.08 L24.45 545.08 L24.45 548.07 L25.95 548.07 L25.95 581.51
							 ZM8.48 578.51 L8.48 575.52 L5.49 575.52 L5.49 578.51 L8.48 578.51 ZM8.48 570.53 L8.48 567.53 L5.49 567.53
							 L5.49 570.53 L8.48 570.53 ZM8.48 562.54 L8.48 559.55 L5.49 559.55 L5.49 562.54 L8.48 562.54 ZM8.48 554.56
							 L8.48 551.56 L5.49 551.56 L5.49 554.56 L8.48 554.56 ZM15.47 581.51 L15.47 575.52 L12.48 575.52 L12.48
							 581.51 L15.47 581.51 ZM15.47 570.53 L15.47 567.53 L12.48 567.53 L12.48 570.53 L15.47 570.53 ZM15.47
							 562.54 L15.47 559.55 L12.48 559.55 L12.48 562.54 L15.47 562.54 ZM15.47 554.56 L15.47 551.56 L12.48 551.56
							 L12.48 554.56 L15.47 554.56 ZM22.46 578.51 L22.46 575.52 L19.46 575.52 L19.46 578.51 L22.46 578.51 ZM22.46
							 570.53 L22.46 567.53 L19.46 567.53 L19.46 570.53 L22.46 570.53 ZM22.46 562.54 L22.46 559.55 L19.46 559.55
							 L19.46 562.54 L22.46 562.54 ZM22.46 554.56 L22.46 551.56 L19.46 551.56 L19.46 554.56 L22.46 554.56 Z"
						class="st30"/>
			</g>
		</g>
		<g id="shape1301-227" v:mID="1301" v:groupContext="shape" transform="translate(36,-418.5)">
			<title>VnetBox.1301</title>
			<rect x="0" y="459.794" width="299.25" height="125.206" rx="4.5" ry="4.5" class="st31"/>
		</g>
		<g id="group1286-229" transform="translate(304.147,-537.353)" v:mID="1286" v:groupContext="group">
			<title>Vnet_hexbck.1286</title>
			<g id="shape1287-230" v:mID="1287" v:groupContext="shape" transform="translate(3.87558,-0.932567)">
				<title>Hexagon.1255</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<path d="M12.48 572.78 L3.34 572.78 L-1.22 578.89 L3.34 585 L12.48 585 L17.05 578.89 L12.48 572.78 Z" class="st32"/>
			</g>
			<g id="group1288-232" v:mID="1288" v:groupContext="group">
				<title>Sheet.1288</title>
				<g id="group1289-233" transform="translate(5.88364,-5.96604)" v:mID="1289" v:groupContext="group">
					<title>Sheet.1289</title>
					<g id="shape1290-234" v:mID="1290" v:groupContext="shape">
						<title>Sheet.1290</title>
						<path d="M12.23 583.92 A1.51278 1.50952 90 1 1 10.72 582.41 A1.51148 1.50822 90 0 1 12.23 583.92 ZM5.89 582.42
									 A1.51278 1.50952 -90 1 0 7.4 583.93 A1.51148 1.50822 -90 0 0 5.89 582.42 ZM1.07 582.42 A1.51278
									 1.50952 -90 1 0 2.58 583.93 A1.51278 1.50952 -90 0 0 1.07 582.42 L1.07 582.42 Z" class="st33"/>
					</g>
				</g>
				<g id="group1291-238" transform="translate(0.00521395,0)" v:mID="1291" v:groupContext="group">
					<title>Sheet.1291</title>
					<g id="shape1292-239" v:mID="1292" v:groupContext="shape">
						<title>Sheet.1292</title>
						<path d="M8.05 584.02 L7.19 584.89 a0.391913 0.391067 90 0 1 -0.55271 0 L0.23 578.49 a0.783827 0.782134 90
									 0 1 -7.77156E-16 -1.10907 L1.09 576.51 L8.05 583.46 a0.391913 0.391067 90 0 1 -1.77636E-14 0.55521
									 L8.05 584.02 Z" class="st34"/>
					</g>
				</g>
				<g id="group1293-241" transform="translate(0,-5.65893)" v:mID="1293" v:groupContext="group">
					<title>Sheet.1293</title>
					<g id="shape1294-242" v:mID="1294" v:groupContext="shape">
						<title>Sheet.1294</title>
						<path d="M7.06 576.7 L7.93 577.58 a0.391913 0.391067 90 0 1 -2.66454E-15 0.55521 L1.1 585 L0.23 584.14 a0.783827
									 0.782134 90 0 1 1.11022E-16 -1.10907 L6.52 576.71 a0.391913 0.391067 90 0 1 0.554014 0 L7.06
									 576.7 Z" class="st35"/>
					</g>
				</g>
				<g id="group1295-244" transform="translate(15.2878,-0.00261275)" v:mID="1295" v:groupContext="group">
					<title>Sheet.1295</title>
					<g id="shape1296-245" v:mID="1296" v:groupContext="shape">
						<title>Sheet.1296</title>
						<path d="M7.07 576.51 L7.94 577.38 a0.783827 0.782134 90 0 1 0 1.10907 L1.53 584.89 a0.391913 0.391067 90
									 0 1 -0.55271 0 L0.11 584.02 a0.391913 0.391067 90 0 1 -3.88578E-15 -0.553902 L7.07 576.51 Z"
								class="st34"/>
					</g>
				</g>
				<g id="group1297-247" transform="translate(15.4129,-5.66282)" v:mID="1297" v:groupContext="group">
					<title>Sheet.1297</title>
					<g id="shape1298-248" v:mID="1298" v:groupContext="shape">
						<title>Sheet.1298</title>
						<path d="M7.82 584.14 L6.95 585 L0.11 578.13 a0.391913 0.391067 90 0 1 -7.99361E-15 -0.553902 L0.99 576.7
									 a0.391913 0.391067 90 0 1 0.55271 0 L7.82 583.02 a0.783827 0.782134 90 0 1 -0.00413832 1.10907
									 L7.82 584.14 Z" class="st35"/>
					</g>
				</g>
			</g>
		</g>
		<g id="shape1329-250" v:mID="1329" v:groupContext="shape" transform="translate(202.5,-33.75)">
			<title>VnetBox.1329</title>
			<rect x="0" y="250.213" width="302.625" height="334.787" rx="4.5" ry="4.5" class="st31"/>
		</g>
		<g id="group1316-252" transform="translate(473.029,-361.059)" v:mID="1316" v:groupContext="group">
			<title>Vnet_hexbck.1316</title>
			<g id="shape1317-253" v:mID="1317" v:groupContext="shape" transform="translate(3.87558,-0.932567)">
				<title>Hexagon.1255</title>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
				</v:userDefs>
				<path d="M12.48 572.78 L3.34 572.78 L-1.22 578.89 L3.34 585 L12.48 585 L17.05 578.89 L12.48 572.78 Z" class="st32"/>
			</g>
			<g id="group1318-255" v:mID="1318" v:groupContext="group">
				<title>Sheet.1318</title>
				<g id="group1319-256" transform="translate(5.88364,-5.96604)" v:mID="1319" v:groupContext="group">
					<title>Sheet.1319</title>
					<g id="shape1320-257" v:mID="1320" v:groupContext="shape">
						<title>Sheet.1320</title>
						<path d="M12.23 583.92 A1.51278 1.50952 90 1 1 10.72 582.41 A1.51148 1.50822 90 0 1 12.23 583.92 ZM5.89 582.42
									 A1.51278 1.50952 -90 1 0 7.4 583.93 A1.51148 1.50822 -90 0 0 5.89 582.42 ZM1.07 582.42 A1.51278
									 1.50952 -90 1 0 2.58 583.93 A1.51278 1.50952 -90 0 0 1.07 582.42 L1.07 582.42 Z" class="st33"/>
					</g>
				</g>
				<g id="group1321-260" transform="translate(0.00521395,0)" v:mID="1321" v:groupContext="group">
					<title>Sheet.1321</title>
					<g id="shape1322-261" v:mID="1322" v:groupContext="shape">
						<title>Sheet.1322</title>
						<path d="M8.05 584.02 L7.19 584.89 a0.391913 0.391067 90 0 1 -0.55271 0 L0.23 578.49 a0.783827 0.782134 90
									 0 1 -7.77156E-16 -1.10907 L1.09 576.51 L8.05 583.46 a0.391913 0.391067 90 0 1 -1.77636E-14 0.55521
									 L8.05 584.02 Z" class="st34"/>
					</g>
				</g>
				<g id="group1323-263" transform="translate(0,-5.65893)" v:mID="1323" v:groupContext="group">
					<title>Sheet.1323</title>
					<g id="shape1324-264" v:mID="1324" v:groupContext="shape">
						<title>Sheet.1324</title>
						<path d="M7.06 576.7 L7.93 577.58 a0.391913 0.391067 90 0 1 -2.66454E-15 0.55521 L1.1 585 L0.23 584.14 a0.783827
									 0.782134 90 0 1 1.11022E-16 -1.10907 L6.52 576.71 a0.391913 0.391067 90 0 1 0.554014 0 L7.06
									 576.7 Z" class="st35"/>
					</g>
				</g>
				<g id="group1325-266" transform="translate(15.2878,-0.00261275)" v:mID="1325" v:groupContext="group">
					<title>Sheet.1325</title>
					<g id="shape1326-267" v:mID="1326" v:groupContext="shape">
						<title>Sheet.1326</title>
						<path d="M7.07 576.51 L7.94 577.38 a0.783827 0.782134 90 0 1 0 1.10907 L1.53 584.89 a0.391913 0.391067 90
									 0 1 -0.55271 0 L0.11 584.02 a0.391913 0.391067 90 0 1 -3.88578E-15 -0.553902 L7.07 576.51 Z"
								class="st34"/>
					</g>
				</g>
				<g id="group1327-269" transform="translate(15.4129,-5.66282)" v:mID="1327" v:groupContext="group">
					<title>Sheet.1327</title>
					<g id="shape1328-270" v:mID="1328" v:groupContext="shape">
						<title>Sheet.1328</title>
						<path d="M7.82 584.14 L6.95 585 L0.11 578.13 a0.391913 0.391067 90 0 1 -7.99361E-15 -0.553902 L0.99 576.7
									 a0.391913 0.391067 90 0 1 0.55271 0 L7.82 583.02 a0.783827 0.782134 90 0 1 -0.00413832 1.10907
									 L7.82 584.14 Z" class="st35"/>
					</g>
				</g>
			</g>
		</g>
		<g id="group1330-272" transform="translate(44.9949,-432.45)" v:mID="1330" v:groupContext="group">
			<title>Sheet.1330</title>
			<g id="shape1076-273" v:mID="1076" v:groupContext="shape">
				<title>Sheet.1076</title>
				<rect x="0" y="514.248" width="67.176" height="70.7522" class="st15"/>
			</g>
			<g id="group1064-275" transform="translate(10.6901,-14.85)" v:mID="1064" v:groupContext="group">
				<title>Firewalls.1051.1064</title>
				<g id="group1065-276" v:mID="1065" v:groupContext="group">
					<title>Icon-networking-84</title>
					<g id="shape1066-277" v:mID="1066" v:groupContext="shape" transform="translate(0,-6.24819)">
						<title>Sheet.1066</title>
						<path d="M45.8 574.81 A10.3295 10.3295 -180 0 0 36.87 564.92 A12.9755 12.9755 -180 0 0 23.51 552.53 A13.3062
									 13.3062 -180 0 0 10.79 561.2 A12.2631 12.2631 -180 0 0 -0 573.01 A12.4666 12.4666 -180 0 0 12.9
									 584.99 L34.91 584.99 A3.71454 3.71454 -180 0 0 35.47 584.99 A10.4312 10.4312 -180 0 0 45.8 574.81
									 Z" class="st36"/>
					</g>
					<g id="shape1067-281" v:mID="1067" v:groupContext="shape" transform="translate(9.43901,0)">
						<title>Sheet.1067</title>
						<path d="M26.92 571.24 A1.06857 1.06857 -180 0 0 25.85 570.19 L1.07 570.19 A1.06857 1.06857 -180 0 0 0 571.24
									 L0 583.96 A1.06857 1.06857 -180 0 0 1.07 585 L25.85 585 A1.06857 1.06857 -180 0 0 26.92 583.96
									 L26.92 571.24 Z" class="st37"/>
					</g>
					<g id="shape1068-283" v:mID="1068" v:groupContext="shape" transform="translate(10.9401,-10.1005)">
						<title>Sheet.1068</title>
						<rect x="0" y="581.769" width="7.30187" height="3.23114" class="st38"/>
					</g>
					<g id="shape1069-285" v:mID="1069" v:groupContext="shape" transform="translate(19.336,-10.1005)">
						<title>Sheet.1069</title>
						<rect x="0" y="581.769" width="7.30187" height="3.23114" class="st39"/>
					</g>
					<g id="shape1070-287" v:mID="1070" v:groupContext="shape" transform="translate(10.9401,-5.77535)">
						<title>Sheet.1070</title>
						<path d="M16.79 577.44 L24.09 577.44 L24.09 580.67 L16.79 580.67 L16.79 577.44 ZM0 581.77 L3.03 581.77 L3.03
									 585 L0 585 L0 581.77 Z" class="st38"/>
					</g>
					<g id="shape1071-289" v:mID="1071" v:groupContext="shape" transform="translate(15.0617,-5.77535)">
						<title>Sheet.1071</title>
						<path d="M16.94 581.77 L19.97 581.77 L19.97 585 L16.94 585 L16.94 581.77 ZM0 581.77 L7.3 581.77 L7.3 585
									 L0 585 L0 581.77 Z" class="st39"/>
					</g>
					<g id="shape1072-291" v:mID="1072" v:groupContext="shape" transform="translate(10.9401,-1.4502)">
						<title>Sheet.1072</title>
						<path d="M12.54 577.44 L19.84 577.44 L19.84 580.67 L12.54 580.67 L12.54 577.44 ZM0 581.77 L7.3 581.77 L7.3
									 585 L0 585 L0 581.77 Z" class="st38"/>
					</g>
					<g id="shape1073-293" v:mID="1073" v:groupContext="shape" transform="translate(19.336,-1.4502)">
						<title>Sheet.1073</title>
						<rect x="0" y="581.769" width="7.30187" height="3.23114" class="st39"/>
					</g>
					<g id="shape1074-295" v:mID="1074" v:groupContext="shape" transform="translate(27.7319,-1.4502)">
						<title>Sheet.1074</title>
						<rect x="0" y="581.769" width="7.30187" height="3.23114" class="st38"/>
					</g>
				</g>
			</g>
		</g>
		<g id="group1331-297" transform="translate(152.095,-432)" v:mID="1331" v:groupContext="group">
			<title>Sheet.1331</title>
			<g id="shape1078-298" v:mID="1078" v:groupContext="shape">
				<title>Sheet.1078</title>
				<rect x="0" y="514.248" width="67.176" height="70.7522" class="st15"/>
			</g>
			<g id="group1079-300" transform="translate(11.2551,-13.5)" v:mID="1079" v:groupContext="group">
				<title>Bastion.1052.1079</title>
				<g id="group1080-301" v:mID="1080" v:groupContext="group">
					<title>Sheet.1080</title>
					<g id="shape1081-302" v:mID="1081" v:groupContext="shape">
						<title>Sheet.1081</title>
						<path d="M18.25 574.28 C12.93 577.96 4.42 583.61 3.6 584.26 C2.54 585 0.98 584.67 0.49 584.02 C0 583.28 0
									 581.73 1.06 580.99 C1.72 580.58 12.93 572.97 16.37 570.59 L18.34 572.72 C19.24 573.62 18.25
									 574.28 18.25 574.28 ZM18.01 569.12 C18.01 569.12 8.19 558.56 7.04 557.41 C5.98 556.19 6.96 554.79
									 7.53 554.3 C8.1 553.81 9.82 553.08 10.72 553.98 C11.62 554.88 22.59 566.66 23.41 567.56 C24.23
									 568.46 24.15 569.86 22.76 570.84 C22.51 571 21.86 571.49 20.87 572.15 C20.79 572.23 20.38 572.56
									 19.97 572.8 C19.97 572.8 20.87 572.15 20.22 571.41 L18.01 569.12 ZM25.05 562.16 C29.47 566.91
									 36.35 574.36 37.08 575.1 C37.98 576.08 39.54 576.08 40.19 575.5 C40.85 574.93 41.18 573.46 40.27
									 572.48 C39.78 571.9 30.53 562 27.75 558.97 L25.38 560.61 C24.23 561.26 25.05 562.16 25.05 562.16
									 ZM26.03 557.33 C26.03 557.33 37.9 549.06 39.21 548.08 C40.52 547.1 39.87 545.54 39.37 544.97
									 C38.88 544.4 37.41 543.25 36.35 543.99 C35.28 544.73 22.1 553.89 21.12 554.63 C20.14 555.37
									 19.89 556.68 21.12 557.99 C21.28 558.23 21.86 558.81 22.67 559.71 C22.76 559.79 23.08 560.2
									 23.41 560.52 C23.41 560.52 22.67 559.71 23.49 559.13 L26.03 557.33 Z" class="st40"/>
					</g>
				</g>
			</g>
			<g id="group1098-304" transform="translate(51.3051,-59.4)" v:mID="1098" v:groupContext="group">
				<title>Network Security Group.1098</title>
				<g id="group1099-305" v:mID="1099" v:groupContext="group">
					<title>Icon-networking-67</title>
					<g id="shape1100-306" v:mID="1100" v:groupContext="shape">
						<title>Sheet.1100</title>
						<path d="M16.22 574.42 C16.22 579.76 9.78 584.05 8.38 584.93 a0.506969 0.506969 0 0 1 -0.529011 0 C6.45 584.05
									 0 579.76 0 574.42 L0 568.01 a0.506969 0.506969 0 0 1 0.495948 -0.506969 C5.51 567.37 4.35 565.16
									 8.11 565.16 C11.87 565.16 10.71 567.37 15.73 567.5 a0.506969 0.506969 0 0 1 0.495948 0.506969
									 L16.22 574.42 Z" class="st41"/>
					</g>
					<g id="shape1101-308" v:mID="1101" v:groupContext="shape" transform="translate(0.672285,-0.823307)">
						<title>Sheet.1101</title>
						<path d="M14.88 575.3 C14.88 580.19 8.97 584.12 7.68 584.93 a0.473906 0.473906 0 0 1 -0.484927 0 C5.91 584.14
									 0 580.19 0 575.3 L0 569.42 a0.462884 0.462884 0 0 1 0.451863 -0.462884 C5.05 568.83 3.99 566.81
									 7.44 566.81 C10.89 566.81 9.83 568.83 14.43 568.95 a0.462884 0.462884 0 0 1 0.451863 0.462884
									 L14.88 575.3 Z" class="st42"/>
					</g>
					<g id="shape1102-310" v:mID="1102" v:groupContext="shape" transform="translate(0.705348,-0.823903)">
						<title>Sheet.1102</title>
						<path d="M7.41 575.91 L7.41 566.82 C10.86 566.82 9.8 568.83 14.39 568.95 a0.473906 0.473906 0 0 1 0.451863
									 0.473906 L14.85 575.31 A5.3893 5.3893 0 0 1 14.85 575.91 L7.41 575.91 ZM7.41 575.91 L0 575.91
									 C0.44 580.51 5.93 584.17 7.16 584.93 a0.429821 0.429821 -180 0 0 0.198379 0.0661264 L7.41 585
									 L7.41 575.91 Z" class="st43"/>
					</g>
					<g id="shape1103-314" v:mID="1103" v:groupContext="shape" transform="translate(0.697126,-0.823903)">
						<title>Sheet.1103</title>
						<path d="M0.43 568.95 C5.02 568.83 3.96 566.82 7.41 566.82 L7.41 575.91 L0.01 575.91 A5.3893 5.3893 0 0 1
									 0.01 575.31 L0.01 569.43 a0.473906 0.473906 0 0 1 0.4188 -0.473906 ZM14.82 575.91 L7.41 575.91
									 L7.41 585 a0.429821 0.429821 -180 0 0 0.198379 -0.0661264 C8.89 584.17 14.38 580.51 14.82 575.91
									 Z" class="st44"/>
					</g>
				</g>
			</g>
		</g>
		<g id="group1332-316" transform="translate(254.695,-431.55)" v:mID="1332" v:groupContext="group">
			<title>Sheet.1332</title>
			<g id="shape1087-317" v:mID="1087" v:groupContext="shape">
				<title>Sheet.1087</title>
				<rect x="0" y="514.248" width="67.176" height="70.7522" class="st15"/>
			</g>
			<g id="group1088-319" transform="translate(9.98446,-12.6)" v:mID="1088" v:groupContext="group">
				<title>Gateway.1088</title>
				<g id="shape1089-320" v:mID="1089" v:groupContext="shape">
					<title>Sheet.1089</title>
					<path d="M23.6 585 C22.44 585 21.35 584.55 20.53 583.72 L1.28 564.47 C0.46 563.66 0 562.54 0 561.4 C0 560.25
								 0.46 559.13 1.28 558.32 L20.53 539.07 C21.35 538.25 22.44 537.79 23.6 537.79 C24.77 537.79 25.86
								 538.25 26.68 539.07 L45.93 558.32 C46.75 559.14 47.21 560.23 47.21 561.4 C47.21 562.56 46.75 563.65
								 45.93 564.48 L26.68 583.72 C25.86 584.55 24.77 585 23.6 585 Z" class="st45"/>
				</g>
				<g id="shape1090-322" v:mID="1090" v:groupContext="shape" transform="translate(4.14195,-4.58145)">
					<title>Sheet.1090</title>
					<path d="M38.92 565.98 L32.68 559.73 L32.68 564.14 L26.06 564.14 C25.42 561.85 23.63 560.05 21.35 559.4 L21.35
								 553.08 L25.71 553.08 L19.46 546.84 L13.22 553.08 L17.57 553.08 L17.57 559.4 C15.3 560.05 13.51 561.85
								 12.87 564.12 L6.24 564.12 L6.24 559.76 L0 566 L6.24 572.24 L6.24 567.84 L12.87 567.84 C13.52 570.12
								 15.3 571.91 17.57 572.56 L17.57 576.94 C16.63 577.62 15.19 579.07 15.19 580.75 C15.19 583.09 17.11
								 585 19.45 585 C21.79 585 23.71 583.09 23.71 580.75 C23.71 579.09 22.29 577.66 21.35 576.96 L21.35
								 572.56 C23.61 571.91 25.4 570.12 26.05 567.86 L32.68 567.86 L32.68 572.22 L38.92 565.98 Z"
							class="st46"/>
				</g>
				<g id="shape1091-324" v:mID="1091" v:groupContext="shape" transform="translate(18.6392,-18.6397)">
					<title>Sheet.1091</title>
					<path d="M4.96 575.07 C2.23 575.07 0 577.3 0 580.04 C0 582.77 2.23 585 4.96 585 C7.7 585 9.93 582.77 9.93 580.04
								 C9.93 577.3 7.7 575.07 4.96 575.07 Z" class="st45"/>
				</g>
			</g>
			<g id="group1104-326" transform="translate(51.3051,-59.85)" v:mID="1104" v:groupContext="group">
				<title>Network Security Group.1104</title>
				<g id="group1105-327" v:mID="1105" v:groupContext="group">
					<title>Icon-networking-67</title>
					<g id="shape1106-328" v:mID="1106" v:groupContext="shape">
						<title>Sheet.1106</title>
						<path d="M16.22 574.42 C16.22 579.76 9.78 584.05 8.38 584.93 a0.506969 0.506969 0 0 1 -0.529011 0 C6.45 584.05
									 0 579.76 0 574.42 L0 568.01 a0.506969 0.506969 0 0 1 0.495948 -0.506969 C5.51 567.37 4.35 565.16
									 8.11 565.16 C11.87 565.16 10.71 567.37 15.73 567.5 a0.506969 0.506969 0 0 1 0.495948 0.506969
									 L16.22 574.42 Z" class="st41"/>
					</g>
					<g id="shape1107-330" v:mID="1107" v:groupContext="shape" transform="translate(0.672285,-0.823307)">
						<title>Sheet.1107</title>
						<path d="M14.88 575.3 C14.88 580.19 8.97 584.12 7.68 584.93 a0.473906 0.473906 0 0 1 -0.484927 0 C5.91 584.14
									 0 580.19 0 575.3 L0 569.42 a0.462884 0.462884 0 0 1 0.451863 -0.462884 C5.05 568.83 3.99 566.81
									 7.44 566.81 C10.89 566.81 9.83 568.83 14.43 568.95 a0.462884 0.462884 0 0 1 0.451863 0.462884
									 L14.88 575.3 Z" class="st42"/>
					</g>
					<g id="shape1108-332" v:mID="1108" v:groupContext="shape" transform="translate(0.705348,-0.823903)">
						<title>Sheet.1108</title>
						<path d="M7.41 575.91 L7.41 566.82 C10.86 566.82 9.8 568.83 14.39 568.95 a0.473906 0.473906 0 0 1 0.451863
									 0.473906 L14.85 575.31 A5.3893 5.3893 0 0 1 14.85 575.91 L7.41 575.91 ZM7.41 575.91 L0 575.91
									 C0.44 580.51 5.93 584.17 7.16 584.93 a0.429821 0.429821 -180 0 0 0.198379 0.0661264 L7.41 585
									 L7.41 575.91 Z" class="st43"/>
					</g>
					<g id="shape1109-335" v:mID="1109" v:groupContext="shape" transform="translate(0.697126,-0.823903)">
						<title>Sheet.1109</title>
						<path d="M0.43 568.95 C5.02 568.83 3.96 566.82 7.41 566.82 L7.41 575.91 L0.01 575.91 A5.3893 5.3893 0 0 1
									 0.01 575.31 L0.01 569.43 a0.473906 0.473906 0 0 1 0.4188 -0.473906 ZM14.82 575.91 L7.41 575.91
									 L7.41 585 a0.429821 0.429821 -180 0 0 0.198379 -0.0661264 C8.89 584.17 14.38 580.51 14.82 575.91
									 Z" class="st44"/>
					</g>
				</g>
			</g>
		</g>
		<g id="group1333-337" transform="translate(570.75,-279)" v:mID="1333" v:groupContext="group">
			<title>Cloud.1013.1333</title>
			<g id="shape1334-338" v:mID="1334" v:groupContext="shape">
				<title>Sheet.1334</title>
				<path d="M61.83 564.41 C67.47 564.41 72 569.11 72 574.75 C72 580.39 67.39 585 61.75 585 L12.2 585 L12.2 584.91 C7.5
							 584.66 3.23 581.84 1.18 577.57 C-0.79 573.3 -0.27 568.26 2.55 564.41 C5.45 560.65 10.15 558.69 14.85
							 559.46 L14.85 559.29 C14.85 552.2 19.8 546.04 26.64 544.42 C33.47 542.8 40.65 546.04 43.89 552.37 C47.82
							 551 52.18 551.6 55.6 553.99 C58.93 556.38 60.98 560.23 60.98 564.41 L61.83 564.41 Z" class="st47"/>
			</g>
		</g>
		<g id="shape1335-342" v:mID="1335" v:groupContext="shape" transform="translate(554.25,-260.25)">
			<title>Sheet.1335</title>
			<desc>Internet</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="48.7125" cy="577.406" width="97.43" height="15.1875"/>
			<rect x="0" y="569.812" width="97.425" height="15.1875" class="st2"/>
			<text x="31.37" y="580.41" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Internet</text>		</g>
		<g id="group1336-345" transform="translate(234.265,-176.294)" v:mID="1336" v:groupContext="group">
			<title>Sheet.1336</title>
			<g id="group1337-346" v:mID="1337" v:groupContext="group">
				<title>Page-1</title>
				<g id="group1338-347" v:mID="1338" v:groupContext="group">
					<title>Container Kubernetes Service</title>
					<g id="group1339-348" transform="translate(6.52479,-24.6657)" v:mID="1339" v:groupContext="group">
						<title>polygon41</title>
						<g id="shape1340-349" v:mID="1340" v:groupContext="shape">
							<title>Sheet.1340</title>
							<path d="M0 574.76 L0 583.61 L6.35 585 L12.72 582.39 L12.72 575.78 L6.35 573.56 L0 574.76 Z"
									class="st48"/>
						</g>
					</g>
					<g id="group1341-353" transform="translate(6.6577,-24.7784)" v:mID="1341" v:groupContext="group">
						<title>path43</title>
						<g id="shape1342-354" v:mID="1342" v:groupContext="shape">
							<title>Sheet.1342</title>
							<path d="M6.46 584.97 L12.27 582.57 a0.454248 0.443863 -90 0 0 0.266318 -0.317973 L12.54 576.26 a0.47696
										 0.466057 -90 0 0 -0.288512 -0.408823 L6.37 573.8 L6.1 573.8 L0.33 574.89 a0.454248 0.443863
										 -90 0 0 -0.332898 0.408823 L0 583.32 a0.431535 0.42167 -90 0 0 0.332898 0.431535 L6.17 585
										 a0.726796 0.710178 -90 0 0 0.283106 -0.0212329 Z" class="st49"/>
						</g>
					</g>
					<g id="group1343-356" transform="translate(6.52479,-24.6657)" v:mID="1343" v:groupContext="group">
						<title>path45</title>
						<g id="shape1344-357" v:mID="1344" v:groupContext="shape">
							<title>Sheet.1344</title>
							<path d="M0 574.76 L0 583.61 L6.41 585 L6.41 573.64 L0 574.76 ZM2.71 582.93 L0.91 582.57 L0.91 575.76
										 L2.71 575.46 L2.71 582.93 ZM5.51 583.46 L3.44 583.12 L3.44 575.32 L5.51 574.96 L5.51 583.46
										 Z" class="st50"/>
						</g>
					</g>
					<g id="group1345-359" transform="translate(20.2624,-24.5521)" v:mID="1345" v:groupContext="group">
						<title>polygon47</title>
						<g id="shape1346-360" v:mID="1346" v:groupContext="shape">
							<title>Sheet.1346</title>
							<path d="M0 574.76 L0 583.61 L6.35 585 L12.7 582.37 L12.7 575.78 L6.35 573.56 L0 574.76 Z" class="st48"/>
						</g>
					</g>
					<g id="group1347-363" transform="translate(20.2624,-24.5521)" v:mID="1347" v:groupContext="group">
						<title>path49</title>
						<g id="shape1348-364" v:mID="1348" v:groupContext="shape">
							<title>Sheet.1348</title>
							<path d="M0 574.76 L0 583.61 L6.37 585 L6.37 573.64 L0 574.76 ZM2.68 582.93 L0.88 582.57 L0.88 575.76
										 L2.68 575.46 L2.68 582.93 ZM5.49 583.46 L3.42 583.12 L3.42 575.32 L5.49 574.94 L5.49 583.46
										 Z" class="st50"/>
						</g>
					</g>
					<g id="group1349-366" transform="translate(0.0221932,-12.4464)" v:mID="1349" v:groupContext="group">
						<title>polygon51</title>
						<g id="shape1350-367" v:mID="1350" v:groupContext="shape">
							<title>Sheet.1350</title>
							<path d="M0 574.76 L0 583.61 L6.35 585 L12.72 582.39 L12.72 575.78 L6.35 573.56 L0 574.76 Z"
									class="st48"/>
						</g>
					</g>
					<g id="group1351-370" transform="translate(7.99361E-14,-12.5145)" v:mID="1351" v:groupContext="group">
						<title>path53</title>
						<g id="shape1352-371" v:mID="1352" v:groupContext="shape">
							<title>Sheet.1352</title>
							<path d="M0 574.82 L0 583.61 L6.41 585 L6.41 573.64 L0 574.82 ZM2.68 583.03 L0.88 582.64 L0.88 575.82
										 L2.68 575.51 L2.68 583.03 ZM5.51 583.61 L3.44 583.27 L3.44 575.4 L5.51 575.03 L5.51 583.61
										 Z" class="st50"/>
						</g>
					</g>
					<g id="group1353-373" transform="translate(13.7153,-12.5145)" v:mID="1353" v:groupContext="group">
						<title>polygon55</title>
						<g id="shape1354-374" v:mID="1354" v:groupContext="shape">
							<title>Sheet.1354</title>
							<path d="M0 574.73 L0 583.59 L6.35 585 L12.7 582.37 L12.7 575.76 L6.35 573.53 L0 574.73 Z" class="st48"/>
						</g>
					</g>
					<g id="group1355-377" transform="translate(13.7153,-12.5145)" v:mID="1355" v:groupContext="group">
						<title>path57</title>
						<g id="shape1356-378" v:mID="1356" v:groupContext="shape">
							<title>Sheet.1356</title>
							<path d="M0 574.73 L0 583.61 L6.39 585 L6.39 573.64 L0 574.73 ZM2.68 582.93 L0.88 582.54 L0.88 575.74
										 L2.68 575.42 L2.68 582.93 ZM5.49 583.44 L3.42 583.09 L3.42 575.3 L5.49 574.94 L5.49 583.44
										 Z" class="st50"/>
						</g>
					</g>
					<g id="group1357-380" transform="translate(27.4085,-12.4237)" v:mID="1357" v:groupContext="group">
						<title>polygon59</title>
						<g id="shape1358-381" v:mID="1358" v:groupContext="shape">
							<title>Sheet.1358</title>
							<path d="M0 574.76 L0 583.61 L6.35 585 L12.72 582.39 L12.72 575.78 L6.35 573.56 L0 574.76 Z"
									class="st48"/>
						</g>
					</g>
					<g id="group1359-384" transform="translate(27.4085,-12.5145)" v:mID="1359" v:groupContext="group">
						<title>path61</title>
						<g id="shape1360-385" v:mID="1360" v:groupContext="shape">
							<title>Sheet.1360</title>
							<path d="M0 574.84 L0 583.61 L6.41 585 L6.41 573.64 L0 574.84 ZM2.71 583.05 L0.91 582.66 L0.91 575.85
										 L2.71 575.53 L2.71 583.05 ZM5.51 583.55 L3.44 583.2 L3.44 575.42 L5.51 575.05 L5.51 583.55
										 Z" class="st50"/>
						</g>
					</g>
					<g id="group1361-387" transform="translate(6.36943,-0.0908494)" v:mID="1361" v:groupContext="group">
						<title>polygon63</title>
						<g id="shape1362-388" v:mID="1362" v:groupContext="shape">
							<title>Sheet.1362</title>
							<path d="M0 574.73 L0 583.59 L6.35 585 L12.72 582.37 L12.72 575.78 L6.35 573.56 L0 574.73 Z"
									class="st48"/>
						</g>
					</g>
					<g id="group1363-391" transform="translate(6.4792,-0.251296)" v:mID="1363" v:groupContext="group">
						<title>path65</title>
						<g id="shape1364-392" v:mID="1364" v:groupContext="shape">
							<title>Sheet.1364</title>
							<path d="M6.48 584.98 L12.27 582.71 a0.408823 0.399477 -90 0 0 0.266318 -0.408823 L12.54 576.39 a0.454248
										 0.443863 -90 0 0 -0.28851 -0.499672 L6.39 573.85 a0.386111 0.377284 -90 0 0 -0.266318 0
										 L0.36 574.92 a0.431535 0.42167 -90 0 0 -0.35509 0.431535 L0 583.39 a0.431535 0.42167 -90
										 0 0 0.332898 0.431535 L6.17 584.98 a0.522385 0.510443 -90 0 0 0.310704 0 L6.48 584.98 Z"
									class="st49"/>
						</g>
					</g>
					<g id="group1365-394" transform="translate(6.36943,-0.0908494)" v:mID="1365" v:groupContext="group">
						<title>path67</title>
						<g id="shape1366-395" v:mID="1366" v:groupContext="shape">
							<title>Sheet.1366</title>
							<path d="M0 574.73 L0 583.59 L6.41 585 L6.41 573.46 L0 574.73 ZM2.71 582.93 L0.91 582.54 L0.91 575.74
										 L2.71 575.42 L2.71 582.93 ZM5.51 583.46 L3.44 583.12 L3.44 575.3 L5.51 574.94 L5.51 583.46
										 Z" class="st50"/>
						</g>
					</g>
					<g id="group1367-397" transform="translate(20.0848,0)" v:mID="1367" v:groupContext="group">
						<title>polygon69</title>
						<g id="shape1368-398" v:mID="1368" v:groupContext="shape">
							<title>Sheet.1368</title>
							<path d="M0 574.76 L0 583.61 L6.35 585 L12.72 582.39 L12.72 575.78 L6.35 573.56 L0 574.76 Z"
									class="st48"/>
						</g>
					</g>
					<g id="group1369-401" transform="translate(20.0848,-3.48698E-08)" v:mID="1369" v:groupContext="group">
						<title>path71</title>
						<g id="shape1370-402" v:mID="1370" v:groupContext="shape">
							<title>Sheet.1370</title>
							<path d="M0 574.76 L0 583.61 L6.41 585 L6.41 573.64 L0 574.76 ZM2.71 582.95 L0.91 582.57 L0.91 575.76
										 L2.71 575.44 L2.71 582.95 ZM5.51 583.46 L3.44 583.12 L3.44 575.32 L5.51 574.96 L5.51 583.46
										 Z" class="st50"/>
						</g>
					</g>
				</g>
			</g>
		</g>
		<g id="group1371-404" transform="translate(419.845,-258.75)" v:mID="1371" v:groupContext="group">
			<title>Sheet.1371</title>
			<g id="shape24-405" v:mID="24" v:groupContext="shape">
				<title>Sheet.24</title>
				<rect x="0" y="514.248" width="67.176" height="70.7522" class="st15"/>
			</g>
			<g id="group29-407" transform="translate(15.0669,-15.353)" v:mID="29" v:groupContext="group">
				<title>Application Gateways.1047</title>
				<g id="group30-408" transform="translate(-3.0375,-3.15)" v:mID="30" v:groupContext="group">
					<title>Icon-networking-76</title>
					<g id="shape31-409" v:mID="31" v:groupContext="shape" transform="translate(-391.424,171.343) rotate(-45)">
						<title>Sheet.31</title>
						<path d="M-0 583.55 A1.45123 1.45123 -180 0 0 1.45 585 L29.99 585 A1.45123 1.45123 -180 0 0 31.44 583.55
									 L31.44 555.01 A1.45123 1.45123 -180 0 0 29.99 553.56 L1.45 553.56 A1.45123 1.45123 -180 0 0
									 -0 555.01 L0 583.55 Z" class="st51"/>
					</g>
					<g id="group32-413" transform="translate(9.52761,-6.70695)" v:mID="32" v:groupContext="group">
						<title>Sheet.32</title>
						<g id="shape33-414" v:mID="33" v:groupContext="shape" transform="translate(10.7197,-11.6823)">
							<title>Sheet.33</title>
							<path d="M6.82 585 L14.05 585 a0.280055 0.280055 -180 0 0 0.254595 -0.280055 L14.31 577.51 a0.254595
										 0.254595 -180 0 0 -0.458272 -0.178217 L11.86 579.32 L11.86 579.45 a0.254595 0.254595 0 0
										 1 -0.356434 0 L2.67 570.74 a0.254595 0.254595 -180 0 0 -0.356434 0 L0.08 572.98 a0.280055
										 0.280055 -180 0 0 -9.60343E-15 0.381893 L8.76 582.05 a0.280055 0.280055 0 0 1 8.88178E-15
										 0.381893 L8.63 582.56 L6.64 584.54 a0.280055 0.280055 -180 0 0 0.178217 0.458272 Z"
									class="st52"/>
						</g>
						<g id="shape34-416" v:mID="34" v:groupContext="shape" transform="translate(0,-11.6823)">
							<title>Sheet.34</title>
							<path d="M7.43 585 L0.2 585 a0.280055 0.280055 0 0 1 -0.203676 -0.280055 L0 577.51 a0.254595 0.254595
										 0 0 1 0.432812 -0.178217 L2.44 579.32 L2.44 579.45 a0.254595 0.254595 -180 0 0 0.356434
										 0 L11.58 570.74 a0.254595 0.254595 0 0 1 0.356434 1.13687E-13 L14.18 572.98 a0.280055 0.280055
										 0 0 1 2.66454E-14 0.381893 L5.5 582.05 a0.280055 0.280055 -180 0 0 0 0.381893 L5.5 582.56
										 L7.49 584.54 a0.280055 0.280055 0 0 1 -0.0509191 0.458272 Z" class="st52"/>
						</g>
						<g id="shape35-418" v:mID="35" v:groupContext="shape" transform="translate(7.12597,0)">
							<title>Sheet.35</title>
							<path d="M0.05 579.84 L5.15 584.93 a0.254595 0.254595 -180 0 0 0.356434 0 L10.59 579.84 a0.254595 0.254595
										 -180 0 0 -0.178217 -0.432812 L7.44 579.4 a0.280055 0.280055 0 0 1 -0.254595 -0.254595 L7.18
										 566.93 a0.254595 0.254595 -180 0 0 -0.254595 -0.280055 L3.77 566.65 a0.280055 0.280055 -180
										 0 0 -0.280055 0.280055 L3.49 579.15 a0.254595 0.254595 0 0 1 -0.254595 0.254595 L0.23 579.4
										 a0.254595 0.254595 -180 0 0 -0.178217 0.432812 Z" class="st52"/>
						</g>
					</g>
					<g id="shape36-420" v:mID="36" v:groupContext="shape" transform="translate(16.9701,-25.2888)">
						<title>Sheet.36</title>
						<path d="M9.49 584.95 A6.89954 6.89954 0 0 1 1.04 574.05 A6.89954 6.89954 0 0 1 9.41 585 L9.49 584.95 Z"
								class="st53"/>
						<path d="M9.49 584.95 A6.89954 6.89954 0 0 1 1.04 574.05 A6.89954 6.89954 0 0 1 9.41 585" class="st54"/>
					</g>
					<g id="shape37-423" v:mID="37" v:groupContext="shape" transform="translate(16.2998,-28.8022)">
						<title>Sheet.37</title>
						<ellipse cx="2.21498" cy="582.785" rx="2.21498" ry="2.21498" class="st55"/>
					</g>
					<g id="shape38-427" v:mID="38" v:groupContext="shape" transform="translate(16.2998,-25.8744)">
						<title>Sheet.38</title>
						<path d="M0.03 583.55 L0.51 584.31 L0.87 584.82 A15.123 15.123 0 0 1 1.73 582.02 A2.16406 2.16406 0 0 1 0.59
									 581.44 A17.0834 17.0834 -180 0 0 0.03 583.55 ZM1.48 577.77 A10.1838 10.1838 0 0 1 0.87 575.4
									 A6.31397 6.31397 -180 0 0 -0 576.57 A8.9363 8.9363 -180 0 0 0.51 578.46 A2.54595 2.54595 0 0
									 1 1.48 577.77 ZM3.84 581.36 A2.21498 2.21498 0 0 1 2.62 582.02 A11.7623 11.7623 -180 0 0 3.79
									 583.07 A10.5403 10.5403 -180 0 0 5.45 584.06 L5.45 583.88 A1.34936 1.34936 0 0 1 5.96 582.86
									 A9.16544 9.16544 0 0 1 3.84 581.36 ZM10.51 584.06 A10.5403 10.5403 0 0 1 8.3 583.8 A1.40027
									 1.40027 0 0 1 7.89 584.8 A9.77646 9.77646 -180 0 0 10.64 585 A7.45965 7.45965 -180 0 0 11.58
									 583.93 L10.51 584.06 Z" class="st56"/>
					</g>
					<g id="shape39-429" v:mID="39" v:groupContext="shape" transform="translate(21.7482,-25.5689)">
						<title>Sheet.39</title>
						<ellipse cx="1.42573" cy="583.574" rx="1.42573" ry="1.42573" class="st57"/>
					</g>
					<g id="shape40-431" v:mID="40" v:groupContext="shape" transform="translate(24.9052,-29.0059)">
						<title>Sheet.40</title>
						<ellipse cx="1.52757" cy="583.472" rx="1.52757" ry="1.52757" class="st58"/>
					</g>
					<g id="shape41-433" v:mID="41" v:groupContext="shape" transform="translate(19.3295,-28.344)">
						<title>Sheet.41</title>
						<path d="M3.46 579.45 A10.9221 10.9221 0 0 1 8.25 578.38 A5.70294 5.70294 -180 0 0 7.05 577.23 A11.4568 11.4568
									 -180 0 0 3.26 578.13 C3 578.13 2.78 578.41 2.52 578.53 A14.8938 14.8938 0 0 1 0.92 576.17 L0.05
									 576.5 A13.6209 13.6209 -180 0 0 1.71 579.04 A9.98014 9.98014 -180 0 0 0 580.44 A2.21498 2.21498
									 0 0 1 1.09 581.33 A8.37619 8.37619 0 0 1 2.65 580.11 A23.3209 23.3209 -180 0 0 5.63 582.66 A1.45119
									 1.45119 0 0 1 6.14 581.84 A21.4879 21.4879 0 0 1 3.46 579.45 ZM9.42 583.7 L8.96 583.7 L8.71
									 583.55 A1.55303 1.55303 0 0 1 8.2 584.31 L8.5 584.49 L8.78 584.64 L9.5 585 A9.11452 9.11452
									 -180 0 0 9.73 584.11 L9.42 583.7 Z" class="st56"/>
					</g>
					<g id="shape42-435" v:mID="42" v:groupContext="shape" transform="translate(16.2998,-28.8022)">
						<title>Sheet.42</title>
						<ellipse cx="2.21498" cy="582.785" rx="2.21498" ry="2.21498" class="st58"/>
					</g>
					<g id="shape43-437" v:mID="43" v:groupContext="shape" transform="translate(21.7482,-25.5689)">
						<title>Sheet.43</title>
						<ellipse cx="1.42573" cy="583.574" rx="1.42573" ry="1.42573" class="st58"/>
					</g>
				</g>
			</g>
			<g id="group1045-439" transform="translate(59.8551,-56.25)" v:mID="1045" v:groupContext="group">
				<title>Network Security Group.1045</title>
				<g id="group1046-440" v:mID="1046" v:groupContext="group">
					<title>Icon-networking-67</title>
					<g id="shape1047-441" v:mID="1047" v:groupContext="shape">
						<title>Sheet.1047</title>
						<path d="M16.22 574.42 C16.22 579.76 9.78 584.05 8.38 584.93 a0.506969 0.506969 0 0 1 -0.529011 0 C6.45 584.05
									 0 579.76 0 574.42 L0 568.01 a0.506969 0.506969 0 0 1 0.495948 -0.506969 C5.51 567.37 4.35 565.16
									 8.11 565.16 C11.87 565.16 10.71 567.37 15.73 567.5 a0.506969 0.506969 0 0 1 0.495948 0.506969
									 L16.22 574.42 Z" class="st41"/>
					</g>
					<g id="shape1048-443" v:mID="1048" v:groupContext="shape" transform="translate(0.672285,-0.823307)">
						<title>Sheet.1048</title>
						<path d="M14.88 575.3 C14.88 580.19 8.97 584.12 7.68 584.93 a0.473906 0.473906 0 0 1 -0.484927 0 C5.91 584.14
									 0 580.19 0 575.3 L0 569.42 a0.462884 0.462884 0 0 1 0.451863 -0.462884 C5.05 568.83 3.99 566.81
									 7.44 566.81 C10.89 566.81 9.83 568.83 14.43 568.95 a0.462884 0.462884 0 0 1 0.451863 0.462884
									 L14.88 575.3 Z" class="st42"/>
					</g>
					<g id="shape1049-445" v:mID="1049" v:groupContext="shape" transform="translate(0.705348,-0.823903)">
						<title>Sheet.1049</title>
						<path d="M7.41 575.91 L7.41 566.82 C10.86 566.82 9.8 568.83 14.39 568.95 a0.473906 0.473906 0 0 1 0.451863
									 0.473906 L14.85 575.31 A5.3893 5.3893 0 0 1 14.85 575.91 L7.41 575.91 ZM7.41 575.91 L0 575.91
									 C0.44 580.51 5.93 584.17 7.16 584.93 a0.429821 0.429821 -180 0 0 0.198379 0.0661264 L7.41 585
									 L7.41 575.91 Z" class="st43"/>
					</g>
					<g id="shape1050-448" v:mID="1050" v:groupContext="shape" transform="translate(0.697126,-0.823903)">
						<title>Sheet.1050</title>
						<path d="M0.43 568.95 C5.02 568.83 3.96 566.82 7.41 566.82 L7.41 575.91 L0.01 575.91 A5.3893 5.3893 0 0 1
									 0.01 575.31 L0.01 569.43 a0.473906 0.473906 0 0 1 0.4188 -0.473906 ZM14.82 575.91 L7.41 575.91
									 L7.41 585 a0.429821 0.429821 -180 0 0 0.198379 -0.0661264 C8.89 584.17 14.38 580.51 14.82 575.91
									 Z" class="st44"/>
					</g>
				</g>
			</g>
		</g>
		<g id="group1372-450" transform="translate(322.645,-258.75)" v:mID="1372" v:groupContext="group">
			<title>Sheet.1372</title>
			<g id="shape16-451" v:mID="16" v:groupContext="shape">
				<title>Sheet.16</title>
				<rect x="0" y="514.248" width="67.176" height="70.7522" class="st15"/>
			</g>
			<g id="group18-453" transform="translate(13.5051,-21.15)" v:mID="18" v:groupContext="group">
				<title>Gateway</title>
				<g id="shape19-454" v:mID="19" v:groupContext="shape">
					<title>Sheet.19</title>
					<path d="M20.51 585 C19.5 585 18.55 584.61 17.84 583.89 L1.11 567.16 C0.4 566.46 0 565.49 0 564.49 C0 563.49
								 0.4 562.52 1.11 561.81 L17.84 545.09 C18.55 544.37 19.5 543.98 20.51 543.98 C21.52 543.98 22.47
								 544.37 23.19 545.09 L39.91 561.81 C40.63 562.53 41.02 563.48 41.02 564.49 C41.02 565.5 40.63 566.45
								 39.91 567.17 L23.19 583.89 C22.47 584.61 21.52 585 20.51 585 Z" class="st45"/>
				</g>
				<g id="shape20-456" v:mID="20" v:groupContext="shape" transform="translate(3.59906,-3.98095)">
					<title>Sheet.20</title>
					<path d="M33.82 568.47 L28.4 563.05 L28.4 566.88 L22.64 566.87 C22.08 564.89 20.53 563.32 18.55 562.75 L18.55
								 557.26 L22.34 557.26 L16.91 551.84 L11.49 557.26 L15.27 557.26 L15.27 562.75 C13.3 563.32 11.74
								 564.88 11.18 566.86 L5.43 566.86 L5.43 563.07 L0 568.49 L5.43 573.92 L5.43 570.09 L11.19 570.09
								 C11.75 572.07 13.3 573.62 15.27 574.19 L15.27 577.99 C14.45 578.59 13.2 579.85 13.2 581.31 C13.2
								 583.34 14.86 585 16.9 585 C18.93 585 20.6 583.34 20.6 581.31 C20.6 579.86 19.37 578.62 18.55 578.02
								 L18.55 574.19 C20.52 573.63 22.07 572.07 22.63 570.1 L28.4 570.11 L28.4 573.9 L33.82 568.47 Z"
							class="st46"/>
				</g>
				<g id="shape21-458" v:mID="21" v:groupContext="shape" transform="translate(16.1962,-16.1966)">
					<title>Sheet.21</title>
					<path d="M4.31 576.37 C1.94 576.37 0 578.31 0 580.69 C0 583.06 1.94 585 4.31 585 C6.69 585 8.63 583.07 8.63 580.69
								 C8.63 578.31 6.69 576.37 4.31 576.37 Z" class="st45"/>
				</g>
			</g>
			<g id="group1051-460" transform="translate(58.9551,-57.15)" v:mID="1051" v:groupContext="group">
				<title>Network Security Group.1051</title>
				<g id="group1052-461" v:mID="1052" v:groupContext="group">
					<title>Icon-networking-67</title>
					<g id="shape1053-462" v:mID="1053" v:groupContext="shape">
						<title>Sheet.1053</title>
						<path d="M16.22 574.42 C16.22 579.76 9.78 584.05 8.38 584.93 a0.506969 0.506969 0 0 1 -0.529011 0 C6.45 584.05
									 0 579.76 0 574.42 L0 568.01 a0.506969 0.506969 0 0 1 0.495948 -0.506969 C5.51 567.37 4.35 565.16
									 8.11 565.16 C11.87 565.16 10.71 567.37 15.73 567.5 a0.506969 0.506969 0 0 1 0.495948 0.506969
									 L16.22 574.42 Z" class="st41"/>
					</g>
					<g id="shape1054-464" v:mID="1054" v:groupContext="shape" transform="translate(0.672285,-0.823307)">
						<title>Sheet.1054</title>
						<path d="M14.88 575.3 C14.88 580.19 8.97 584.12 7.68 584.93 a0.473906 0.473906 0 0 1 -0.484927 0 C5.91 584.14
									 0 580.19 0 575.3 L0 569.42 a0.462884 0.462884 0 0 1 0.451863 -0.462884 C5.05 568.83 3.99 566.81
									 7.44 566.81 C10.89 566.81 9.83 568.83 14.43 568.95 a0.462884 0.462884 0 0 1 0.451863 0.462884
									 L14.88 575.3 Z" class="st42"/>
					</g>
					<g id="shape1055-466" v:mID="1055" v:groupContext="shape" transform="translate(0.705348,-0.823903)">
						<title>Sheet.1055</title>
						<path d="M7.41 575.91 L7.41 566.82 C10.86 566.82 9.8 568.83 14.39 568.95 a0.473906 0.473906 0 0 1 0.451863
									 0.473906 L14.85 575.31 A5.3893 5.3893 0 0 1 14.85 575.91 L7.41 575.91 ZM7.41 575.91 L0 575.91
									 C0.44 580.51 5.93 584.17 7.16 584.93 a0.429821 0.429821 -180 0 0 0.198379 0.0661264 L7.41 585
									 L7.41 575.91 Z" class="st43"/>
					</g>
					<g id="shape1056-469" v:mID="1056" v:groupContext="shape" transform="translate(0.697126,-0.823903)">
						<title>Sheet.1056</title>
						<path d="M0.43 568.95 C5.02 568.83 3.96 566.82 7.41 566.82 L7.41 575.91 L0.01 575.91 A5.3893 5.3893 0 0 1
									 0.01 575.31 L0.01 569.43 a0.473906 0.473906 0 0 1 0.4188 -0.473906 ZM14.82 575.91 L7.41 575.91
									 L7.41 585 a0.429821 0.429821 -180 0 0 0.198379 -0.0661264 C8.89 584.17 14.38 580.51 14.82 575.91
									 Z" class="st44"/>
					</g>
				</g>
			</g>
		</g>
		<g id="group1373-471" transform="translate(216.995,-258.083)" v:mID="1373" v:groupContext="group">
			<title>Sheet.1373</title>
			<g id="shape3-472" v:mID="3" v:groupContext="shape">
				<title>Sheet.3</title>
				<rect x="0" y="514.248" width="67.176" height="70.7522" class="st15"/>
			</g>
			<g id="group5-474" transform="translate(10.8581,-19.0588)" v:mID="5" v:groupContext="group">
				<title>Azure Private Link</title>
				<g id="group6-475" v:mID="6" v:groupContext="group">
					<title>Artboard 1</title>
					<g id="shape7-476" v:mID="7" v:groupContext="shape" transform="translate(20.9929,-19.7305)">
						<title>Sheet.7</title>
						<rect x="0" y="567.006" width="2.85624" height="17.9943" class="st59"/>
					</g>
					<g id="shape8-478" v:mID="8" v:groupContext="shape" transform="translate(18.2319,-34.1069)">
						<title>Sheet.8</title>
						<path d="M8.38 580.81 A4.18915 4.18915 0 1 1 0 580.81 A4.18915 4.18915 0 1 1 8.38 580.81 Z" class="st60"/>
					</g>
					<g id="shape9-480" v:mID="9" v:groupContext="shape" transform="translate(11.6279,-9.2576)">
						<title>Sheet.9</title>
						<path d="M10.22 573.58 L5.65 573.58 A5.71247 5.71247 -180 0 0 4.77 584.92 A6.10282 6.10282 0 0 1 4.82 582.8
									 A3.60838 3.60838 0 0 1 5.65 575.67 L10.22 575.67 A3.6179 3.6179 0 0 1 10.22 582.91 L8.97 582.91
									 A2.72295 2.72295 -180 0 0 8.7 584.05 A2.64678 2.64678 -180 0 0 8.88 585 L10.22 585 A5.71247
									 5.71247 -180 0 0 10.22 573.58 Z" class="st61"/>
					</g>
					<g id="shape10-484" v:mID="10" v:groupContext="shape" transform="translate(17.2798,-4.49721)">
						<title>Sheet.10</title>
						<path d="M11.17 573.65 A6.66455 6.66455 0 0 1 11.23 574.53 A5.96001 5.96001 0 0 1 11.11 575.77 A3.60838 3.60838
									 0 0 1 10.28 582.91 L5.71 582.91 A3.6179 3.6179 0 1 1 5.71 575.67 L6.97 575.67 A2.68486 2.68486
									 -180 0 0 7.24 574.53 A2.64678 2.64678 -180 0 0 7.05 573.58 L5.71 573.58 A5.71247 5.71247 -180
									 0 0 5.71 585 L10.28 585 A5.71247 5.71247 -180 0 0 11.17 573.65 Z" class="st62"/>
					</g>
					<g id="shape11-488" v:mID="11" v:groupContext="shape" transform="translate(0,-0.832336)">
						<title>Sheet.11</title>
						<path d="M14.2 583.73 L13.15 584.79 a0.714059 0.714059 0 0 1 -1.0092 0 L0.42 573.1 A1.43764 1.43764 0 0 1
									 0.42 571.07 L1.48 570.02 L14.2 582.73 a0.704538 0.704538 0 0 1 -1.77636E-15 1.0092 Z"
								class="st63"/>
					</g>
					<g id="shape12-490" v:mID="12" v:groupContext="shape" transform="translate(0,-11.5521)">
						<title>Sheet.12</title>
						<path d="M12.93 570.27 L14.1 571.44 a0.714059 0.714059 0 0 1 -1.77636E-15 1.0092 L1.59 585 L0.42 583.84 A1.43764
									 1.43764 0 0 1 0.42 581.81 L11.92 570.27 a0.714059 0.714059 0 0 1 1.0092 0 Z" class="st60"/>
					</g>
					<g id="shape13-492" v:mID="13" v:groupContext="shape" transform="translate(31.3767,0)">
						<title>Sheet.13</title>
						<path d="M0.21 583.73 a0.704538 0.704538 0 0 1 -7.04992E-15 -1.0092 L12.94 570.03 L14 571.09 A1.43764 1.43764
									 0 0 1 14 573.11 L2.27 584.79 a0.704538 0.704538 0 0 1 -1.0092 0 L0.21 583.73 Z" class="st63"/>
					</g>
					<g id="shape14-494" v:mID="14" v:groupContext="shape" transform="translate(31.4789,-10.7143)">
						<title>Sheet.14</title>
						<path d="M2.39 570.27 L13.9 581.81 A1.42812 1.42812 0 0 1 13.9 583.83 L12.73 585 L0.21 572.45 a0.733101 0.733101
									 0 0 1 -5.9952E-15 -1.01872 L1.38 570.27 a0.714059 0.714059 0 0 1 1.0092 0 Z" class="st60"/>
					</g>
				</g>
			</g>
			<g id="group1057-496" transform="translate(59.2936,-56.449)" v:mID="1057" v:groupContext="group">
				<title>Network Security Group.1057</title>
				<g id="group1058-497" v:mID="1058" v:groupContext="group">
					<title>Icon-networking-67</title>
					<g id="shape1059-498" v:mID="1059" v:groupContext="shape">
						<title>Sheet.1059</title>
						<path d="M16.22 574.42 C16.22 579.76 9.78 584.05 8.38 584.93 a0.506969 0.506969 0 0 1 -0.529011 0 C6.45 584.05
									 0 579.76 0 574.42 L0 568.01 a0.506969 0.506969 0 0 1 0.495948 -0.506969 C5.51 567.37 4.35 565.16
									 8.11 565.16 C11.87 565.16 10.71 567.37 15.73 567.5 a0.506969 0.506969 0 0 1 0.495948 0.506969
									 L16.22 574.42 Z" class="st41"/>
					</g>
					<g id="shape1060-500" v:mID="1060" v:groupContext="shape" transform="translate(0.672285,-0.823307)">
						<title>Sheet.1060</title>
						<path d="M14.88 575.3 C14.88 580.19 8.97 584.12 7.68 584.93 a0.473906 0.473906 0 0 1 -0.484927 0 C5.91 584.14
									 0 580.19 0 575.3 L0 569.42 a0.462884 0.462884 0 0 1 0.451863 -0.462884 C5.05 568.83 3.99 566.81
									 7.44 566.81 C10.89 566.81 9.83 568.83 14.43 568.95 a0.462884 0.462884 0 0 1 0.451863 0.462884
									 L14.88 575.3 Z" class="st42"/>
					</g>
					<g id="shape1061-502" v:mID="1061" v:groupContext="shape" transform="translate(0.705348,-0.823903)">
						<title>Sheet.1061</title>
						<path d="M7.41 575.91 L7.41 566.82 C10.86 566.82 9.8 568.83 14.39 568.95 a0.473906 0.473906 0 0 1 0.451863
									 0.473906 L14.85 575.31 A5.3893 5.3893 0 0 1 14.85 575.91 L7.41 575.91 ZM7.41 575.91 L0 575.91
									 C0.44 580.51 5.93 584.17 7.16 584.93 a0.429821 0.429821 -180 0 0 0.198379 0.0661264 L7.41 585
									 L7.41 575.91 Z" class="st43"/>
					</g>
					<g id="shape1062-505" v:mID="1062" v:groupContext="shape" transform="translate(0.697126,-0.823903)">
						<title>Sheet.1062</title>
						<path d="M0.43 568.95 C5.02 568.83 3.96 566.82 7.41 566.82 L7.41 575.91 L0.01 575.91 A5.3893 5.3893 0 0 1
									 0.01 575.31 L0.01 569.43 a0.473906 0.473906 0 0 1 0.4188 -0.473906 ZM14.82 575.91 L7.41 575.91
									 L7.41 585 a0.429821 0.429821 -180 0 0 0.198379 -0.0661264 C8.89 584.17 14.38 580.51 14.82 575.91
									 Z" class="st44"/>
					</g>
				</g>
			</g>
		</g>
		<g id="shape114-507" v:mID="114" v:groupContext="shape" transform="translate(-16.2857,15)">
			<title>logo png.114</title>
			<rect v:rectContext="foreign" x="0" y="506.364" width="148.875" height="78.6361" class="st2"/>
			<image x="0" y="506.364" width="148.875" height="78.6361" preserveAspectRatio="none" xlink:href="data:image/png;base64,
						iVBORw0KGgoAAAANSUhEUgAABqYAAAODCAYAAADetLAsAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYl
						AUlSJPAAAGu3SURBVHhe7d3BkeNImibsFqE02LKRoATYw4iQt/9aIrQZBdgSIQXYQx1WgLzunlqEFCE1mLJRoH/zGrAnyh0RQcL9dTjJ
						5/CYdX+VEXDAQTD4vXTgb//85z//BgAAAAAAAGlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAA
						AAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABA
						QlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAA
						AAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICE
						pgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAA
						AAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlN
						AQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAA
						AAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoC
						AAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAA
						ACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUA
						AAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAA
						SGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAA
						AAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ
						0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAA
						AAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACCh
						KQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAA
						AABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJT
						AAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAA
						AICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYA
						AAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAA
						AAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEA
						AAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAA
						EpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAA
						AAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAk
						NAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAA
						AAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEho
						CgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAA
						AACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAU
						AAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAA
						ACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkA
						AAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAA
						QEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAA
						AAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACA
						hKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAA
						AAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAACA1V0ul3+/XC6/Xi6X3zbfLpfLP3Zc/3vxpfxc/bsAmKcpAAAAAACs5HK5/LSFUL9f
						Lpcfl8vlnwOU31N+Xwmrfqq3CUBGUwAAAAAAONubMKqseqpDpdGsogKYpCkAAAAAAJxlC6TKbff+2AmQUgRTAJM0BQAAAACA2U4KpK4E
						UwCTNAUAAAAAgJlKMDTw2VFHCKYAJmkKAAAAHHe5XH7Zmmu/1P8NAGhdLpevO0HRbIIpgEmaAgAAALe7XC5fLpfL7x98y7vcjuhbeXh7
						/bMA8Mq2W/d933nvPINgCmCSpgAAAMDntkDqvTDqPSWkElAB8PK2Fcb3vo8mCaYAJmkKAKv6zy//9v/955d/+zsc8D/q8+kW/+v//e1/
						/K//97e/wwH/sz6fGO/N7dLO8lM9JlrbN6HrYzdLZI62fSorpOqG1j3+kRofAKxu+zuufFmjfn88k2AKYJKmALCq//zyb//xn1/+7Z9w
						wN/r8+kWW8DwTzjgH/X5xHhbY79uKMxk1csNynHaOXazDG8wDb7lUPk9wikAXsqioVQx/O8GAPY1BYBVCaboIJhiNsHUBAsEU+b5BgND
						nCOGN5gC+yOcAuBlbF/wWOn2fW8N/7sBgH1NAWBVgik6CKaYTWAxwQLBVPFzPS7+23Y7vfqYzTS0wXS5XL7ubGOEr/W2AOAZBb7gMdLQ
						vxsAeF9TAFiVYIoOgilmE0xNsEgwJVD4wIDnMPUa1mCaELINGysArOhyufy28/63Eu/FAJM0BYBVCaboIJhiNsHUBIsEUz/qcfFftlv1
						1MdrtmENpsvl8m3n94/0rd4mADyLCV/wGGHY3w0AfKwpAKxKMEUHwRSzCaYmWCSYKr7UY+PP+fn7zrGabUiDaWLI5taQADylxW/hdzXk
						7wYAPtcUAFYlmKKDYIrZBFMTLBRMWemyY5EHmw9pMJXwced3J/xabxsAHt0iX1a5xZC/GwD4XFMAWJVgig6CKWYTTE2wUDBVWOnyxkK3
						6xnSYJr4TIzf6m0DwCPbVh3/sfOet6IhfzcA8LmmALAqwRQdBFPMJpiaYLFg6tB15llNeB7TrYY0mCYGU7/X2waAR1ZWA++83yWVFdu/
						b6u0yhdlaqX+9Z1bCw75u+FWl8vlF7eEBl5VUwBYlWCKDocaxoIpOgimJlgsmPpRj+9VldVjO8fnLEMaTBODKdcOAJ7KxFv7li/F3PW+
						v/3NUt7jr2O86+eP2MKxEoxdt+m9H3hJTQFgVYIpOgimmM0HzAkWC6aKeDPjEUwMcW4xZE4m7tPXetsA8Kgm3dq33Caw+/1+W9k1/NbM
						260My+8uq7j2bmnocwPwkpoCwKoEU3QQTDGbD5gTLBhMuQ3b3G9G36K7UVVMDKY8YwqAp7GFMfV73Ujldnw/1ds927YSq9wy8JZbG/vc
						ALykpgCwKsEUHQRTzOYD5gQLBlPFcs2RmcpzEnaOyZlGBVMzvvFdeM4EAE/jnRVCo5TfvczfXdvzot57dtVHfG4AXlJTAFiVYIoOgilm
						8wFzgkWDqV/rcb6SBedkSDBVhJtrxVINNgDosQU19XvdSMPe44/avpBTVoX1rBb3uQF4SU0BYFWCKToIppjNB8wJFgxBiu/1OF/Fdtua
						+nicbVjTasLt/NwKEoCnsd3Krn6vG+WU98w3z4u65RZ9t/K5AXhJTQFgVYIpOgimmM0HzAkWDaaKX+qxvoLt9jX1sTjbyGCqNKNSq6bK
						7x3+wHUAOEv4+VKnvGfujGMEnxuAl9QUAFYlmKKDYIrZfMCcYOFg6ms91lcQDG16DAumiuAztF76FpAAPJ8Dz1q61Wl/Z++MZYTT9gfg
						TE0BYFWCKToIppjNB8wJFg6m/qjH+uy229rUx2EFQ4OpIrAy7JTbEQFA0s773SiHPtuNsDOWEXxuAF5SUwBYlWCKDoc+vAim6OAD5gQL
						B1PFS62AWXguhgdTxcDnTb3k6joAntt2+9v6PW+U026ZvDOWEXxuAF5SUwBYlWCKDoIpZvMBc4KFw5DiZc6B0iDa2f9VRIKpYrut39Hb
						F5af+1L/TgB4BuX9d+e9b4h6WzPVYxnkZf5mBHirKQCsSjBFB8EUs/mAOcHiwVRxyoO5Zws/3LxXLJgqtm+El9VTP3a2vaf8u/Lvf6p/
						FwA8i2Awderf2DvjGeHUfQI4S1MAWJVgig6CKWbzAXOCBwimfqvH/Gy2YOboqqEZosHUW1sTroROJagr5+ZV+f+lPm0sAHAmwdRdTt0n
						gLM0BYBVCaboIJhiNh8wJ3iAYOpHPeZnU56ltbPfKxEGAcBkwWDq1C/97IxnBJ8bgJfUFABWJZiig2CK2XzAnOABgqniqZ8jdMct7M4i
						mAKAyQRTd/G5AXhJTQFgVYIpOgimmM0HzAkeJJj6Vo/7WQSbTiMJpgBgsuDfCIIpgCfRFABWJZiig2CK2XzAnOBBgqnip3rsz2B7dlK9
						r6sRTAHAZIKpu/jcALykpgCwKsEUHQRTzOYD5gQPFEwdugatrIRtO/u5IsEUAEwmmLqLzw3AS2oKAKsSTNHhUFNYMEUHHzAnCART3y+X
						yx879V4/6rE/uhK27eznUeU5VeXY1/URBFMAMJlg6i4+NwAvqSkArEowRQfBFLP5gDlBIJgqvy91e7pf6vE/si1MqvfxqN8Cc3klmAKA
						yQRTd/G5AXhJTQFgVYIpOgimmM0HzAkCYUb5fb/s1Ef4vR7/o7pcLl929q/Hz4G5vBJMAcBkgqm7+NwAvKSmALAqwRQdBFPM5gPmBIEw
						4895C91Wrtwi8Kd6Hx7R5XL5trN/R33bfufoubwSTAHAZIKpu/jcALykpgCwKsEUHQRTzOYD5gSBMOMaTP26899G+LXeh0ezrW6q96vH
						n8ckMJdXgikAmEwwdRefG4CX1BQAViWYooNgitl8wJwgEGZcg6mfthVO9X/v9b3eh0ezPQ+q3q+j/njze0fP5ZVgCgAmE0zdxecG4CU1
						BYBVCaboIJhiNh8wJwiEGf+at/JMqJ3/PsLP9X48ksvl8mNnn476+ub3jp7LK8HUSbbVdaUx+fct0Py6zXOthMDl9pl1vfxMUX7HQ79u
						uM2bc6asWr3Of1GfG3vnSVGef1d+/ilum5qy89ost2etj+ve67L8u7evy1/q3/1qtudSvj2WH13r6mN4PY5P+T4lmLqLzw3AS2oKAKsS
						TNFBMMVsPmBOsDV56g/3Pd4GU6NvWXf1rzDm0QRucfivsCEwl1dP2fBbzfZ6KaHAtSFbz8MIpVFefvefzdx6DK9ia/Zew5rPnodX/vu1
						Eb5UiPBmP8r4PtuPo8oxKudkuXa9bLi5hSclOCnHOrIaePsyR9nGU742t5XU5Rp3fe2N/JLG1dtrXNnWQwesgqm7+NwAvKSmALAqwRQd
						BFPM5gPmBIEG+F/mLfD7i3/dvu7RDD4eM451MbRJ+qaZPtrDNc23Zndp+qdChVuURnsJHYY3cLewrZ6nEe6e661JXRr/vaHCaQ3d7XiW
						4CL1Wr9FCRPKOfulHt+zefP6TAQotyjzXOb77vN9FYtc48q2y3XjlGC58z0vtfL8Gt6dpR7PCOV1Wm9nhId9/QGvoSkArEowRQfBFLMJ
						piYINDjrsGT0CqGrh2uKBlaQ/Vr9/tFzeTU6mCqNnnobIwwdZ8p2HpzZ7P7InyFVPeajgt/2v3mut2vQyGM9NZh6E0ad2dh/Twn5yrl8
						SsM/ZTtnVjveZTzlPBgeII+2QKD3kTKmqWFf8D2POW5+vwE4Q1MAWJVgig6CKWYTTE0QCDOaeRuwQmHPt3o7q9sadfV+HNWsGgvM5dXQ
						pkywSTd0nKNtIU0Jfupxr6i8Zss8dTXBzwymtuZ44jUxJZh6sPOlKMf603lZWSDETJhy/h2xaKD3kbIaKR5QBd/zmOOhr2vA82sKAKsS
						TNFBMMVsTcDBeIHGbTNvgwOZt+INpZEGB3TNc7YCc3k1tCkTbNINHecoW8CQmpu0JgC9x1nB1LYiov6ZUaLBwIOfL8XDBVTbMX+UQCV6
						/h3xIIHeR6IBVfA9jzke6noGvJ6mALAqwRQdBFPM1gQcjBdogDbzFriF3dVyDbr3BG5p2DTRAnN5NbQpE2zSDR1nr+28T83JNPV+3eOM
						YCr4TJaryHXnWc6XN8q+NNep1QS/OJESOf+O2J7b9siBVK2cC12rRPcE3/OY4933G4AVNAWAVQmm6CCYYrYm4GC8QCN0d94C2yl+1NtZ
						1eBv4888xsXQpkywSTd0nD2C+zhdvW/3mB1MTQiliqHBQGmEP9P5UimrRA/9/Zi2BYEjr8uzDD3/jtiO3SPdZvIeJWjbvb4c9cSv71cx
						9HwAGK0pAKxKMEWHQ40FwRQddpvvjBUIM3bnLbBi6Gr5hsH2rJt63D1+rbexbWf0XF4NPcbBJt3QcR6xzfUjNrvfVe/jPWYGU5NCqWJY
						MPCM58s7yrVp+EqUo7bjPvLWqjMNO/+O2N7LH/XY3aO5Xe5Rwfc85mjebwBW0hQAViWYooNgitl2Aw7GCoQZ785bqJn1e72d1QxumL/7
						zJ/AXF4NbcoEm3RDx3mvZ23Y1vt5j1nB1OTbsQ0JBsLPwVpReW38Uh+H2YJfkphlyPl3r21l38j3skdQQuPuQDX4nsccp/5tAfCZpgCw
						KsEUHQRTzPZuwME4gTDj3XkLNme6G0cpWzNvZFjxbhAXmMuroU2Z4HkwdJz3mByMTFXv6z1mBFPbc27q/57UHQy8YIP/rd0VnzM8QShV
						dJ9/99rex15hZd+e7kA1+J7HHKf9bQFwi6YAsCrBFB0EU8z2bsDBOIEw4915255LUf/7EQ5dn2YINELfbZAF5vJqaFMm2KQbOs5bPXvI
						UO/vPdLBVCD4vcXhYODFG/xvTQ+ngufibIfPvyMe/LaHo3SFU8H3POY45W8LgFs1BYBVCabocKjxK5iiw7sBB+MEwowP5y30wPTv9XZW
						sT1IvR7vUR/uZ2Aur4Y2ZYJNuqHjvMWzh1JFvc/3CIYB12DqjON/KBgQSjWmhVPblyKeJVw5dP4dIZT6i8PhVPA9jzmm/20BcI+mALAq
						wRQdBFPM9mHAwRiBMOPDeQvedutQwygp0JT/sJEbmMuroU2ZYJNu6Dg/c1IoMl293/cIvAauyu8tTfO6PsPdwYBQ6l0fXtNGebJjf/f5
						d4RQatehcCr4nsccU/+2ALhXUwBYlWCKDoIpZvsw4GCMQJjx6bwNXkV09e6zl84yOLgoDbEPn6UVmMuroU2ZYJNu6Dg/Mnhul1bv+z3C
						wVRi9eUt7goGhFKfir5ug9ebs9x1/h0hlPrQp+/FtSc8B19N9BoF0KspAKxKMEUHwRSzfRpw0C8QZnw6b6EmzR/1ds60NaPrMfb4NHgL
						zOXV0KZMaP6LoeN8T3D8S6r3/x7BYGr0s9vucVcw8Eoh5kGl0f9zfdxGCD7X8Ex3nX/3EqTe5MPb6tZe7T3jCU352wLgqKYAsCrBFB0E
						U8z2acBBv0CY8em8BZuFU24LdYvL5fL3nfH1+PT2QYG5vBralAk26YaOc0/wVpTLqo/BPYLBVGLV5a1uDgaC5/qzuavRf6snDQVvPv+O
						CL6PPJuv9bF7j+vAw4v/bQHQoykArEowRQfBFLN9GnDQL9CEumneQrfhumnbMwxunN/UtA3M5dXQpkywSTd0nLUtUH2521vVx+EewWDq
						TDcFA0+670k3N/pvEfwCxEfKSqNyHf66XeeuyvtdqY94X7jp/DsieG1+Vl/qY7jHcX140b8tAHo1BYBVCaboIJhitmVChmcWCDNumrdg
						0zZyS6h7BFbV3LQSLDCXV0ObMsEm3dBx1ha7vVUZS2l2XxvfZYVeeU0V5X9f693N8Po43CP4Oj/Tp8HAdju0FULMMoZyDhRvg5K350fq
						unHEsNfwFg7Vv3+0cnzLqqybAoo3Y7u+Tstr+N7z5NPz74iFXqvlelXOybfXt6syp+W/rXItvul5U8H3POYYdl0CSGgKAKsSTNFBMMVs
						NwUc9Ak0JW+et96G+TuGfuv+iMGrwW5qfG3bHT2XV0ObMsEm3dBxvhUc861KI7aM4dA+bkFJCUxLE/2uRnj9u+6xULN7pE+DgcHXgHtc
						g5LyDK67QvpthVH5uSNhySg/6nEdNWEfyuvxpmvzZ968Nutt7Pn0/LvXdn1IvB/fomy3BE7lGNx1PMstbreAL/Xed4tbnv949vsHfQ69
						7wLM0hQAViWYooNgitluDjg4LtDQuXneAs9hKoY1No8I3D7q06bXm22PnsuroU2ZYJNu6DivtuZnva0ZSmO9NGzvChhusTWBbwpP6p+9
						x6LB1HVFRn27tevqoc8CjQ+DgcCKyVuUcd+0svIWW1BRQqozwooPj+8twuddOT8+febfETce9+7jU5u0uqxWgrhh1+ztvbe8jj97/SYM
						249a8Fwefh7dY2c8I9z89yfAM2kKAKsSTNFBMMVsPmBOEAgzbp63rQlX//wId91WaaRA6HJzAzQwl1dDm26BY3Q1dJxXJ9w2qjRWh63G
						+MjWzP1wpUb9M/cINlXvVRr9JQi/KeR7s3po79h82ND9JFQYrWwrct5fbcdhZrP/5lWi7wkHLTdfk3tsr529a/qH59+9Tgjeyz7d9Do8
						Yvu7IvUe856b/+65V/AaOvQ8utfOeEaIzQPAypoCwKoEU3QQTDGbD5gTvNP46nHXvL3T+O31rd7OLIOb0net/grM5dXQxnewaTh0nMXW
						lK+3k1RWMcWatu/5KKCq/+09gk3VW3UHNzurWN5t6AbP7T3Tblu6HYObVtgN8u4xvkXwWtg1riN2AqqhYwgeq1oJHIet6vvMFrjN/FJB
						13XmPcFr6NDz6F474xnhrr8/AZ5FUwBYlWCKDoIpZvMBc4JAU+queQs2Xc5o7o8OMe667gbm8mpowy3YvB89ztKMn7VSZGrT9j3b6/Ev
						zdz639wj+Pq+xfDG67bqavd1OfF8Kds4ZVVo6Pare7pWTe38vlEOj6nXm5Vrw87ria/Pck2ZstLsre01uRu4B9z1t8+tgnM07Dw6Ymc8
						I0TmAGB1TQFgVYIpOuw2Yj4jmKKDD5gTBMKMu+dt8Cqjq0PXrB6BY3lXEzSw/avRgc+jBFOpcdZiz6w5agsg/gxZ6v92j2BT9SOnBDeT
						zpfTz5VAAP+eQ43z4C1i735vG23bt2HXueB7xlsllLrrvWy0ieHUsLm5Cl5DD72+RtkZzwinv0YBztAUAFYlmKLDoSavYIoOPmBOEGhM
						3T1voW/h33UbvF7brdDqMfT4vd7GZwJzeTW02RZs4A8b58TVL6c3bd+zndPf6/o9gk3V95wS3Ew6X07Ztz2Twqk/6u3eInjOndrIHy14
						nN5a5vo2KZy6+337M8F5OvV83hnPCHf//QnwDJoCwKoEU3QQTDGbD5gTBMKMu+ct+A33YUHFZ8rzXna23+PusQfm8urusXzkQYKpRFha
						W6ZpmxJsqr7nlOBmUlAzfRXYR4Kv47fu3ufgOXdqI3+0CUFNCVKn31L3I/VtSkOGXtOf9XzeGc8Id//9CfAMmgLAqgRTdBBMMZsPmBME
						woxD8xZqkg3/9vJ7Bq+WOLTaKzCXV8MCnyLY0B42ztDtJd9aZvVLUrCpuue0Z3RNaHif2kR+T/Cac/Wt3uZngufcknNwRPDLIG8Nux6P
						Mmll46HPS+951vN5ZzwjHPr7E+DRNQWAVQmm6HDog5Zgig4+YE4QaCwemrfSpN/5Xb1KA2rot5f3BFZLHLreBubyamiDcfVgqqzS2Pnd
						ow0Z6+qCTdXa3QHGKKFr11tdt1NMCtzCdM9d1/DgOXfovW1FE1aEfq23uYrA+3Vt6Os1eD4LpgCeRFMAWJVgig6HGqWCKTr4gDlBIMw4
						PG+hVQfxVRSBcd/ViH0zjtFzeTU0RHmAYOrbzu8e6dSG4EzBpupbUwLo9wRu41kbcl6nBF/PV3ddw4Pn3KFnXq0o8J711qmvx1sE3yuv
						ht3CMHg+n/o+tDOeEQ7//QnwyJoCwKoEU3QQTDGbD5gTBBo0h+ct9E3mod9ergVWSxy+/WBgLq+GNsaDjezucU64xVX0fFxNsKn61tkN
						1uRtHw9fD2aZcHu0u45B+Jy7+5lXq5mwyu2uIPEM4XOkGHYMgmM9+7pZj2eEw39/AjyypgCwKsEUHQRTzOYD5gSBMOPwvAUbnLFn+QSe
						jXU4XAnM5dXhMe1ZPJhKhKNvdY/xkQSbqlenrs4IBNO1YSsvksKrxu5aqRQ+5x4+WA7fxu/Q8xHPEF41NuzWosHzWTAF8CSaAsCqBFN0
						EEwxmw+YEwTCjK55CwQ9ReR5F4EgraupF5jLq6FhyuLBVPI2fl2vjUcUbKpe3bWaZrRwk/9hzpcJq3Bu/nLBhFWPp55zvcLXuFPDjnuE
						v4RwV5j6keA19NS52hnPCA9zzQQYqSkArEowRQfBFLP5gDlBIMzomrfQCoRhTaK3Ao2tQ9fZN+MZPZdX3YHPW4sHU/XvHKl7fI8m2FS9
						ujmwSAg3+YfdDmyG8AqUu66NOz8/2sOGU4O/TFF7iBV+xYQAc8i1KXgNFUwBPImmALAqwRQd7moKXAmm6OAD5gSBMKN73kINzuFN3sCz
						ZbpuSRaYy6uhgcqqwVSwAVh0vy4eUfiYdq0wHCHY5I+E6Unh1WN3BUHBa+FbZRsPE8QUoS9+XA27fd0sjxAsB6+hgimAJ9EUAFYlmKKD
						YIrZfMCcINDA6563wEqkYmjTLNAs6h5fYC6vugKf2sLBVGpcxZAm5aMJvE7euiusGC3c5D91344I387vrmc7hV/LtbKtri8VzBIODx/u
						Ghf6W+NqSPATvIYOGd9RO+MZofvvT4BH1BQAViWYooNgitl8wJwgEGZ0z1vg2U1Xw77dHngW1pd6G/cKzOVVV+BTCzaNu8YZPH7lXH6I
						xvVowaZqcWojPNzUPnXfjgqsIv2XelsfKdfT+ufDymt8+YAq8L711rD311nCYWr330JF8BoqmAJ4Ek0BYFWCKToIppjNB8wJAs34IfN2
						uVy+7vzuXkMaMYFnUwy5JVlgLq+6Ap/awsFU/ftG6V4N96iCTdXi1EZ48DwuTt23o8K3RrvrmIS+3PCZpQOq4HvEkPewMwTPkyHHJHgN
						HfL30FE74xlhyN+fAI+mKQCsSjBFB8EUs/mAOUGgUTVk3kLfZB7VKBp9O6QhDaLAXF51BT61YEP/8DjDt2V7yNUvIwSbqnetoEkIvt4e
						7vlSV8HXdnHX6zu8OugWZfu/1OM6084YR3m4W09eBV/HQ65RwWvokL87jtoZzwhD/v4EeDRNAWBVgik6CKaYzQfMCQJNmWHzFhhbMeKW
						eaNvV3XXSoD3hI5XcVdD+DPB5vXhcYZv/TVkfh9RsKk67DpzVOA6cHX6vh0VnO/iroA39OWGI8p1+a6xJ4SPx6khR4/g+1HRfe0PvqZO
						nbOd8YzwsNdOgB5NAWBVgik6CKaYzQfMCQJhxrB5Cz3Dpeub3YEAY9ht3gJzeXU48NkTbAQeHmdwTA+7+mWEYFN12OvmqJ0xjXJqw7hH
						eOXh3ccleE08otwyrtyitjusOCL4WiwOX3vPFlgB/Vb3cQnO292vp5F2xjPCsL8/AR5JUwBYlWCKDoIpZvMBc4JA427ovIWe/3D4+R+B
						Z6h0r+C6CszlVXdz7a1gCHR4nMHbfg19PTyaJ26qjn7O3FuH/t5axc7+jHL3nIdXCfUo7yOHr1dHhL7ocbXULQvvEbxGFd1zHBzf3a+n
						kXbGM8JLv98Cr6spAKxKMEWHQ40SwRQdfMCcIBBmDJ237Rvm9TZ6HbqeBZqcQ555dRWYy6vu5tpbiwZTqWP3td7WK3nipmpqv4rD5/EK
						dvZnlEOr5ILXmxHKdWfKfCePQ72tRxJ+LR/6W2PS+M6+htbjGWHo358Aj6IpAKxKMEWHQx+uBFN08AFzgkBDfui8BcKg4lAgFGjsDW0M
						BebyamjjNHAcrw6P83K5fN/5fSMMneNHE2yqnvrMnuB+FYfP4xUEr0OH31uCr+9R4gFV8Lr70MFUUe/PQN3X/+C1pntsPXbGM8LhawTA
						I2sKAKsSTNFBMMVsPmBOEGgiDp+3wBiLu289VAKtnd/TY+izRkLHqRjaMA02SA+Pc+d3jTLsVo2PKNhUPTzXIwSeNffW4VuNriB4HTr8
						3rJ9wSFxW9jRyi1FI/MfWn1cHJ6XVezs0yjd4U/wGto9th474xnh4c9FgCOaAsCqBFN0EEwxmw+YEwSaiMPnLdQE/r3ezkcCz+c4dFuq
						jwTm8mpoCPBiwdThMT2DYFP11OMaPIefYfVJ6jrU9d5SvozwIOFUGeOhv7k/suq8rGBnn0bpDn+C19DusfXYGc8ID38uAhzRFABWJZii
						w6EPyYIpOviAOUGgWRWZt8BqpdL8u/mb6YHjNHwlTWCMV0NDgGBT//A4d37XKIfH9AyCTdVTj2vwHBZMva/7veWBwqmiHMeb36M+s/K8
						nG1nn0bpDn+C19DusfXYGc8ID38uAhzRFABWJZiig2CK2XzAnCDQrIrMW6gRfNNzagLPufqj3sYIgbm8GhoChOayODTOrVld/65RDo3p
						WQSbqqce1+A5LJh635D3lu31vvozp65KiHb3bWf3rD4vZ9rZp1G6w5/gNbR7bD12xjPCw5+LAEc0BYBVCaboIJhiNh8wJwg0qyLzFgiH
						ipvGGng2x9d6GyME5vJqaAgQbOofGmew8VccGtOzCB7bU49r8BwWTL3vpuv1LcpKpHI71Z1trOqmL1F85BHm5Sw7+zRKd/gTvIZ2j63H
						znhGePhzEeCIpgCwKsEUHQRTzOYD5gSBZlVs3kKNxJ/r7dQCt376dJtHBObyamgIEGzqHxpnsPFXHBrTswge21OPa/AcFky9b/h7S3mW
						U+D6ntIVTj3SvMy2s0+jdIc/wWto99h67IxnhIc/FwGOaAoAqxJM0UEwxWw+YE4QaFbF5q08l2lne70+XL1UmoE7P9MjeXxGz+XV0BAg
						2NQ/NM5g4684NKZnETy2px7X4DksmHpf5Nq5rcZNjXm0w+FUcB8j8zLTzj6N0h3+BK+h3WPrsTOeER7+XAQ4oikArEowRQfBFLP5gDlB
						oFkVnbfL5fJjZ5s9PnzeU+BZJIcbi58JzOXV0BAg2NQ/NM5g4684NKZnETy2px7X4DksmHpf+r2lfPFh9PtLwqFz/1HnZYadfRqlO/wJ
						XkO7x9ZjZzwjPPy5CHBEUwBYlWCKDoIpZvMBc4JAsyo6b6GG8Jd6O9u2ftn5tz0+DMF6Beby6lAj9D2hOSwOjTPY+Ct2z61XETy2h+Z6
						lOA5XPxUb++RBK9DH65uHeUBbu9Xxnb3ORKcl+/1th7Nzj6N0n39D15DBVMAT6IpAKxKMEUHwRSz+YA5QaBZFZ237bZL9TZ7fau3s23r
						951/2yPaWA3M5dXQECDY1D80ztLk3fldo5za/DtbsKl6aK5HCd1W9OrUfeu1sz+jTHstbdeEElCtuoJq9z3rI4H3s3+pt/VIgteoovu1
						HBzftNfTnp3xjBD9+xNgVU0BYFWCKToIppjNB8wJAmFGfN5CDbafq22UxuTob83/ZRujBebyqru59tZqwdQ2pvp3jRINI1cXbKoenusR
						gvtVnLpvvXb2Z5RTGunbcwZH39J1hLvOk+B1VzD1vrvmaE9wfKe8nq52xjNC/O9PgBU1BYBVCaboIJhiNh8wJwiEGfF5CzVq/tKk2ZqR
						9b/pMeO4jJ7Lq+7m2lvBBunhce78rlHi876y0Gu1ODzXIwT3qzi1YdwjcPvTt1aY88SXIo666xZ6wetucerc9NhWxtX7M0S9rSOC15pT
						rzM74xnhpd9vgdfVFABWJZiig2CK2XzAnCAQZkyZt8Atln6Ef/+v9T6MFpjLq6FNx2CD9PA4A/N9FX2u2OqCTdXDcz1C+PaPD7vKLjjf
						xalzfrXNfbmGpa4Z97j5mAS+bPFW97OUzhJ8PxJMfWBnPCNM+fsTYDVNAWBVgik6CKaYzQfMCQJhxpR5C33L+c8mX6ARNCWcCMzl1c3N
						z1sEG4GHxxk8dkX0Fo4rC7yWrg7P9Sg7YxplyjU0IfjaHtLkH20Le84MqG5+1lTwtVicGnL0CF77h7yOg/N26pztjGeEIccc4NE0BYBV
						CaboIJhiNh8wJwg0ZabMW2jFwu/b7x59u6YpKyACc3k1NAQINq8PjzMw52/FV8utKthUPTzXoySfO1Rv61GUoKTel0H+sqJ1Ndt5nrr+
						fuanejx7SkC+87OjTHnfTwg8S/Lqz78negWvoYIpgCfRFABWJZiig2CK2XzAnCDQTJs2b6EwIdG8+6Uee0JgLq+GhgCLBlOpMRU3r2p4
						NsGm6uG5HiX4eiumXDNGC64eeojX0EkrqG4Ovnd+dpQpq4JHC73fXx363FQLXkMFUwBPoikArEowRYdDH7AEU3TwAXOCQHN12ryFGjaj
						m4p3PaC+R2Aur4aGAMEQ6PA4Q+fSVflG/k2rGp5N8LgenutRgudxcehvrjOFm/ynNtHvFT43ajevzAm+RxQPF6aGn7s15BoVvIae+pra
						Gc8I0/7+BFhJUwBYlWCKDoeaJIIpOviAOUGgUTV13gJB0mg3f5u9V2Aur4Y02K6CTdvD4wzdGvKtaefBSoJN1cNzPcrlcvmyM65RpgXa
						o4Se+3d1+nzfqwQ1yds9vnHzuVJuK7vz86NMuWXtSMFbTw67HWfwGiqYAngSTQFgVYIpOgimmM0HzAkCYcbUeQt/47nX1JUygbm8GtoU
						XjGY2saVDDmnvi5WEWyqds31COEVQsXP9TZX9ghN/tm2wDseTtXbfU/4/XLp54DtCT5fatj1PngNFUwBPImmALAqwRQdBFPM5gPmBIEw
						Y+q8bY2/VHOp1823WBohMJdXQ0OAhYOpxDPL3uoa3yMKNlWXOJbhMPPUxvE9wiHd1PeU0SaFUzfdRi88T8USr8tbhEO6Ya/d4DV02BiP
						2BnPCA99rQA4qikArEowRQfBFLP5gDlBIMyYPm8TAoWjbmoWjhKYy6uhzcaFg6lko7KY/to4W7Cp2jXXo4SvPQ+zAiX4mi4O/f25ki0Q
						Sn6B4ubXQzhMnfpljB7B98ti2Ht/8BoqmAJ4Ek0BYFWCKTocagwIpujgA+YEgebM9HnbnuVRj+NsNz/3Y5TAXF7d3PS8RbCJ3TXOCc+Z
						Kr7U231mwaZq11yPMiHMXP7ZZBNWrT7ULQ3fE7zuFTe/HsJharH8fAWvS8XQQDk4VsEUwJNoCgCrEkzRQTDFbD5gThAIM06Ztwm3SrrX
						9IZyYC6vbm563iLYoO0eZ/I5OZuyWmHac8fOFmyqds/1CBPCzKnPqTsi+Houpgf8KeFz5ebXQwnHd35+pG/1NlcTfK8svtbb6xG8hj5j
						MPVHvR2AV9AUAFYlmKKDYIrZTgk4Xk2gQXPKvE1YuXCPU5rJgbm8urnpeYtgI7t7nJPOo+Ubt6MEm6rdcz3KhDBzaKN7pAmrpQ797bmq
						4Bco7no9hOesWHZlaPCadDXsNn5FcLzPGEz9s94OwCtoCgCrEkzR4VBzQDBFh1MCjlcTCDNOmbcJDdJ7nPKcjcBcXt3V9PzM4sHUrPPo
						0Hvqowk2VbvnepRJYeYy+/vWhFBu+dvC3SN4G727zo/gOK5O+XLGZ7bre/IZW0Nv41cEr6FPGUzd+1oAeAZNAWBVgik6HGqiCabocErA
						8WoCYcZp8zah2Xarod+YvlVgLq+GNnpWDqaKiefR9Ns93qt3jMGm6pC5HmFSmLlco39CIHco4N+eObjM+fHWKte+4OvyrdP+FnjPhGt7
						1/VyT3CunjWYOvR5FeCRNQWAVQmm6HDoD33BFB2Wa2o8o0CYcdq8bQ3JejyznfZMlMBcXt3V9PzMKs3Z95RVGju/O2V4I3OE7Rj8eT7V
						/+0ewabqkLkepdxub2eMo5XbwC0RTm3X2nQYd2iO35xzZU6WOF5XwWDk7i9DhFcOXR0KFxMmBKmR8Dh4DT07mErd1vK0v8EAztIUAFYl
						mKKDYIrZTgs4XkkgzDh13gL7c69D18oRgvt+qEH8ntWDqSJ4LPecds7sKeN5GzrU//0ewabqsLkeYWKYeXo4NSmUOvw+Up1zJXxZJvxN
						XVfq7dxiQlBzdXo4NWlfI0FP8BoaGe+tUq+FzVPdAhTgM00BYFWCKTocapwJpuhwuDHF7QLNgVPnbVID6iOnNY0Dc3k1NAR4kGAq1Qx8
						T3lWz2nnTrHtc/Mt9vrf3SN4HIfN9SjB1TC1MkenNF63+UyHUsXh+X3nnCvXxsO/c4Ttlo/1uEY4/FyjSaumivLaOOX6tgXt9XhGi6yW
						Kt45n0d45mDq9DAUYKamALAqwRQdBFPMdmrA8SoCzYHT521S43TPqc2QwFxeDW3oPkIwVWxhUb2NpNIk/lKPI21rfL577tT//h7BpurQ
						uR5h4qqpolzjpp4rwddtres95JNz7rSAKnj8vtXbutXkL3KUQPXuWw4etQWBs67hsZDnk/O5R2zMtwi+Hq5OeZ0DnKEpAKxKMEUHwRSz
						dTWnuM1HDemDTp+3Sc972XNqIyQwl1dD9yvYkBo9zhI0nBFyTmmel2DjlnOm/rl7BJuq8eNzxMRVU1dl/qLN/m0Om5V0QV2rwW4858px
						m3aLv/DtDw/9ff5mbLNWTV3FV09tgVvqeNcOr1i7xY3n8xFnB1PplWxl/ruvjdvx77omAaQ1BYBVCabocOiDr2CKDqcHHK/glsb0nU6f
						t8krF66izalbBObyamgI8CjBVBEc6y1KGFAarMOauNtrowS3Nzej699xj2BTdfhcj7Ct0pjVEH+rvPaHrqC6NbgcrLtZfuc5V+aqvB66
						G9jvCYdSRVfT/M7jNUo5HiWgGnbct9deuV7efG0bJHotCs5P92utR3C/3irn2aHPr9vr9vpFg+gcA/RqCgCrEkzR4dAf9oIpOpwecLyC
						QONxiXkL7NdnDl0jRwru89CmTDDsGTrOq8mrRd5T5rYct7v2cWvWloChNN8P7Uf9O+8RbD7edRxmmrAS4CPXhn+Z87sCi+pcmd3cL8o2
						u0PYjnOubH9YSLWFwOkVdN/r7R4x8ZZ3e67HvczbXfO/HeMSRpXjnAz/3hO/fW/H+fyZU4OpYmdMKeUcK9flD6+J2/lU/l39Xrns+w1A
						0RQAViWYosOhpqtgig5LBBzPLhBmLDFvk5+dUdzVUEsIzOXV0KbMAwZT6RUPR5TxlPm+BlZvlSZtqQ8JF+rjcY9gUzUy16MEX4tHXM+T
						0vyvz5Xrf1vh/B4VCI0458rxKGHNn2HwZw3tbbvlOlGCvfIzdWM7ZcjtCE9c6bfno2tbOYev/63+udmGBKmfGXQ+71khmJr1OnmrzFt9
						PSyv9Y/GsvT7DUBTAFiVYIoOgilmWyLgeHaBBs8y8zax0Rb/1vQtAnN5NbQpszWC6m2MMHScb50QdC6jPhb3CDZVY3M9wmKN/kdw6G/M
						PcFz7uoajAwLfw8q59ewYGTCcXs2Q4LUzwTnZYVg6qzngd5r6fcbgKYAsCrBFB0ONQ0EU3RYJuB4ZoEwY5l5m9j0WKJpEZjLq6H794jB
						VDHhtlxLqo/DPYJN1ehcjxDc92czNNh/oeM+PFgIXpufzZCVarcIns/Dz597basM63GtaPn3G+C1NQWAVQmm6CCYYrZlAo5nFggzlpm3
						7XkB9fhG+1Fv9yyBubwa2pQJNj+HjnPPJ7f7eUr1MbhHsKkan+sRTn7e1CMor6dhq36K4Dm3ktht5E5+3tQjGBqkfiZ4Pp8eTBUnrzq8
						1UO83wCvqykArEowRQfBFLMtE3A8s0CYsdS8TWiyHbo2JgTm8mpoU+bBg6lyi7aXCqfqY3CPYFM1PtejvOpKuxsMD6WK4Dm3ktj5/4rX
						uDtMDaWK4Pm8SjCV+ntgpNjrDWCEpgCwKsEUHQ41XwVTdFgq4HhWgTBjqXnbHkZfj3Gk4Y3VowJzeTW0KRNsRA0d53terXFb7/89gk3V
						KXM9inCqEQmliuA5t4qv9T6P9mrXuBtND6WK4Pm8SjBVzrV6bKt5qPcb4PU0BYBVCaboIJhitqUCjmcVCDOWm7fgrWJOaVS9JzCXV0Ob
						Mo8eTBWv1Lit9/0ewabqtLkeRTj1L7FQqgiecyv4Xu9vyitd425w2nt98HxeIpgqHuDa+HDvN8BraQoAqxJM0UEwxWzLBRzPKBBmLDdv
						wSDkS72tMwXm8mpoUyY4H0PH+ZlXadzW+32PYFN16lyP8gAN2LRoKFUEz7mzxY9d7VWucZ84LZQqgufzSsFUOc/+2BnjKh7y/QZ4HU0B
						YFWCKToIpphtuYDjGQXCjOXm7XK5/Lwzzl4/6u2cLTCXV0ObMs8STBVbQy39HLNT1ft8j2BTdfpcj1KeS7ezP6+ghHLxYCV4zp1peih1
						tV3jXjVQPfTZZ6Tg+bxMMFUsfl182Pcb4DU0BYBVCabocOjDmWCKDssFHM8oEGYsOW+B8GCppk4RmMuroU2ZZwqmroL7dLp6X+8RbKqe
						NtcjbMdl5RUCox36G/KI4Dl3ltNCqbcWDw5GK6/NJa4xwfN5xb9hRv+dNsoS5wLAe5oCwKoEU3Q41FQQTNFhyYDj2QTCjCXnrdx2b2es
						PX6ut3G2wFxeDW3KBEOcoeO819ZATD3P7DT1ft4j2FQ9da5H2FaipF6zqyivh1/qfU8KnnNnKI3600OpqzKXz3iNq5TX5ErHPHU+rxhM
						rXrryId/vwGeW1MAWJVgig6CKWZbMuB4NoHG6LLzNrCh9q3+3SsIzOXV0KbMswZTxdZY+7oztodV7+M9gk3V0+d6lG0lyjOuniqv8+kN
						/uA5N9uhv7vTnvEatymvwV/r/T1b8HxeLpgqtlsvr3Y9fJr3G+A5NQWAVQmm6HDoA7Jgig7LBhzPJBBmLDtvA29F9KX+3SsIzOXV0KbM
						MwdTV1szccVvft+r6/UcbKouM9cjPNlzfMp16LQVpcFzbpZy/KauMjtiWz2Ves+ZrQRt00PUWwTP5yWDqWLBlVNP9X4DPJ+mALAqwRQd
						BFPM1tUQ5TaBxtKy87Y1O+rx3utH/XtXEZjLq6FNmVcIpq7KN/AHrtSbqZxL3ccz2FTtHtuKttUCjxpQDTlnem2ByWorLm5RrhPLrdj5
						zPYaT733pJXX2mkh6i2C19Blg6li+3ttlWdOnX5dA/hIUwBYlWCKDoIpZls24HgmgYbS0vM2oOm7bDMnMJdXQ5syrxRMXW0BVWp+Riqv
						j2HHMdhUHTbGFW0BVVnF8QgBSzlnllvhs73mVmlsf+QhA6na9lrvfX+dobymynvQ0oHUVfAauuzfMm9tzwc9+zr41O83wONrCgCrEkzR
						QTDFbEsHHM8i0Cxfet4GNHmWbWYF5vJqaFPmFYOpq201x2qBQ7llUrnN5fBbWQ14vb1n+bkeZdGApYynjGv4OTPatvLiegxXet0NDYFX
						sR3vcj1Z6VZsZd7L8V7yNrwfCV5DHyKYKrZzqvzdcMbr99RbkwLcoikArEowRQfBFLMtHXA8i61RXhofoyz3zfnazphvtfS+Bebyamjz
						eVsNUm9jhKHjTHsTUs1u4JYVGqVJGw8WtoZiPU8jRMe9ojcBS5m72beHnHbOpG2vuxKczA6qrsewrAB56GN4q+1af8axLsp1tVxfHy6M
						eit4DX24sKUKmev5HqmEUeW8fbhjBLympgCwKsEUHQRTzCaYAl7Cm+Zj+VZ4abqNCqtKg638vvJ7S0Nco+1JbE3/Mqejz5nye17mnHkT
						lF+P44iVpyWEKr/nJY7hrbZQsAQL5biU4zMqYC2/q4R+5feWuXyJ4O+Vbe+Z5bVVwsee1+zb693TrWAEXkNTAFiVYIoOgilmE0wBL23n
						2/LlW9ylgVZ7+2+WXtlHXnU+XMOrWqn/69/Vv4NmFeo1UHmrvB7fHmuByAE717m9Y+06x4eq82jvuvf2mue1CjyNpgCwKsEUHQRTzCaY
						AgAAANjRFABWJZiig2CK2QRTAAAAADuaAsCqBFN0EEwxm2AKAAAAYEdTAFiVYIoOgilmE0wBAAAA7GgKAKsSTNFBMMVsgikAAACAHU0B
						YFWCKToIpphNMAUAAACwoykArEowRQfBFLMJpgAAAAB2NAWAVQmm6CCYYjbBFAAAAMCOpgCwKsEUHQRTzCaYAgAAANjRFABWJZiig2CK
						2QRTAAAAADuaAsCqBFN0EEwxm2AKAAAAYEdTAFiVYIoOgilmE0wBAAAA7GgKAKsSTNFBMMVsgikAAACAHU0BYFX/+eXf/vd/fvm3f8AB
						/7M+n27xv/7f3/5nCRjggENhKAAAAMCzawoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAA
						EpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAsCq
						/vZ//uM//vZ//uOfcMDf6/PpJv/3b3//5//92z/hgH805xMAAAAAgingcQim6CCYYjbBFAAAAMCOpgCwKsEUHQRTzCaYAgAAANjRFABW
						JZiig2CK2QRTAAAAADuaAsCqBFN0EEwxm2AKAAAAYEdTAFiVYIoOgilmE0wBAAAA7GgKAKsSTNFBMMVsgikAAACAHU0BYFWCKToIpphN
						MAUAAACwoykArEowRQfBFLMJpgAAAAB2NAWAVQmm6CCYYjbBFAAAAMCOpgCwKsEUHQRTzCaYAgAAANjRFABWJZiig2CK2QRTAAAAADua
						AsCqBFN0EEwxm2AKAAAAYEdTAFiVYIoOgilmE0wBAAAA7GgKAKsSTNFBMMVsgikAAACAHU0BYFWCKToIpphNMAUAAACwoykArEowRQfB
						FLMJpvjb5XL57XK5/CPsS71dAAAAWFlTAFiVYIoOgilmE0y9uMvl8tPlcvnnBN/rbQMAAMDKmgLAqgRTdBBMMZtg6sVdLpdfd0KklJ/r
						7QMAAMCqmgLAqgRTdBBMMZtg6sWVlUw7AVLK13r7AAAAsKqmALAqwRQdBFPMJph6YWUF0054lPSjHgMAAACsqikArEowRQfBFLMJpl5Y
						WcG0Ex6lfanHAQAAACtqCgCrEkzRQTDFbIKpF1ZWMO0ER2m/1+MAAACAFTUFgFUJpuggmGI2wdSLKiuXdkKjWX6qxwMAAACraQoAqxJM
						0UEwxWyCqRdVVi7tBEaz/FqPBwAAAFbTFABWJZiig2CK2QRTL6isWNoJi2b6Xo8JAAAAVtMUAFYlmKKDYIrZBFMvqKxY2gmLZvu5HhcA
						AACspCkArEowRQfBFLMJpl5QWbG0ExTN9rUeFwAAAKykKQCsSjBFB8EUswmmXkxZqbQTEp3hRz02AAAAWElTAFiVYIoOgilmE0y9mLJS
						aSckOsuXenwAAACwiqYAsCrBFB0EU8wmmHoxl8vlj52A6Cy/1+MDAACAVTQFgFUJpuggmGI2wdQLKSuUdsKhs/1UjxMAAABW0BQAViWY
						ooNgitkEUy/kcrl82wmGzvZrPU4AAABYQVMAWJVgig6CKWYTTL2IsjJpJxRawfd6rAAAALCCpgCwKsEUHQRTzCaYehGXy+XvO6HQKn6p
						xwsAAABnawoAqxJM0UEwxWyCqRdRVibtBEKr+FqPFwAAAM7WFABWJZiig2CK2QRTL6CsSNoJg1byRz1mAAAAOFtTAFiVYIoOgilmE0y9
						gLIiaScMWs2XetwAAABwpqYAsCrBFB0EU8wmmHoBZUXSThC0mm/1uAEAAOBMTQFgVYIpOgimmE0w9eTKSqSdEGhVP9XjBwAAgLM0BYBV
						CaboIJhiNsHUkysrkXYCoF6psOvYNRAAAAACmgLAqgRTdDjWlBVMcZxg6omVFUg74U+v79vv/r7z33r9+bsBAABgBU0BYFWCKToIpphN
						MPXEygqknfCn16/B3138Uu8HAAAAnKEpAKxKMEUHwRSzCaaeWGhV05/PgbpcLj/v/LcRvtb7AQAAAGdoCgCrEkzRQTDFbIKpJ1VWHu2E
						Pr2+VdtIBF9/1PsCAAAAZ2gKAKsSTNFBMMVsgqknVVYe7YQ+vf68jd+bbaRu5/el3h8AAACYrSkArEowRQfBFLMJpp5UWXm0E/j0+vM2
						fm+28dPOvxnhLyuzAAAA4AxNAWBVgik6CKaYTTD1hMqKo52wp9fv9Xa2bX3b+bcj/CUEAwAAgNmaAsCqBFN0EEwxm2DqCYXCot3b65Xb
						++382xGOXQ8BAABgkKYAsCrBFB2ONWIFUxwnmHoyl8vl552Qp9cf9XbebC91O7/v9bYAAABgpqYAsCrBFB0EU8wmmHoyZaXRTsjTa/c2
						fm+2mVihVfxSbwtgzxaS//umrOT8bVP+97W+9C1Cty8WlHFex/775XL5xwfKf7/+2/JzrpkAAIM1BYBVCaboIJhiNsHUk7lcLj92Ap5e
						u7fxe7PNxDOtiq/1tgCKLcQpQXwJxv/YuX68p1wjy8/8Wv/O2d6EUCVkqsfZ4/sWWpVQ7ud6uwAA3K4pAKxKMEUHwRSzCaaeSPm2/E6D
						stePejt77mwM3+rdWwiuKtBgfjr1MdsTPI7/Xm9rBan9rbfTIzTGu9+DtrClBC/177pb/btn2MZ/b5jWqxyvEuIJqQAA7tQUAFYlmKKD
						YIrZ7m4Ksq7tG/J1Q7LXTauWQtsuTl/VcI9Q8/6p1MdsT/A4CqYOCo3x5vegLdAZuiK03kbKtrrr6+Qw6j1lHh/qugoAcKamALAqwRQd
						BFPMdnNTkPWFmp43PbMkeDu/b/W2VhZq3j+V+pjtCR5HwdRBoTF++h60rQQdskKqVm9rtC2QSoX2vUrIJ6ACAPhEUwBYlWCKDoIpZvu0
						Kchj2FYT1I3HXjfdxu/NGBLBWPEwt58KNe+fSn3M9gSPo2DqoNAYP3wP2q5rqevK0OPz1uVy+WlbIdVsc0El9FvydQEAsIKmALAqwRQd
						BFPM9mFTkMexPbOkbjj2+q3ezkeCjdhj18YThJr3T6U+ZnuCx3HJBnxqf+vt9AiN8d33oHL92fn3Q9XbHGFbPRoL04LK9funen8AAF5d
						UwBYlWCKDsear4Ipjnu3Kcjj2G4XVTcZR7hrpdJ2y636d4xw18qtM4Wa90+lPmZ7gsdRMHVQaIy770Gzbn9Xb7fHtkoq8QWBmcrt/W66
						fSsAwKtoCgCrEkzRQTDFbLtNQR5LWVG002Ds9b3ezi22xmb9u0Z4iGZpqHn/VOpjtid4HAVTB4XG2LwHha5nu+ptH7WF8o+4SmpP2Q/P
						ngIA2DQFgFUJpuggmGK2pinI4wmFQYeuR8Hb+f1eb2tFoeb9U6mP2Z7gcRRMHRQa41/eg7bb4NX/JqbexyNCz/dbwaH3AACAZ9MUAFYl
						mKLDsSaAYIrjBFMPLnj7vLtu4zdhPH/U21pRqHn/VOpjtid4HAVTB4XG+K/3oO2WpFNXHdX7eK9gEL+Kh/hCAABAUlMAWJVgig6CKWYT
						TD240LNYus6L0AquYvnbS4Wa98/kt/qY7QkeR8HUQaExvg2mEr//Q/U+3iN07V3Rsb9NAQCeRFMAWJVgig7HPvwLpjiuK4DgfKEVBl0B
						UPAZMd/qba3mjOb6A7n5ehM8joKpg0Jj/POcmH0Lv6t6H2/1QqHUVdd7AgDAI2sKAKsSTNFBMMVsNzeKWU/w2SY/1du6x3ZLrvp3jnLo
						FoOzhJr3z6AEqDefV8HjKJg6KDTGazCVWmX5oXofb/GCodTVL/WxAAB4BU0BYFWCKToIpphNMPXAygqineZhryGrki6Xy/ed3z3Csevk
						JKHm/TO4KxAKHse7xjFLan/r7fQIjbH8zlTA/ql6Hz8TXA36CO4KlwEAnkVTAFiVYIoOxxqugimOE0w9qOCqpCG3bAo2cH/U21pJqHn/
						6L7Wx+kzweMomDooNMbyO1Mh9qfqffzIWbcbXMyQLy4AADySpgCwKsEUHQRTzCaYelCh4GfYN+KDwVmx7C2lQs37R/a9Pka3CB5HwdRB
						oTEmnpF3s3of31OuOSePtWy7HP+rM8fypT4+AADPrCkArEowRQfBFLMJph5U6Jksv9fb6RG61WAxdJwjhZr3j6o0zw89Eyx4HAVTB6XG
						eKZ6H/eUsP6EVV3l2lm+fPBhCL8FZuVWiOW5V7PCqmFfYAAAeARNAWBVgik6CKaYTTD1gLZmZN0sHGHoN+GDz475o97WKp6xed/h8G0h
						g8dRMHVQaoxnqvdxT7kVZf1zISXw+a0n9NmuuTPm6bd62wAAz6opAKxKMEUHwRSzCaYe0Pbt+LpR2Gt42LOtNKi3M8rh0CNpCw3//UEk
						G9hdq9qCYxNMHZQa45nqfaxtr5Pm5wK6AqnaFlAlV1AdXg0JAPBomgLAqgRTdBBMMZtg6sFsYU+i4dgVJLwneDu/b/W2uF1ZHbdzTEcp
						t5nsarIHQxDB1EGpMR5UroHl2lICnTpwLcpt8Mp/K//m3due1vtY++hnBym3CPzwdn1HTbgF4dd6mwAAz6gpAKxKMEUHwRSzCaYeTPD2
						eKnmaGq8hW/sH1COWyjcvOo+l4IhiGDqoNQY71TGcPctR7eVjOWWfH8Jmup/V/1MCbbq7Y9UVr52BbifCYdTnjUFALyEpgCwKsEUHQRT
						zCaYejCh5vCPejsjBUMQzzk5INioLo69j7VjTJznhWDqoNQYb1TO2SFzt4Xlf74G6v/25t+kVqZeRVao7gmHU0Ne7wAAK2sKAKsSTNHh
						2Ad8wRTHCaYeyLbSpW4MjhC9JVPomVhFNFB7Rtuqkfo4jjLs9orBEGRIuDFaan/r7fRIjfEGkevTR6FK8JpVTAulroKrJF2DAYCn1xQA
						ViWYosO7TZIPCaY4TjD1QIK3luq+9dpHws8zWjJoWNH27J36+I0y9LZewRBkyfMltb/1dnqkxviJX+txpAW/AFBMD6WugrdVjb5/AACc
						rSkArEowRQfBFLMJph5I/XyUQaZ84z30bf3itEbvI5lwa7KhgU8wBBk6zlFS+1tvp0dqjB+YHkoVwVWF5XZ6w8LbI0JzGFnRBgCwiqYA
						sCrBFB0EU8wmmHoQwdUux647dwreGmvoSp1nFWpIXw1/1ldwvIKpg1JjfMdZoVQywD19ZVHofWTKlxsAAM7SFABWJZiiw7EGsWCK4wRT
						DyIY7Pxcbysh1BC9OqWJ/SiCt4AsvtfbGyEYggimDkqNccdpqyCDt7tbZlVRaOXt6aEbAEBKUwBYlWCKDoIpZhNMPYDgt/gjocJ7Qg3R
						wnn8jnAgWM7JSLAZDEEEUwelxlgp14jTVkBut9urx9RrqVWdoVsVHvv7FQDgATQFgFUJpuhw7IO9YIrjNPQfQPBb/MeuOQeFGqJXkYDk
						kW2BZioMLL7U2xwlGIIIpg5KjbFy2urHcg3ZGc8Iw2912aOsbtoZY69v9XYAAJ5FUwBYlWCKDseaxIIpjhNMPYBgQ3jqt/hDDdGrpZq/
						KyjN4p3jNEr0dmvBc14wdVBqjG+c+qyiUHC+1Gqpq8AK3FPnDgAgqSkArEowRQfBFLMJphYX/Bb/Kd9wD67g0Rh9o6yG2zlGo5TbnUWb
						7cEQRDB1UGqMb5y2WqoI3cYvGuAeFZrL6DUBAOAsTQFgVYIpOgimmE0wtbiyEminATjCKU3g4P4US4YOs4VXphW/1NscLdQ4L5Y8R1L7
						W2+nR2qMmz/q7c0U/ALAqudb4jq85L4CAPRqCgCrEkzRQTDFbIKpxQVXGJ3y7fZgA7hYcnXCTBOeK3XsfepOwRBkyeZ5an/r7fRIjXFz
						6ms39By/ZVdxhvZ3yrUBAGC2pgCwKsEUHY59qBdMcZxgamGlib7T/BvhlNv4XYVumVUs+TyXmUqDf+e4jDLtvAmGIIKpg1Jj3HyptzdT
						6HVzatj2kdB7i+f8AQBPqSkArEowRQfBFLMJphYWapYWZzeBk88/OuUWhSsIrYK4KquwpoV+wRBEMHVQaoxFva3ZQqsMT73OfiQUTPl7
						AgB4Sk0BYFWCKToIpphNI2lR2y3ZygqguvnX69RnuRTh2/m95Dm9PVcqcb5cTQ10giHI1P24VWp/6+30SI3x7Nfsdq2txzTCtCD3XqF9
						PnUeAQBSmgLAqgRTdBBMMZtG0qKCq1+WuL1UsMld/Fxv75ltTebU7RGL6bfoCp4fgqmDUmO8XC5f623NFFo99L3ezmp2xtzr9C89AAAk
						NAWAVQmm6CCYYjbB1KKCTeAlbi8VDN6K6UHKmUpjf+cYjHLKNSJ4/gumDkqN8ezbb4ZuLVpWL5bjtbJ6zN3qYwsA8AyaAsCqBFN0EEwx
						2ylNZz4WvNXdMt9oD91K6upHvb1nVYLGnf0fpTTXT1l9lmqcC6aOS43x7DkpQfbOmDigPrYAAM+gKQCsSjBFB8EUswmmFhRcAXPqLbNq
						l8vl284YRzm12T3DFmAmnyt12uq6Zw1B3pPa33o7PVJjPPtZTMH9ejn1sQUAeAZNAWBVgik6CKaYTTC1oLLip274DfJLva0zhW/nt8Sz
						tJLCz5U6NcQMhgWCqYMeYYxHpPbrRZ0aMgIAJDQFgFUJpuggmGI2wdRigrdmW+72dtvt/FIrfsrvfdomaXBVXVECr1OPXTAsEEwd9Ahj
						PKIeD12WfH0BAPRoCgCrEkzRQTDFbIKpxZSVPjvNPo75tT6+z6A0f3f2dZQS6J2+si4VgqzaOE/tb72dHqExnv4etDMmjlvy9QUA0KMp
						AKxKMEUHwRSznd4U5L9tK4jqRh/Hfa+P8aMLrzIrlgjzQiFIsWTjPLW/9XZ6hMZ4+nvQzpg4bsnXFwBAj6YAsCrBFB0EU8x2elOQ/xZ+
						5tKr+rk+zo8sFA5cfau3d5bgfi7ZOE/tb72dHqExnvoe5MsAwy35+gIA6NEUAFYlmKKDYIrZTm0K8lfbs33qRh99vtbH+VFdLpffdvZv
						lB9nP1fqrVAIUizZOE/tb72dHqExnvoeFL4t5ita8vUFANCjKQCsSjBFB8EUs53aFOS/lZU9O00++v2oj/UjmtBAP/25Um+FQpBiycZ5
						an/r7fQIjfHU96AJr6tXs+TrCwCgR1MAWJVgig6CKWY7tSnIfysre3aafIzxpT7ej2S73VhZ0VTv1yi/1ds8WygEKZZsnKf2t95Oj9AY
						T30PEkwNt+TrCwCgR1MAWJVgig6CKWY7tSnIfwsHD6/u9/p4P5Ly7KedfRplyWtAKAQplmycp/a33k6P0BhPPf8EU8Mt+foCAOjRFABW
						JZiig2CK2U5tCvJfyoqenQYfYy3z/KR7XC6Xv+/syyh/rHpcQiFIsWTjPLW/9XZ6hMZ46nuQYGq4JV9fAAA9mgLAqgRTdBBMMdupTUH+
						S1nRs9PgY6xf6+O+uvLcp539GGnZWxyGQpBiycZ5an/r7fQIjfHU96AJr7FXs+TrCwCgR1MAWJVgig6CKWY7tSnIn43R8vygurnHeN/r
						Y7+y7bz4vrMfo3ytt7mSUAhSLNk4T+1vvZ0eoTGe/h60MyaOW/L1BQDQoykArEowRQfBFLOd3hR8dWUlz05zj4yf6+O/qvAquuVDulAI
						UizZOE/tb72dHqExnv4etDMmjlvy9QUA0KMpAKxKMEUHwRSznd4UfHXhVTH81dKrhK7CYWV5rtQv9TZXEwpBiiUb56n9rbfTIzTG09+D
						dsY0wpLnGQAA92sKAKsSTNFBMMVspzcFX1lZwbPT0CTnRz0Hq9meeVPCo3rsozzEs7ZCIUixZGCQCqjr7fQIzcnp70HlurAzrl7LPr8N
						AID7NAWAVQmm6CCYYrbTm4KvrKzg2WlokrV0wzgVUGx+r7e3qlAIUqwaTNXjHKLeTo/QnJz+HhTar9/q7QAA8JiaAsCqBFN0EEwx2+lN
						wVcW+qY+H1s2nAkHleVc+6ne5qpCYUEhmDooNCenvweF9mvZ6wwAAPdpCgCrEkzRQTDFbKc3BV9VWbmz08xkjuUCmgnnw/LPlXorFBYU
						gqmDQnNy+ntQWd20M65e3+vtAADwmJoCwKoEU3QQTDHb6U3BV3W5XL7tNDOZY6nnLG3PGks+V+rYe8uJQiFIsdyxKGHZzjiHqLfVIzQn
						p78HlXNiZ1wjLBeAAwBwv6YAsCrBFB2ONcwEUxx3elPwFZWG5U4Tk3mWWs0Qfq7UQ77GQ6tYiuWe/VOC0p1xDlFvq8cTB1OpYHDp59kB
						AHCbpgCwKsEUHQRTzHZ6U/AVBb+hX5RnCZWm/qNLhjXFz/W8nGHb13pso5RVWA+5aiN4XL7V2zpb8tli9bZ6PGswVeyMa4Sv9XYAAHg8
						TQFgVYIpOgimmG2JpuCrCYcux64ji0muItmc3jQOrtS4WvJ5SrcIBlNLrZYrkteDels9njyYKoF+PbZeP+rtAADweJoCwKoEU3Q41lAW
						THHcEk3BV3K5XH7ZaWCOtMRKoBHCz136o97eTNvtHJP7d3rw1iMYTA0Na3ptzxdrxjhKvb0eTx5M/b4zthEeNhwGAOC/NAWAVQmm6CCY
						YrYlmoKvJHnbrhVXg/QINouvTnsGTKjJf/Xw50GZm539GmWZsCB8W0/B1I2CKzR/r7cFAMBjaQoAqxJM0UEwxWxLNAVfSXiVzLFryKLC
						4URxyvOGkquBtvPr4VfNhW9zuMxqstAt5P6l3l6PJw+mkivXHv71CADwypoCwKoEU3Q41lQWTHHcEk3BVzEhaHm6Bmi6cV9uqVdvM2nC
						rRxPWwU2UjgoWOLZP+Hw7U/1Nns8czBVBK81ywShAADcrykArEowRQfBFLMt0xR8BWWFzk7TcpSHv33bnvCtD4tj190DtudKpZrfxVPd
						Nmxn/0Y6/XZ+oaDnL+pt9giNd5n3oPC15um+NAAA8CqaAsCqBFN0ONYgFUxx3DJNwWe3hRJ1s3KkX+ttPoMJK4ymBXrpYHL26q+0bZ/q
						/Rzl1BAv+Eyjv6i32+MFgqnktWaZ/QQA4D5NAWBVgik6CKaYTbNskrIyZ6dZOdJThRJvhVcZFb/U2xxtwvzH92G2Eh7t7OdIpxyzLaRO
						PmvuX+pt93j2YKoIX2uO/Y23kGd+nwEAeE9TAFiVYIoOx5oWgimOW6op+MzCqz++1dt7JhNCnegzYMIrMYpj7x2LmzDvp1z/QgHPrnrb
						PULjPmUO3jPhnHvYla3bsXnq9xoAgD1NAWBVgik6HGsuCqY4bqmm4LOaEEw8bLPzFuX5LDv7PNIf9TZH2VbHCCUPmPC6KY697x40YRXY
						X9Tb7/EiwVT6lqvFQ12vt9fhde6Xmi8AgBmaAsCqBFN0ONYgE0xxnCbTBGVFzk5zcqSnv71SONwpvtTbHCEcRJTbwT313E+65V1k7mvh
						c2FXPYYerxBMFZPm6djfexNtXwioj8Vy8wUAkNYUAFYlmKLDsUaFYIrjNJkmCDfXn3bFzFtllcHOvo80/DhOGPO/19t8NmVedvZ7tPL6
						jIZTOw3+Kepx9HihYCq9QvOqnNvLBcvvBFJXy80XAEBaUwBYlWCKDoIpZtNkCisN753m3kgPdVuooybdYmtYk3i7/VUykPyt3uYzmhDu
						vTX8mG7nQXq137vq8fR4lWCq+CCYGa1cI4afd0eUoPuG/V5yvgAAkpoCwKoEU3QQTDGbJlPYhBUfw8KU1U04lseuwTvCYURpZpcm8qO6
						+ZydFEi+VeateyXaJ6tOpqnH1ePFgqkyf8lgufajXH/ueW2MsO1n2W7Zfj2mPUvOFwBAUlMAWJVgig7HmqKCKY7TZAqa0FQffvu5lU1Y
						PfO93uYRE54p9ujuCn4mBJJ7SkBVGvY/1+N5z/Z6LyskzxjvrnqMPV4pmCrKSqad8c5QAs3YrSW3VXxl346E58vOFwBASlMAWJVgig6C
						KWbTZAraGtt1Y2+kl7iN31sTVjH8Um/zHhNu3fgM7g2myiqr+nfMVFaTlFCmNPP3lCDqSJM/rj6WPV4tmCruWEmUcj3vynXl7mvT9top
						gX75HSPmb+n5AgBIaAoAqxJM0UEwxWyaTEHhZvUf9fZewYTbo32tt3mrE27/9ajuCqa2Y3t2QPCQ6uPYY1CwUVv6PWhbXVSPeQVlLt6T
						fN9Zer4AABKaAsCqBFN0EEwxmyZTyISG5u/1Nl/BhBVJhwO/cEP4mRwJptK3cXxK9XHs8YrBVHHiLf1WtPx8AQCM1hQAViWYooNgitk0
						mUImrOyJPYNkdRNWz9x9bDWv73J3MLUdY8Hfnepj2ONVg6lipeeGnewh5gsAYKSmALAqwRQdBFPMpskUEr6l2+FVPc+g3G5v55iM9K3e
						5kcWeAbSozkaTD3rcS6hRyL0EUwNcrlcfhKM/ukh5gsAYKSmALAqwRQdBFPMpskUMOG2Yy95G7+rCbdJLH6ut/uecAj5jA4FU9uxToeS
						s5XVfyX0SIQ+gqmBPEPuTw8zXwAAozQFgFUJpuggmGI2TaaACbd9uvtWc89mwu38br4e7/wsH+sJpp5p5UoJOX7Z9isR+gimBttC8VcO
						px5qvgAARmgKAKsSTNHh5kboXwimOE6TabDtW/V1M2+kl76N31UJjnaOzUg/6m2+Z+dn+djhYGo73s+wcuVfodS2T4nQRzAV8OLh1MPN
						FwBAr6YAsCrBFB0EU8ymyTTYhMDkpW/jdzUhACz+FRx8ZOfn+FhXMLUd80cOB/4SSm37kwh9BFMhT7Zy7x4POV8AAD2aAsCqBFN0EEwx
						mybTYBNuMdfd1H8WExrDN4WAOz/Hx4acww8aTjWh1LYvidBHMBW0hVPp27au5mHnCwDgqKYAsCrBFB0EU8ymyTTQ1iivG3kj3Xx7uVdw
						uVx+3TlGI91028Sdn+NjQ4KpYnvNpQPKUUqw81O9D9t+JEIfwdQE2yrZRwtIj/pW7z8AwLNrCgCrEkzRQTDFbA/fFFxJWWGz08gb6Wu9
						zVe2rVioj9Fov9bbre38DB8bFkwVD7Jy5bd63NU+JEIfwdQk261FE8dnFSV4+/RaCADwjJoCwKoEU3QQTDHbUzQFVzHhW/PNLcBe3YRA
						4tMVAjs/w8eGBlNXl8vly4Rbad6rhBU/12OtpUKNejs9QmN8qvegbRXnaudgj7IvZUXY7ko/AIBX0BQAViWYooNgitmeqil4pgm3lXMb
						vx0TjnvxYbCw8+/5WCSYKrbVU79NCIk/U0Kcm/czFPoIpk6w0DnYo8y1FVIAAIIp4JEIpuggmGK2p2sKnmXCyh238XvHhAbwh9fmnX/P
						x24ObI7awoEzVq+U23nevX+h0EcwdbLtHHyUZ6CVcZbVUR8G8QAAr6YpAKxKMEWHD5uf7xJMcdxTNwVn2Z4vUjf5RnMbv3dMeLbXh6vV
						dv49H7s7uOlRXjvbCpZEQFBC0RJKlwDi8O3OQqGPYGoR2zn49YSg9CPl3C3XznLuCqMAAN7RFAAAAOBW20qqf9+CqtKUL2HLrWFB+Xfl
						35eAoawsERZzty2kKudPCTTTqz3fup67JYhy7gIA3KgpAAAAwEjbCsgSXmneE/fmfCthaQmOSoB0ZHXa9edK4FV+Vwm/yu+1GgoAoENT
						AAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAA
						AICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYA
						AAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAA
						AAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEA
						AAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAA
						EpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAA
						AAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAk
						NAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAA
						AAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEho
						CgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAA
						AACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAU
						AAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAA
						ACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkA
						AAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAA
						QEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAA
						AAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACA
						hKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAA
						AAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJ
						TQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAA
						AAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKa
						AgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAA
						AAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQF
						AAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAA
						AEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoA
						AAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAA
						kNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAA
						AAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAg
						oSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAA
						AAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBC
						UwAAAAAAAICEpgAAAAAAAAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAA
						AACAhKYAAAAAAAAACU0BAAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISm
						AAAAAAAAAAlNAQAAAAAAABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEBCUwAAAAAAAICEpgAAAAAA
						AAAJTQEAAAAAAAASmgIAAAAAAAAkNAUAAAAAAABIaAoAAAAAAACQ0BQAAAAAAAAgoSkAAAAAAABAQlMAAAAAAACAhKYAAAAAAAAACU0B
						AAAAAAAAEpoCAAAAAAAAJDQFAAAAAAAASGgKAAAAAAAAkNAUAAAAAAAAIKEpAAAAAAAAQEJTAAAAAAAAgISmAAAAAAAAAAlNAQAAAAAA
						ABKaAgAAAAAAACQ0BQAAAAAAAEhoCgAAAAAAAJDQFAAAAAAAACChKQAAAAAAAEDC/w8aGiK+5Pn7SwAAAABJRU5ErkJggg=="/>
			<rect v:rectContext="foreign" x="0" y="506.364" width="148.875" height="78.6361" class="st2"/>
		</g>
		<g id="shape1388-511" v:mID="1388" v:groupContext="shape" v:layerMember="1"
				transform="translate(298.125,753.75) scale(1,-1)">
			<title>Top/bottom to side.1286</title>
			<v:userDefs>
				<v:ud v:nameU="TextPos" v:val="VT0(1):5"/>
				<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
			</v:userDefs>
			<path d="M0 577.32 L0 576.96 L0 561.37 L264.21 561.37" class="st12"/>
		</g>
		<g id="group1389-518" transform="translate(199.324,-374.824)" v:mID="1389" v:groupContext="group">
			<title>Sheet.1389</title>
			<g id="group1374-519" transform="translate(27,-18)" v:mID="1374" v:groupContext="group">
				<title>Vnet_hexbck.1374</title>
				<g id="shape1375-520" v:mID="1375" v:groupContext="shape" transform="translate(3.87558,-0.932567)">
					<title>Hexagon.1255</title>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
					</v:userDefs>
					<path d="M12.48 572.78 L3.34 572.78 L-1.22 578.89 L3.34 585 L12.48 585 L17.05 578.89 L12.48 572.78 Z"
							class="st32"/>
				</g>
				<g id="group1376-522" v:mID="1376" v:groupContext="group">
					<title>Sheet.1376</title>
					<g id="group1377-523" transform="translate(5.88364,-5.96604)" v:mID="1377" v:groupContext="group">
						<title>Sheet.1377</title>
						<g id="shape1378-524" v:mID="1378" v:groupContext="shape">
							<title>Sheet.1378</title>
							<path d="M12.23 583.92 A1.51278 1.50952 90 1 1 10.72 582.41 A1.51148 1.50822 90 0 1 12.23 583.92 ZM5.89
										 582.42 A1.51278 1.50952 -90 1 0 7.4 583.93 A1.51148 1.50822 -90 0 0 5.89 582.42 ZM1.07 582.42
										 A1.51278 1.50952 -90 1 0 2.58 583.93 A1.51278 1.50952 -90 0 0 1.07 582.42 L1.07 582.42 Z"
									class="st33"/>
						</g>
					</g>
					<g id="group1379-527" transform="translate(0.00521395,0)" v:mID="1379" v:groupContext="group">
						<title>Sheet.1379</title>
						<g id="shape1380-528" v:mID="1380" v:groupContext="shape">
							<title>Sheet.1380</title>
							<path d="M8.05 584.02 L7.19 584.89 a0.391913 0.391067 90 0 1 -0.55271 0 L0.23 578.49 a0.783827 0.782134
										 90 0 1 -7.77156E-16 -1.10907 L1.09 576.51 L8.05 583.46 a0.391913 0.391067 90 0 1 -1.77636E-14
										 0.55521 L8.05 584.02 Z" class="st34"/>
						</g>
					</g>
					<g id="group1381-530" transform="translate(0,-5.65893)" v:mID="1381" v:groupContext="group">
						<title>Sheet.1381</title>
						<g id="shape1382-531" v:mID="1382" v:groupContext="shape">
							<title>Sheet.1382</title>
							<path d="M7.06 576.7 L7.93 577.58 a0.391913 0.391067 90 0 1 -2.66454E-15 0.55521 L1.1 585 L0.23 584.14
										 a0.783827 0.782134 90 0 1 1.11022E-16 -1.10907 L6.52 576.71 a0.391913 0.391067 90 0 1 0.554014
										 0 L7.06 576.7 Z" class="st35"/>
						</g>
					</g>
					<g id="group1383-533" transform="translate(15.2878,-0.00261275)" v:mID="1383" v:groupContext="group">
						<title>Sheet.1383</title>
						<g id="shape1384-534" v:mID="1384" v:groupContext="shape">
							<title>Sheet.1384</title>
							<path d="M7.07 576.51 L7.94 577.38 a0.783827 0.782134 90 0 1 0 1.10907 L1.53 584.89 a0.391913 0.391067
										 90 0 1 -0.55271 0 L0.11 584.02 a0.391913 0.391067 90 0 1 -3.88578E-15 -0.553902 L7.07 576.51
										 Z" class="st34"/>
						</g>
					</g>
					<g id="group1385-536" transform="translate(15.4129,-5.66282)" v:mID="1385" v:groupContext="group">
						<title>Sheet.1385</title>
						<g id="shape1386-537" v:mID="1386" v:groupContext="shape">
							<title>Sheet.1386</title>
							<path d="M7.82 584.14 L6.95 585 L0.11 578.13 a0.391913 0.391067 90 0 1 -7.99361E-15 -0.553902 L0.99 576.7
										 a0.391913 0.391067 90 0 1 0.55271 0 L7.82 583.02 a0.783827 0.782134 90 0 1 -0.00413832 1.10907
										 L7.82 584.14 Z" class="st35"/>
						</g>
					</g>
				</g>
			</g>
			<g id="shape1387-539" v:mID="1387" v:groupContext="shape">
				<title>Virtual Networks.1387</title>
				<desc>Virtual network peering</desc>
				<v:textBlock v:margins="rect(0,0,0,0)"/>
				<v:textRect cx="36.4842" cy="575.826" width="72.97" height="18.3487"/>
				<rect x="0" y="566.651" width="72.9685" height="18.3487" class="st64"/>
				<text x="9.6" y="573.43" class="st65" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Virtual network <tspan
							x="22.97" dy="1.2em" class="st4">peering</tspan></text>			</g>
		</g>
		<g id="group1390-543" transform="translate(382.2,-397.8)" v:mID="1390" v:groupContext="group">
			<title>Sheet.1390</title>
			<g id="group1302-544" transform="translate(25.9412,0)" v:mID="1302" v:groupContext="group">
				<title>Vnet_hexbck.1302</title>
				<g id="shape1303-545" v:mID="1303" v:groupContext="shape" transform="translate(3.87558,-0.932567)">
					<title>Hexagon.1255</title>
					<v:userDefs>
						<v:ud v:nameU="visVersion" v:val="VT0(15):26"/>
					</v:userDefs>
					<path d="M12.48 572.78 L3.34 572.78 L-1.22 578.89 L3.34 585 L12.48 585 L17.05 578.89 L12.48 572.78 Z"
							class="st32"/>
				</g>
				<g id="group1304-547" v:mID="1304" v:groupContext="group">
					<title>Sheet.1304</title>
					<g id="group1305-548" transform="translate(5.88364,-5.96604)" v:mID="1305" v:groupContext="group">
						<title>Sheet.1305</title>
						<g id="shape1306-549" v:mID="1306" v:groupContext="shape">
							<title>Sheet.1306</title>
							<path d="M12.23 583.92 A1.51278 1.50952 90 1 1 10.72 582.41 A1.51148 1.50822 90 0 1 12.23 583.92 ZM5.89
										 582.42 A1.51278 1.50952 -90 1 0 7.4 583.93 A1.51148 1.50822 -90 0 0 5.89 582.42 ZM1.07 582.42
										 A1.51278 1.50952 -90 1 0 2.58 583.93 A1.51278 1.50952 -90 0 0 1.07 582.42 L1.07 582.42 Z"
									class="st33"/>
						</g>
					</g>
					<g id="group1307-552" transform="translate(0.00521395,0)" v:mID="1307" v:groupContext="group">
						<title>Sheet.1307</title>
						<g id="shape1308-553" v:mID="1308" v:groupContext="shape">
							<title>Sheet.1308</title>
							<path d="M8.05 584.02 L7.19 584.89 a0.391913 0.391067 90 0 1 -0.55271 0 L0.23 578.49 a0.783827 0.782134
										 90 0 1 -7.77156E-16 -1.10907 L1.09 576.51 L8.05 583.46 a0.391913 0.391067 90 0 1 -1.77636E-14
										 0.55521 L8.05 584.02 Z" class="st34"/>
						</g>
					</g>
					<g id="group1309-555" transform="translate(0,-5.65893)" v:mID="1309" v:groupContext="group">
						<title>Sheet.1309</title>
						<g id="shape1310-556" v:mID="1310" v:groupContext="shape">
							<title>Sheet.1310</title>
							<path d="M7.06 576.7 L7.93 577.58 a0.391913 0.391067 90 0 1 -2.66454E-15 0.55521 L1.1 585 L0.23 584.14
										 a0.783827 0.782134 90 0 1 1.11022E-16 -1.10907 L6.52 576.71 a0.391913 0.391067 90 0 1 0.554014
										 0 L7.06 576.7 Z" class="st35"/>
						</g>
					</g>
					<g id="group1311-558" transform="translate(15.2878,-0.00261275)" v:mID="1311" v:groupContext="group">
						<title>Sheet.1311</title>
						<g id="shape1312-559" v:mID="1312" v:groupContext="shape">
							<title>Sheet.1312</title>
							<path d="M7.07 576.51 L7.94 577.38 a0.783827 0.782134 90 0 1 0 1.10907 L1.53 584.89 a0.391913 0.391067
										 90 0 1 -0.55271 0 L0.11 584.02 a0.391913 0.391067 90 0 1 -3.88578E-15 -0.553902 L7.07 576.51
										 Z" class="st34"/>
						</g>
					</g>
					<g id="group1313-561" transform="translate(15.4129,-5.66282)" v:mID="1313" v:groupContext="group">
						<title>Sheet.1313</title>
						<g id="shape1314-562" v:mID="1314" v:groupContext="shape">
							<title>Sheet.1314</title>
							<path d="M7.82 584.14 L6.95 585 L0.11 578.13 a0.391913 0.391067 90 0 1 -7.99361E-15 -0.553902 L0.99 576.7
										 a0.391913 0.391067 90 0 1 0.55271 0 L7.82 583.02 a0.783827 0.782134 90 0 1 -0.00413832 1.10907
										 L7.82 584.14 Z" class="st35"/>
						</g>
					</g>
				</g>
			</g>
			<g id="shape1315-564" v:mID="1315" v:groupContext="shape" transform="translate(0,-14.8235)">
				<title>Virtual Networks.1315</title>
				<desc>Virtual network peering</desc>
				<v:textBlock v:margins="rect(0,0,0,0)"/>
				<v:textRect cx="36.4842" cy="575.826" width="72.97" height="18.3487"/>
				<rect x="0" y="566.651" width="72.9685" height="18.3487" class="st64"/>
				<text x="9.6" y="573.43" class="st65" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Virtual network <tspan
							x="22.97" dy="1.2em" class="st4">peering</tspan></text>			</g>
		</g>
		<g id="shape1391-568" v:mID="1391" v:groupContext="shape" transform="translate(225,-149.25)">
			<title>Sheet.1391</title>
			<desc>System node pool</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="43.65" cy="572.962" width="87.31" height="24.075"/>
			<rect x="0" y="560.925" width="87.3" height="24.075" class="st2"/>
			<text x="7.76" y="575.66" class="st6" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>System node pool</text>		</g>
		<g id="shape1392-571" v:mID="1392" v:groupContext="shape" transform="translate(313.5,-253.5)">
			<title>Sheet.1392</title>
			<desc>Internal load balancer</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="43.125" cy="569.25" width="86.26" height="31.5"/>
			<rect x="0" y="553.5" width="86.25" height="31.5" class="st2"/>
			<text x="15.33" y="566.25" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Internal load <tspan
						x="24.39" dy="1.2em" class="st4">balancer </tspan> </text>		</g>
		<g id="shape1393-575" v:mID="1393" v:groupContext="shape" transform="translate(525,-35)">
			<title>VnetBox.1393</title>
			<rect x="0" y="407.028" width="160.875" height="177.972" rx="4.5" ry="4.5" class="st31"/>
		</g>
		<g id="shape1394-577" v:mID="1394" v:groupContext="shape" transform="translate(569,-179)">
			<title>Sheet.1394</title>
			<desc>Azure Monitor workspace</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="39.0438" cy="572.962" width="78.09" height="24.075"/>
			<rect x="0" y="560.925" width="78.0875" height="24.075" class="st2"/>
			<text x="7.18" y="569.96" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Azure Monitor<v:lf/><tspan
						x="15.75" dy="1.2em" class="st4">workspace</tspan></text>		</g>
		<g id="group1395-581" transform="translate(532.5,-168)" v:mID="1395" v:groupContext="group">
			<title>Sheet.1395</title>
			<g id="shape1396-582" v:mID="1396" v:groupContext="shape">
				<title>Sheet.1396</title>
				<ellipse cx="17.75" cy="567.313" rx="17.75" ry="17.6874" class="st66"/>
			</g>
			<g id="shape1397-586" v:mID="1397" v:groupContext="shape" transform="translate(2.29706,-2.29706)">
				<title>Sheet.1397</title>
				<ellipse cx="15.4529" cy="569.61" rx="15.4529" ry="15.3903" class="st19"/>
			</g>
			<g id="shape1398-588" v:mID="1398" v:groupContext="shape" transform="translate(4.63588,-8.41559)">
				<title>Sheet.1398</title>
				<path d="M0 576.65 A13.0306 13.0306 -180 0 0 3.8 585 L7.98 580.82 A7.37147 7.37147 0 0 1 5.89 576.65 L0 576.65 Z"
						class="st67"/>
			</g>
			<g id="shape1399-590" v:mID="1399" v:groupContext="shape" transform="translate(9.12559,-18.6062)">
				<title>Sheet.1399</title>
				<path d="M17.25 576.04 A13.0515 13.0515 -180 0 0 9.54 572.85 L9.54 578.67 A7.12088 7.12088 0 0 1 13.11 580.13 L17.25
							 576.04 ZM0 576.04 L4.18 580.22 A7.12088 7.12088 0 0 1 7.75 578.76 L7.75 572.85 A13.0515 13.0515 -180
							 0 0 0 576.04 ZM14.43 581.43 A7.51765 7.51765 0 0 1 15.91 585 L21.74 585 A12.8635 12.8635 -180 0 0 18.54
							 577.34 L14.43 581.43 Z" class="st68"/>
			</g>
			<g id="shape1400-592" v:mID="1400" v:groupContext="shape" transform="translate(4.63588,-18.6062)">
				<title>Sheet.1400</title>
				<path d="M7.31 581.43 L3.13 577.25 A12.8635 12.8635 -180 0 0 0 585 L5.83 585 A7.51765 7.51765 0 0 1 7.31 581.43 Z"
						class="st69"/>
			</g>
			<g id="shape1401-594" v:mID="1401" v:groupContext="shape" transform="translate(18.6897,-17.2279)">
				<title>Sheet.1401</title>
				<path d="M9.79 580.36 a0.939706 0.939706 -180 0 0 -1.19029 -0.522059 L0 583.31 L0.67 585 L9.27 581.6 a0.918824 0.918824
							 -180 0 0 0.522059 -1.23206 Z" class="st70"/>
			</g>
			<g id="shape1402-596" v:mID="1402" v:groupContext="shape" transform="translate(15.2441,-15.1815)">
				<title>Sheet.1402</title>
				<ellipse cx="2.50588" cy="582.494" rx="2.50588" ry="2.50588" class="st71"/>
			</g>
		</g>
		<g id="shape1403-600" v:mID="1403" v:groupContext="shape" transform="translate(494,-120)">
			<title>Sheet.1403</title>
			<path d="M0 585 L23.34 585" class="st14"/>
		</g>
		<g id="shape1404-605" v:mID="1404" v:groupContext="shape" transform="translate(562.5,-111.5)">
			<title>Sheet.1404</title>
			<desc>Metrics</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="39.0438" cy="572.962" width="78.09" height="24.075"/>
			<rect x="0" y="560.925" width="78.0875" height="24.075" class="st2"/>
			<text x="22.86" y="575.96" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Metrics</text>		</g>
		<g id="shape1405-608" v:mID="1405" v:groupContext="shape" transform="translate(562.5,-46.5)">
			<title>Sheet.1405</title>
			<desc>Managed Prometheus</desc>
			<v:textBlock v:margins="rect(4,4,4,4)"/>
			<v:textRect cx="39.0438" cy="572.962" width="78.09" height="24.075"/>
			<rect x="0" y="560.925" width="78.0875" height="24.075" class="st2"/>
			<text x="18.13" y="569.96" class="st3" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Managed <tspan
						x="12.56" dy="1.2em" class="st4">Prometheus</tspan></text>		</g>
		<g id="group1406-612" transform="translate(589.919,-137.5)" v:mID="1406" v:groupContext="group">
			<title>Sheet.1406</title>
			<g id="shape1407-613" v:mID="1407" v:groupContext="shape" transform="translate(1.43261,-9.87901)">
				<title>Sheet.1407</title>
				<path d="M18.74 571.18 L12.79 581.78 L6.49 575.36 L0 583.78 L1.52 584.97 L6.67 578.33 L13.21 585 L20.43 572.15 L18.74
							 571.18 Z" class="st72"/>
			</g>
			<g id="shape1408-615" v:mID="1408" v:groupContext="shape" transform="translate(5.55135,-15.5796)">
				<title>Sheet.1408</title>
				<ellipse cx="2.44737" cy="582.538" rx="2.44737" ry="2.46229" class="st73"/>
			</g>
			<g id="shape1409-619" v:mID="1409" v:groupContext="shape" transform="translate(12.1921,-9.47609)">
				<title>Sheet.1409</title>
				<ellipse cx="2.44737" cy="582.538" rx="2.44737" ry="2.46229" class="st73"/>
			</g>
			<g id="shape1410-622" v:mID="1410" v:groupContext="shape" transform="translate(18.3553,-20.4445)">
				<title>Sheet.1410</title>
				<ellipse cx="2.44737" cy="582.538" rx="2.44737" ry="2.46229" class="st73"/>
			</g>
			<g id="shape1411-625" v:mID="1411" v:groupContext="shape" transform="translate(1.16906E-13,-8.40164)">
				<title>Sheet.1411</title>
				<ellipse cx="2.44737" cy="582.538" rx="2.44737" ry="2.46229" class="st73"/>
			</g>
			<g id="shape1412-628" v:mID="1412" v:groupContext="shape" transform="translate(6.65565,0)">
				<title>Sheet.1412</title>
				<path d="M0 584.58 a0.417853 0.417853 -180 0 0 0.417843 0.417843 L3.43 585 a0.417853 0.417853 -180 0 0 0.417843 -0.417843
							 L3.85 574.11 a0.417853 0.417853 -180 0 0 -0.417843 -0.417843 L0.42 573.69 a0.417853 0.417853 -180 0
							 0 -0.417843 0.417843 L0 584.58 Z" class="st74"/>
			</g>
			<g id="shape1413-630" v:mID="1413" v:groupContext="shape" transform="translate(12.789,-1.13687E-13)">
				<title>Sheet.1413</title>
				<path d="M-0 584.58 a0.417853 0.417853 -180 0 0 0.417843 0.417843 L3.43 585 a0.417853 0.417853 -180 0 0 0.417843
							 -0.417843 L3.85 578.02 a0.417853 0.417853 -180 0 0 -0.417843 -0.417843 L0.42 577.6 a0.417853 0.417853
							 -180 0 0 -0.417843 0.417843 L0 584.58 Z" class="st75"/>
			</g>
			<g id="shape1414-632" v:mID="1414" v:groupContext="shape" transform="translate(18.9223,0)">
				<title>Sheet.1414</title>
				<path d="M-0 584.58 a0.417853 0.417853 -180 0 0 0.417843 0.417843 L3.43 585 a0.417853 0.417853 -180 0 0 0.417843
							 -0.417843 L3.85 570.32 a0.417853 0.417853 -180 0 0 -0.417843 -0.417843 L0.42 569.9 a0.417853 0.417853
							 -180 0 0 -0.417843 0.417843 L0 584.58 Z" class="st75"/>
			</g>
			<g id="shape1415-634" v:mID="1415" v:groupContext="shape" transform="translate(0.522304,-1.13687E-13)">
				<title>Sheet.1415</title>
				<path d="M-0 584.58 a0.417853 0.417853 -180 0 0 0.417843 0.417843 L3.43 585 a0.417853 0.417853 -180 0 0 0.417843
							 -0.417843 L3.85 578.02 a0.417853 0.417853 -180 0 0 -0.417843 -0.417843 L0.42 577.6 a0.417853 0.417853
							 -180 0 0 -0.417843 0.417843 L0 584.58 Z" class="st76"/>
			</g>
		</g>
		<g id="group1416-636" transform="translate(584.419,-76)" v:mID="1416" v:groupContext="group">
			<title>Sheet.1416</title>
			<g id="shape1417-637" v:mID="1417" v:groupContext="shape">
				<title>path4486</title>
				<path d="M17.13 550.75 C7.67 550.75 0 558.42 0 567.88 C0 577.33 7.67 585 17.13 585 C26.58 585 34.25 577.33 34.25
							 567.88 C34.25 558.42 26.58 550.75 17.13 550.75 ZM17.13 582.8 C14.43 582.8 12.25 581 12.25 578.79 L22
							 578.79 C22 581 19.82 582.8 17.13 582.8 ZM25.17 577.46 L9.08 577.46 L9.08 574.54 L25.17 574.54 L25.17
							 577.46 L25.17 577.46 ZM25.12 573.04 L9.12 573.04 C9.07 572.98 9.01 572.91 8.96 572.85 C7.32 570.85 6.93
							 569.81 6.55 568.74 C6.54 568.71 8.55 569.15 9.97 569.47 C9.97 569.47 10.7 569.64 11.77 569.84 C10.74
							 568.63 10.13 567.1 10.13 565.54 C10.13 562.11 12.77 559.11 11.82 556.68 C12.74 556.76 13.73 558.63 13.8
							 561.57 C14.78 560.21 15.19 557.73 15.19 556.21 C15.19 554.63 16.23 552.8 17.27 552.74 C16.34 554.27
							 17.51 555.57 18.54 558.82 C18.93 560.03 18.88 562.08 19.18 563.38 C19.28 560.68 19.74 556.74 21.46 555.38
							 C20.7 557.1 21.57 559.24 22.16 560.27 C23.12 561.93 23.7 563.19 23.7 565.57 C23.7 567.17 23.11 568.67
							 22.11 569.85 C23.25 569.64 24.03 569.45 24.03 569.45 L27.7 568.73 C27.7 568.73 27.17 570.92 25.12 573.04
							 Z" class="st77"/>
			</g>
		</g>
	</g>
</svg>

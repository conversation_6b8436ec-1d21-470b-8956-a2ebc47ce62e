<!-- Parent: Paved Roads -->
<!-- Parent: Reference Architecture -->
<!-- Title: RFC-483 Reference Architecture Azure Kubernetes Service (AKS) -->
# Reference Architecture Azure Kubernetes Service

```text
Author: Sri Jonnala
Publish Date: 2025-03-20
Category: Paved-Roads
Subtype: Reference Architecture
```

## Executive Summary

This reference architecture outlines the design, deployment, and configuration of Azure Kubernetes Service (AKS) using Terraform and Azure DevOps CI/CD pipelines. It is intended to provide a standardized and reusable template for deploying AKS clusters in a secure and scalable manner, leveraging Azure best practices.

This architecture ensures:

- Automated and consistent deployment.
- Secure integration with Azure services like Key Vault and Managed Identities.
- Scalability and flexibility for various workloads.

## Usage Guidance

### When to Use

- 🔲 Mission-critical applications requiring enterprise-grade authentication
- 🔲 Systems that need both customer and employee access management
- 🔲 Applications requiring fine-grained access control and audit capabilities
- 🔲 Solutions that must comply with specific security and compliance requirements

### When to Avoid

- 🔲 Simple applications with basic authentication needs
- 🔲 Projects with strict vendor independence requirements
- 🔲 Systems with offline-first authentication requirements
- 🔲 Solutions with regulatory restrictions on cloud-based identity providers

## Source Repository

This reference architecture has an accompanying GitHub repository containing implementation resources:

- **Repository URL**: <https://github.com/wexinc/arch-reference-azure-aks>

The repository includes:

- Infrastructure as Code templates
- CI/CD workflow definitions
- Example implementations
- Configuration templates
- Deployment scripts

## Value Proposition

- Pay-as-you-go: AKS offers a pay-as-you-go pricing model, allowing businesses to pay only for the resources they consume.

- Resource Optimization: AKS provides tools and features for optimizing resource utilization and reducing costs.

- Cost Transparency: AKS provides cost transparency, allowing businesses to track and manage their Kubernetes costs effectively.

### Business Benefits

- Time to market improvements
- Cost optimizations
- Risk reductions

### Technical Benefits

- Managed Control Plane: AKS handles the complexities of managing the Kubernetes control plane, including upgrades, patching, and scaling, allowing you to focus on your applications.

- Easy Deployment: AKS provides a streamlined way to deploy and manage Kubernetes clusters, making it easier to get started with containerized applications.

- Integration with Azure Services: AKS integrates seamlessly with other Azure services, such as Azure Container Registry, Azure Active Directory, and Azure Monitor, enabling a comprehensive cloud-native application development and deployment experience.

- Scalability: AKS allows you to easily scale your Kubernetes clusters up or down based on demand, ensuring that your applications can handle fluctuating workloads.

- High Availability: AKS ensures high availability for your applications by automatically distributing your pods across multiple nodes and regions.

- Scalability: AKS allows you to easily scale your Kubernetes clusters up or down based on demand, ensuring that your applications can handle fluctuating workloads.

- Self-Healing: AKS provides self-healing capabilities, automatically restarting failed pods and rebalancing resources to maintain application availability.

## Cost Optimization Controls

- **Reserved Instances**: Expected savings of X% with 1-year commitment
- **Auto-scaling**: Cost reduction during off-peak hours
- **Storage Tiering**: Data lifecycle management savings
- **Network Optimization**: CDN and caching strategies

## Architecture Diagrams

### Azure Kubernetes Architecture

![Azure Kubernetes](./aks-baseline-architecture.svg)

### Azure Kubernetes Deployment Architecture

```mermaid
graph TD
    subgraph Control Plane
        APIServer["API Server"]
        etcd["etcd"]
        ControllerManager["Controller Manager"]
        Scheduler["Scheduler"]
    end

    subgraph Node Pool
        Node1["Node 1"]
        Node2["Node 2"]
        Node3["Node 3"]
    end

    subgraph Networking
        VNet["Virtual Network"]
        LoadBalancer["Load Balancer"]
        IngressController["Ingress Controller"]
    end

    subgraph Storage
        AzureDisks["Azure Disks"]
        AzureFiles["Azure Files"]
    end

    subgraph Security
        AAD["Azure Active Directory"]
        NetworkPolicies["Network Policies"]
    end

    APIServer --> Node1
    APIServer --> Node2
    APIServer --> Node3
    Node1 --> VNet
    Node2 --> VNet
    Node3 --> VNet
    VNet --> LoadBalancer
    LoadBalancer --> IngressController
    Node1 --> AzureDisks
    Node2 --> AzureDisks
    Node3 --> AzureDisks
    Node1 --> AzureFiles
    Node2 --> AzureFiles
    Node3 --> AzureFiles
    Node1 --> NetworkPolicies
    Node2 --> NetworkPolicies
    Node3 --> NetworkPolicies
    AAD --> APIServer
```

## Azure AKS Networking Concepts

## Authentication & Identity

### Kubernetes RBAC

Kubernetes RBAC provides granular filtering of user actions. With this control mechanism:

- You assign users or user groups permission to create and modify resources or view logs from running application workloads.
- You can scope permissions to a single namespace or across the entire AKS cluster.
- You create roles to define permissions, and then assign those roles to users with role bindings.
  
## Azure RBAC for Kubernetes Authorization

Azure role-based access control (RBAC) is an authorization system built on Azure Resource Manager that provides fine-grained access management of Azure resources.

- Kubernetes RBAC Designed to work on Kubernetes resources within your AKS cluster.
- Azure RBAC Designed to work on resources within your Azure subscription.
- With Azure RBAC, you create a role definition that outlines the permissions to be applied. You then assign a user or group this role definition via a role assignment for a particular scope. The scope can be an individual resource, a resource group, or across the subscription.

![Azure Kubernetes RBAC](./azure-rbac-flow.png)
  
Azure AKS Built-in roles

- AKS provides the following four built-in roles. They are similar to the Kubernetes built-in roles with a few differences, like supporting CRDs. See the full list of actions allowed by each Azure built-in role.
![Azure Kubernetes RBAC_Roles](./azure-rbac-roles.png)

For more information, see What is Azure role-based access control (Azure RBAC) <https://learn.microsoft.com/en-us/azure/aks/concepts-identity>

### Customer Authentication

- **Provider**: Azure IAM/RBAC Groups
- **Authentication Flows**:
  - Azure IAM
  - MFA requirements
  - Session management
- **Integration Patterns**:
  
### Employee Authentication

- **Provider**: Azure IAM
- **Access Patterns**:
  - Azure RBAC Groups
  - Cluster Admin access
  - Cluster Readonly access
  - Monitoring systems
- **Security Controls**:
  - Conditional access policies
  - Device compliance
  - Geographic restrictions

## Technology Stack

| Layer | Technology | Purpose | Version |
|-------|------------|---------|---------|
| Azure Cli | Kubernetes | Container Orchestration | 1.32 |

## Implementation Guide

<https://github.com/wexinc/arch-reference-azure-aks>

## Azure Kubernetes Best Practices Guide

<https://learn.microsoft.com/en-us/azure/aks/best-practices>

## Azure Kubernetes Tutorial guide

<https://learn.microsoft.com/en-us/azure/aks/tutorial-kubernetes-deploy-application?tabs=azure-cli>

### Prerequisites

- 🔲 Required access and permissions
- 🔲 Development environment setup
- 🔲 Infrastructure requirements

### Quick Start

- Basic setup steps
- Minimal configuration
- Validation checks

### Configuration Reference

### Example configuration

<ttps://github.com/wexinc/arch-reference-azure-aks/blob/main/azure-reference-architecture-terraform-script.txt>

## Operating Model

### Monitoring & Alerting

- Key metrics to track
- Alert thresholds
- Dashboard templates

### Scaling Considerations

- Performance limits
- Auto Scaling triggers
- Cost implications

### Security & Compliance

- Security controls
- Compliance requirements
- Audit procedures

### Cost Management

- Monthly cost review procedures
- Budget alerting thresholds
- Cost allocation (tagging strategy)
- Optimization review schedule
- Waste identification processes

## Support & Contact

## Resources

### Internal Repositories

- [Fabric AKS Infrastructure](https://github.com/wexinc/fabric-aks-infrastructure)
- [Fabric AKS Configuration](https://github.com/wexinc/fabric-aks-configuration)

### Documentation

- [AKS Deployment Recipe](link-to-confluence)
- [WEX Fabric Journey](link-to-confluence)
- [Datadog in AKS using Helm Charts](link-to-confluence)
- [AKS FAQ](link-to-confluence)

### External Resources

- [Azure Container Services Guide](https://learn.microsoft.com/azure/architecture/guide/container-services)
- [AKS Baseline Architecture](https://learn.microsoft.com/azure/architecture/reference-architectures/containers/aks/baseline-aks)
- [Secure Ingress Gateway for Istio](https://learn.microsoft.com/azure/aks/istio-secure-ingress)

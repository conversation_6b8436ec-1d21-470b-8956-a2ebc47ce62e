<!-- Parent: Paved Roads -->
<!-- Parent: Reference Architecture -->
<!-- Title: RFC-417 Control-M Job Scheduling -->
# Control-M Reference Architecture

```text
Author: <PERSON> Nize
Publish Date: 2025-01-08
Category: Paved Roads
Subtype: Reference Architecture
```

## Executive Summary

Control M is a batch or job Scheduler from BMC Software. This architecture proposes to be a recipe for any Control-M implementation at Wex, to incorporate automation on job definitions/executions via CTM Automation API component and Github Actions CI/CD pipelines. A Gitops strategy is used to keep job definitions consistant and having git as source of truth for such job definitions.

### Table of Contents

<!-- Include: ac:toc -->

## Usage Guidance

### When to Use

- ✅ Mission-critical applications requiring enterprise-grade job scheduling
- ✅ Systems that need centralized batch job management
- ✅ Applications requiring advanced workflow orchestration capabilities
- ✅ Solutions that need consistent job execution with audit trails

### When to Avoid

- ❌ Simple applications with basic scheduling needs
- ❌ Projects requiring minimal infrastructure overhead
- ❌ Systems with only ad-hoc, non-recurring processes
- ❌ Solutions where cloud-native scheduling tools are preferred

## Source Repository

This reference architecture has an accompanying GitHub repository containing implementation resources:

- **Repository URL**: [control-m-reference-architecture](https://github.com/wexinc/control-m-reference-architecture)

The repository includes:

- Infrastructure as Code templates
- CI/CD workflow definitions
- Example implementations
- Configuration templates
- Deployment scripts

## Value Proposition

### Business Benefits

- Centralized job definition management
- Reduced deployment time from days to hours
- Decreased operational risks through:
  - Automated testing
  - Version control
  - Audit trails

### Technical Benefits

- Standardization advantages
- Source control
- Code review
- Security posture

## Architecture Overview

### Cloud Architecture Diagram

![Network diagram](network_diagram.png)

### Authentication & Identity

Authentication and authorization are specified for each role, to the various API functionalities and services.
For the scope of this document, we cover the Automation API interface access category. To use the Authentication service to create, update, delete, or get details of your own tokens, you must have such category.

#### Employee Authentication

- **Provider**: Okta Workforce Identity
- **Access Patterns**:
  - Administrative access
  - Operation
  - Developer

#### API Authentication

- **Provider**: Control-M Authentication service
- **Access Patterns**:
  - Support tools
  - Monitoring systems
  - CI/CD pipelines
- **Security Controls**:
  - LDAP/Active Directory
  - API key authentication

### Technology Stack

| Layer | Technology | Purpose | Version |
|-------|------------|---------|---------|
| Orchestration | Control-M Enterprise Manager | Central point of access and control for Control‑M/Servers | Latest LTS |
| Processing | Control-M Server | Process jobs and handle communication between EM and agents | Latest LTS |
| Storage | Control-M Server DB | Stores data associated with CTM server and agents in specific LoB | Latest LTS |
| Integration | Control-M Automation API | Interface to test, run and deploy job definitions and packages | Latest LTS |
| Execution | Control-M Agents | Submit and monitor jobs, perform post-processing analysis | Latest LTS |
| CLI Tools | CTM CLI, Control-M Automation CLI | Communicate with Automation API for management tasks | Latest LTS |
| CI/CD | GitHub Actions | Local Github runner in LoB environment for automation | Latest |
| Secrets | HashiCorp Vault | Securely manage secrets and protect sensitive data | Latest |

## Implementation Guide

### Prerequisites

- GitHub repository with appropriate permissions
- Control-M Server v9.0.20 or higher
- Enterprise Manager with Automation API enabled
- Network connectivity between:
  - GitHub runners and Control-M EM
  - Control-M EM and Servers
  - Control-M Servers and Agents

### Quick Start

1. Create a github repository with workflows and initial files
2. Configure authentication and authorization in Enterprise Manager
3. enable automation API in Enterprise manager via Web UI
4. Setup GH workflows, following the reference repository

### Configuration Reference

## Operating Model

### Monitoring & Alerting

- Key metrics related to jobs and executions
- Alert thresholds
- Github workflows/actions

### Scaling Considerations

- Performance limits
- Average execution time for job deployment
- Parallel execution for job definitions
- Cost implications with Github runners, workflow execution time

### Security & Compliance

- Security controls
- Compliance requirements
- Audit procedures

### Cost Management

- Monthly cost review procedures
- Budget alerting thresholds
- Cost allocation (tagging strategy)
- Optimization review schedule
- Waste identification processes

## Support & Contact

For questions regarding usage, contact: Rafael Nize

### Getting Help

- Internal Documentation: [Confluence](https://wexinc.atlassian.net/wiki/spaces/EnCompass/pages/154290651653/Control-M+In-depth)
- API Installation: [BMC external link](https://docs.bmc.com/xwiki/bin/view/Control-M-Orchestration/Control-M/Control-M-Automation-API/ctmapimonthly/Installation/#Installation-download)

### Design Consultation

WEX Contacts who contributed to this solution:

- Rafael Nize <[<EMAIL>](mailto:<EMAIL>)>: Software Development Engineer

### Components

- **Control-M Enterprise Manager** - provides a central point of access and control for Control‑M/Servers.
- **Control-M Server** - responsible to process jobs and also communication between Control-M enterprise manager (EM) and agents
- **Control-M Server DB instance** - stores data associated with CTM server and agents in the specific line of business, LoB
- **Control-M Automation API** - offers an interface to test, run and deploy job definitions and packages to Control-M.
- **Control-M Agents** - submit jobs for execution on the host, monitor jobs, and perform post-processing analysis of completed jobs.
- **Management tools (CTM CLI, Control-M Automation CLI)** - communicate with Automation API, offering management tasks
- **Github actions and workflows** - Github code, responsible for CI/CD pipelines and automate tasks through software development lifecycle
- **Local Github runner** in a desired line of business environment, LoB, github runner, which is machines a machine that executes Github workflows
- **HashiCorp Vault** is a tool for securely managing secrets and protecting sensitive data. It provides a unified interface to manage and control access to secrets across different environments and systems.
- **Secret vault application** - in this document, we refers to GH repository secrets and Hashicorp Vault
- **Authentication service (Active Directory)** - responsible to manage information about users, devices and other resources. Control-M can integrate with Active Directory to facilitate user authentication.

### Technology

Control-M/EM Services:

- **Configuration Agent** - responsible for managing configuration changes and updates across Control-M components
- **Health Monitor** - monitors the health and performance of Control-M services and components
- **Control-M Web** - provides web-based access to Control-M functionality and interfaces
- **Scheduling Service** - handles job scheduling and orchestration across the Control-M environment
- **Control-M/Server** - manages job execution and communication between Control-M components
- A **Control-M Enterprise Manager** as a central point of the architecture. Enterprise Manager sends job commands and receives job statuses from the Control-M servers.
- A **Control-M server** can handle multiple CTM agents in multiple networks or LoB infrastructure. For the purpose of this document, we assume a Control-M Enterprise Manager EM with, at least, one CTM Server deployed in a line of business.

Both EM and Server components can be deployed in a high availability schema. To reduce the chance of data loss and reduce the RTO, a high availabily deployment for servers and databases is recommended.

Other services and applications:

- **Database schemas (EM and Server componments)** are deployed in a SaaS architecture/service such as "Azure Database for PostgreSQL - Flexible Server" or "AWS RDS for PostgreSQL" since they provide a set of common administrative tasks such as patching, backup-up, provisioning and scheduling maintenance. An optional high availability solution is also one of the features offered by those services described (multi-zone or multi-region).
- **Github actions and workflows** - responsible for the CI/CD pipelines, it allows building continuous integration and continuous deployment pipelines for testing, releasing and deploying software without the use of third-party websites/platforms
- **GitHub Secrets** are a secure way to store sensitive information, such as API keys, tokens, passwords, and other credentials, that you need to use in your GitHub Actions workflows.
- **Naming service** - A DNS service is important to keep API endpoints available everytime, in a Load Balancer configuration, as a HA solution.
- **API tokens and authentication** - required to the Automation API
- **HashiCorp Vault** - a tool for securely managing secrets and protecting sensitive data. It provides a unified interface to manage and control access to secrets across different environments and systems.
- **User authorization and authentication** - user and roles can be managed internally, via the Enterprise Manager component, however, BMC recommends an external authentication with an Identity Provider (IdP), through SAML 2.0 protocol. For the purpose of this document, following Wex standards, we will LDAP method (Active Directory)

### Terminology

- **Control-M**: A workload automation software from BMC Software that schedules, manages, and monitors batch jobs.
- **CI/CD**: Continuous Integration and Continuous Deployment. A practice in software development where code changes are automatically tested and deployed.
- **GitOps**: A set of practices that use Git repositories as the source of truth for managing infrastructure and application code.
- **Control-M Enterprise Manager (EM)**: The central component of the Control-M architecture that manages job scheduling and execution across multiple Control-M servers.
- **Control-M Server**: A component that executes jobs and communicates with Control-M agents.
- **Control-M Agent**: A software component installed on target machines to execute jobs as instructed by the Control-M server.
- **GitHub Actions**: A CI/CD platform that allows you to automate your build, test, and deployment pipeline.
- **GitHub Secrets**: A secure way to store sensitive information, such as API keys and tokens, used in GitHub Actions workflows.
- **HashiCorp Vault**: A tool for securely managing secrets and protecting sensitive data across different environments and systems.
- **SAML 2.0**: Security Assertion Markup Language. A protocol used for single sign-on (SSO) and exchanging authentication and authorization data between parties.
- **LDAP**: Lightweight Directory Access Protocol. A protocol used to access and manage directory information services, such as Active Directory.
- **API Token**: A token used to authenticate API requests, providing secure access to the API.
- **High Availability (HA)**: A system design approach that ensures a certain degree of operational continuity during a given period.
- **Load Balancer**: A device or software that distributes network or application traffic across multiple servers to ensure reliability and performance.
- **DNS Service**: Domain Name System service. It translates domain names into IP addresses, allowing users to access websites using human-readable addresses.

### Design Considerations

This reference architecture can be used in the transition of a traditional Control-M environment, based on bare metal (or virtualized servers) to the cloud. A single Control-M Enterprise Manager can coordinate multiple Control-M server hosts, each one in its respective line of business network. Different cloud providers can be used in the architecture described here. A high availability option, which includes a fail-over/spare server for the Enterprise Manager, Server and Agents is accepted.

Job definition automation is delivered by CTM Automation API, consumed by Github CI/CD pipelines, responsible to validate and deploy such jobs

Redundancy and high availability for the database components are expected. With the intent of reducing administrative tasks, we consider an IaaS or SaaS in this case. AWS and Microsoft Azure have equivalent database services  Such services offer also backup and multi-zone, multi-region database instances

Although Control-M supports Postgres, Oracle DB, and Microsoft SQL Server, including stand-alone installations from these, we focus on the first option: an already deployed Postgres schema over AWS RDS or Azure PostgreSQL Flexible server. Such installation option is important to maintain the compability with a high available schema.

Container-based deployment is not covered by this document.

Github runners in this architecture have a key role, sending commands to CTM Automation API and receiving statuses from the executions or possible errors. Is essential that the runner has proper access to the API, hosted in the CTM Enterprise Manager component/server.

-----------------------------------------------------------------

## Procedures

### Provisioning

Infrastructure as Code IaC is the recommended approach to setup and deploy the infrastructure related to Control-M components. Configuration management tools are desired for this reference architecture. They make the process easier to reproduce and debug possible issues when adjusting configuration parameters. A code repository/version control is desired for both IaC code and configuration management files. This practice helps in auditing and troubleshooting cases.
For the purpose of this document, we will consider Automation API configuration, Job definition pipelines and their repositories, only.
Instance, database and network provisioning are out of scope.

Repository configuration:

- GH Workflows
- GH Actions
- GH Secrets
- Repository policies

GH runners and secret management

- Github runners
- Vault secrets
- Networking in the LoB
  - GH runner must reach the Control-M Enterprise Manager server and the Automation API
- Disaster recovery scenario for Github runners

Control-M Enterprise Manager configuration

#### Repository Structure

- Job definition files are stored in a **controlm** folder undernath a specific environment’s folder. For example, QA environment has a “qa” folder and a “controlm” folder down below, with job definition files for such environment.

**Job definition files**
Job definition are stored in .json format.

A job is an execution unit, such as a script or command, that is executed at the operating system level, or part of an external application, such as Hadoop, Microsoft SQL server

[Further information can be found at BMC product’s page](https://documents.bmc.com/supportu/API/Monthly/en-US/Documentation/API_CodeRef_JobProperties.htm)

#### Installation tasks

- Create github workflows and make sure GH actions can run.
- Create GH repository secrets or Hashicorp Vault secrets to store the API service username and password
- Create a new job definition file and store in the **controlm** directory. This will be the source directory job definitions will be consumed from.
- Add a new user or role

### Workflow process

![Workflow diagram](workflow.png)

In your solution repository:

- Create a local topic branch, from the main branch
- Commit to local topic branch early and often
- Discrete single-purpose commits
- Include meaningful commit messages
- Fetch & merge/rebase latest changes from upstream branch before pushing remote
- Push to remote topic branch
- Create a pull request (PR)
- Pull request reviews

The CI/CD pipelines on this project use Hashicorp Vault as a secret management solution. Control-M Enterprise Manager (EM) provides the Automation API endpoint which must be reacheable by the Github runners, specific to the line of business. Such runners make API calls, sending and retrieving messages or "payloads".

## Acronyms / Terms

| Acronym     | Definition |
| ----------- | ----------- |
| CTM         | BMC Control-M |
| EM          | Control-M Enterprise Manager |
| CM          | Control-M Server |
| Pipeline / GitHub Action / Workflow | Used interchangeably throughout, a process in which to execute steps in the CI/CD process. |

### Administration & Maintenance

For support regarding the implementation of this reference architecture, please consult with the contacts listed within the [Design Consultation](#design-consultation).

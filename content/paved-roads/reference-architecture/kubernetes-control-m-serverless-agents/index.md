<!-- Parent: Paved Roads -->
<!-- Parent: Reference Architecture -->
<!-- Title: RFC-493 Kubernetes Control-M Serverless Agents Reference Architecture -->

<!-- Include: macros.md -->
# Kubernetes Control-M Serverless Agents Reference Architecture

:draft:

```text
Author: <PERSON>
Publish Date: 2025-04-28
Category: Paved Roads
Subtype: Reference Architecture
```

## Executive Summary

This reference architecture outlines the deployment and management of Control-M Serverless Agents running in Kubernetes. It provides a standardized approach for implementing scalable, resilient batch job processing with reduced operational overhead by leveraging containerization and Kubernetes orchestration capabilities for Control-M workloads.
Kubernetes installation and provisioning is out of the scope for the purpose of this document.

## Usage Guidance

### When to Use

- ✅ Batch workloads requiring elastic scaling based on processing demands
- ✅ Environments with existing Kubernetes infrastructure (AWS EKS or Azure AKS)
- ✅ Applications needing centralized job scheduling with distributed execution
- ✅ Operational overhead reduction for job scheduling agents
- ✅ Workloads with variable resource requirements throughout the day/week

### When to Avoid

- ❌ Simple scheduling use cases with minimal scaling requirements
- ❌ Environments without Kubernetes expertise or infrastructure
- ❌ Applications requiring direct host system access not available to containers
- ❌ Ultra low-latency job execution requirements (sub-millisecond)
- ❌ Workloads dependent on specialized hardware acceleration

## Source Repository

This reference architecture has an accompanying GitHub repository containing implementation resources:

- **Repository URL**: [github.com/wexinc/kubernetes-control-m-serverless-agents](https://github.com/wexinc/kubernetes-control-m-serverless-agents)

The repository includes:

- Kubernetes manifest files and Helm charts
- GitHub Actions workflow definitions
- Example job definitions and configurations
- Deployment and maintenance scripts

## Value Proposition

### Business Benefits

- **Cost Optimization**: Pay only for resources used during job execution with auto-scaling
- **Time-to-Market**: Faster deployment of new job agents through containerization
- **Risk Reduction**: Enhanced resilience through Kubernetes self-healing capabilities
- **Operational Efficiency**: Reduced agent maintenance compared to traditional Control-M agents

### Technical Benefits

- **Standardization**: Consistent agent deployment across all environments
- **Scalability**: Automatic scaling based on workload demand
- **Resilience**: Self-healing capabilities through Kubernetes orchestration
- **Portability**: Consistent deployment across AWS EKS and Azure AKS
- **GitOps Integration**: Control-M job definitions as code with version control

## Cost Optimization Controls

- **Right-Sizing**: Configure appropriate resource limits for job containers
- **Auto-Scaling**: Scale to zero when no jobs are running to eliminate idle costs
- **Spot Instances**: Leverage Kubernetes node selectors to use spot/low-priority instances for non-critical jobs
- **Resource Quotas**: Implement namespace resource quotas to prevent runaway costs
- **Horizontal Pod Autoscaler**: Optimize resource utilization based on CPU and memory metrics

## Architecture Overview

### Cloud Architecture Diagram

![CTM Kubernetes Agents](KubernetesDiagramOnprem.png)

```mermaid
graph TD
    CTMEM -->|Schedule Jobs| K8S[Kubernetes Cluster]
    
    subgraph "Kubernetes Cluster"
        SA[Serverless Agent Pod] -->|Execute Jobs| JS[Job Execution Services]
        HPA[Horizontal Pod Autoscaler] -->|Scale| SA
        JS -->|Results| CTMEM
    end
    
    subgraph "Authentication & Identity"
        K8S --> MSI[Managed Service Identity]
        MSI --> Okta[Okta Workforce Identity]
    end
```

### Authentication & Identity

#### Employee Authentication

- **Provider**: Okta Workforce Identity
- **Access Patterns**:
  - Administrative access to Control-M Enterprise Manager
  - CI/CD pipeline access for job deployments
  - Monitoring and support access
- **Security Controls**:
  - Role-based access control
  - Multi-factor authentication
  - Session management

#### Service Authentication

- **Provider**: Control-M Authentication service with HashiCorp Vault
- **Access Patterns**:
  - Serverless Agent to Enterprise Manager communication
  - Job execution service authentication
  - API access for monitoring and management

- **HashiCorp Vault Integration**:
  - **Dynamic Secrets**: Auto-generated credentials with defined TTL
  - **Secret Paths**:
    - `/shared/kvv2/<secret_folder> <secret_name>`

  - **Access Control**:
    - RBAC with least privilege principle
    - Service account authentication using Kubernetes auth method
    - Namespace isolation for different environments

- **Security Controls**:
  - Automated secret rotation with audit trails
  - API key authentication with short-lived tokens
  - Secret versioning and lifecycle management
  - Integration with WEX's key management system

### Technology Stack

| Layer                  | Technology                  | Purpose                                 | Version     |
|------------------------|----------------------------|-----------------------------------------|-------------|
| Orchestration          | Control-M Enterprise Manager| Central scheduling and monitoring        | Latest LTS  |
| Containerization       | Control-M Serverless Agent  | Containerized job execution             | Latest      |
| Container Orchestration| Kubernetes (AKS/EKS)        | Container management and scaling        | 1.28+       |
| GitOps                 | GitHub Actions              | CI/CD for job definitions               | Latest      |
| GitOps Deployment      | ArgoCD                      | Declarative application delivery        | Latest      |
| Package Management     | Helm Charts                 | Kubernetes application packaging/deploy | Latest      |
| Secret Management      | HashiCorp Vault             | Secure credential storage               | Latest      |
| Identity               | Okta Workforce Identity     | Authentication and authorization        | Latest      |

## Implementation Guide

### Prerequisites

- Existing Kubernetes cluster (AKS or EKS)
- Control-M Enterprise Manager with Automation API enabled
- Network connectivity between Kubernetes cluster and Control-M EM
- Appropriate RBAC permissions in Kubernetes
- GitHub repository for job definitions and CI/CD workflows
- Control-M Agent container image in JFrog Artifactory
- Helm CLI installed in a workstation
- Helm charts to install the solution
- A secret management system to store API token (i.e. Hashicorp Vault)

>**Notice:**
> Component versions must match the minimum compatibility requirements.

Supported versions of Kubernetes for the Control-M 9.0.21.315 are currently from 1.28 to 1.31

### Control-M-for-Kubernetes-Compatibility

| Component | Minimum Version | Installation |
|-----------|-----------------|--------------|
| Control-M/EM | 9.0.21.100* | [Control-M/Enterprise Manager Installation](https://documents.bmc.com/supportu/9.0.21.300/en-US/Documentation/Control-M_Enterprise_Manager_installation.htm) |
| Control-M/Server | 9.0.20.200 | [Control-M/Server Installation](https://documents.bmc.com/supportu/9.0.21.300/en-US/Documentation/Control-M_Server_installation.htm) |
| Control-M Automation API | 9.0.21.340 | [Control-M Automation API Installation](https://documents.bmc.com/supportu/API/Monthly/en-US/Documentation/API_Installation.htm) |

### Quick Start

Notice: we assume that Kubernetes plug-in is already installed on Control-M Enterprise Manager

Download the official chart from the vendor:

1. Deploy the Control-M Serverless Agent Helm chart to your Kubernetes cluster:

   ```bash
        helm install <releaseName> controlm/controlm-agent --version 9.21.315 \
            --set server.name=LocalControlM \
            --set server.host=controlm --set server.port=7005 --set server.ip=*********** \
            --set api.endpoint=https://controlm:8443/automation-api \
            --set api.token=<apiToken> \
            --set pvc.storageClass=efs-sc
   ```

2. Configure the Serverless Agent in Control-M Enterprise Manager:
   - Add the Serverless Agent host group
   - Assign appropriate job execution permissions
   - Configure connection profiles

3. Create and deploy your first job definition:

   ```json
   {
     "jobDefinition": {
       "job1": {
         "Type": "Job:Command",
         "ConnectionProfile": "KUBERNETES",
         "SubApplication": "demo",
         "Host": "k8s-serverless",
         "Command": "echo 'Hello World'"
       }
     }
   }
   ```

### Configuration Reference

#### Installation procedure

This procedure describes how to set up Control-M for Kubernetes:

- Ensure that your Kubernetes environment meets the requirements listed in [Control-M for Kubernetes Compatibility](#Control-M-for-Kubernetes-Compatibility)
- Publish the Kubernetes Plug-in in Control-M/EM.
- (Optional) Set up the dynamic deployment of additional plug-ins.
- Prepare your container image.
- Prepare Persistent-Storage.
- Prepare a Service Account with minimum privileges to operate
- Setup Control-M Access Control policies.
- Deploy Agents Using a Helm Chart and ArgoCD

### Publish the Kubernetes Plug-in in Enterprise Manager

In your workstation:

1. Log in to the Control-M/EM server via console or SSH as an administrator.
2. Create a temporary directory to save the downloaded files.
3. Download Kubernetes plug-in version 2.1.02 from the Kubernetes plug-in download page in the EPD site.
4. Publish the plug-in by saving the downloaded zip file in the following location:
  -- UNIX: $HOME/ctm_em/AUTO_DEPLOY
  -- Windows: <EM_HOME>\AUTO_DEPLOY

### Prepare Persistent Storage

Persistent storage is required to run multiple agents also such storage must support the Kubernetes *ReadWriteMany* access mode.

BMC recommends that you run multiple Agents on separate nodes in the namespace.
The default is two Agents.
To support multiple Agents, the storage class (and underlying storage technology) must support ReadWriteMany access mode (for example, a Storage Class for Amazon EFS).
The recommended storage size is 5Gi per agent.

```yaml

apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: efs-sc
provisioner: efs.csi.aws.com  # For AWS EFS
parameters:
  provisioningMode: efs-ap    # EFS access point provisioning
  fileSystemId: fs-xxxx       # Your EFS file system ID
  directoryPerms: "700"       # Permissions for created directories
reclaimPolicy: Retain
volumeBindingMode: Immediate
allowVolumeExpansion: true

```

### Prepare Control-M Access and Authorizations

#### Active Directory

Active directory users at Wex are created via form at Employee service portal. If you or someone else require an account, open a case at [*Active Directory Support Request*](https://wexinc.atlassian.net/servicedesk/customer/portal/1016/create/1834)

#### Control-M Enterprise Manager

At the Control-M side, you will need to create a role in the Enterprise Manager. A local admin user is also advised for emergency purposes.

Creating an internal user via Enterprise Manager web or GUI

1. From the ![check](icons/I_Square_Web_Dropdown_26x29.png) icon, select *Configuration*, The Configuration domain opens.
2. From the ![check](icons/I_Web_Dropdown_31x29.png) drop-down list, select *Users*. The Users tab appears.
3. Click *Add User*. The Add User dialog box appears.
4. In the *User Name* field, type the username that you want to add to Control-M.
5. (Optional) In the *Full Name* and *Description* fields, type the full name of the user and a description.
6. In the Assigned Roles drop-list, select one or more existing roles to assign to this user.
7. Following the security standards, authentication must be configured with LDAP (Active Directory) option as follows:
    - select the Enable External Authentication Only checkbox and define the LDAP User and Domain field by typing the domain name that hosts the LDAP servers that authenticates the Control-M/EM users in the following format:
    - *CN[OU]@DC*
    - where CN=user, OU=org_unit, and DC=domain.
    - The associated LDAP group appears in the User Details tab of the user. You can use this information to copy the LDAP group to another user with the same attributes.

>**Notice:**
> We assume there is an internal user with admin privileges already created in case of emergency or Active Directory is not available. Such user password must be secured in a vault.

#### Kubernetes

In the kubernetes cluster, you have to create a service account.
A Service Account is an identity used by processes in a pod to control permissions and access levels.

Use this manifest as a reference:

```yaml
# Sample Role for running Kubernetes jobs
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: job-executor
rules:
- apiGroups:
  - batch
  resources:
  - jobs
  - jobs/status
  verbs:
  - create
  - get
  - list
  - delete
- apiGroups:
  - ""
  resources:
  - pods
  - pods/log
  verbs:
  - get
  - list
```

Grant the role you created to the Agent pod service account, using this role binding, making changes specific to your organization

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: controlm-agent-job-privileges
  namespace: namespace_where_jobs_run
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: job-executor
subjects:
- kind: ServiceAccount
  name: controlm-agent-pod-serviceaccount
  namespace: namespace_where_agent_is_running
```

Please, check this document for further information about [Service Account Privileges](
https://docs.bmc.com/xwiki/bin/view/Control-M-Orchestration/Control-M/Control-M-for-Kubernetes/CTMK8S/Setting-Up-Control-M-for-Kubernetes/Service-Account-Privileges/)

<https://documents.bmc.com/supportu/9.0.21.300/en-US/Documentation/Users_and_Roles.htm>

### Control-M Agent serverless container image

BMC offers a simple image available at Docker Hub, with no TLS/SSL certificates available. For the purpose of this document, we assume TLS termination will be handled by load balancer with a HTTPS listener such as AWS Application Load Balancer or Network Load Balancer, so, no certificates needed inside the container or image.
Avoiding any external service unavailability, we recommend that container image has be pushed to Wex's Jfrog Artifactory.
In some situations, a new container image must be built, following Wex standards and recommendations from the vendor. To simplify the process, in this document, we refer to the official BMC image, available for download at [Control-M docker image](https://hub.docker.com/r/controlm/agent) and published in the Artifactory.
After the image is built, it should be pushed to Wex JFrog Artifactory repository. Please contact the artifactory SaaS support team at Wex in case you need assistance in repository creation.

For more information about JFrog Artifactory, check the [JFrog Artifactory Confluence page](https://wexinc.atlassian.net/wiki/spaces/WH/pages/154109378713/JFrog+Artifactory)

#### Build container image

Before creating the container image, make sure you have Wex certificates available. You must have one of the following for the component where SSL configuration is deployed:

- Private key, certificate, trusted root CA certificate and the certificate chain, all in PEM format, and the password of the private key. The certificates must not be locked/protected by password.

- PKCS#12 file that includes the private key, certificate, trusted root CA certificate and the certificate chain. If the PKCS#12 contains multiple certificates and key pairs, then you must also have available the name of the pair to use. You must also have the password of the keystore.

More information about TLS and SSL certificates can be found on [Wex User Guide on SSL Certificates](https://wexinc.atlassian.net/wiki/spaces/COAWS/pages/153780289876/WEX+User+Guide+on+SSL+Certificates.)

> [!note]
> The version of Control-M/EM listed in the CCM must be the same version of Control-M/EM that you want to generate certificates for in this procedure.
> Control-M/Server and Agent can be any supported version.
> The certificates for all components must be signed by the same root CA.

### Install Control-M agents using Helm charts

1. Add a repository named controlm to contain the Helm charts of the Control-M/Agent that is obtained from the Control-M Repository by running the following command:
helm repo add controlm <https://controlm-charts.s3.us-west-2.amazonaws.com/>

2. Ensure that the controlm repository is listed as one of your repositories by running the following command:
helm repo list

3. List the charts within the new controlm repository by running the following command:
helm search repo controlm

4. Replace server name, hostname, TCP port and the API token accordingly.

Obtain the parameters and replace them in the command below, before executing it.
Tip: you can get some connection details using  *ctm config servers::get* <https://documents.bmc.com/supportu/API/Monthly/en-US/Documentation/API_Services_ConfigService_Servers.htm#configserversget>

```bash
helm install <releaseName> controlm/controlm-agent --version 9.21.315 \
--set server.name=LocalControlM --set server.host=controlm --set server.port=7005 --set server.ip=*********** \
--set api.endpoint=https://controlm:8443/automation-api \
--set api.token=<apiToken> \
--set pvc.storageClass=efs-sc
```

#### App-of-Apps Pattern Implementation

The app-of-apps pattern provides a scalable approach to managing multiple Control-M agent deployments across environments. The structure follows WEX's standard template:

```yaml
# Root Application (app-of-apps)
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: controlm-agents
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/wexinc/controlm-k8s-config
    path: apps
    targetRevision: HEAD
  destination:
    server: https://kubernetes.default.svc
    namespace: controlm-system
  syncPolicy:
    automated:
      prune: true
      selfHeal: true

# Child Application Example (apps/controlm-agent.yaml)
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: controlm-agent-prod
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/wexinc/controlm-k8s-config
    path: environments/prod
    targetRevision: HEAD
  destination:
    server: https://kubernetes.default.svc
    namespace: controlm-prod
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
```

The app-of-apps pattern enables:

- Centralized management of multiple Control-M agent deployments
- Environment-specific configurations and policies
- Automated synchronization and drift detection
- Simplified promotion between environments
- Consistent deployment practices across teams

The ArgoCD integration provides several key benefits:

- Automated consistency checks
- Git-based audit trails
- Automated rollbacks
- Drift detection and remediation

#### HashiCorp Vault Integration

ArgoCD integrates with HashiCorp Vault to manage Control-M agent secrets:

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: controlm-secrets
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/wexinc/controlm-k8s-config
    path: vault-config
    targetRevision: HEAD
  destination:
    server: https://kubernetes.default.svc
    namespace: controlm-system
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
```

Secret paths follow WEX's standard convention:

- Control-M credentials: `/shared/kvv2/controlm/agent-credentials`
- Environment-specific configs: `/shared/kvv2/controlm/env/<env-name>`
- Kubernetes service accounts: `/shared/kvv2/controlm/k8s-auth`

### Integrate with JFrog Artifactory

#### Push Docker Image to Artifactory

Before making the Control-M agent available through Helm charts, push the Docker image to WEX's JFrog Artifactory:
You need to open a ticket to the Artifactory team asking to make the docker image available through docker registry and make a helm repository available.

#### Package and Push Helm Chart

1. Create a chart structure:

```bash
helm create controlm-agent
```

Update the `values.yaml` with Artifactory image reference:

```yaml
image:
  repository: wex-docker.jfrog.io/controlm/agent
  tag: "9.21.315"
  pullPolicy: IfNotPresent

imagePullSecrets:
  - name: artifactory-pull-secret
```

1. Create a Chart.yaml for the Control-M agent:

   ```yaml
   apiVersion: v2
   name: controlm-agent
   description: Control-M Serverless Agent for Kubernetes
   version: 1.0.0
   appVersion: "9.21.315"
   ```

2. Package and push the Helm chart to Artifactory:

   ```bash
   helm package .
   helm push controlm-agent-1.0.0.tgz wex-helm
   ```

#### ArgoCD Integration with Artifactory

Update your ArgoCD application to use the Artifactory-hosted Helm chart:

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: controlm-agent
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://wex.jfrog.io/artifactory/helm-local
    chart: controlm-agent
    targetRevision: 1.0.0
    helm:
      values: |
        image:
          repository: wex-docker.jfrog.io/controlm/agent
          tag: "9.21.315"
        imagePullSecrets:
          - name: artifactory-pull-secret
  destination:
    server: https://kubernetes.default.svc
    namespace: controlm-system
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
```

#### Artifactory Access Configuration

1. Create a Kubernetes secret for Artifactory authentication:

   ```yaml
   apiVersion: v1
   kind: Secret
   metadata:
     name: artifactory-pull-secret
     namespace: controlm-system
   type: kubernetes.io/dockerconfigjson
   data:
     .dockerconfigjson: <base64-encoded-docker-config>
   ```

2. Configure image pull secrets in your deployment:

   ```yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: controlm-agent
   spec:
     template:
       spec:
         imagePullSecrets:
         - name: artifactory-pull-secret
   ```

> [!important]
>
> - Always use specific versions for production deployments instead of the `latest` tag
> - Ensure proper access controls are configured in Artifactory
> - Regularly scan container images for vulnerabilities
> - Follow WEX's container security guidelines

### Additional deployment scenarios

```yaml
# Kubernetes values.yaml for Control-M Serverless Agent
controlM:
  server: "ctm-server.example.com"
  hostGroup: "k8s-agents"
  imageTag: "latest"
  
resources:
  requests:
    memory: "512Mi"
    cpu: "500m"
  limits:
    memory: "2Gi"
    cpu: "2"

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
```

## Operating Model

### Monitoring & Alerting

### Scaling Considerations

#### Control-M Agents

- Kubernetes Job Limitations in Control-M
- Maximum number of executions per agent
- Maximum number of jobs per agent
- Job output size

#### Kubernetes cluster and workers

- **Horizontal Scaling**: Configure HPA to scale based on CPU/memory metrics
- **Vertical Scaling**: Adjust resource requests/limits based on job requirements
- **Scaling Limits**: Set appropriate maxReplicas to control cost
- **Scaling Triggers**: Consider custom metrics for job-queue-based scaling
- **Node Affinity**: Configure node affinity for specialized job requirements

### Security & Compliance

- **Pod Security Context**: Run containers with least privilege
- **Network Policies**: Restrict network communication to required services
- **Secret Management**: Use HashiCorp Vault for credentials
- **Image Security**: Scan container images for vulnerabilities
- **Audit Logging**: Enable Kubernetes audit logs and Control-M job auditing

### Cost Management

- **Monthly Review**: Schedule regular cost reviews for Kubernetes resources
- **Resource Quotas**: Implement namespace resource quotas for control
- **Tagging Strategy**: Use consistent labels for chargeback/showback
- **Idle Detection**: Monitor and alert on idle agent pods
- **Right-Sizing**: Regularly review and adjust resource requests/limits

## Support & Contact

### Getting Help

- [Kubernetes plug-in download page:](https://www.bmc.com/available/ddl.html?path=/LP/432491/432492&fltk_=H1Xk4lhaAEEB4zdvEUcM1h6T8hJksIsesTplqG41Fye3juWNbvqKNkmGfUTL11Lj)
- [Control-M for Kubernetes](https://docs.bmc.com/xwiki/bin/view/Control-M-Orchestration/Control-M/Control-M-for-Kubernetes/CTMK8S/)
- [Certificate basics](https://wexinc.atlassian.net/wiki/spaces/WH/pages/154291798184/Security+of+WEX+Health+Services.+Certificate+basics)
- [Vault migration](https://wexinc.atlassian.net/wiki/spaces/WF/pages/154591428666/2024+Vault+connection+migration)
- [ArgoCD](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155503886355/RFC-517+Argo+CD)

### Design Consultation

WEX Contacts who contributed to this solution:

- Rafael Nize <[<EMAIL>](mailto:<EMAIL>)>: Software Development Engineer

<!-- Parent: Paved Roads -->
<!-- Parent: Reference Architecture -->
<!-- Title: RFC-452 OWI For Mobile Applications -->
# OWI For Mobile Applications

```text
Author: <PERSON><PERSON> Mittal
Publish Date: 2025-03-07
Category: Paved Roads
Subtype: Reference Architecture
```

## Overview

This reference architecture provides a comprehensive guide for implementing mobile application authentications using Auth0. It covers the setup, configuration, and best practices to ensure secure and efficient authentication processes.

## Background

The OneWEX Identity Project provides universal login to users for logging into web and mobile applications using single set of credentials.our objective is to enable customers with a single login experience across all applications. This reference architecture specifically talks about OWI for mobile based applications. For more details on OWI, please refer https://wexinc.atlassian.net/wiki/spaces/DCS/overview?homepageId=153743982944

## AS-IS Architecture

![Architecture Diagram](As-Is_Arch.png)

As seen in the diagram above, Wex Inc offers a range of products to its customers. Here are a few examples:

- Encompass: Currently integrated with an in-house developed stack for authentication and authorization.
- Fleet
- Health Core
- CDH Consumer Portal

As part of Wex Direct, there are multiple products, approximately 60 in total, each of which has an isolated user security store to handle authentication (authN) and authorization (authZ).
Each of the applications independently manages their authentication (authN) and authorization (authZ) flows for users, and these flows may vary between apps.

## TO-BE Architecture

![Architecture Diagram](To-Be-Arch.png)

All applications are onboarded to Auth0.All users in all Wex Direct applications are present in the Auth0 database connection.

## Cloud Architecture

![Architecture Diagram](cloud-arch.png)

## Detailed Level Architecture

![Architecture Diagram](Detailed_Level_Arch.png)

## Sequence Diagram for Mobile-based application

![Architecture Diagram](OWI_Mobile_Flow.png)

## Components

- **Auth0**: Primary cloud provider for authentication services.
- **Mobile Application**: The client application that requires authentication.

## Authentication Flow

1. The user clicks Login within the mobile application.
2. Auth0’s SDK is going to render a WebView for where the user is going to authenticate.
   - This WebView will go through the owi-mod-auth /authorize endpoint.
   - The owi-mod-auth will create a request to the Auth0 Authorization Server (/authorize endpoint).
   - The Auth0 Authorization Server returns to the owi-mod-auth login and authorization prompt URL.
   - The owi-mod-auth forwards back to the mobile app the login/authorization URL.

3. The user authenticates using one of the configured login options and may see a consent page listing the permissions Auth0 will give to the application.
4. The Auth0 will redirect back to the callback URI with a code to the callback URI.
   - The Auth0 SDK is going to provide this code to owi-mod-auth to request the token.
   - owi-mod-auth is going to request the token from Auth0
   - The Auth0 Authorization Server responds to owi-mod-auth with an ID token and access token to the mobile app.

5. From now on, the calls shall be done using the access token/ID token provided. Since it’s not recommended to use the refresh token, every time that is needed, he user will be forced to re-authentication from step #1.
6. Your application can use the access token to call an API to access information about the user.
7. The API responds with the requested data.

## Best Practices

- Use secure storage for tokens.
- Implement multi-factor authentication (MFA).
- Regularly update dependencies and libraries.

## Useful Links

- <https://auth0.com/docs/get-started/authentication-and-authorization-flow/authorization-code-flow/call-your-api-using-the-authorization-code-flow>
- <https://auth0.com/docs/quickstart/native>
- <https://github.com/auth0/Auth0.Android>
- <https://auth0.com/docs/libraries/auth0-android>
- <https://auth0.com/docs/libraries/auth0-swift>

## Maturity Score

- **Security**: High
- **Scalability**: Medium
- **Maintainability**: High
- **Performance**: Medium

## Recommendations for Improvement

- Enhance scalability by implementing load balancing.
- Optimize performance by reducing authentication latency.
- Regularly review and update security policies.

## Conclusion

This reference architecture provides a robust framework for mobile application authentications using Auth0. By following the guidelines and best practices outlined, you can ensure a secure and efficient authentication process for your mobile applications.

<!-- Parent: Paved Roads -->
<!-- Title: Reference Architecture -->
# Reference Architecture

A reference architecture is a blueprint or a set of guidelines that provides a recommended structure and design for building a particular type of system or application. It serves as a reference point for developers, architects, and other stakeholders involved in the software development process.

Reference architectures are valuable resources as they help in reducing development time, improving system quality, and promoting consistency across projects. They provide a starting point for developers and architects, allowing them to focus on solving specific business problems rather than reinventing the wheel.

> <!-- Info -->
> Info
> While reference architectures provide significant benefits, they are not always necessary. If there is no practical path for re-use, implementing a reference architecture may not be the best approach. In such cases, it may be more efficient to focus on developing a custom solution tailored to the specific needs of the project rather than adhering to a reference architecture that may not provide tangible benefits.

## Reference Architecture Index

These are the recommended reference architectures for consideration in your solution designs.

> <!-- Info -->
> Note
> The following content is a work-in-progress.  The Architecture organization is compiling a formalized collection of existing reference architectures used at WEX.
>
> If you would like to contribute a reference architecture profile, refer to the [Drafting a Reference Architecture](#drafting-a-reference-architecture) section below.

### Cloud Agnostic Architectures

- [**Descriptive Name**](#reference-architecture-index)

### AWS Architectures

- [Secure Hosting For Static AWS S3 CDN](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154808713383/RFC-338+Secure+Hosting+For+Static+AWS+S3+CDN)
- [**Descriptive Name**](#reference-architecture-index)

### Azure Architectures

- [**Descriptive Name**](#reference-architecture-index)

## Drafting a Reference Architecture

> <!-- Info -->
> Info While both reference architectures and design patterns are essential tools in software development, they serve different purposes and operate at different levels of abstraction. A reference architecture provides a broad, overarching framework for system design, while design patterns offer targeted solutions to particular design issues within that framework.

A reference architecture **must** include the following information:

- A brief summary of the reference architecture's scope and purpose
- The practical value proposition for standardization
- Guidance on when and when not to use the reference architecture
- Contact instructions for design consultations on adopting the reference architecture
- A high-level outline of the major system components and their interactions within the system
- A high-level summary of the technology stack recommended for building the system
- A summary of the major considerations when configuring the system
- A prescriptive procedure for provisioning a system following the reference architecture
- A collection of procedures for administering and maintaining a system following the reference architecture

> <!-- Info -->
> Note
> A [reference architecture template](https://github.com/wexinc/arch-rfc-content/blob/main/templates/reference-architecture.md) is available in the RFC Content GitHub Repository that includes placeholders for all required information.
>
> For more information on drafting an RFC, refer to [RFC1- Using RFCs](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154468680237/RFC1-Using+RFCs).

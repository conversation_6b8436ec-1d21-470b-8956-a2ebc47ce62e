<!-- Parent: <PERSON>ved Roads -->
<!-- Parent: Reference Architecture -->
<!-- Title: RFC-533 Azure AKS Microservices Reference Architecture -->

<!-- Include: macros.md -->
# Azure AKS Microservices Reference Architecture

```text
Author: Enterprise Architecture
Publish Date: 2025-07-11
Category: Paved Roads
Subtype: Reference Architecture
```

## Executive Summary

This reference architecture provides a standardized approach for building and deploying microservices-based applications on Azure Kubernetes Service (AKS). It incorporates WEX's prioritized standards for security, monitoring, and infrastructure while leveraging Azure's managed services for optimal reliability and scalability. The architecture is designed to support mission-critical applications with enterprise-grade security, observability, and operational excellence.

## Usage Guidance

### When to Use

- ☑️ Cloud-native applications requiring containerized microservices architecture
- ☑️ Systems with requirements for high scalability and reliability
- ☑️ Applications needing enterprise-grade security and compliance controls
- ☑️ Services requiring sophisticated monitoring and observability
- ☑️ Projects that align with WEX Fabric development standards

### When to Avoid

- 🔲 Simple monolithic applications with minimal scaling needs
- 🔲 Applications with specific requirements that prevent containerization
- 🔲 Projects with strict data residency requirements outside Azure regions
- 🔲 Systems with ultra-low latency requirements that need bare metal servers

## Source Repository

This reference architecture has an accompanying GitHub repository containing implementation resources:

- **Repository URL**: [github.com/wexinc/azure-aks-reference-architecture](https://github.com/wexinc/azure-aks-reference-architecture)

The repository includes:

- Terraform templates for infrastructure provisioning
- ArgoCD configuration for GitOps deployment
- Helm charts for common services
- Example microservice implementations
- Security and compliance configurations

## Value Proposition

### Business Benefits

- **Accelerated Time to Market**: Pre-configured infrastructure templates and CI/CD pipelines
- **Cost Optimization**: Auto-scaling and pay-per-use infrastructure model
- **Risk Reduction**: Standardized security controls and compliance configurations
- **Operational Excellence**: Built-in monitoring and automated management
- **Innovation Enablement**: Flexible architecture supporting rapid experimentation

### Technical Benefits

- **Standardization**: Consistent deployment patterns across services
- **Security**: Defense-in-depth with multiple security layers
- **Scalability**: Horizontal scaling with AKS node pools
- **Observability**: Comprehensive monitoring with DataDog and Splunk
- **Maintainability**: GitOps workflow with ArgoCD

## Cost Optimization Controls

- **AKS Reserved Instances**: 40% savings with 1-year commitment
- **Spot Instances**: Up to 90% cost reduction for non-critical workloads
- **Auto-scaling**: Optimized resource utilization during off-peak hours
- **Zone-redundant Storage**: Tiered storage strategy for cost-effective data management
- **Network Optimization**: Azure CDN and caching for reduced egress costs

## Architecture Overview

### Cloud Architecture Diagram

```mermaid
%%{init: {"theme": "base", "themeVariables": { "primaryColor": "#007FFF", "fontFamily": "arial" }}}%%
flowchart TB
    subgraph internet["External Traffic"]
        client["Client Applications"]
        api-consumers["API Consumers"]
    end

    subgraph az-front["Azure Front Door"]
        afd["Azure Front Door WAF"]
    end

    subgraph identity["Identity & Security"]
        okta["Okta Identity"]
        vault["HashiCorp Vault"]
    end

    subgraph aks-cluster["AKS Cluster"]
        subgraph ingress["Ingress Layer"]
            istio["Istio Gateway"]
            apigee["Apigee Gateway"]
        end
        
        subgraph services["Service Mesh"]
            api["API Services"]
            backend["Backend Services"]
            workers["Background Workers"]
        end

        subgraph monitoring["Observability"]
            datadog["DataDog APM"]
            splunk["Splunk Logging"]
        end
    end

    subgraph data["Data Layer"]
        cosmos["Azure Cosmos DB"]
        redis["Azure Cache for Redis"]
        blob["Azure Blob Storage"]
    end

    subgraph messaging["Event Bus"]
        kafka["Aiven Kafka"]
    end

    client --> afd
    api-consumers --> afd
    afd --> istio
    istio --> apigee
    apigee --> api
    api --> backend
    backend --> workers
    api --> cosmos
    api --> redis
    workers --> blob
    backend --> kafka
    workers --> kafka
    
    %% Security and monitoring connections
    api --> vault
    backend --> vault
    api --> okta
    datadog --> api
    datadog --> backend
    datadog --> workers
    splunk --> api
    splunk --> backend
    splunk --> workers
```

### Authentication & Identity

#### Customer Authentication

- **Provider**: Okta Customer Identity
- **Authentication Flows**:
  - OAuth 2.0/OIDC with PKCE for web/mobile clients
  - JWT-based service authentication
  - Configurable MFA policies
- **Integration Patterns**:
  - Native SDK integration
  - Token validation at Apigee gateway
  - Automated token refresh handling

#### Employee Authentication

- **Provider**: Okta Workforce Identity
- **Access Patterns**:
  - Role-based access control (RBAC)
  - Kubernetes RBAC integration
  - GitOps workflow authentication
- **Security Controls**:
  - Conditional access based on device posture
  - Geographic access restrictions
  - Just-in-time access provisioning

### Technology Stack

| Layer | Technology | Purpose | Version |
|-------|------------|---------|---------|
| Front Door | Azure Front Door | Global load balancing, WAF | Enterprise |
| API Management | Apigee | API Gateway, rate limiting | Latest |
| Container Platform | Azure AKS | Container orchestration | 1.27+ |
| Service Mesh | Istio | Service-to-service communication | 1.19+ |
| Identity | Okta | Authentication & authorization | Enterprise |
| Secrets | HashiCorp Vault | Secrets management | 1.13+ |
| Monitoring | DataDog | Application performance monitoring | Enterprise |
| Logging | Splunk | Log aggregation and analysis | Cloud |
| Database | Azure Cosmos DB | Primary data store | Latest |
| Cache | Azure Cache for Redis | Distributed caching | Enterprise |
| Storage | Azure Blob Storage | Object storage | Latest |
| Message Bus | Aiven Kafka | Event streaming | Latest |
| CI/CD | ArgoCD | GitOps deployment | 2.8+ |

## Implementation Guide

### Prerequisites

- ☑️ Azure subscription with appropriate permissions
- ☑️ Okta tenant configuration
- ☑️ Terraform and Azure CLI installed
- ☑️ Access to WEX Artifactory
- ☑️ Kubernetes and Helm knowledge

### Quick Start

1. Clone the reference architecture repository
2. Configure environment variables for authentication
3. Run Terraform init and apply
4. Deploy ArgoCD using provided Helm charts
5. Configure GitOps repositories
6. Deploy sample microservice

### Configuration Reference

```yaml
# Example AKS cluster configuration
cluster:
  name: ${env}-${region}-aks
  version: 1.27.3
  nodeGroups:
    system:
      vmSize: Standard_D4s_v3
      minCount: 3
      maxCount: 5
    application:
      vmSize: Standard_D8s_v3
      minCount: 3
      maxCount: 20
  addons:
    monitoring: true
    policy: true
    networkPolicy: azure
```

## Operating Model

### Monitoring & Alerting

- **Key Metrics**:
  - Service response times (p95, p99)
  - Error rates by service
  - CPU/Memory utilization
  - Network latency
  - API call volume

- **Alert Thresholds**:
  - Critical: P95 latency > 500ms
  - Warning: Error rate > 1%
  - Critical: Pod restart count > 3/hour
  - Warning: CPU utilization > 80%

### Scaling Considerations

- Horizontal pod autoscaling based on CPU/memory
- Node pool autoscaling triggers
- Custom metrics scaling for specific workloads
- Kafka partition scaling guidelines
- Database throughput units management

### Security & Compliance

- Network security with Azure CNI
- Pod security policies enforcement
- Regular security scanning with Aqua
- Audit logging to Splunk
- Compliance reporting automation

### Cost Management

- Monthly cost review by team leads
- Budget alerts at 80% threshold
- Resource tagging for cost allocation
- Spot instance usage optimization
- Regular right-sizing reviews

## Support & Contact

### Getting Help

- WEX Enterprise Architecture Team
- Azure Platform Team
- Security Operations Center
- Documentation: [internal-docs-link]
- Training: [learning-portal-link]

### Design Consultation

Contact Enterprise Architecture Team at [<EMAIL>](mailto:<EMAIL>) for implementation guidance.

## Maturity Assessment

Current maturity score: 4/5

### Strengths

- Comprehensive security controls
- Robust monitoring and observability
- Automated deployment pipeline
- Cost optimization features
- Standard compliance controls

### Areas for Improvement

1. **Disaster Recovery**:
   - Implement cross-region failover
   - Add backup verification automation

2. **Security Automation**:
   - Enhance automated security scanning
   - Implement automated remediation

3. **Performance Optimization**:
   - Add performance testing automation
   - Implement cache optimization strategies

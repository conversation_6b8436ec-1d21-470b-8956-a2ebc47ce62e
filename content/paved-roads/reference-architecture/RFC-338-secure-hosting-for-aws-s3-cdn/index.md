<!-- Parent: <PERSON>ved Roads -->
<!-- Parent: Reference Architecture -->
<!-- Title: RFC-338 Secure Hosting For Static AWS S3 CDN -->
# Secure Hosting For Static AWS S3 CDN

```text
Author: <PERSON>
Publish Date: 2024-10-31
Category: Paved Roads
Subtype: Reference Architecture
```

## Introduction

Many WEX products include static content (ex. images, javascript, css) delivered over the web. In many cases these static assets are published to an S3 bucket as the method by which they are made available to the internet via a delivery mechanism such as a web server.

For security purposes, access to these static assets should be controlled and monitored. Common use cases include DDoS protection as well as regional/IP restrictions.

The purpose of this Reference Architecture is to provide a solution to this problem, in a manner that is consistent with the guidelines and desires set forth by the WEX Security and engineering teams.

WEX Contacts who contributed to this solution:

- <PERSON> <[<EMAIL>](mailto:<EMAIL>)>: Information Security Architecture
- <PERSON> <[<EMAIL>](mailto:<EMAIL>)>: DevOps Engineer
- <PERSON> <[<EMAIL>](mailto:<EMAIL>)>: Cloud Security Architect
- <PERSON><PERSON> <[<EMAIL>](mailto:<EMAIL>)>: Software Development Engineer

### Components

- AWS Application Load Balancer (ALB)
- AWS Certificate Manager (ACM)
- AWS S3

### Technology

Application teams are currently using static websites hosted on S3. Due to limitations in Imperva, CloudFront is used as the origin. CloudFront doesn't have the ability to restrict source IPs, which is a security requirement to prevent WAF bypass. So teams must also enable AWS WAF and attach it to CloudFront to implement the source IP restrictions. Key features such as WAF and CDN, which are already handled by Imperva, are duplicated by in this design; CloudFront providing CDN and AWS WAF providing a web application firewall. This redundancy has caused operational issues, impacting availability and reliability, with conflicting policies and functions between AWS CDN (CloudFront) and WAF.

Additionally, neither CloudFront nor AWS WAF are managed centrally by CloudEng nor CloudOps. They are managed by whichever team spins them up. This decentralization increases the likelihood of conflicting policies in CDN or WAF and creates confusion over who is responsible when issues arise.

Adding AWS WAF also introduces additional ITGCs for SOC, SOX, HITRUST, and PCI certifications, leading to more audits, governance, and scrutiny. It violates WEX security policy by using a non-approved WAF and not following the WAF management policies.

#### Solution Architecture

![Example Architecture](example-alb-s3-architecture.png)

Imperva is an edge firewall solution that is outside the scope and control of development teams. Therefore, teams can implement a secure delivery of static assets by leveraging an AWS ALB and S3 (no AWS WAF).

## Procedures

### Provisioning

**Key Considerations:**

- This setup allows public internet access to your S3 content through the ALB
- The ALB terminates SSL and forwards requests to the S3 VPC Endpoint
- Use an Interface endpoint for S3, not a Gateway endpoint
- The target group should use IP addresses as targets
- Ensure proper security measures are in place, including bucket policies and ALB security groups

#### Terraform

- [Data](https://github.com/wexinc/arch-aws-s3-static-tf/blob/main/data.tf)
- [ALB](https://github.com/wexinc/arch-aws-s3-static-tf/blob/main/load-balancer.tf)
- [Route 53](https://github.com/wexinc/arch-aws-s3-static-tf/blob/main/route53.tf)
- [S3 Bucket](https://github.com/wexinc/arch-aws-s3-static-tf/blob/main/s3_bucket.tf)

#### Without Terraform

1. Create an S3 VPC Endpoint:
   - Log in to your VPC Dashboard
   - Navigate to "Endpoints" and select "Create Endpoint"
   - Choose the Amazon S3 Interface Endpoint service
   - VPC endpoint has to be interface type
   - Select your VPC and at least two subnets in different Availability Zones
   - Configure security group to allow access on ports 80 and 443 from the ALB security group
   - Give it a few minutes, and the new endpoints should be created with the status Available. Head to the Subnets tab and make a note of the IP addresses and VPC Endpoint ID
2. Configure S3 bucket:
   - Create an S3 bucket with your desired content
   - You must name the bucket exactly the same as the FQDN (DNS name) you will be using. If the bucket is not created like this, you will receive an error when accessing the site.
   - Set up bucket policy to allow access from the VPC Endpoint

   ```yml
   {
     "Version": "2012-10-17",
     "Statement": [
        {
            "Sid": "VPCE",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": [
                "arn:aws:s3:::web.jensa.mytestdomain.io/*",
                "arn:aws:s3:::web.jensa.mytestdomain.io"
            ],
            "Condition": {
                "StringEquals": {
                    "aws:SourceVpce": "vpce-01c3d4e8ae4b8596d"
                }
            }
        }
     ]
   }
   ```

3. Set up external-facing ALB:
   - Go to EC2 Dashboard > Load Balancers
   - Create a new Application Load Balancer
   - Choose "internet-facing" scheme instead of "internal"
   - Configure listener for HTTPS (port 443)
   - Select public subnets for the ALB
   - Associate an SSL certificate from AWS Certificate Manager
4. Configure ALB Target Group:
   - Create a new target group with target type "IP"
   - On the Register targets step, specify the IP addresses got after creating the S3 VPC Endpoint.
   - Use HTTPS protocol for the target group
   - Set health check protocol to HTTP and add "307,405" to success codes
   - Register the VPC Endpoint IP addresses as targets
5. Set up DNS:
   - Create a public hosted zone in Route 53 for your domain
   - Create an A record alias pointing to the ALB
6. Configure security:
   - Ensure ALB security group allows inbound traffic on port 443 from the internet
   - Configure S3 bucket policy to allow access only from the VPC Endpoint
7. Connect Imperva WAF/CDN
   - Set up a DNS A record as an Origin server for Imperva WAF/CDN. You need to work with network security on this. I believe there is ticket you can create for Imperva configuration.
     - Create a public hosted zone in Route 53 for your domain
     - Create an A record alias pointing to the ALB
     - Use this A record as an Origin server for Imperva WAF.

### Administration & Maintenance

For support regarding the implementation of this reference architecture, please consult with the contacts listed within the [Introduction](#introduction).

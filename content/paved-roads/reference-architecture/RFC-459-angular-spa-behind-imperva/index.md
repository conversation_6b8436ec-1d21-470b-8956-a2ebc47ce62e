<!-- Parent: Paved Roads -->
<!-- Parent: Reference Architecture -->
<!-- Title: RFC-459 Single-Page Application Behind Imperva -->
# Single-Page Application Behind Imperva Reference Architecture

```text
Author: <PERSON>
Publish Date: 2025-03-18
Category: Paved Roads
Subtype: Reference Architecture
```

## Executive Summary

This reference architecture provides a secure, scalable pattern for deploying Single Page Applications (SPAs), such as Angular webapps, running within Amazon Web Services (AWS). The architecture incorporates Docker containers, fronted by AWS Application Load Balancer for traffic distribution, all behind the WEX standard Imperva WAF/CDN solution. WEX Security has [discouraged (and subsequently banned)](#Why-Cloudfront-is-being-eliminated-at-WEX) the use of Cloudfront; this reference architecture demonstrates a simple way to host an SPA within AWS without the complexities (and security concerns) of a Cloudfront + AWS S3 solution.

## Usage Guidance

### When to Use

- Single Page Applications built with Javascript frameworks such as Angular
- Applications requiring enterprise-grade WAF protection
- Solutions needing automatic scaling and high availability
- A deployment model that avoids using AWS Cloudfront

### When to Avoid

- Static web content can be delivered via the design outlined in [RFC-338-secure-hosting-for-aws-s3-cdn](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154808713383/RFC-338+Secure+Hosting+For+Static+AWS+S3+CDN), although the solution within this reference architecture should also work for delivery of fully-static assets

### Additional Usage Notes

- Imperva WAF is the WEX standard web application firewall. It is deployed and maintained in a fault-tolerant, distributed manner for maximum availability and resilience

## Architecture Overview

### Cloud Architecture Diagram

```mermaid
graph TD;
    Client[Client Browser]
    Imperva[Imperva WAF]
    ALB[ALB - Application Load Balancer]
    ContainerHost[EKS / ECS]
    Container1[SPA Container 1]
    ContainerN[SPA Container N]
    
    Client --> Imperva
    Imperva --> ALB
    ALB --> ContainerHost
    ContainerHost --> Container1
    ContainerHost --> ContainerN
```

### Technology Stack

| Layer | Technology | Purpose | Version |
|-------|------------|---------|---------|
| Frontend | Angular | SPA Framework | ^17.3.0 (for Phoenix UI compatibility) |
| Container | Node.js | Build Layer | ^20.9.0 |
| Container | Nginx | Runtime Layer | Latest |
| Container Runtime | EKS or ECS Fargate | Container Execution | Latest |
| Load Balancer | AWS ALB | Traffic Distribution | Latest |
| WAF | Imperva | Security Layer | Latest |
| Infrastructure | Terraform | IaC | Latest |
| CI/CD | GitHub Actions | Deployment Pipeline | Latest |

### Technology Considerations to Remember

- Deploying the application within a container introduces the need to maintain updates for the runtime environment (base image version, security patches, zero-day exploits). This is not the case when utilizing Cloudfront+S3. Additional care must be given to the deployment pipeline and cadence to support this additional complexity.
- Deploying the application within a container _does_ introduce opportunities to "shift" the deployable unit to the left. Runtime containers can be created in lower environments, then promoted to upper environments once automated/manual tests have confirmed their performance.

## Implementation Guide

### Prerequisites

- WEX Fabric ID
- AWS Account with appropriate permissions
- GitHub access

### Quick Start

An example implementation of this reference architecture can be found in the [Reference Architecture Demo Application](https://github.com/wexinc/reference-arch-demo-spa). More-detailed technical information and guides can be found there.

A summary of how this architecture can be implemented are:

1. A code repository containing your Single Page Application (ex. Angular)
2. Continuous Integration (CI) workflow(s) that execute standard steps such as unit tests, integration tests, and code security scans
3. Continuous Delivery (CD) workflow(s) that construct a runtime container for the application and deploy the containers to the target container runtime (EKS, ECS, etc.)
    - The recommended runtime container is an Nginx web server that contains a production build of the application
    - A multistage Dockerfile is a simple way to accomplish this. Example:

```dockerfile
FROM node:20-alpine AS build

# Install Angular 17
RUN npm i -g @angular/cli@17.3.13

# Install application
WORKDIR /app/src
COPY . ./
RUN npm ci

# Create Production Build
RUN npm run build

##############################################
FROM nginx:latest AS runtime

COPY --from=build /app/src/dist/my-amazing-application-name/browser /usr/share/nginx/html

EXPOSE 80
```

### Security & Compliance

- Benefit of existing (and maintained) WEX Imperva WAF rules and policies
- AWS Security Groups configuration for additional, granular security configuration as needed

### Cost Management

- ALB allows for scaling policies for cost control
- Cost savings by elimination of Cloudfront and AWS S3 (see below)
- Cost savings via architecture and build pipeline simplification

#### Cost Comparison: Cloudfront vs. ECS Fargate for SPA Hosting

This is an example cost comparison between a Cloudfront/S3/WAF solution vs. the architecture in this RFC:

##### Option 1: CloudFront + S3 + AWS WAF

| Service | Configuration | Monthly Cost |
| --- | --- | --- |
| CloudFront | 1TB data transfer out + 10M requests | $90.00 |
| S3 Storage | 1GB storage + 5M requests | $0.50 |
| AWS WAF | 20M requests | $40.00 |
| **Total** | | **$130.50** |

##### Option 2: ECS Fargate + ALB + Imperva WAF

| Service | Configuration | Monthly Cost |
| --- | --- | --- |
| ECS Fargate | 2 containers x 0.5 vCPU/1GB (24/7) | $43.20 |
| ALB | 1 ALB + 1TB processed | $32.12 |
| Imperva WAF | Existing enterprise plan | $0.00* |
| **Total** | | **$75.32** |

*Imperva costs are already covered under WEX's enterprise license, so there's no additional per-application cost.

## Support & Contact

### Getting Help

- [Cloud Engineering](https://wexinc.atlassian.net/wiki/spaces/CLOUD/overview) for AWS related issues
- [Security Team](https://wexinc.atlassian.net/wiki/spaces/ESA/overview?homepageId=412614866) for Imperva WAF configuration

## Footnotes

### Why Cloudfront is being eliminated at WEX

WEX Security has begun limiting the use of Cloudfront distributions for WEX web applications, for a few reasons:

- Cloudfront distributions require configuration in order to apply security and configuration controls (IP restrictions, rate limiting etc.)
- Many WEX Teams secure their Cloudfront distributions via an AWS WAF appliance. AWS WAF configurations have a good amount of overlap with functionality already provided by Imperva. This overlap can lead to duplicated security configurations that can make issue investigation and diagnosis more difficult
- Imperva also functions as a CDN, therefore the CDN aspects of Cloudfront are negated and unnecessary

<!-- Parent: Paved Roads -->
<!-- Parent: Reference Architecture -->
<!-- Title: RFC-516 External File Exchange With SFTP and PGP on AWS -->

<!-- Include: macros.md -->

# External File Exchange With SFTP and PGP on AWS

:draft:

```text
Author: WEX Architecture Team
Publish Date: 2025-06-11
Category: Paved Roads
Subtype: Reference Architecture
```

## Executive Summary

> This reference architecture defines the standard approach for secure file exchange at WEX, leveraging the existing and readily available SFTP (Secure File Transfer Protocol) with PGP (Pretty Good Privacy) encryption infrastructure on AWS. It utilizes the pre-deployed GoAnywhere MFT for secure file transfers and Control-M for file-level encryption, providing a comprehensive solution for both external and internal file exchange workflows with enterprise-grade security without requiring new infrastructure provisioning.

## Usage Guidance

### When to Use

- ☑️ Batch server-to-server secure file transfers with external partners
- ☑️ User-to-system ad-hoc file transfers requiring encryption
- ☑️ Person-to-person file collaboration with security requirements
- ☑️ Applications that must comply with PCI DSS, HIPAA, HITECH, SOX, and state privacy laws
- ☑️ Systems handling sensitive data that requires end-to-end encryption

### When to Avoid

- 🔲 Simple internal file transfers with minimal security requirements
- 🔲 Real-time data streaming use cases where batch file transfers are not appropriate
- 🔲 Systems that require proprietary file transfer protocols not supported by GoAnywhere
- 🔲 Use cases requiring direct database-to-database synchronization
- 🔲 Environments with regulatory restrictions on AWS cloud hosting

## Value Proposition

### Business Benefits

- **Regulatory Compliance**: Meets requirements for PCI DSS, HIPAA, HITECH, SOX, and state privacy laws due to pre-configured security and compliance controls
- **Risk Mitigation**: Strong encryption and secure transfer protocols reduce data breach risks inherent in the established infrastructure
- **Partner Integration**: Standardized approach on the existing platform accelerates onboarding of new business partners
- **Operational Efficiency**: Automation of file transfers using the pre-built workflows reduces manual intervention and human error
- **Auditability**: Comprehensive logging and tracking of all file exchange activities are already enabled

### Technical Benefits

- **Standardization**: Consistent enterprise-wide approach to secure file transfers is enforced by the existing platform
- **Security**: End-to-end encryption with strong authentication mechanisms are built-in
- **Automation**: Workflow capabilities provided by GoAnywhere and Control-M eliminate the need for custom scripts
- **Reliability**: AWS Multi-Region deployment of the existing infrastructure ensures high availability
- **Scalability**: The established infrastructure can scale to handle increasing file transfer volumes without requiring new infrastructure build-out
- **Visibility**: Centralized monitoring and management of all file transfer activities are readily available

## Cost Optimization Controls

- **Multi-Region Optimization**: Strategic placement of resources within the existing infrastructure to reduce cross-region data transfer costs
- **Automated Scaling**: Capacity adjusts based on scheduled file transfer peaks and off-hours via pre-configured auto-scaling policies
- **Storage Management**: Automated archiving and cleanup of processed files to optimize storage costs are implemented in the existing system

## Architecture Overview

### Cloud Architecture Diagram

![alt text](cloudarch.png)

### File Exchange Workflows

#### External File Ingestion with PGP Encryption

![alt text](external.png)

1. External partner uploads PGP-encrypted file to GoAnywhere MFT via SFTP
2. GoAnywhere validates authentication and authorization
3. File is stored in secure S3 bucket
4. Control-M MFT job decrypts the file using configured PGP template
5. Decrypted file is delivered to target internal application
6. Complete audit trail is generated and stored

#### Internal File Distribution with PGP Encryption

![alt text](internal.png)

1. Internal system generates file for external distribution
2. Control-M MFT encrypts the file using PGP with partner's public key
3. Encrypted file is transferred to GoAnywhere MFT
4. GoAnywhere makes the file available via SFTP for partner download
5. Partner downloads encrypted file using authenticated SFTP connection
6. Complete audit trail is generated and stored

### Security Model

#### Authentication & Access Control

- **SFTP Authentication**:
  - Password-based authentication
  - SSH key-based authentication (preferred)
  - Multi-factor authentication for privileged users
- **Access Control**:
  - Role-based access control in GoAnywhere MFT
  - Virtual folders with granular permissions
  - IP-based access restrictions
  - Okta Workforce Identity integration for internal users

#### Encryption

- **In-Transit Encryption**:
  - SFTP/SSH for secure file transfers
  - TLS for web interface access
- **At-Rest Encryption**:
  - PGP encryption for file content security
  - AWS S3 server-side encryption
  - KMS-managed encryption keys

### Technology Stack

| Layer           | Technology     | Purpose                         | Version |
| --------------- | -------------- | ------------------------------- | ------- |
| File Transfer   | GoAnywhere MFT | Secure file transfer management | Latest  |
| File Encryption | Control-M      | PGP encryption/decryption       | Latest  |
| Storage         | AWS S3         | File storage                    | Latest  |
| Key Management  | AWS KMS        | Encryption key management       | Latest  |
| Monitoring      | AWS CloudWatch | Operational monitoring          | Latest  |
| Audit           | AWS CloudTrail | Security audit logging          | Latest  |

## Implementation Guide

### Prerequisites

- ☑️ Access to an AWS account with appropriate permissions provisioned for this service
- ☑️ GoAnywhere MFT user accounts and permissions configured
- ☑️ Control-M MFT job definitions and permissions configured
- ☑️ PGP key pairs for encryption/decryption generated and exchanged as needed
- ☑️ Network access configuration for external connectivity verified for your use case
- ☑️ AWS VPC with public and private subnets already in place for the service

### Quick Start to Onboard to the Paved Road

1. Utilize the pre-deployed GoAnywhere MFT on AWS EC2.
2. Access the pre-configured GoAnywhere Gateway in DMZ for secure external connections.
3. Leverage the established Control-M MFT integration with the PGP application.
4. Define or select existing PGP templates for your encryption/decryption operations.
5. Configure S3 buckets for file storage with appropriate encryption settings, or use designated existing buckets.
6. Establish authentication mechanisms for external partners within the existing GoAnywhere framework.
7. Create initial workflows for your specific file transfer and encryption processes using the provided tools.

## Operating Model

### Monitoring & Alerting

- **Key Metrics**:

  - File transfer success/failure rates
  - Transfer latency and throughput
  - Authentication failures
  - Error rates and types
  - Storage utilization

- **Alert Thresholds**:

  - Critical: Multiple consecutive transfer failures
  - Critical: Authentication attacks detection
  - Warning: Unusual file sizes or transfer patterns
  - Warning: Storage capacity thresholds (>80%)

- **Dashboards**:
  - Operational dashboard for file transfer status
  - Security dashboard for authentication and access events
  - Compliance dashboard for audit requirements

### Scaling Considerations

- **Performance Limits**:

  - Maximum concurrent SFTP connections: 500
  - Maximum file size: 10GB per transfer
  - Maximum throughput: 1GB/minute per instance

- **Scaling Triggers**:

  - Auto-scaling based on CPU utilization (>70%)
  - Auto-scaling based on connection count (>400)
  - Instance type upgrades for consistent high utilization

- **Cost Implications**:
  - Multi-region deployment of the existing infrastructure increases resilience but has associated costs.
  - Reserved instances already in place reduce costs for baseline capacity.
  - On-demand instances are utilized for variable loads.

### Security & Compliance

- **Security Controls**:

  - Regular security scanning of infrastructure is performed by the platform team
  - PGP key rotation policy (annual) is enforced
  - Access reviews (quarterly) are conducted by the platform team
  - Vulnerability management process is in place for the underlying infrastructure

- **Compliance Requirements**:

  - PCI DSS for payment card data
  - HIPAA for healthcare information
  - SOX for financial reporting
  - State privacy laws

- **Audit Procedures**:
  - Daily automated audit log review
  - Monthly compliance checks
  - Quarterly security posture assessment

### Cost Management

- **Monthly Cost Review Procedures**:

  - EC2 instance utilization analysis
  - S3 storage growth trend analysis
  - Data transfer cost optimization review

- **Budget Alerting Thresholds**:

  - 80% of monthly budget allocation
  - Unusual spending patterns (>30% deviation)

- **Cost Allocation**:

  - Tagging strategy: Business-Unit, Application, Environment, Partner
  - Chargeback model based on transfer volume and storage

- **Optimization Review Schedule**:
  - Bi-weekly EC2 right-sizing analysis
  - Monthly storage lifecycle policy review
  - Quarterly reserved instance coverage assessment

## Regional Placement Considerations

The existing "paved road" infrastructure is deployed across multiple AWS regions to ensure high availability, disaster recovery, and compliance with data residency requirements. When utilizing this platform, consider the following:

- **Data Residency and Sovereignty**: Ensure that the AWS region chosen for file storage and processing aligns with any legal or regulatory requirements for where sensitive data must reside (e.g., data related to European citizens may need to remain within EU AWS regions).
- **Latency**: Select the AWS region geographically closest to your external partners or internal systems that will be interacting most frequently with the file exchange platform to minimize network latency and optimize transfer speeds.
- **Disaster Recovery and Business Continuity**: Leverage the multi-region capabilities for robust disaster recovery strategies. In the event of a regional outage, the ability to switch to another active region within the "paved road" infrastructure is crucial for business continuity.
- **Compliance Requirements**: Verify that the specific regional deployment of the infrastructure meets any unique local compliance mandates beyond the standard enterprise-wide requirements.
- **Network Connectivity**: Evaluate the network connectivity between your on-premises environments (if applicable) and the chosen AWS region, as well as connections to other dependent AWS services or external endpoints.

## Support & Contact

### Getting Help

- **Support Channels**:

  - IT Service Desk for operational issues
  - Security Operations for security-related concerns
  - AWS Support for infrastructure issues

- **Documentation**:

  - GoAnywhere MFT documentation: [Internal Link]
  - Control-M MFT documentation: [Internal Link]
  - PGP encryption guidelines: [Internal Link]
  - Partner onboarding process: [Internal Link]

- **Training Resources**:
  - GoAnywhere administrator training
  - Control-M workflow design training
  - PGP key management best practices

### Design Consultation

> Contact the Enterprise Architecture Team at <<EMAIL>> for implementation guidance and design reviews for secure file exchange solutions.

## Maturity Assessment

The External File Exchange with SFTP and PGP on AWS reference architecture demonstrates a high level of maturity in the following areas:

- **Security**: Strong encryption and authentication mechanisms
- **Compliance**: Meets regulatory requirements across multiple frameworks
- **Automation**: Well-defined workflows reduce manual intervention
- **Standardization**: Consistent approach using approved enterprise tools

Areas for potential improvement include:

1. **API Integration**: Developing APIs for programmatic control of file transfers
2. **Advanced Analytics**: Implementing pattern detection for anomalous transfers
3. **Multi-Cloud Support**: Extending the architecture to support hybrid cloud scenarios
4. **Zero Trust Architecture**: Further enhancing security with a complete zero trust approach
5. **Self-Service Portal**: Implementing a user-friendly portal for partner onboarding and management

<!-- Parent: Paved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC-353 Phoenix Design Components -->
# Phoenix Design Components

```text
Author: <PERSON>
Publish Date: 11/12/2024
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

The Phoenix Design Components are a collection of standardized UX components used to build effective and consistent user experiences across WEX products.

## Standardization Value Proposition

Standardizing UX design components across WEX products offers several key benefits:

1. **Consistency**: Ensures a uniform look and feel across all products, enhancing brand identity and user familiarity.
2. **Efficiency**: Reduces design and development time by reusing components, allowing teams to focus on innovation.
3. **Quality**: Promotes best practices and reduces the likelihood of design flaws, leading to a more polished user experience.
4. **Collaboration**: Facilitates better collaboration among design and development teams by providing a common language and set of tools.

By adopting standardized UX design components, WEX can deliver a more cohesive and high-quality user experience across its product suite.

## Components

The Phoenix Design Components are distributed via a private NPM repository (`wex-npm-prod`) in Artifactory. Add NPM dependency references to your project for assets and stylesheets to begin using the framework.

Components are currently available for Angular using Node.js.

> <!-- Info -->
> Info
> Refer to the [WEX Design System](https://www.wexinc.design/) site for current documentation on the framework.

## Onboarding Instructions

> <!-- Info -->
> Info
> Refer to the [Getting Started Guide](https://www.wexinc.design/get-started/developers) for documentation on adding the dependency package to your project.

## User Guides

> Refer to the [UX Component Guide](https://www.wexinc.design/components) for documentation on the individual UX controls.
> Refer to the [WEX Brand](https://wexchange.wexinc.com/home/<USER>

## Contact Information

For technical design consultations, contact the WEX Digital [Enterprise Standards team](https://github.com/orgs/wexinc/teams/enterprise-standards).

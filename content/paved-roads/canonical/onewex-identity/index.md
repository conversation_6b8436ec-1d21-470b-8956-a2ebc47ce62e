<!-- Parent: <PERSON>ved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC-468 oneWEX Identity (OWI) -->
# oneWEX Identity (OWI)

```text
Author: Sandeep Mittal
Publish Date: 2025-03-19
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

oneWEX Identity (OWI) is the centralized identity and access management system for WEX applications and services. It provides unified authentication (Single Sign-On) and user management capabilities across the WEX ecosystem, focusing on improving customer satisfaction through Adaptive Multi-Factor Authentication (MFA), enabling seamless multi-product experiences, and enhancing security.

> <!-- Info -->
> **Note:** This documentation is a work in progress. The OWI system is actively being enhanced with additional features and improvements planned for future releases. Please check back regularly for updates.

## Standardization Value Proposition

- Consistent authentication experience across all WEX applications.
- Centralized user management and access control.
- Enhanced security through standardized identity protocols.
- Standardized base claims for application authorization.
- Reduced development effort through reusable authentication components.
- Simplified compliance with security standards and regulations.

## Components

### OWI Microservices

The OWI system consists of microservices that handle different aspects of identity and access management. These microservices act as an abstraction layer between WEX applications and Auth0, providing standardized integration patterns, consistent user experiences, and additional business logic that Auth0 alone cannot provide. This approach shields applications from Auth0-specific implementation details and allows for future identity provider changes with minimal impact to consuming applications.

#### Migration Gateway

This service is responsible for migrating users from legacy systems to the new OWI system. It handles user data transformation, validation, and storage in the OWI database.

#### Authentication Endpoints
  
These endpoints coordinate the login/logout process and handle user authentication requests. They interact with Auth0 to manage user sessions and permissions.

> <!-- Info -->
> It's important to note that OWI handles application-level access controls (which applications a user can access) but does not manage granular permissions or role-based access controls within individual applications. Application teams remain responsible for implementing their own authorization logic for features and functions within their applications.

## High-Level Design

> <!-- Info -->
> **Note:** The OWI architecture design is being continuously refined. Upcoming improvements to the system architecture and component interactions are planned to enhance system resilience and scalability.

```mermaid
---
title: OWI High Level Design
---

graph TD
  subgraph A[WEX Apps]
    A1[Encompass App]
    A2[Fleet App]
    A3[...]
  end
  subgraph C[OWI Microservices]
    C1["Login related Endpoints (Authentication, Token, Privacy Policy, MFA...)"]
    C2["User Onboarding Endpoints (Create user, Change password...)"]
  end
  subgraph E[Auth0]
    E1["Application (Authentication Endpoints, Organization, Db Connection, MFA)"]
    E2["Universal Login (Application Specific branding)"]
    E3["Action Scripts (Custom control of Login and other authentication flows)"]
  end
  A --> C
  B[Standalone Login Page] --> C
  C --> D[Apigee Gateway]
  C --> E
  D --> E
```

## Application Authentication Flow

This section describes the authentication flow for applications using OWI without an external Identity Provider (IdP).

```mermaid
sequenceDiagram
    participant User
    participant App as Application
    participant OWI as OWI
    participant Auth0 as Auth0
        
    User->>App: Click Login
    App->>OWI: Request login
    OWI->>Auth0: Request login authentication prompt
    Auth0-->>OWI: Return login and authentication prompt URL
    OWI-->>App: Forward login/authentication URL
    App->>User: Redirect to login/authentication prompt
    User->>Auth0: Enter credentials, MFA and consent
    Auth0-->>OWI: Call the callback URL with a code 
    OWI->>Auth0: Request token
    Auth0-->>OWI: Respond with ID token and access token
    OWI-->>App: Forward ID token and access token
```

## Onboarding Instructions

For onboarding requests, contact the OWI team at [<EMAIL>](mailto:<EMAIL>). The team will evaluate your request and guide you through the onboarding process, which includes:

1. Completing the [OWI Onboarding Questionnaire](https://docs.google.com/document/d/1SthcdYkxgcV7LyuKFJJBmn-ZrrmwBIttSgC2CUE4Dgo/).
2. Providing application technical details and infrastructure requirements.
3. Working with the OWI team for Auth0 tenant configuration and implementation.

> <!-- Info -->
> **Note:** The onboarding process is currently being refined. The OWI team is working on improving the onboarding experience and documentation for new applications.

### Required Information

- Application technical details (language, environments, authentication methods).
- User management details (uniqueness criteria, data storage, status types).
- Infrastructure requirements (custom domain URLs, VPC peering details, AWS account info).
- API connectivity details (endpoints, authentication).
- Migration strategy (all at once or group-based).
- Callback URLs and token forwarding preferences.
- Branding requirements (login page, error page customization).

### Key Tasks

#### OWI Team Responsibilities

- Auth0 tenant configuration.
- Infrastructure setup (VPC peering, AWS Secrets).
- Service configurations (UserUpdateService, Migration Gateway).
- UI/Branding implementation.

#### Application Team Responsibilities

- Provide infrastructure details.
- Implement required endpoints (user verification, profile management).
- Frontend integration (account selector, logout handlers).
- API authentication setup.

## User Guides & Documents

For detailed implementation guides and documentation, visit:

- [OneWEX Identity Technical Overview](https://wexinc.atlassian.net/wiki/spaces/DCS/pages/************/Technical+Documentation)
- [OneWEX Identity Confluence Space](https://wexinc.atlassian.net/wiki/spaces/DCS/overview?homepageId=************)
- [Mobile Reference Architecture RFC](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/************/RFC-452+OWI+For+Mobile+Applications)
- [How to Integrate with OWI Migration Gateway Service](https://wexinc.atlassian.net/wiki/spaces/DCS/pages/************/How+to+Integrate+with+the+OWI+Migration+Gateway+Service+-+Updated)

## Support Flow

> <!-- Info -->
> **Note:** The support processes and escalation procedures are being refined as the OWI system matures. Additional support resources and improved troubleshooting documentation are under development.

### Service Outages Escalation Levels

1. **Level 1**: Application Product Support Team (on-call and app teams).
2. **Level 2**: Application Team & OWI Platform Team.
3. **Level 3**: Security Engineering.
4. **Level 4**: EA SME / Auth0 Support escalation.

```mermaid
---
title: Service Outages Escalation Flow
---
flowchart TD
    Issue[Service Outage Detected]
    L1[Level 1: Application Product Support Team]
    L2[Level 2: Application Team & OWI Platform Team]
    L3[Level 3: Security Engineering]
    L4[Level 4: EA SME / Auth0 Support]
    
    Issue --> L1
    L1 -->|Escalate if needed| L2
    L2 -->|Escalate if needed| L3
    L3 -->|Escalate if needed| L4
    
    style L1 fill:#f9f,stroke:#333,stroke-width:1px
    style L2 fill:#bbf,stroke:#333,stroke-width:1px
    style L3 fill:#bfb,stroke:#333,stroke-width:1px
    style L4 fill:#fbb,stroke:#333,stroke-width:1px
```

### Issue-Specific Monitoring and Resolution

#### Password Resets/MFA Issues, User Authorization Issues

1. **Level 1**: OWI (Application on-call support).
2. **Level 2**: OWI (Application app team) & Security Engineering.
3. **Level 3**: EA SME / Auth0 support escalation.

## Contact Information

- **Team**: [OneWEX Identity (OWI)](https://wexinc.atlassian.net/jira/software/c/projects/OWI/summary)
- **Product Owner**: [Mo Richards](mailto:<EMAIL>)
- **Technical Leader**: [Sandeep Mittal](mailto:<EMAIL>)

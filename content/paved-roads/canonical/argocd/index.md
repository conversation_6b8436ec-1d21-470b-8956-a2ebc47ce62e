<!-- Parent: Paved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC-517 Argo CD -->

# Argo CD

```text
Author: <PERSON>
Publish Date: 2025-06-09
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

ArgoCD is WEX's standardized GitOps continuous delivery platform for Kubernetes applications. It automates the deployment and lifecycle management of applications by monitoring Git repositories and ensuring cluster states match the desired configuration defined in Git. WEX maintains separate ArgoCD instances for AWS and Azure environments, with this document focusing specifically on the Azure instance.

Key capabilities include:

- Automated deployment synchronization
- Git-based configuration management
- Drift detection and remediation
- Multi-cluster and multi-environment support
- Kubernetes resource visualization
- Role-based access control

> [!NOTE]
> This profile is part of the Minimum Critical Knowledge Inventory initiative.  
> ☑️ State: In Progress

## Standardization Value Proposition

### Business Benefits

- **Reduced Deployment Risk**: Automated consistency checks ensure reliable deployments
- **Faster Recovery**: Automated rollbacks quickly restore stability when issues occur
- **Improved Compliance**: Git-based audit trails track all deployment changes
- **Reduced Operational Costs**: Automation reduces manual intervention needs
- **Accelerated Delivery**: Streamlined deployment process speeds time-to-market

### Technical Benefits

- **Consistent Deployment Model**: Same patterns work across all environments
- **Infrastructure as Code**: All configurations stored as version-controlled code
- **Self-Healing Applications**: Automated drift detection and correction
- **Simplified Multi-Environment Management**: App-of-Apps pattern streamlines management
- **Enhanced Security**: Reduced need for direct cluster access

## Components

- **Argo CD Core**: The main controller and API server, responsible for monitoring and synchronizing application state.
- **Argo CD CLI**: Command-line interface for interacting with Argo CD, supporting automation and scripting.
- **Argo CD UI**: Web-based dashboard for managing applications, visualizing sync status, and troubleshooting.
- **Repositories**: Git repositories containing Kubernetes manifests and application definitions.
- **RBAC**: Role-based access control for secure operations and multi-tenancy.
- **Notifications**: Integration with Slack, email, or other channels for deployment and sync alerts.

```mermaid
mindmap
  root((Argo CD))
    Core
    CLI
    UI
    Repositories
    RBAC
    Notifications
```

### Current capabilities used in WEX

- GitOps-based deployment and configuration management for Kubernetes workloads
- Automated application synchronization and drift detection
- Multi-cluster and multi-tenant support via projects and RBAC
- Visual management of applications and sync status through the Argo CD UI
- Integration with Slack/email for deployment and sync notifications
- Audit logging and change tracking for compliance

### Features/Capabilities not to be used in WEX

- Direct management of non-Kubernetes resources
- Use of Argo CD in unsupported or legacy environments

### Exceptions to use in non-prod environments

- Proof-of-concept or pilot projects with explicit approval
- Training and enablement sessions
- System performance and reliability testing

## Onboarding Instructions

### Prerequisites

- ☑️ Access to WEX GitHub repositories
- ☑️ Azure subscription access
- ☑️ ArgoCD access credentials
- ☑️ Kubernetes application manifests or Helm charts

### High-Level Steps

1. 🔲 **Request Access:** Contact the Platform Engineering Team to request access to ArgoCD and required repositories.
2. 🔲 **Review Documentation:** Familiarize yourself with the [official ArgoCD documentation](https://argo-cd.readthedocs.io/) and WEX-specific onboarding guides.
3. 🔲 **Prepare Application Manifests:** Ensure your application configuration is stored in Git using Helm, Kustomize, or plain YAML.
4. 🔲 **Submit for Deployment:** Follow internal processes to submit your application for deployment via ArgoCD. This typically involves a pull request to the appropriate GitOps repository.
5. 🔲 **Monitor Deployments:** Use the ArgoCD UI to monitor application status, sync progress, and health.
6. 🔲 **Support:** For questions or troubleshooting, reach out to the Platform Engineering Team.

> [!NOTE]
> All onboarding and deployment steps should follow GitOps best practices and use declarative configuration stored in version control.

For technical implementation details, refer to the internal onboarding documentation and reach out to the system owner or support teams as needed.

### Step-by-Step Onboarding

```text
Step 1: Ensure the helm chart and the values are setup appropriately for the application in the application repo.
```

Organize your application code repository with the following structure:

```text
my-application/
├── charts/                  # Helm charts
│   └── my-app/
│       ├── templates/
│       ├── values.yaml      # Default values
│       └── Chart.yaml       # Chart metadata
└── overlays/                # Environment-specific values
    ├── dev/
    │   └── values.yaml      # Dev values
    ├── stage/
    │   └── values.yaml      # Stage values
    └── prod/
        └── values.yaml      # Production values
```

```text
Step 2: Create the application manifest using YAML in the App-of-Apps repository. For Architecture related applications, use [fabric-aks-configuration](https://github.com/wexinc/fabric-aks-configuration) repository for storing the manifests.
```

Following is a sample manifest for LaunchDarkly Relay Proxy:

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ld-relay-proxy
  namespace: argocd
  labels:
    name: ld-relay-proxy
spec:
  project: fabric-prod

  source:
    repoURL: https://github.com/wexinc/fabric-ld-relay-helm.git
    targetRevision: main
    path: charts/ld-relay-azure
    helm:
      valueFiles:
      - ./overlays/stage/values.yaml

  destination:
    server: https://kubernetes.default.svc
    namespace: ld-relay-proxy

  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
```

Following is a sample configuration of a running application within ArgoCD:

![image argocdsampleappconfig.png](./argocdsampleappconfig.png)

```text
Step 3: Credentials to the repository containing the application manifest needs to be configured so that ArgoCD would be able to connect. This can be done by placing the keys in the KeyVault and adding entries to the yaml maintained for the Secrets Provider. For Architecture related applications, [kv-secretprovider.yaml](https://github.com/wexinc/fabric-aks-configuration/blob/main/helm/argocd/templates/kv-secretprovider.yaml) is the file used.
```

![image azureargocdrepositories.png](./azureargocdrepositories.png)

Once the application is correctly configured, ArgoCD automatically reads the manifest and proceeds with the deployment.

![image argocdsamplerunningapp.png](./argocdsamplerunningapp.png)

### Best Practices

- Use declarative configurations stored in Git
- Do not use ArgoCD UI for creating applications and use the declarative configurations only.
- Implement proper RBAC controls
- Configure automated sync policies with appropriate sync windows
- Set up health checks for reliable monitoring
- Use Helm or Kustomize for templating
- Implement proper secret management
- Follow the App-of-Apps pattern for complex deployments

### Deployment Workflow

```mermaid
sequence
  participant Dev as "Developer"
  participant Git as "Git Repository"
  participant Argo as "ArgoCD"
  participant K8s as "Kubernetes"
  Dev->>Git: Push application manifests
  Git->>Argo: Webhook notification
  Argo->>Git: Pull changes
  Argo->>Argo: Validate manifests
  Argo->>K8s: Apply changes
  K8s-->>Argo: Status update
  Argo-->>Dev: Deployment status
```

## User Guides

- [WEX Fabric - AWS ArgoCD](https://docs.wexfabric.com/tools_and_platforms/argocd/)
- [Benefits - Wex Fabric Journey](https://wexinc.atlassian.net/wiki/spaces/WH/pages/154090340954/Wex+Fabric+Journey)
- [ArgoCD - App of Apps](https://argo-cd.readthedocs.io/en/stable/operator-manual/cluster-bootstrapping/#app-of-apps-pattern)
- [ArgoCD Official Documentation](https://argo-cd.readthedocs.io/)
- [GitOps Best Practices](https://wexinc.atlassian.net/wiki/spaces/Platform/GitOps)

## Architecture

```mermaid
architecture-beta
  container "dev" [
    Developers
    Person: developers
    Technology: Code Changes
  ]

  container "git" [
    Git Repositories
    Container: git-repos
    Technology: Version Control
  ]

  container "argocd" [
    ArgoCD
    Container: argocd
    Technology: GitOps Controller
  ]

  container "k8s" [
    Azure AKS
    Container: kubernetes
    Technology: Container Orchestration
  ]

  container "apps" [
    Applications
    Container: apps
    Technology: Containerized Services
  ]

  rel "dev" -> "git": Push manifests
  rel "git" -> "argocd": Pull changes
  rel "argocd" -> "k8s": Apply changes
  rel "k8s" -> "apps": Run workloads
  rel "argocd" -> "argocd": Monitor & reconcile
```

## Contact Information

| Name         | Role             | Contact                       |
|--------------|------------------|-------------------------------|
| Erik Englund | System Owner     | <<EMAIL>>     |

> [!TIP]
> To update this profile, submit a Git pull request to this repository.  
> ☑️ Instructions provided to system owner.
>
> [!SUCCESS]
> ☑️ State: Review Ready in Minimum Critical Knowledge Inventory

```text
- [Published profile on Confluence](...)
- [Updated Link column in Minimum Critical Knowledge Inventory](...)
```

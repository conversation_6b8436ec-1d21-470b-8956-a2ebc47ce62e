<!-- Parent: <PERSON>ved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC-524 Grafana APM Cloud -->

<!-- Include: macros.md -->
# Grafana APM Cloud

```text
Author: Architecture Team
Publish Date: 2025-06-23
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

Grafana APM Cloud is WEX's standardized Application Performance Monitoring (APM) solution built on Grafana Cloud. This canonical system provides comprehensive monitoring, tracing, and observability capabilities for applications and services across the enterprise. It enables teams to monitor application performance, troubleshoot issues, and gain insights into system behavior through unified dashboards and analytics.

## Standardization Value Proposition

Standardizing on Grafana APM Cloud provides several key benefits:

* Unified Observability Platform
  * Centralized monitoring and alerting across all applications
  * Consistent metrics collection and visualization
  * Integrated logging and tracing capabilities

* Cost Optimization
  * Shared infrastructure and licensing
  * Optimized data retention policies
  * Reduced operational overhead

* Enhanced Collaboration
  * Standardized dashboards and alerts
  * Common troubleshooting workflows
  * Shared knowledge base and best practices

* Security & Compliance
  * Centralized access control
  * Audit logging
  * Compliance with data retention requirements

## Components

### Core Services

```mermaid
mindmap
  root((Grafana APM))
    Metrics
      Prometheus
      OpenTelemetry Metrics
      Custom Exporters
    Logs
      Loki
      OpenTelemetry Logs
      Log Forwarding
    Traces
      Tempo
      OpenTelemetry Traces
      Distributed Tracing
    Alerting
      Alert Rules
      Notifications
      On-Call Management
    Dashboards
      Visualization
      Templates
      Sharing
```

### Key Features

* **Metrics Collection and Storage**
  * Time-series data collection
  * Custom metrics support
  * Long-term storage and aggregation

* **Log Management**
  * Centralized log aggregation
  * Full-text search
  * Log correlation

* **Distributed Tracing**
  * End-to-end request tracking
  * Service dependency mapping
  * Performance bottleneck analysis

* **Alerting and Notifications**
  * Multi-channel notifications
  * Alert templating
  * Incident management integration

## Onboarding Instructions

1. Request Access
   * Submit access request through ServiceNow
   * Provide application and team details
   * Complete security assessment

2. Configure Data Sources
   * Install required agents/exporters
   * Set up data collection
   * Verify data ingestion

3. Set Up Monitoring
   * Deploy standard dashboards
   * Configure alerts
   * Define SLOs/SLIs

4. Team Training
   * Complete Grafana fundamentals training
   * Review best practices documentation
   * Schedule team workshop if needed

## User Guides

### Documentation

* [Grafana Cloud Documentation](https://grafana.com/docs/grafana-cloud/)
* [OpenTelemetry Integration Guide](https://grafana.com/docs/opentelemetry/)
* [WEX Observability Standards](internal-link)
* [Dashboard Creation Guide](internal-link)

### Best Practices

* Implement standardized naming conventions
* Use service discovery for dynamic monitoring
* Follow the RED method (Rate, Errors, Duration)
* Implement proper data retention policies
* Use templating for reusable dashboards

## Contact Information

### Primary Contacts

* **Platform Team**
  * Email: <<EMAIL>>
  * Slack: #grafana-support

### Escalation Path

1. Platform Team (24/7 Support)
2. Cloud Operations Team
3. Architecture Team

### Additional Resources

* Internal Knowledge Base: [link]
* Training Materials: [link]
* Support Portal: [link]

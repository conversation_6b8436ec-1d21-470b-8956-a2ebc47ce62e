<!-- Parent: <PERSON><PERSON> Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC458 Imperva WAF and CDN -->
# Imperva WAF and CDN

```text
Author: <PERSON>
Publish Date: 2025-03-12
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

Imperva provides enterprise-grade Web Application Firewall (WAF) and Content Delivery Network (CDN) services for Wex applications. It offers protection against web-based threats and optimizes content delivery across our global infrastructure.
The document cover the WAF functionality of Imperva.

## Standardization Value Proposition

- WAF can help protect content providers and users from a variety of attacks that could compromise delivery and availability.
- DDoS protection: WAF is an important DDoS protection  
- WAFs are not an ultimate security solution, rather they are meant to be used in conjunction with other network perimeter security solutions such as network firewalls and intrusion prevention systems to provide a holistic defense strategy.
- A security solution on the web application level which – from a technical point of view – does not depend on the application itself
- Compliance with security standards and regulations

## Components

- [Imperva WAF](https://wexinc.atlassian.net/wiki/spaces/ISC/pages/154122781639/Imperva+WAF+Request+access+report+an+issue+or+raise+another+non-access+request)
- Imperva CDN

### Imperva WAF Access request

Access the Employee Service Portal, go to Digital Tools and Imperva WAF

[Access request](https://wexinc.atlassian.net/wiki/spaces/ISC/pages/154122781639/Imperva+WAF+Request+access+report+an+issue+or+raise+another+non-access+request#Access-Request)

### Incident management

[Incident form for WAF](https://wexinc.atlassian.net/wiki/spaces/ISC/pages/154122781639/Imperva+WAF+Request+access+report+an+issue+or+raise+another+non-access+request#Incident)

### Imperva CDN

Imperva CDN is the Wex standard solution for content delivery network services. It provides global distribution of static and dynamic content to improve application performance, reduce bandwidth costs, and enhance user experience. The Imperva CDN delivers web assets from edge locations closest to users, dramatically reducing latency and improving application responsiveness worldwide.
No configuration is required for websites behind Imperva CDN. Support cases can be opened using the corresponding contact information below.

## User Guides

- Internal Documentation:
  
  - [Imperva Training recording](https://wexchange.wexinc.com/home/<USER>/space/4821220863246336/cup-o-tech/post/1285227209325693)
  - [Incident Response Playbook](https://docs.google.com/document/d/1oWDuXnDbDN8hst3_G3DycWTXCxJk5N4x8DdSIFYwwnc/edit?tab=t.0#heading=h.gjdgxs)

## Internal Resources

- [Global Network Security Policy - 02446](https://aodocs.altirnao.com/?locale=en_GB&aodocs-domain=wexinc.com#Menu_viewDoc/LibraryId_UaBPCN63arqiBS4iO9/ViewId_UaBParq0TMEuM5J1ov/DocumentId_UdTZ4e0ANeJ6MQOzPz/VersionId_COMMITTED)
- [Cloud Networking Content Distribution Guidelines](https://wexinc.atlassian.net/wiki/spaces/ESA/pages/154654146575/Cloud+Networking+Content+Distribution+Guidelines)

## Contact Information

Ask questions or report an issue, based on the following subjects:

[Imperva WAF Issue](https://wexinc.atlassian.net/servicedesk/customer/portal/13/create/1861)
[Imperva WAF Site Decommission](https://wexinc.atlassian.net/servicedesk/customer/portal/13/create/2016)
[Imperva WAF Pen Test](https://wexinc.atlassian.net/servicedesk/customer/portal/13/create/1726)
[Imperva WAF Automated IP Allowlist](https://wexinc.atlassian.net/servicedesk/customer/portal/13/group/42/create/2068)
[Imperva WAF New Site Onboarding](https://wexinc.atlassian.net/servicedesk/customer/portal/13/group/42/create/1728)
[Imperva WAF Security Update](https://wexinc.atlassian.net/servicedesk/customer/portal/13/group/42/create/1727)
[Imperva CDN issues](https://wexinc.atlassian.net/servicedesk/customer/portal/13/create/1861)

Cases not related to those above, check the [Employee Service Portal](https://wexinc.atlassian.net/servicedesk/customer/portals) then, go to *Infrastructure Technology Service page* and search for "Imperva".

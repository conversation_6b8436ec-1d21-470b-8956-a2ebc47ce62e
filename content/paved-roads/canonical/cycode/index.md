<!-- Parent: Paved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC-467 Cycode Software Security Platform -->
# Cycode Software Security Platform

```text
Author: <PERSON>
Publish Date: 2025-03-17
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

Cycode is a comprehensive software supply chain security platform that provides end-to-end visibility and protection across the software development lifecycle (SDLC). It helps secure source code, build processes, and CI/CD pipelines against threats while ensuring compliance with security standards.
This tool monitors our source code repositories as well as public repositories to detect if our source has been leaked to the public and if any secrets (such as API keys, passwords, etc.) have been saved within our repositories.

## Standardization Value Proposition

- focuses on source code repositories, CI/CD pipelines
- emphasizes code leak prevention and secrets management
- aims to secure the entire software development lifecycle (SDLC)
- by integrating Snyk into the developer workflow and Cycode into the repository and pipeline, organizations can achieve a more comprehensive security posture across the entire software development lifecycle.

## Components in use at Wex

Cycode provides several key security components that protect our software development environment:

- **Secrets Detection** - Scans repositories to identify hardcoded credentials, API keys, tokens and other sensitive information that should not be committed to source code, helping prevent unauthorized access.

- **Code Leak Protection** - Monitors public repositories and sources to detect if any proprietary Wex code has been exposed, enabling quick remediation of potential intellectual property theft.

- **CI/CD Pipeline Security** - Analyzes build processes and deployment pipelines for security vulnerabilities, ensuring that the code delivery mechanism remains protected from tampering or exploitation.

- **Application Security Posture Management (ASPM)** - Provides comprehensive visibility into security risks across the entire software development lifecycle, helping teams prioritize and address vulnerabilities before they reach production.

### Cycode integration with Github Actions

![Cycode integration with Github Actions](./cycode_diagram.png)

Cycode integrates to WEX GitHub via OAuth with a read only account.  The scans would then take place in the Cycode environment as shown in the adjacent diagram.

## Onboarding Instructions

1. Log in via the Cycode Okta tile
2. Inform the Security Architecture team that you have logged in by contacting <<EMAIL>>

## User Guides

For detailed usage instructions, refer to:

- [Snyk and Cycode WexChange page](https://wexchange.wexinc.com/home/<USER>

## Contact Information

- Support
  - Google Chat: [#snyk_support](https://chat.google.com/room/AAAAvAl0oaY?cls=7)

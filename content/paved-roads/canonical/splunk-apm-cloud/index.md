<!-- Parent: <PERSON>ved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC-465 Splunk Enterprise Log Management -->
# Splunk Enterprise Log Management

```text
Author: <PERSON>
Publish Date: 2025-15-03
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

Splunk Enterprise Log Management is a powerful software platform designed to search, analyze, and visualize machine-generated data from various sources such as websites, applications, sensors, and devices. It was founded in 2003 with the primary goal of making sense of machine-generated log data.

## Standardization Value Proposition

- Enterprise Log Management and Monitoring across all applications and services.
- Reduced mean time to detection (MTTD) and resolution (MTTR) for issues.
- Consistent monitoring practices across teams.
- Improved collaboration through shared dashboards and alerts.
- Cost optimization through centralized licensing and management.

## Components

- Real-time Log Monitoring
- Distributed Tracing
- Service Maps
- Performance Analytics
- Custom Dashboards
- Alerting System
  
## Current capabilities used in WEX

- Real-time monitoring: Splunk provides real-time visibility into data across your entire IT infrastructure. This enables organizations to proactively identify and address issues before they escalate, ensuring continuous system availability and performance.
- Advanced alerting mechanisms: With Splunk, you can set up sophisticated alerts based on specific log data patterns or anomalies.
- Splunk Log Observer: Splunk Log Observer Connect integration lets you query logs from the Splunk Cloud Platform or Splunk Enterprise using Splunk Log Observer. It also allows you to review content from Splunk Observability Cloud.
- View overall system health using timeline.
- Log metricization.
- Log aggregation to group logs into fields.
- Logging rules.

### Features/Capabilities not to be used in WEX

- Splunk in Non-Prod environments

### Exceptions to use in non-prod environments

- System Performance testing.
- System Stability and Reliability profiling.

## Onboarding Instructions

For Splunk OnBoardng instructions refer to the below link.

[Splunk Access] (<https://wexinc.atlassian.net/servicedesk/customer/portal/1016/create/1347>)

[Splunk Incident] (<https://wexinc.atlassian.net/servicedesk/customer/portal/1016/create/1537>)

[Splunk Support Request] (<https://wexinc.atlassian.net/servicedesk/customer/portal/1016/create/1516>)

## Architecture Diagrams

[Splunk Cloud Architecture] ![Splunk Architecture Diagram](./splunk-cloud-architecture.png)

[Splunk Enterprise Log Management Architecture] ![Splunk APM Architecture Diagram](./splunk-log-architecture.png)

## User Guides

- [Splunk Log Management] (<https://docs.splunk.com/observability/>)

## Contact Information

- Technical Lead:  (<<EMAIL>>)

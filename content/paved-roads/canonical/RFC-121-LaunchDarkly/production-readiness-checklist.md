# LaunchDarkly Production Readiness Checklist

```text
Author: <PERSON><PERSON>
Publish Date: 2023-09-15
Product Name: LaunchDarkly Feature Flags
LOB: Platform Services
```

This document provides a comprehensive assessment of LaunchDarkly's production readiness across key operational domains.

## Code Quality & Deployment

### Code Reviews

There is a CODEOWNERS file maintained at the root of the repository. For each LaunchDarkly project (each folder under /terraform), this file contains the GitHub team responsible for performing code reviews.

### Unit Tests

Manual testing is performed for each change. The GitOps workflow includes validation stages that ensure changes meet quality standards before deployment.

### Integration Tests

For each testing scenario, a change is introduced and run through the CI/CD pipelines to see if it reflects within LaunchDarkly. This validation ensures proper integration between the GitOps repository and the LaunchDarkly service.

### End-To-End Tests

End-to-end testing follows the same pattern as integration testing, with changes validated through the entire workflow from code commit to flag availability in applications.

### CI/CD Pipelines

GitHub Actions workflows are used to enable Continuous Integration and synchronization of feature flags with LaunchDarkly. Through Powershell scripts, actions automate all steps involved in the deployment process:

- **"Sync changes from LD into LD repository" workflow**
  - Trigger: Manual
  - Purpose: Import flags created directly in the LaunchDarkly UI into the GitOps repository

- **"Checkin Actions - Import Feature Flags" workflow**
  - Trigger: On Push
  - Purpose: Automatically process feature flag imports when code is pushed

- **"Merge Actions - Terraform Apply" workflow**
  - Trigger: On PR Merge
  - Purpose: Apply terraform changes when pull requests are merged

- **"PR Actions - Run Terraform Plan" workflow**
  - Trigger: On create of a PR
  - Purpose: Run terraform plan for validation when PRs are created

- **"Manually Run Terraform Apply" workflow**
  - Trigger: Manual
  - Purpose: Force terraform apply operations when needed

- **"Manually update feature flags" workflow**
  - Trigger: Manual
  - Purpose: Manual flag updates when necessary

## Performance & Scalability

### Stress Testing

Relay Proxy has been stress tested with several sets of concurrent user requests and has performed reliably well. Performance metrics demonstrate acceptable response times under various load conditions:

![Performance metrics](image-20230712-212130.png)

## Observability

### Application Performance Monitoring

Datadog is configured to monitor:

- The LaunchDarkly status page for any outages
- Relay Proxies in both cloud environments:
  - AWS: <https://ld-relay-aws.prod.wexfabric.com>
  - Azure: <https://ld-relay-azr-prod.wexglobal.com>

In case of outages, alerts are sent via email to `gl-architecture-evolution`.

### Logging

The LaunchDarkly relay proxies emit logs that are collected and processed according to WEX logging standards.

## Security

### Security Architecture Review

> _Provide evidence demonstrating that a Security Architecture review has been conducted._

A security review has been performed on the design for the relay proxies within AWS and Azure landing zones. The review verified that the architecture follows WEX security standards for cloud deployments and network communication, focusing on:

- Network segmentation
- Access control models
- API security patterns
- Data protection

### Security Scanning

> _Provide evidence that the [required security scanning tools](https://wexinc.atlassian.net/wiki/spaces/ESA/pages/154923565420) have been implemented._

All repositories involved in the LaunchDarkly GitOps process undergo standard WEX security scanning:

- GitHub Advanced Security SAST scanning runs on every pull request
- Dependency scanning identifies vulnerable packages in both application and infrastructure code
- Secret scanning prevents credential leakage with pre-commit hooks and automated scans
- Container image scanning for the relay proxy images

The LaunchDarkly service itself undergoes regular security assessments by the vendor, with SOC 2 Type II reports available for review under NDA.

### Vulnerability Remediation

> _Are application and infrastructure vulnerabilities being effectively identified, assessed, prioritized, and remediated in accordance with the [WEX Vulnerability Management policy](https://wexinc.policytech.com/dotNet/documents/?docid=2150&app=pt&source=search)? Please provide a link to the latest vulnerability testing results and remediation plans._

Vulnerabilities are tracked and remediated according to WEX policy with the following processes:

- Critical updates to the relay proxy infrastructure are applied within 30 days of release
- High-priority vulnerabilities addressed within 60 days
- Medium and low vulnerabilities addressed in accordance with standard release cycles
- LaunchDarkly vendor vulnerabilities tracked through their security bulletins

Latest vulnerability reports for WEX-managed components are available in the security team's vulnerability tracking system.

### Data At Rest & In Transit

> _Provide details on the specific controls implemented to ensure data is encrypted at rest, in transit, and in use, as required by the [WEX Encryption Policy](https://wexinc.policytech.com/dotNet/documents/?app=pt&source=unspecified&docid=2340)._

LaunchDarkly implements the following data protection controls:

1. **Data in Transit:**
   - All connections to LaunchDarkly services and relay proxies use HTTPS (TLS 1.2+)
   - Internal service communications between relay proxies and applications enforce TLS
   - API keys are transmitted securely using header-based authentication

2. **Data at Rest:**
   - Feature flag configurations stored in LaunchDarkly are encrypted at rest
   - API keys stored in AWS/Azure secret management services with encryption
   - Terraform state files encrypted at rest

3. **Data Access Controls:**
   - Role-based access controls limit who can view and modify flag data
   - Audit logging tracks all configuration changes

### Role-Based Access Control

Roles and access permissions have been reviewed and adjusted to enforce proper access controls. Two primary roles have been established:

| Permission | Account Admin | Development |
| --- | --- | --- |
| Toggle Flag | X | X |
| Create Context | X | X |
| Create Flag | X | X |
| Manage API tokens | X | X |
| Create Environment | X |  |
| Create Project | X |  |
| Configure Extensions | X |  |
| Membership | Architecture, DevOps | Development, QA, Operations |

Access control is managed through:

- Okta SSO integration for authentication
- SCIM provisioning for automated user management
- Regular access reviews (quarterly)
- Just-in-time access for emergency management needs

## Reliability

### High Availability

The LaunchDarkly Relay Proxy provides high availability for feature flag values. Applications should configure a relay proxy in the same cloud provider they use:

- **AWS:** ld-relay-aws.prod.wexfabric.com (https and port 443)
- **Azure:** ld-relay-azr-prod.wexglobal.com (https and port 443)

Feature flags are cached locally by the relay proxy, ensuring availability even during temporary connectivity issues with LaunchDarkly.

## Infrastructure & Environment

### Infrastructure as Code (IaC)

All LaunchDarkly configurations are managed through terraform in a GitOps repository. This enables:

- Tracking of changes
- Collaboration between teams
- Consistent deployments across environments

## Operational Readiness

### Documentation

LaunchDarkly documentation is comprehensive and includes:

- API documentation available at <https://launchdarkly.com/docs/api/feature-flags/get-feature-flag>
- User documentation divided into:
  - Canonical profile document (RFC-121)
  - GitOps guidance document at RFC77: "How to use LaunchDarkly GitOps"

### On-Call & Escalation

The Architecture Evolution team is responsible for:

- LaunchDarkly WEX account administration
- WEX LaunchDarkly GitOps workflow maintenance
- Facilitating onboarding and training for WEX teams

For questions regarding onboarding, training, or usage, contact: Sayi Repakula  
For questions regarding licensing or account settings, contact: Phil Jenkins

In case of outages, Datadog will notify the architecture-evolution team via email.

<!-- Parent: Paved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC121-LaunchDarkly Feature Flags -->
# LaunchDarkly Feature Flags

```text
Author: Sayi Repakula
Publish Date: 2024-08-30
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

LaunchDarkly is a system for managing feature flags serving applications across WEX.

> A virtual [workshop](https://drive.google.com/file/d/1tlqep8JD5gkXgua3iVqpPLaAzHIXWL2n/view?usp=sharing) was held with LaunchDarkly and technical resources across WEX.  This training provides a overview of the product, concepts, features, usage, and additional documentation resources.

## Standardization Value Proposition

Standardizing on LaunchDarkly for feature flag management offers several benefits and value propositions:

- **Decoupling**: Feature flags enable the decoupling of feature releases from code deployments. This allows teams to deploy code to production without immediately exposing new features to users, reducing the risk associated with releases and enabling more controlled rollouts.
- **Efficiency**: LaunchDarkly provides a centralized platform for managing feature flags, which streamlines the process of enabling, disabling, and configuring features across multiple environments. This reduces the overhead associated with manual feature management and allows for faster iterations and experimentation.
- **Consistency**: By using a standardized tool like LaunchDarkly, teams can ensure consistent implementation and usage of feature flags across all projects. This consistency helps in maintaining a uniform approach to feature management, making it easier to understand and manage feature states across the organization.
- **Quality Improvements**: Feature flags facilitate continuous integration and continuous deployment (CI/CD) practices by allowing features to be tested in production with real users. This leads to early detection of issues and faster feedback loops, ultimately improving the quality of the software. Additionally, the ability to quickly roll back features without redeploying code enhances the stability and reliability of the applications.

Overall, standardizing on LaunchDarkly for feature flag management enhances the agility, reliability, and quality of software development and deployment processes.

## Components

### Concepts

| **Name**         | **Description** |
| :-: | :-: |
|[Resource](https://docs.launchdarkly.com/home/<USER>/role-concepts#resources) | General term for constructs in LaunchDarkly.  Includes items such as Projects, Flags, Environments, Metrics, Custom Roles, etc |
| [Account Member](https://docs.launchdarkly.com/home/<USER>/managing) | LaunchDarkly web interface user that is associated with a WEX Okta user |
| [Team](https://docs.launchdarkly.com/home/<USER>
| [Role](https://docs.launchdarkly.com/home/<USER>/role-concepts) | Collection of account members and teams used for access control policy evaluation |
| [Context](https://docs.launchdarkly.com/home/<USER>
| [Segment](https://docs.launchdarkly.com/home/<USER>
| [Project](https://docs.launchdarkly.com/home/<USER>/projects/?q=project) | A security boundary in LaunchDarkly that provides isolation between different WEX LoBs |
| [Environment](https://docs.launchdarkly.com/home/<USER>/environments) | An operational boundary in a Project that provides isolation of flag values between product environments |
| [Tag](https://docs.launchdarkly.com/home/<USER>/role-tags) | Metadata associated to a resource used for access control policy evaluation |

### Feature Flag Types

The following feature flag types have been identified.

| **Type** | **Temporary** | **Description** | **Examples** |
| :-: | :-: | :-: | :-: |
| cycle | True | release cycle flag used to enable new/changed functionality in whole or in part | experiment toggles release toggles |
| migration | True | short-lived flag used to manage large-scale architectural changes in a system | network changes software upgrades software standard changes |
| customer | False | long-lived feature flag controlling enablement of a logical component of an application for a discrete segment of users | buy-up features |
| kill | False | long-lived flag used by Operations to temporarily disable a part of the system | disable vendor integration disable customer processing |

> <!-- Info -->
> Info
> If a new type of feature flag is warranted, please bring that to Architecture for review.

### Relay Proxy

The [LaunchDarkly Relay Proxy](https://docs.launchdarkly.com/home/<USER>/?q=relay+p) is a lightweight Go application that can be self-hosted on WEX infrastructure. The Relay Proxy is used to:

- provide high availability to WEX feature flags
- decrease an application’s number of outbound connections  
- provide higher performance for feature flag evaluation using a local cache

> <!-- Info -->
> Info
> A relay proxy must be available to provide high availability of feature flag values. This proxy is a requirement for broad/robust usage of LaunchDarkly feature flags in production.

Applications should [configure a relay proxy](https://docs.launchdarkly.com/home/<USER>/using) in the same cloud provider (i.e. AWS or Azure) used to run the application.

- **AWS:** ld-relay-aws.prod.wexfabric.com (https and port 443)
- **Azure:** ld-relay-azr-prod.wexglobal.com (https and port 443)

### Architecture Diagram

The following diagram illustrates the LaunchDarkly architecture at WEX, showing how applications interact with LaunchDarkly through relay proxies:

```mermaid
architecture-beta
   title LaunchDarkly Feature Flag Architecture
   Person Developer
   System GitOps [LaunchDarkly GitOps Repository]
   System DevOps [WEX DevOps]
   System LaunchDarkly [LaunchDarkly SaaS]
   
   Cluster AWSApplication {
      System AwsApp1 [WEX Application]
      System AwsApp2 [WEX Application]
      System AwsRelayProxy [Relay Proxy]
   }

   Cluster AzureApplication {
      System AzureApp1 [WEX Application]
      System AzureApp2 [WEX Application]
      System AzureRelayProxy [Relay Proxy]
   }

   System Datadog [Datadog Monitoring]
   
   Developer -> GitOps: Updates feature flag configurations
   GitOps -> DevOps: CI/CD pipelines
   DevOps -> LaunchDarkly: Terraform apply
   
   AwsApp1 -> AwsRelayProxy: Flag evaluation
   AwsApp2 -> AwsRelayProxy: Flag evaluation
   AwsRelayProxy -> LaunchDarkly: SDK connections and cache refresh
   
   AzureApp1 -> AzureRelayProxy: Flag evaluation
   AzureApp2 -> AzureRelayProxy: Flag evaluation
   AzureRelayProxy -> LaunchDarkly: SDK connections and cache refresh
   
   Datadog -> AwsRelayProxy: Monitors health
   Datadog -> AzureRelayProxy: Monitors health
   Datadog -> LaunchDarkly: Monitors status page
```

### Capabilities Matrix

Full-scale adoption of feature flags into products requires proper access, manual or automated mechanisms for adding & retiring flags, and, the ability to turn on and off flags through the release pipelines along with other capabilities. After each capability is worked on, it’s released for general availability. The following table contains an inventory of capabilities that have already been released, currently being worked, or those that are scoped into future work.

| **Feature** | **Expected Availability** | **AWS** | **Azure** |
| :-: | :-: | :-: | :-: |
| Relay Proxy | | 04/28/2023 | 06/21/2023 |
| GitHub SCM Integration | | 06/29/2023 | 06/29/2023 |
| Okta Integration | | 12/1/2022 | 12/1/2022 |
| Terraform Repo / GitHub Actions | | 5/14/2024 | 5/14/2024 |
| Okta SCIM Role Assignment | | 6/5/2024 | 6/5/2024 |
| Feature Flag Standards Linter | Q1 2024 | | |
| Onboarding Documentation | | 6/5/2024 |6/5/2024 |
| Distribute Cache for Relay Proxy | Q3 2024 | | |

### Mobile SDKs - out of scope

Current licensing places limits on the number of client side integrations.  The use of Mobile SDKs is limited and requires coordination with Architecture and Finance.

Alternatively, it is recommended that a backend layer that connects to the LaunchDarkly relay proxy is employed for evaluating flags and passing the results to the mobile app.

### Performance Metrics

Relay Proxy has been stress tested with several sets of concurrent user requests and it has performed reliably well. Following document summarizes the average response times over different loads.

![image image-20230712-212130.png](image-20230712-212130.png)

## Onboarding Instructions

LD offers all its features through the use of a Web UI. (Infra as code) As WEX is standardized on the use of GitOps as much as we developed GitOps solution. It is the recommended approach for applications using Feature Flags.

> Please refer to this [link](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/RFC77-How+to+use+LaunchDarkly+GitOps) for a comprehensive documentation on its usage.

## User Guides

### Experimentation

LaunchDarkly offers the creation and maintenance of Experimentation via their Web UI and it's fully supported by their APIs. LaunchDarkly's terraform provider does not support this and hence it would not able available through WEX's GitOps yet. The terraform config related to Experimentation is not supported by the terraform provider.

### Managing Flags: LaunchDarkly API

> <!-- Info -->
> Info
> This is the preferred mode of managing flags in LaunchDarkly.  Using the API does not require a dedicated account member license for the LaunchDarkly web UI.

LaunchDarkly provides a [REST API](https://apidocs.launchdarkly.com/) which can perform all the same functions as the web interface.

[Creating projects](https://apidocs.launchdarkly.com/tag/Projects#operation/postProject)
[Creating environments](https://apidocs.launchdarkly.com/tag/Environments#operation/postEnvironment)
[Creating feature flag](https://apidocs.launchdarkly.com/tag/Feature-flags#operation/postFeatureFlag)
[Toggling feature flag](https://apidocs.launchdarkly.com/tag/Feature-flags#operation/patchFeatureFlag)

### LaunchDarkly UI

> <!-- Info -->
> Info
> WEX has a limited number of seat licenses (for [account members](https://docs.launchdarkly.com/home/<USER>/managing#understanding-members-in-launchdarkly) ) in LaunchDarkly.  Access is limited to manager-approved employees in the Development and DevOps organizations.
>
> This constraint is only applies to the LaunchDarkly web interface.  WEX engineering and development staff are encouraged to use the LaunchDarkly REST  API wherever possible.
>
> Employees who have been approved for access may submit a Cherwell request access in Cherwell Portal: [Service Catalog / Access / Corporate / LaunchDarkly]

![image image-********-203252.png](image-********-203252.png)

The LaunchDarkly web interface is accessible via Okta that allows users can manage projects, environments, and flags.  LaunchDarkly provides comprehensive procedural [documentation](<https://docs.launchdarkly.com/home/<USER>

[Creating projects](https://docs.launchdarkly.com/home/<USER>/projects#creating-new-projects)
[Creating environments](https://docs.launchdarkly.com/home/<USER>/environments#creating-environments)
[Creating feature flag](https://docs.launchdarkly.com/home/<USER>/feature-flags)
[Toggling feature flag](https://docs.launchdarkly.com/home/<USER>/toggle)

### Evaluating Flags

Feature flags should be retrieved in an application layer.  Feature flag values may be passed into a shared library but should not be retrieved from within a shared library. Shared libraries that retrieve flags may result in tight-coupling to a particular feature flag management product.

#### .NET Example

- Add Nuget reference in the project to [LaunchDarkly.ServerSdk](https://www.nuget.org/packages/LaunchDarkly.ServerSdk/)
- Import the LaunchDarkly namespaces in the necessary CS files
  - using namespace LaunchDarkly.Sdk;
  - using namespace LaunchDarkly.Sdk.Server;
- Register LDClient as a singleton in the project’s DI framework
- Evaluate feature flag in application code
  - Instantiation or inject (using constructor injection) the LDClient instance
  - Build LaunchDarkly Context instance with appropriate metadata
  - Retrieve feature flag value using LDClient and Context in application layer
  - Evaluate boolean value in application or shared library logic

```LdClient client = new LdClient("sdk-key-123abc");
var context = Context.Builder("context-key-123abc")
    .Name("MyName")
    .Build();
bool showFeature = client.BoolVariation("your.feature.key", context, false);
if (showFeature) {
    // application code to show the feature
}
else {
    // the code to run if the feature is off
}
```

### GitOps

Creation of the Projects, Environments, Feature Flags and their subsequent maintenance can be fully automated by coding terraform scripts within fabric-launchdarkly-terraform repository. Since the repository is shared across all projects, care has to be taken while working with terraform and GitHub actions. =

A detailed GitOps process document has been created [**here**](https://wexinc.atlassian.net/wiki/spaces/WF/pages/************) for reference. The structure and usage guidance provided is the recommended approach for maintaining all Projects and Feature Flags.

### Standards

**MUST: Be authorized for access to LaunchDarkly**
It is recommended to use LaunchDarkly API keys instead of the web interface for key management use cases.  API keys may be used in CI-CD pipelines, internal support applications, or CLI shells.

> <!-- Info -->
> Info
> WEX has a limited number of seat licenses (for account members) in LaunchDarkly.  This constraint is limited to access to the LaunchDarkly web interface.  Access is limited to manager-approved employees in the Development and DevOps organizations.
>
> Employees who have been approved for access may submit a Global Service Portal access request access:
> [Service Catalog / Access / Corporate / LaunchDarkly]

Account members are [assigned to / authorized against] one of two roles.

| Permission | Account Admin | Development |
| :-: | :-: | :-: |
| Toggle Flag | X | X |
| Create Context | X | X |
| Create Flag | X | X |
| Manage API tokens | X | X |
| Create Environment | X |  |
| Create Project | X |  |
| Configure Extensions | X |  |
| Membership (select individuals) | Architecture DevOps | Development QA Operations |

**MUST: Evaluate MAU usage & licensing before adopting client-side integration (browser, mobile)**
Client-side integration (e.g. mobile, javascript, etc) is possible with LaunchDarkly but has capacity limitations.  Server-side integration allows for an unlimited number of MAUs (Monthly Active Users) while client-side integrations have a limited number of pre-negotiated MAUs available.

Teams can request approval for client-side integrations from Architecture

**MUST: Conform to flag naming conventions**
Flags must conform to the standard naming conventions for each feature flag type.  These naming conventions allow users to find the flags they need quickly and also understand the context of any flag in the project.

| Flag Type | Convention |
| :-: | :-: |
| cycle | cycle-{workItemNumber}-{name} |
| migration | migration-{name} |
| tenant | tenant-{name} |
| kill | kill-{killType}-{name} |

The property placeholders in the above convention are defined as follows:

| Token | Description |
| :-: | :-: |
| {workItemNumber} | Identifier for work item of feature, user story, or bug (e.g. Jira, Azure Boards) |
| {name} | Verbose name describing the scope and purpose of the flag.  Should be lowercase alphanumeric with hyphen (-) word separators. |
| {killType} | Kill type values include: shard, tenant, process |

**SHOULD: Conform to standard context properties for the project**
Context properties are collections of contextual metadata that describe the scope of the use case that need the feature flag value.  

Different projects may require different patterns for context properties. Not all feature flags will require context properties, but applications should be consistent with the properties that are used.  Solution documentation for the project should include (or link to) context property standards for feature flags.

Recommended standard context properties include:

| Property | Description |
| :-: | :-: |
| organization | Unique identifier for a service provider or top-level customer consisting of one or more tenants (e.g. business party) |
| tenant | Unique identifier for a single instance of a customer consisting of one or more groups (e.g. administrator) |
| group | Unique identifier for a managed group of consumers (e.g. employer) |
| product | Unique identifier for a product line |
| application | Unique identifier for an application or service |
| userType | Classification of user such as: administrator, employer, consumer, vendor |

**SHOULD NOT: Retrieve flags from shared libraries**
Flag values should be retrieved at the application layer and passed into shared libraries.  Libraries and packages that are shared by multiple applications make dependency management difficult.  Using shared libraries can result in inefficient usage compute and network resources which can lead to further application instability.

**SHOULD: Limit scope of feature flag values**
Feature flag values should be constrained to the smallest unit of work as possible.  Attempting to use feature flags to control multiple operations can cause confusion

**SHOULD: Review all temporary feature flags every development cycle**
Temporary feature flags should be reviewed and maintained as part of every development cycle.  This is needed to ensure that old flags are removed once they are no longer necessary.

> <!-- Info -->
> Info
> A feature should not be considered complete until all functionality has been delivered and all code cleanup has been completed and released to customers.

**SHOULD: Use Segments in Customer feature flags**
Segments should be used in customer feature flags.  Segments simplify the administration of customer feature flags through the use of targeting rules.  These targeting rules allow a flag to be toggled for multiple users based on a common attribute.

## Contact Information

The Architecture Evolution team is responsible for:

- LaunchDarkly WEX account administration
- WEX LaunchDarkly GitOps workflow maintenance
- Facilitating onboarding and training for WEX teams

For questions regarding onboarding, training, or usage, contact: Sayi Repakula  
For questions regarding licensing or account settings, contact: Phil Jenkins

## Production Readiness

LaunchDarkly has undergone comprehensive production readiness assessment to ensure reliability and operational excellence. We maintain a detailed [production readiness checklist](/c:/Git/github/arch-rfc-content/content/paved-roads/canonical/RFC-121-LaunchDarkly/production-readiness-checklist.md) that covers all aspects of the system's readiness for production use.

### Security & Compliance

The LaunchDarkly implementation at WEX includes the following security controls:

- **Authentication**: Integration with Okta SSO and SCIM for automated user provisioning
- **Authorization**: Role-based access controls with clearly defined permissions
- **Data Protection**: Encryption of data both in transit (TLS 1.2+) and at rest
- **Vulnerability Management**: Timely remediation following WEX vulnerability policies

Following is the approved WEX wide architecture for LaunchDarkly
![LaunchDarkly Architecture](LDArchitecture.png)

### Documentation & Monitoring

LaunchDarkly documentation is comprehensive and includes:

- API documentation available at <https://launchdarkly.com/docs/api/feature-flags/get-feature-flag>
- User documentation divided into:
  - Canonical profile document (this RFC)
  - GitOps guidance document at RFC77: "How to use LaunchDarkly GitOps"

Datadog is configured to monitor the LaunchDarkly status page for any outages. In case of an outage, it will notify `gl-architecture-evolution` via email.

Relay Proxies are also monitored in both cloud environments:

- AWS: <https://ld-relay-aws.prod.wexfabric.com>
- Azure: <https://ld-relay-azr-prod.wexglobal.com>

### CI/CD Pipelines

GitHub Actions workflows enable continuous integration and synchronization of feature flags with LaunchDarkly:

| Workflow | Trigger | Purpose |
| --- | --- | --- |
| "Sync changes from LD into LD repository" | Manual | Import flags created in LaunchDarkly UI |
| "Checkin Actions - Import Feature Flags" | On Push | Process feature flag imports |
| "Merge Actions - Terraform Apply" | On PR Merge | Apply terraform changes |
| "PR Actions - Run Terraform Plan" | On PR Creation | Validate terraform plans |
| "Manually Run Terraform Apply" | Manual | Force terraform apply |
| "Manually update feature flags" | Manual | Update flags manually |

### Reliability & Performance

Relay Proxy has been stress tested with various concurrent user loads and has performed reliably. The proxy provides high availability for feature flag values in both cloud environments:

- **AWS:** ld-relay-aws.prod.wexfabric.com (https and port 443)
- **Azure:** ld-relay-azr-prod.wexglobal.com (https and port 443)

![Performance metrics chart](image-20230712-212130.png)

#### High Availability

The LaunchDarkly architecture is designed for high availability through:

- Cached flag values in relay proxies that continue functioning during temporary LaunchDarkly outages
- Multiple relay proxy instances for redundancy
- Default flag values configured in applications as a last-resort fallback

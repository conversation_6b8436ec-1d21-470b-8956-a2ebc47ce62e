<!-- Parent: Paved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC-464 Datadog APM Cloud -->
# Datadog APM Cloud

```text
Author: <PERSON>
Publish Date: 2025-03-15
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

Datadog APM Cloud is our enterprise Application Performance Monitoring solution providing real-time observability into application performance, infrastructure metrics, and user experience. It enables teams to monitor, troubleshoot, and optimize their applications across our distributed environment.

## Standardization Value Proposition

- Unified observability platform integrating metrics, traces, and logs
- Real-time performance monitoring and anomaly detection
- Streamlined incident response and root cause analysis
- Consistent monitoring practices across development teams
- Cost optimization through centralized licensing
- Enhanced collaboration with shared dashboards and alerts
  
## Components

- APM Agent Libraries
- Distributed Tracing
- Real-time Metrics Collection
- Service Dependency Mapping
- Custom Dashboards
- Automated Alerting
- Log Management
- Synthetic Monitoring
- Real User Monitoring (RUM)

### Current capabilities used in WEX

- Infrastructure Monitoring: Provides real-time visibility into the performance and health of servers, containers, and cloud services.
- Application Performance Monitoring (APM): Allows teams to monitor application performance, identify bottlenecks, and optimize code.
- Programmable Dashboards: Users can create customizable dashboards to visualize metrics and data.
- Alerts and Notifications: Datadog can send alerts based on urgent issues, helping teams respond quickly.
- Full API Access: Enables integration and observability across all applications and infrastructure.

### Features/Capabilities not to be used in WEX

- APM in Non-Prod environments

### Exceptions to use in non-prod environments

- System Performance testing
- System Stability and Reliability profiling

## Onboarding Instructions

For Datadog OnBoardng instructions refer to the below link.

- <https://wexinc.atlassian.net/wiki/spaces/DA/pages/154242351953/Datadog+Onboarding>

## DataDog Access

[For Datadog Access] <https://wexinc.atlassian.net/servicedesk/customer/portal/1016/create/1328>

[For Datadog Incident] <https://wexinc.atlassian.net/servicedesk/customer/portal/1016/create/1537>

[For Datadog Service Request] <https://wexinc.atlassian.net/servicedesk/customer/portal/1016/create/1509>

## Deployment

To deploy Datadog APM refer to the below link.

- <https://github.com/wexinc/ps-benefits-platform-template-helm/blob/main/helm/templates/deployment.yaml>

## Architecture

[Datadog APM Architecture] ![Datadog APM Architecture Diagram](./DataDog-solution-diagram.jpeg)

## User Guides

- [Official Datadog Documentation](https://docs.datadoghq.com/)
- [Dtadog Monitoring](https://www.datadoghq.com/blog/monitoring-kubernetes-with-datadog/)

## Contact Information

- Platform Lead: David Zevac <<EMAIL>>

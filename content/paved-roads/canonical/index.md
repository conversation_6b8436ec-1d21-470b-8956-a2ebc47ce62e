<!-- Parent: Paved Roads -->
<!-- Title: Canonical Systems -->
# Canonical Systems

Canonical Systems represent standardized, approved components that are used across WEX's technology landscape. These include:

- **Common Tools**: Software applications and utilities that are widely used to assist developers in various tasks, enhancing productivity, streamlining workflows, and improving code quality.

- **Shared Artifacts**: Reusable pre-built components, resources, or assets that can be incorporated into various designs across multiple projects or applications.

- **Shared Code Snippets**: Reusable pieces of code that enable best practices, learning, and experimentation, reducing the need to rewrite commonly used code patterns.

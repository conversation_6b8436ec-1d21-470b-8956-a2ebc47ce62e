<!-- Parent: <PERSON>ved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC-472 Aiven <PERSON>ka -->
# Aiven <PERSON>ka

```text
Author: <PERSON>
Publish Date: 2025-03-20
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

Aiven for Apache Kafka is a fully managed streaming platform that is deployable in the cloud of your choice. Apache Kafka is an open-source stream-processing software platform that handles real-time data storage. Aiven simplifies the deployment process of Apache Kafka by offering it as a service.

## Standardization Value Proposition

- **Managed Service**: You do not need to worry about infrastructure setup, maintenance, or scalability.
- **Cloud Agnostic**: Choose from multiple cloud providers to host your Kafka instance.
- **Security**: <PERSON>ven encrypts Kafka data at REST and in transit.

## Components

- **Kafka Clusters**: Managed Kafka environments for different purposes (prod/non-prod)
- **Schema Registry**: Centralized schema management for data consistency
- **Kafka Connect**: Data integration framework for building streaming pipelines
- **Monitoring & Metrics**: Built-in monitoring through <PERSON><PERSON> Console
- **Security Components**: Authentication, authorization, and encryption features

## Onboarding Instructions

1. New users can request an Aiven account using the [Aiven Access](https://wexinc.atlassian.net/servicedesk/customer/portal/1016/create/1209). In the description, you mention you need access to the Aiven console.
2. Once your ticket is approved, after around 2 hours, the Aiven option should be available in your WEX Okta. Do not use Aiven's general home page to log in.
3. By default, new users are given *read_only* access to the **dev**, **stage**, and prod environments.

On-boarding information can be found here: [Kafka on-boarding](https://wexinc.atlassian.net/wiki/spaces/KAAS/pages/************/Aiven+Platform#Onboarding)

### End-users

End-users who need to generate API tokens for CI/CD pipelines will need elevated access to the stage and prod environments. You can request elevated privileges via the [#Kafka/Aiven - Support](https://chat.google.com/room/AAAA_Me4HIA?cls=1) channel.

More information about end-user roles can be found in the Confluence page: [End-user Aiven roles](https://wexinc.atlassian.net/wiki/spaces/KAAS/pages/************/Aiven+Platform#End-user-Aiven-roles)

### Environments

WEX services connect to Kafka using VPC Endpoints, routed through AWS PrivateLink (see the diagram below) for US regions and AWS Transit Gateway for all others.

Further information about how to setup your environment connecting to Kafka can be found here: [Environments](https://wexinc.atlassian.net/wiki/spaces/KAAS/pages/************/Aiven+Platform#Environments)

## User Guides

Aiven end-users with at least developer access can create Kafka service users. ==End-users and service users are completely different systems in the Aiven platform== (an end-user cannot be a service user, and a service user cannot be an end-user). Service users and least-privileged ACLs are required for producers and consumers to connect to Kafka topics. It is recommended that different users be provided for each of your applications.

Detailed documentation available in Conflunce page: [KaaS - Eventing Platform](https://wexinc.atlassian.net/wiki/spaces/KAAS/overview)

## Contact Information

- [Aiven Access](https://wexinc.atlassian.net/servicedesk/customer/portal/1016/group/1046/create/1209)

For general issues, topics, schemas, ACLs, Kafka Connect, PR Review and Aiven administration:

- [Aiven Support Request](https://wexinc.atlassian.net/servicedesk/customer/portal/1016/create/2001)

If your case is not one of those above, please, contact us via google space:

- [#Kafka/Aiven - Support](https://chat.google.com/room/AAAA_Me4HIA?cls=1)

<!-- Parent: <PERSON>ved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC-470 WEX Data Platform -->
# WEX Data Platform

```text
Author: <PERSON>
Publish Date: 2024-01-16
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

The WEX Data Platform is a comprehensive enterprise data management solution that provides centralized data storage, processing, and analytics capabilities. It serves as the foundation for WEX's data-driven decision making, offering secure and scalable data services across the organization.

Data Platform has been using different types of powerful techinologies and tools to perform the data activities with full speed.

## Standardization Value Proposition

### Efficiency Benefits

- Reporting and analytical workloads can be move to Data platform to reduce the operational systems overhead and as well as enable data products and insights for AI use cases
- WEX has actively functioning different business areas and the data is distrubuted accross different applications, technologies and data storages. Centralized data management is essential to bring all the information into centralized storage area so all sorts of analysis and reporting activies can be done as quickly as possible. This centralization is empowering WEX businesses for their judgement and decision making.

- Standardized data integration patterns and protocols
   WEX is supporting Batch and Real time data integration partterns.

- Unified data governance and security controls
   Data platform has implemented centralize data governance tools like Alation. All applications are considered as single sign on to implement tight security controls.

- Reduced time-to-market for new data initiatives
   New data initiative like to bring AI into different business and reporting applications.
   Quicker process to integrate new customer information into data echo system.

### Quality Improvements

- Consistent data quality standards across the organization
   Enterprise level standards are defined and educating different teams to follow the standard

- Automated data validation and monitoring
   Set of rules are implemented in the applications and tool to perform the data validation based on business data standard. Different tools are also considered to monitor the validation logs.

- Standardized metadata management
   Enterprise level Alation is deployed to maintain the metadata information.

- Enhanced data lineage and traceability
   Alation has been using for data catalog and data governance activities. The dbt (Data Build Tool) is used for transformation the information and maintaining the data lineage. Consolidating the data assets on different WEX busniess for better traceability. Currently the process is evolving.

## Components

### Core Platform Components

- Big Data Lake House
   --Snowflake
   --Iceberg table (S3)
- ETL/ELT Processing Framework
   --ETL: Informatica
   --ELT: dbt
- Data Quality Framework
   --Informatica
   --DBT
- Metadata Management System
   --Alation

### Supporting Services

- Data Catalog
   --Alation
   --Snowflake Open Catalog
- Data Governance Tools
   --Alation
- Security and Access Control
   --OKTA
- Monitoring and Alerting
   --DataDog
   --Splunk
- Self-service Analytics Tools
   --ThoughtSpot
   -- Tableau
   -- SAP Business Objects

## Onboarding Instructions

1. Request Access
   - Submit access request through ServiceNow
   - Complete required training modules
   - Review platform documentation

2. Project Setup
   - Define project requirements
   - Establish data storage locations
   - Configure access controls
   - Set up monitoring

For detailed onboarding documentation, visit the [WEX Data Platform Wiki](https://wiki.wexinc.com/data-platform).

## User Guides

### Getting Started

- Platform Overview Training
- Data Integration Patterns
   -- Event based (Kafka)
   -- CDC (Change Data Capture)
   -- ETL (Extraction Transformation Loading)
   -- ETL (Extraction Load Transfomration)
- Security Best Practices
      --- RBAC – Role based access control Policies
      --- Tokenizer
      --- Masking
      --- Encryption of whole File
   -- Access Security
      --- SSO (Single Sign On)
      --- RBAC (Role Based Access Control)

- Performance Optimization Guidelines

### Documentation Resources

- Technical Documentation
- API References
- Best Practices Guide
- Troubleshooting Guide

## Contact Information

- ETL
   Informatica -- Bharath Sama

- ELT
   dbt -- Pirabhu  Selvaraj

- Master Data Management
   Reltio -- Wes Corbin

- Data Catalog and Data Governance
   Alation -- Adrian Van Der Eb

- BI Reporting
   BO -- Azeem Akhthar
   Tableau -- Pirabhu  Selvaraj
   Power Bi --  Josh Bacon

- AI Analytics Reporting
   ThoughtSpot -- Pirabu Selvaraj

### Platform Support

- AI related : Mavericks <<EMAIL>>
- DBT, Snowflake, QLIK, IICS : Platform Support PENG Group
                             : Data Engineer Hawks <<EMAIL>>
  
<!-- Parent: Paved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC-466 Snyk Software Security Platform -->
# Snyk Software Security Platform

```text
Author: <PERSON>
Publish Date: 2025-03-17
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

Snyk is a developer-first security platform that helps find and fix vulnerabilities in code, dependencies, containers, and infrastructure as code. It integrates directly into development workflows to provide automated security scanning and remediation guidance.

## Standardization Value Proposition

- provides security checks and recommendations within IDEs and other development tools.
- focuses on vulnerability detection during development
- comprehensive security posture across the software development lifecycle
- automated security updates and fix suggestions
- Snyk primarily targets the developer experience with integrated security tools meanwhile Cycode focuses more on supply chain security and repository monitoring

## Components

- **Snyk Open Source**: Scans and monitors dependencies for known vulnerabilities
- **Snyk Code**: Provides SAST (Static Application Security Testing) capabilities
- **Snyk Container**: Scans container images and Kubernetes configurations
- **Snyk Infrastructure as Code**: Scans Terraform, CloudFormation, and other IaC files
- **Snyk API**: Enables programmatic access to Snyk functionality
- **IDE Plugins**: Direct integration with popular development environments

## Onboarding Instructions

1. Log in to Snyk using SSO authentication
2. Install relevant IDE plugins

Common plugins:

- Eclipse plugin
- JetBrains plugin
- Visual Studio extension
- Visual Studio Code extension

## User Guides

- [Snyk and Cycode WexChange page](https://wexchange.wexinc.com/home/<USER>
- [IDE Plugin Setup](https://docs.snyk.io/integrations/ide-tools)

## Contact Information

- Google Chat: [#snyk_support](https://github.com/wexinc/arch-rfc-content/pull/678/files)

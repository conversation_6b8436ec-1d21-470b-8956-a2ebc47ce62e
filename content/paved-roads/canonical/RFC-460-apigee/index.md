<!-- Parent: Paved Roads -->
<!-- Parent: Canonical Systems -->
<!-- Title: RFC-460 Apigee API Gateway -->
# Apigee API Gateway

```text
Author: Vish Meduri
Publish Date: 2025-03-10
Category: Paved Roads
Subtype: Canonical Systems
```

## Overview

Apigee serves as WEX's enterprise API Gateway platform, providing a secure and managed entry point for all external API traffic into WEX's ecosystem. It acts as a critical middleware layer that handles API security, traffic management, monitoring, and policy enforcement. WEX uses Apigee Edge as its platform for developing and managing APIs. It enables building APIs by implementing applications hosted on the API Services platform.

Key capabilities include:

- API security and access control
- API Lifecycle management
- Traffic routing and load balancing
- Request/response transformation
- Rate limiting, spike arrest and quota management
- Analytics and monitoring

## Apigee Architectural Diagrams

![image ApigeeEdgeEcosystem.png](ApigeeEdgeEcosystem.png)

![image ApigeeImpervaWAFDiagram.png](fecc3d6b-de37-4984-af2f-7b0283882ee8.png)

## Standardization Value Proposition

Standardizing on Apigee as WEX's API Gateway delivers significant business and technical benefits:

### Business Benefits

- Reduced time-to-market for new APIs through standardized deployment patterns
- Improved API security and compliance through centralized policy enforcement
- Enhanced visibility into API usage and performance
- Simplified partner onboarding through self-service developer portal
- Consistent experience for external API consumers

### Technical Benefits

- Centralized authentication and authorization
- Prewired ingress with Imperva
- Standardized API security controls and policies
- Built-in monitoring and alerting
- Prebuilt integrations with Auth0 and Splunk
- Automated deployment pipelines
- Reusable API patterns and shared flows
- Simplified API versioning and lifecycle management
- [API Maturity Framework](https://wexinc.atlassian.net/wiki/spaces/APIGEE/pages/************/API+Maturity+Model)

## Components

### Core Platform

- **Apigee Edge** - Cloud-native API management platform that handles API proxy deployment, execution, and monitoring
- **API Proxies** - Virtual APIs that provide security, transformation, and routing capabilities
- **Shared Flows** - Reusable policy fragments for common functionality like authentication and logging
- **Virtual Hosts** - Domain configurations for API endpoints
- **Target Servers** - Backend service configurations

### Development Tools

- **Apigee Edge UI** - Web console for API development and testing
- **Apigee Manager** - Internal tool for deployment management
- **Apigee Deployment Manager** - Configuration and CI/CD automation tool
- **Apigee Linter** - Searches for HTTP and XML errors

## Onboarding Instructions

### 1. Access Request Process

a. Contact <<EMAIL>> for an initial consultation.

b. Submit JSM requests to access the following:

- Apigee Deployment Manager Access
- Apigee Edge Access Request

c. Request repository access:

- Apigee configuration repository
- API proxy repository

### 2. Environment Setup

a. Configure local development environment:

- Install required tools
- Set up Git repositories
- Configure API proxy development environment

b. Review documentation:

- Deployment process guide
- Security policies
- Best practices

### 3. Development Workflow

a. Create/modify API proxies in Apigee Edge
b. Test in development environment
c. Follow deployment process for promotion
d. Monitor API performance and usage

### 4. Accessing Logs

Use the following Splunk indices for obtaining logs related to Apigee and Imperva WAF:

- index=corp_prod_imperva_waf
- index=corp_prod_apigee

## User Guides

### Development Guide

1. Create API proxy in Apigee Edge
2. Implement required policies:
   - Security
   - Traffic management
   - Data transformation
   - Logging
3. Test using Trace tool
4. Deploy using Apigee Manager

### Deployment Process

1. Develop and test in Apigee Edge
2. Download working revision
3. Sanitize configuration:
   - Replace environment variables
   - Update virtual host settings
4. Submit deployment request
5. Approve and promote through environments

### Best Practices

- Use shared flows for common functionality
- Implement consistent error handling
- Follow naming conventions
- Apply appropriate security policies
- Set up monitoring and alerts

## Contact Information

### Primary Support Group

- EA SB (<<EMAIL>>)

### Support Channels

- #Apigee_Support google space (<https://chat.google.com/room/AAAAuWj6KkI?cls=7>)

### Documentation Resources

#### Overview, Tools & How-Tos

- [Apigee Overview Document](https://wexinc.atlassian.net/wiki/spaces/APIGEE/overview)
- [Apigee use cases](https://wexinc.atlassian.net/wiki/spaces/CPTdev/pages/153692373003/Apigee)
- [Guidance document on limiting backend access to Apigee's Static IP addresses](https://wexinc.atlassian.net/wiki/spaces/APIGEE/pages/154149454592/How+to+restrict+backend+access+with+Apigee+static+IPs)
- [Collection of 'How to' articles](https://wexinc.atlassian.net/wiki/spaces/APIGEE/pages/153854771394/How-to+articles)
- [How to Deploy an API using ADM](https://wexinc.atlassian.net/wiki/spaces/APIGEE/pages/153853755579/How+to+deploy+an+API+using+Apigee+Deployment+Manager)
- [Demo of using ADM to deploy an API](https://drive.google.com/file/d/1HL0zdlM8S3X7XbFSVzVrdHOnOo6io20z/view?usp=sharing)
- [Cup O'Tech Imperva training Recordings](https://wexchange.wexinc.com/home/<USER>/space/4821220863246336/cup-o-tech/post/1285227209325693)
- [Apigee Status Page](https://status.apigee.com/)

#### Policy Documents

- [Vendor Policy Reference Overview](https://docs.apigee.com/api-platform/reference/policies/reference-overview-policy)
- [List of policies that can transform requests/responses](https://docs.apigee.com/api-platform/reference/policies/reference-overview-policy )
- [Spike Arrest Policy Vendor document](https://docs.apigee.com/api-platform/reference/policies/spike-arrest-policy)
- [Access Control Policy Vendor document](https://docs.apigee.com/api-platform/reference/policies/access-control-policy)

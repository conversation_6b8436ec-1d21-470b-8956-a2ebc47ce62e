<!-- Parent: Practices -->
<!-- Parent: Design Practices -->
<!-- Title: RFC-399 Production Readiness Checklist -->

# Production Readiness Checklist

```text
Author: <PERSON>
Title: Production Readiness Checklist
Publish Date: 2025-01-08
Category: Practices
Subtype: Design
```

This document provides a comprehensive overview of the Production Readiness Checklist, including its purpose and how to use it effectively.

## Purpose

The Production Readiness Checklist is essential for a smooth transition from development to production, minimizing risks and improving the user experience. It ensures that product, development, and operational teams address critical areas such as code quality, testing, security, performance, scalability, and operational readiness.

This living document, used at both the product and epic level, should be initiated as soon as design/development begins and continuously updated throughout development and testing, concluding before deployment to production.

## Checklist Overview

The Production Readiness Checklist is comprised of the following components:

### Code Quality & Deployment

_Were code reviews conducted and is the project utilizing CI/CD?_

This section ensures all code changes have undergone thorough reviews, and the CI/CD pipelines effectively automate builds, tests, and deployments.

### Performance & Scalability

_Was the solution thoroughly tested for performance, including load, stress, and scalability?_

This section focuses on ensuring that the system meets performance requirements and can scale to handle varying loads.

### Security

_Was a security architecture review conducted? How is sensitive data secured during storage and transmission?_

This section ensures that all necessary security reviews, scans, and remediations have been conducted and that data is encrypted as required.

### Observability

_Are standard enterprise monitoring tools in use? Does logging adhere to established standards? What are the escalation paths for alerts?_

This section ensures that monitoring, logging, and alerting practices are in place and integrated with enterprise standards.

### Reliability

_Are reliability targets (SLAs, SLOs, RTO/RPO, MTTR/MTBF) defined? Does the solution meet high availability requirements? Are there recovery plans in place? How reliable are downstream dependencies?_

This section focuses on ensuring the system is reliable, can recover from failures, and meets defined reliability targets.

### Infrastructure & Environment

_Is Infrastructure as Code (IaC) used for managing and tracking changes across environments? Are environments properly segregated? Are all critical infrastructure components monitored?_

This section ensures environments are managed using best practices, such as IaC, and that critical components are monitored effectively.

### Operational Readiness

_Is there an operational runbook for maintenance and incident response? What are the on-call and escalation procedures? Is there a post-mortem process for analyzing incidents?_

This section ensures operational readiness through clear documentation and processes for maintaining and troubleshooting the system.

### Regulatory & Compliance Checks

_Is the data retention policy adhered to? Are data privacy concerns addressed? Have UI components been reviewed for accessibility? Is the necessary licensing in place and is there a clear lifecycle management plan?_

This section ensures compliance with organizational and legal requirements, including data retention, privacy, accessibility, and licensing.

### User Training & Documentation

_Is user documentation available, including guides for system functionality and features? Are APIs documented for self-service consumption? Is there a channel for user feedback and support?_

This section ensures users and support teams are equipped with the necessary documentation and resources.

## When To Use

The Production Readiness Checklist must be completed for both new product launches and feature set deployment.

### Product-Level PRC

A product-level checklist assesses the entire product before release, ensuring a successful launch. It focuses on overall system stability, security, and operational readiness.

### Epic-Level PRC

An epic-level checklist focuses on a specific set of features within a product to ensure they integrate seamlessly. It addresses functionality, performance, and reliability, and should be completed before major deployments.

## Flow

The following is the expected workflow for the Production Readiness Checklist:

1. **Initiation:** A new checklist is created and shared with stakeholders (engineering, product owners, SRE), setting expectations for the production release.

2. **Ongoing Assessment:** Throughout development and testing, checklist items are reviewed and completed as information becomes available (e.g., architecture, test results, reliability targets).

3. **Pre-Deployment Review & Remediation:** Stakeholders review the checklist, address gaps, and update it accordingly.

4. **Sign-off & Deployment:** Once complete, stakeholders sign off, and the product is released.

5. **Checklist Maintenance:** The checklist must be kept updated throughout the lifecycle of a product or feature set to reflect any changes, ensuring it remains a reliable source of truth for production readiness.

## How To Use

1. **Create the Checklist:** Use the [Production Readiness Checklist Template](https://github.com/wexinc/arch-rfc-content/blob/main/templates/production-readiness-checklist.md) to initiate a new checklist.

2. **Link Responses to Supporting Documents:** Where possible, provide links to living documents such as architecture diagrams, monitoring dashboards, and Confluence pages to support checklist responses.

3. **Collaborate with Stakeholders:** Share the checklist with relevant stakeholders for their input and approval.

4. **Review Regularly:** Continuously update the checklist as development progresses to reflect the current state of readiness.

5. **Conduct Final Review:** Before deployment, ensure all checklist items are completed, reviewed, and signed off by stakeholders.

### Tips

- Ensure responses are detailed and linked to supporting documentation.
- Engage stakeholders early to ensure alignment and accountability.
- Use the checklist as a collaborative tool to identify and address gaps proactively.

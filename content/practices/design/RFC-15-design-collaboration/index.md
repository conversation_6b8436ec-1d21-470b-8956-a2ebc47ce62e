<!-- Parent: Practices -->
<!-- Parent: Design Practices -->
<!-- Title: RFC15-Design Collaboration -->

<!-- Include: macros.md -->
# Design Collaboration

:draft:

```text
Author: <PERSON>
Title: Design Collaboration
Publish Date: 2024-08-19
Category: Practices
Subtype: Design
```

## Introduction

> <!-- Info -->
> Note
> This document has not been formally ratified yet.

This document proposes a standard design practice for collaboration when developing changes for new and existing WEX systems.
A well-defined and well-executed design collaboration practice is essential for:

- Ensuring adherence to WEX IT policies, compliance requirements, and strategic objectives
- Reducing operational overhead and risk through the use of standard tools and services
- Improving innovation velocity with common artifacts, reference architectures, design patterns, and development practices

> <!-- Info -->
> Info
> This practice MUST be followed by all teams responsible for requesting, designing, or implementing changes to WEX systems.

![Review Actors](actors-artifacts.png)

### Table of Contents

- [Primary Roles & Responsibilities](#Primary-Roles-%26-Responsibilities)
- [Artifacts](#Artifacts)
- [Process Steps](#Process-Steps)
- [Design Considerations](#Design-Considerations)
- [Technical Walkthroughs](#Technical-Walkthroughs)
- [References](#References)

## Primary Roles & Responsibilities

- `Technical Architect`: Responsible for designing changes to existing and new WEX systems.
- `Coach`: Responsible for advising a Technical Architect on a particular set of concerns during design discovery
- `Governance Body`: Responsible for evaluating designs for adherence to WEX IT policies, compliance requirements, and strategic objectives.
- `Tech Partner`: Responsible for ensuring that a high-level solution design is known, well-understood, and has completed informal design consultations before proceeding with a formal review.

> <!-- Info -->
> Note
> These roles are specific to this practice and independent of any organization, team, position, or delivery paradigm at WEX.
> The terminology is aligned with the Product Operating Model to facilitate a broader understanding and easier adoption.

## Artifacts

- `Standard`: A common tool, service, artifact, reference architecture, pattern, or practice that has been formally ratified and recommended for improved efficiency, velocity, consistency, quality, and cost management.
- `Epic`: An extensive body of work that is broken down into smaller, manageable tasks or user stories, each contributing to the overall goal.
- `High-Level Solution Design`: A comprehensive overview of the proposed solution for an Epic, detailing the architecture, components, interactions, and technologies involved. It serves as a blueprint that guides the implementation, ensuring alignment with business objectives, technical standards, and stakeholder requirements.

## Process Steps

![Formal Process](design-process.png)

### Design Consultations

Design consultations are quick, informal, and ad hoc reviews focused on a portion of a solution design. These engagements should occur early in the design phase during discovery.

> <!-- Info -->
> Info
> Design consultations are NOT a delivery gate. They are a collaborative effort for the formulation and refinement of a complete high-level design that incorporates both functional and non-functional concerns.

A `Technical Architect` MUST solve for the following considerations in a `High-Level Solution Design` for every `Epic`:

- Training for impacted teams
- Opportunities for re-use
- Impact to customers and WEX
- Design confidence
- Production readiness
- Formal governance design review

A `Technical Architect` has many coaches available to consult with during design discovery:

- `Solution Architect`: Advises on new, complex, and strategic high-level solution designs for cross-cutting concerns. They are also responsible for auditing prioritized work for compliance with WEX IT policy and shared strategic objectives.
- `Security Architect`: Advises on application, data, or network security requirements and best practices for a solution design. They are also responsible for auditing prioritized work for vulnerability detection, remediation, and prevention.
- `PaaS Architect`: Advises on the adoption and operation of the WEX Fabric golden path. Adoption barriers for the WEX Fabric ecosystem should be shared so they can be prioritized and addressed for everyone.
- `Cloud Architect`: Advises on cloud infrastructure and services, ensuring that the solution design aligns with WEX's cloud strategy and best practices.
- `Data Architect`: Advises on data modeling, database design, data integration strategies, and AI/ML data requirements. They ensure that the solution design aligns with WEX's data governance policies, data quality standards, and best practices for data management, analytics, and AI.

> <!-- Info -->
> Note
> **Tips for collaboration:**
>
> - Use tags/labels on work items to track items that have been reviewed
> - Use mentions in work item discussions and technical documentation comments
> - Use collaboration as a training opportunity
> - Avoid handoffs and large group meetings by working in the moment

#### Design Consultation Coaches

The current list of coaches available for design consultations can be found in [RFC-420 Design Consultation Contacts](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155035730254/RFC-420+Design+Consultation+Contacts).

### Formal Design Review

> <!-- Info -->
> Info
> A `Technical Architect` MUST complete design consultations with relevant coaches before requesting a Formal Governance Design Review.
>
> Teams should allocate 2 weeks for governance review in their delivery plans when a formal review is required. The duration of a review may be far less (e.g., days) if all relevant concerns have already been addressed during prior design consultations.

A `Technical Architect` MUST request review and receive approval for a solution design from iSARB before any work is implemented if the design includes:

- [New 3rd-party software](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154594017461/RFC12-Formal+Review+Process#New-Third-Party-Software)
- [Architecture changes](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154594017461/RFC12-Formal+Review+Process#Architecture-Changes)
- [Standards changes](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154594017461/RFC12-Formal+Review+Process#Standards-Changes)

> <!-- Info -->
> Info
> Refer to [RFC12-Formal Review Process](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593689766) and the [iSARB Home](https://wexchange.wexinc.com/home/<USER>

#### Tech Partner Contacts

The current list of iSARB Tech Partners for each organization can be found in [RFC-421 iSARB Tech Partners](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155036287549/RFC-421+iSARB+Tech+Partners).

## Design Considerations

![Informal Concerns](informal-concerns.png)

### Training

Training plays a pivotal role in ensuring that Integrated Engineering teams are well-equipped to handle the complexities of solution design and implementation. It helps in:

- **Skill Development**: Regular training sessions ensure that team members are up-to-date with the latest tools, technologies, and best practices.
- **Consistency**: Training promotes a consistent understanding of standards and procedures across different teams, which is crucial for maintaining quality and coherence in design.
- **Efficiency**: Well-trained teams can work more efficiently, reducing the time required for design and review processes.
- **Collaboration**: Training sessions often serve as a platform for cross-functional collaboration, allowing team members to share knowledge and experiences.
- **Compliance**: Ensuring that all team members are aware of and understand compliance requirements helps in mitigating risks associated with non-compliance.

By investing in training, WEX ensures that its Integrated Engineering teams are capable of delivering high-quality, compliant, and innovative solutions.

> <!-- Info -->
> Info
> If a team lacks the knowledge or skills to confidently deliver on a solution design, refer to [Integrated Engineering Skill Set Shift](https://wexchange.wexinc.com/home/<USER>

### Opportunities for Re-use

Leveraging standardized patterns, practices, tools, services, and reference architectures in your solution design provides many benefits:

- **Efficiency**: Leveraging existing resources reduces the time and effort required to develop new solutions, allowing teams to focus on innovation and value-added activities.
- **Consistency**: Using standardized tools and practices ensures that solutions are consistent across the organization, which simplifies maintenance and support.
- **Quality**: Established practices and tools have typically been vetted and refined over time, leading to higher quality outcomes.
- **Cost Savings**: Re-use minimizes the need for new investments in tools and training, leading to significant cost savings.
- **Knowledge Sharing**: Encouraging the use of common resources fosters a culture of knowledge sharing and collaboration, as team members can easily understand and contribute to each other's work.
- **Compliance**: Standardized practices and tools help ensure that solutions comply with organizational policies and regulatory requirements.

#### Actively Searching for Standardized Resources

Technical Architects should proactively seek out and utilize standardized patterns, practices, tools, services, and reference architectures in their solution designs. This approach ensures that designs are efficient, consistent, and aligned with organizational standards. Key actions include:

- **Research**: [Ask Finn](https://wexchange.wexinc.com/home/<USER>//wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154468680187/Paved+Roads), [Guardrails](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154468745518/Guardrails), and [Practices](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154468974934/Practices) for existing standards and best practices.
- **Consultation**: Engage with `Solution Architect` and `PaaS Architect` to identify and validate applicable standards.
- **Collaboration**: Participate in forums, working groups, and design reviews to stay informed about new and evolving standards.
- **Documentation**: Ensure that all design artifacts reference the standardized resources used.

By actively incorporating standardized resources, Technical Architects can enhance the quality, reliability, and maintainability of their designs, while also promoting a culture of continuous improvement and knowledge sharing within the organization.

> <!-- Info -->
> Info
> Teams should request a design consultation with Architecture or PaaS Engineering if they identify any potential for re-use.

#### Deviating from Standards

When a design deviates from an accepted standard, it is crucial to document the justifications for such deviations thoroughly. This ensures transparency, facilitates understanding, and provides a basis for future reference. Key points to include in the documentation are:

- **Rationale**: Clearly explain the reasons for deviating from the standard. This could include specific business needs, technical constraints, or unique project requirements that necessitate the deviation.
- **Impact Analysis**: Assess and document the potential impacts of the deviation on the project, including any risks, benefits, and trade-offs.
- **Stakeholder Approval**: Record the approval from relevant stakeholders, including any conditions or caveats associated with the deviation.
- **Mitigation Strategies**: Outline any strategies or measures that will be implemented to mitigate potential risks or negative impacts resulting from the deviation.

> <!-- Info -->
> Info
> When encountering barriers to adopting a standard, it is important to share these challenges with Architecture and PaaS Engineering. This helps in identifying common issues, fostering collaboration, and driving continuous improvement.

### Impact to Customers and WEX Support

Mitigating the impact on customers and internal support staff is a critical aspect of solution design. Ensuring minimal disruption and maintaining a high level of service quality are essential for customer satisfaction and operational efficiency.

Key considerations include:

- **Customer Experience**: Solutions should be designed to enhance the customer experience, minimizing any negative impact during implementation and operation.
- **Support Readiness**: Internal support teams must be prepared to handle new solutions, with adequate training and resources to address potential issues.
- **Risk Management**: Identifying and mitigating risks early in the design phase helps prevent disruptions and ensures a smoother rollout.
- **Communication**: Clear and timely communication with customers and support staff about changes and their implications is crucial for managing expectations and reducing confusion.
- **Feedback Loops**: Establishing mechanisms for collecting and acting on feedback from customers and support teams helps in continuous improvement and quick resolution of issues.

By focusing on these areas, WEX can deliver solutions that not only meet technical requirements but also support a positive customer experience and efficient internal operations.

> <!-- Info -->
> Info
> Teams should assess their solution designs and delivery plans for potential impact to customers and internal support staff. The Operations and SRE teams responsible for your solutions should be consulted to minimize impact and disruption.

### Design Confidence

Design confidence refers to the assurance that a high-level solution design will meet the intended requirements and perform effectively in production.

Confidence in a design can be mitigated through design consultations with technical SMEs that focus on knowledge transfer and refinement of the design for concrete gaps and issues.

> <!-- Info -->
> Info
> Teams should request a design consultation with a relevant technical SME for the area in the design where confidence is lacking.

### Production Readiness

Production readiness is a critical aspect of high-level solution designs, ensuring that solutions are secure, reliable, and maintainable in a live environment. Key considerations include:

- **Security & Compliance**: Ensuring that the solution adheres to security best practices and regulatory requirements is paramount. This includes implementing robust security measures to protect data and systems, conducting regular security assessments, and ensuring compliance with relevant laws and standards. By prioritizing security and compliance, teams can mitigate risks, protect sensitive information, and maintain the trust of customers and stakeholders.
- **Availability & Disaster Recovery**: Ensuring high availability and robust disaster recovery plans is essential for maintaining continuous service and minimizing downtime. This involves implementing redundancy, failover mechanisms, and regular backups to protect against data loss and service interruptions. By prioritizing availability and disaster recovery, teams can ensure that solutions remain operational and resilient in the face of unexpected events, thereby maintaining user trust and satisfaction.
- **Data Ingress & Egress**: Ensuring efficient and secure data ingress and egress is crucial for production readiness. Proper management of data flow into and out of the system ensures that data is accurately and securely processed, stored, and retrieved. This includes implementing robust data validation, encryption, and monitoring mechanisms to protect data integrity and confidentiality. By prioritizing data ingress and egress, teams can enhance system reliability, performance, and security, thereby supporting seamless operations and compliance with regulatory requirements.
- **Logging & APM**: Effective logging and Application Performance Monitoring (APM) are essential for maintaining production readiness. Logging provides a detailed record of system activities, which is crucial for diagnosing issues, understanding system behavior, and ensuring accountability. APM tools help monitor the performance of applications in real-time, allowing teams to detect and resolve performance bottlenecks, ensure optimal user experience, and maintain system reliability. Together, logging and APM enable proactive management of production environments, facilitating quick identification and resolution of issues, and ensuring that systems operate smoothly and efficiently.
- **Standards Adoption**: Adopting established standards is crucial for ensuring consistency, quality, and compliance in production environments. Standards provide a framework for best practices, streamline processes, and facilitate interoperability between systems. By adhering to standards, teams can reduce errors, enhance security, and ensure that solutions meet regulatory and organizational requirements. This leads to more reliable, maintainable, and scalable solutions that align with strategic objectives and industry benchmarks.

> <!-- Info -->
> Info
> Refer to [Production Readiness Checklist](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154987233491/RFC-399+Production+Readiness+Checklist) for guidance on evaluating and optimizing a solution design for production readiness.
>
> Teams should request a design consultation with Architecture, Application Security, and/or Cloud Engineering if they identify any aspect of the design that would impact production readiness.  

## Technical Walkthroughs

Technical walkthroughs are structured sessions where a solution or a component of a solution is demonstrated to stakeholders. The primary goals of these sessions are to share knowledge and gather feedback for further enhancement.

The key goals of technical walkthroughs are:

- **Knowledge Sharing**: Walkthroughs provide an opportunity for team members to gain a deeper understanding of the solution, fostering a culture of continuous learning and collaboration.
- **Feedback Collection**: By presenting the solution to a diverse group of stakeholders, teams can gather valuable insights and suggestions that can lead to future improvements.

## References

This document uses the taxonomy frameworks described in:

- [RFC13-Technical Review Taxonomy](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593689766)
- [RFC14-Technical Standards Taxonomy](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593657230)

<!-- Parent: Practices -->
<!-- Parent: Design Practices -->
<!-- Title: RFC-496 Sample CAD -->

<!-- Include: macros.md -->
# cad-template

:draft:

```text
Author: <PERSON><PERSON>wain
Title: CAD Template
Publish Date: 2025-05-07
Category: Practices
Subtype: Design
```

Candidate Architecture Designs (CAD) is a lightweight specification to provide the minimum essential information for a given Agile Use Case, Requirements, and Implementation.

- [cad-template](#cad-template)
  - [1.0 Confluence Document Control](#10-confluence-document-control)
    - [1.1 Revision History](#11-revision-history)
    - [1.2 Audience/RACI](#12-audienceraci)
  - [2.0 Conceptual Solution Overview](#20-conceptual-solution-overview)
    - [2.1 Business Description of Change \& Use Case (Required)](#21-business-description-of-change--use-case-required)
    - [2.2 Business Use Case Diagram (Required)](#22-business-use-case-diagram-required)
    - [2.3 Business Process Flow (Optional)](#23-business-process-flow-optional)
    - [2.4 User Experience (UX) Wireframe (Optional)](#24-user-experience-ux-wireframe-optional)
  - [3.0 Technology Solution Architecture (Required)](#30-technology-solution-architecture-required)
    - [3.1 High-Level Solution Context (Required)](#31-high-level-solution-context-required)
    - [3.2 Patterns Adopted (Required)](#32-patterns-adopted-required)
      - [References](#references)
    - [3.3 Solution Runtime (Required)](#33-solution-runtime-required)
      - [Non-Functional Requirements](#non-functional-requirements)
      - [Monitoring Considerations](#monitoring-considerations)
    - [3.4 Sequence Diagram (Optional)](#34-sequence-diagram-optional)
    - [3.5 Conceptual Data Flow Diagram (Optional)](#35-conceptual-data-flow-diagram-optional)
  - [4.0 Technology Security Architecture (Required)](#40-technology-security-architecture-required)
  - [5.0 Risks, Assumptions, Issues, and Dependencies (Required)](#50-risks-assumptions-issues-and-dependencies-required)
  - [6.0 Solution Measurement \& KPIs](#60-solution-measurement--kpis)
    - [Key Measurement Areas (Vectors)](#key-measurement-areas-vectors)

---

## 1.0 Confluence Document Control

- **Line of Business:** *Insert Line of Business*
- **Product Name:** *Insert Product Name*
- **Reference links:**
  - (Epics/Features/Stories): [*Insert Link*](https://example.com)
  - (Reference Architectures): [*Insert Link*](https://example.com)
  - (Related Design Efforts): [*Insert Link*](https://example.com)

### 1.1 Revision History

| Rev | Date | Detailed Description of Revision | Modified By |
| --- | ---- | -------------------------------- | ----------- |
| 1.0 |      | Initial Draft                    | *Your Name* |
|     |      |                                  |             |

### 1.2 Audience/RACI

This section outlines the Responsible, Accountable, Consulted, and Informed stakeholders.

| Name          | Approver Role                        | Sign-Off Date | Required | Responsible | Accountable | Consulted | Informed |
| ------------- | ------------------------------------ | ------------- | -------- | ----------- | ----------- | --------- | -------- |
| [~Name] | Product Owner | | I | | | | I |
| [~Name] | Security Architect | | X | | | C | |
| | Infrastructure & Cloud Architect | | | | | C | |
| | Solution Architect | | R | R | | | |
| [~Name] | Data Architect                       |               | C        |             |             | C         |          |
|               | Chief Technology                     |               | C        |             |             | C         |          |
|               | Solution Delivery Lead               |               | X        |             | A           |           |          |
|               | Application Development Lead         |               | X        | R           |             |           |          |
|               | Technology Product Owner           |               |          |             |             | C         |          |

---

## 2.0 Conceptual Solution Overview

### 2.1 Business Description of Change & Use Case (Required)

- **Business Objective:** *What does success look like from a business and context model perspective*
- **Business Problem Solved:** *What are the specific problems that are being encountered by the business that are being addressed*

### 2.2 Business Use Case Diagram (Required)

Insert a Use Case Diagram or any written context, wireframe, or visual that describes the system Actors, Relationship of the actors and the use cases.

**Sample Payment Processing Use Case Diagram:**

```mermaid
graph TD
A[Customer] -->|Makes Payment| B[Merchant]
B -->|Processes Payment| C[Payment Processor]
C -->|Validates Payment| D[Bank]
D -->|Confirms Payment| C
C -->|Notifies| B
B -->|Issues Receipt| A
```

*Note: This is a sample diagram that represents typical payment processing flow. Adapt it to your specific use case requirements.*

### 2.3 Business Process Flow (Optional)

Business process diagrams, to include swim-lane diagrams can be useful for Agile teams to understand hand-offs in the process.

**Sample Payment Authorization Process Flow:**

```mermaid
flowchart TD
    subgraph Customer
    A[Customer] --> B[Initiates Payment]
    end
    
    subgraph Merchant
    B --> C[Merchant System]
    C --> D{Valid Payment Info?}
    D -->|No| E[Request Correction]
    E --> B
    D -->|Yes| F[Send to Payment Gateway]
    end
    
    subgraph Payment_Processor
    F --> G[Payment Gateway]
    G --> H[Process Payment]
    H --> I{Authorization?}
    I -->|Declined| J[Decline Message]
    I -->|Approved| K[Approval Message]
    end
    
    subgraph Bank
    H --> L[Bank Authorization]
    L --> H
    end
    
    J --> C
    K --> C
    C --> M{Payment Approved?}
    M -->|Yes| N[Fulfill Order]
    M -->|No| O[Cancel Order]
    
    classDef customer fill:#e6f7ff,stroke:#0066cc;
    classDef merchant fill:#e6ffe6,stroke:#009900;
    classDef processor fill:#fff5e6,stroke:#ff9900;
    classDef bank fill:#ffe6e6,stroke:#cc0000;
    
    class A,B customer;
    class C,D,E,F,M,N,O merchant;
    class G,H,I,J,K processor;
    class L bank;
```

*Note: This sample diagram shows the payment authorization flow between different participants. Adapt it to reflect your specific business process, including the appropriate actors and steps relevant to your use case.*

### 2.4 User Experience (UX) Wireframe (Optional)

Include links to or images of the User Interface and any other presentation that the end user will require that is critical to the application and business objectives.

**Sample Payment Form Wireframe:**

```mermaid
graph TD
    subgraph Payment_Form_Wireframe
        title[<b>Payment Form</b>]
        
        subgraph Customer_Info
            name[Name on Card]
            email[Email Address]
            phone[Phone Number]
        end
        
        subgraph Payment_Details
            cardnum[Card Number]
            expiry[Expiration Date]
            cvv[CVV]
            billing[Billing Address]
        end
        
        subgraph Order_Summary
            amount[Order Amount: $99.99]
            tax[Tax: $8.00]
            shipping[Shipping: $5.99]
            total[<b>Total: $113.98</b>]
        end
        
        subgraph Actions
            save[Save Information]
            pay[Pay Now]
            cancel[Cancel]
        end
        
        Customer_Info --- Payment_Details
        Payment_Details --- Order_Summary
        Order_Summary --- Actions
    end
    
    classDef formTitle fill:#f9f9f9,stroke:#333,stroke-width:2px;
    classDef formSection fill:#f5f5f5,stroke:#ccc;
    classDef formField fill:white,stroke:#ddd;
    classDef formButton fill:#4CAF50,stroke:#388E3C,color:white;
    classDef cancelButton fill:#f44336,stroke:#d32f2f,color:white;
    classDef summaryHighlight fill:#e3f2fd,stroke:#bbdefb,font-weight:bold;
    
    class title formTitle;
    class Customer_Info,Payment_Details,Order_Summary formSection;
    class name,email,phone,cardnum,expiry,cvv,billing formField;
    class pay formButton;
    class cancel cancelButton;
    class total summaryHighlight;
```

*Note: This is a sample wireframe diagram for a payment form. Mermaid has limitations for detailed UI mockups, so consider this a structural representation. For detailed wireframes, include links to designs created in specialized tools like Figma, Sketch, or Adobe XD.*

---

## 3.0 Technology Solution Architecture (Required)

### 3.1 High-Level Solution Context (Required)

Insert a short narrative and system architecture that outlines the component change in the solution. Use links to reference other system architectures.

**Sample Payment Processing System Context:**

```mermaid
graph TD
    classDef external fill:#D4F1F9,stroke:#05AFF2,color:#05445E
    classDef system fill:#FFE5B4,stroke:#FFA500,color:#8B4513
    classDef database fill:#E8F8F5,stroke:#117A65,color:#1E8449
    classDef service fill:#F9EBEA,stroke:#C0392B,color:#922B21
    
    U[Customer] ---|Uses| MB[Mobile App/Website]
    MB ---|Makes Payment Request| PS[Payment System]
    PS ---|Validates Customer| ID[Identity Service]
    PS ---|Processes Transaction| PG[Payment Gateway]
    PS ---|Records Transaction| DB[(Transaction Database)]
    PS ---|Sends Notifications| NS[Notification Service]
    PG ---|Validates with| B[Banking Network]
    NS ---|Sends SMS| SMS[SMS Provider]
    NS ---|Sends Email| EM[Email Service]
    
    subgraph "External Systems"
        B
        SMS
        EM
    end
    
    subgraph "WEX Core Systems"
        PS
        ID
        PG
        DB
        NS
    end
    
    class U,MB,B,SMS,EM external
    class PS,PG,NS system
    class DB database
    class ID service
```

This high-level context diagram illustrates a payment processing system where:

1. **Customers** interact with the system through a mobile app or website
2. The **Payment System** is the central component handling payment requests
3. It interfaces with internal services (Identity, Notification) and data stores
4. External connections include banking networks and communication providers

The proposed solution enhances the existing architecture by [describe specific enhancements or changes to this architecture that are relevant to your project].

### 3.2 Patterns Adopted (Required)

Outline any Industry or WEX patterns and associate application Tier for the solution: UX, DEVOPS, Application, Service, Data, Integration/Event, Cloud, Security, other.

#### References

- Solution Architecture Outcomes: [Architecture Outcomes](https://wexinc.atlassian.net/wiki/pages/createpage.action?spaceKey=PTA&title=Architecture%20Outcomes)
- Security Standards and Guidelines: [Security Standards](https://wexinc.atlassian.net/wiki/spaces/ESA/pages/*********/Security+Standards) [Guidelines](https://wexinc.atlassian.net/wiki/spaces/ESA/pages/*********/Guidelines)
- WEX Fabric: [WEX Fabric](https://example.com/wex-fabric)
- Eventing: [Eventing Platform](https://sites.google.com/wexinc.com/eventingplatform/home)
- Reuse Decision Tree: [Reuse Patterns & Decision Framework](https://example.com/reuse-patterns)
- DEVOPS: [Architecture - DevOps](https://example.com/devops)
- TECH Radar: [TECH RADAR](https://example.com/tech-radar) [WEX Tech Radar](https://turbo-barnacle-6997l4m.pages.github.io/product/2023-02/)

### 3.3 Solution Runtime (Required)

#### Non-Functional Requirements

Unlike functional requirements, non-functional requirements (NFRs) define how a system should perform under various conditions. Business stakeholders often express these needs as, ‘It needs to be fast, always available, and adaptable to changing market conditions’. These statements correspond to key NFRs, such as Performance Efficiency, Reliability, and Flexibility. The following section outlines these runtime requirements and how the proposed solution addresses them.

| Category               | Requirements                                                                  | Solution Approach                                           |
| :--------------------- | :---------------------------------------------------------------------------- | :---------------------------------------------------------- |
| **Reliability**        | Ability to perform under certain conditions over a period of time (i.e. availability percentage, RTO/RPO, MTTR/MTBF) | *Describe how the solution addresses Reliability*           |
| **Flexibility**        | Capability to adapt to changes in solution requirements (i.e. user increase, higher data volumes) | *Describe how the solution addresses Flexibility*           |
| **Performance Efficiency** | Response time (seconds), completion windows (batch), throughput (operations/second) | *Describe how the solution addresses Performance Efficiency* |

#### Monitoring Considerations

Proactive monitoring is essential for ensuring compliance with non-functional requirements. It involves continuous oversight of all components and their interactions to preemptively identify and address potential issues. For the proposed solution, detail the specific monitoring considerations and how this approach effectively addresses them.

| Component   | Considerations                                          | Solution Approach                                    |
| :---------- | :------------------------------------------------------ | :--------------------------------------------------- |
| *Component* | *Specific monitoring aspects for this component*        | *Describe how monitoring is implemented for this component* |
| *Component* | *Specific monitoring aspects for this component*        | *Describe how monitoring is implemented for this component* |
| *Add More Rows as Needed* |                                         |                                                      |

### 3.4 Sequence Diagram (Optional)

Articulate detailed sequence flows.

**Sample Payment Processing Sequence:**

```mermaid
sequenceDiagram
    participant C as Customer
    participant M as Merchant
    participant P as Payment System
    participant G as Payment Gateway
    participant B as Bank
    
    C->>M: Initiates Payment
    M->>P: Submit Payment Request
    P->>P: Validate Request
    
    alt Invalid Request
        P-->>M: Return Validation Error
        M-->>C: Show Error Message
    else Valid Request
        P->>G: Forward to Payment Gateway
        G->>B: Request Authorization
        
        alt Insufficient Funds
            B-->>G: Decline (Insufficient Funds)
            G-->>P: Return Declined Status
            P-->>M: Payment Declined
            M-->>C: Show Declined Message
        else Suspected Fraud
            B-->>G: Decline (Fraud Alert)
            G-->>P: Return Declined Status
            P-->>M: Payment Declined
            M-->>C: Show Declined Message
        else Payment Approved
            B-->>G: Approve Transaction
            G-->>P: Return Approval
            
            par Notification
                P->>C: Send Payment Confirmation Email
            and Transaction Recording
                P->>P: Record Transaction Details
            end
            
            P-->>M: Payment Approved
            M-->>C: Show Success & Receipt
        end
    end
    
    Note over P,G: All communications encrypted with TLS 1.2+
    Note over G,B: Tokenized card data used for enhanced security
```

*Note: This sequence diagram illustrates the detailed flow of a payment processing transaction, including validation steps, error handling paths, and successful transaction processing. The diagram also highlights security considerations with encryption and tokenization notes.*

---

### 3.5 Conceptual Data Flow Diagram (Optional)

Data flows help map out the flow of information for any process/system, to analyze an existing system or model a new one.

**Sample Payment System Data Flow:**

```mermaid
flowchart LR
    classDef userSystem fill:#e1f5fe,stroke:#0288d1
    classDef coreSystem fill:#fff3e0,stroke:#ff9800
    classDef dataStore fill:#e8f5e9,stroke:#4caf50
    classDef externalSystem fill:#fce4ec,stroke:#e91e63

    %% Data sources and consumers
    U[Customer Data]:::userSystem
    M[Merchant Portal]:::userSystem
    PS[Payment System]:::coreSystem
    TX[(Transaction DB)]:::dataStore
    REP[(Reporting DB)]:::dataStore
    ARC[(Archive)]:::dataStore
    FR[Fraud Detection]:::coreSystem
    AN[Analytics Platform]:::coreSystem
    BI[Business Intelligence]:::coreSystem
    EXT[External Partners]:::externalSystem

    %% Data flows with labels
    U -->|PII, Payment Info| M
    M -->|Tokenized Data| PS
    PS -->|Transaction Records| TX
    TX -->|Daily Batch| REP
    TX -->|90+ Days Old| ARC
    PS -->|Suspicious Activity| FR
    TX -->|Raw Transactions| AN
    REP -->|Aggregated Data| BI
    BI -->|Reports| M
    AN -->|Anonymized Data| EXT
    
    %% Subgraphs for organization
    subgraph "Customer Touchpoints"
        U
        M
    end
    
    subgraph "Core Processing"
        PS
        FR
    end
    
    subgraph "Data Storage"
        TX
        REP
        ARC
    end
    
    subgraph "Analytics & Reporting"
        AN
        BI
    end
```

*Note: This conceptual data flow diagram illustrates how data moves through a payment processing system, from customer input through processing, storage, and analytics. It highlights key data transformations (like tokenization) and shows both operational and analytical data paths.*

---

## 4.0 Technology Security Architecture (Required)

Articulate detailed security considerations and designs. Include a Security or Infrastructure diagram if possible to outline firewalls, data flows and associated encryption, other related data encryption in transit and at rest.

Reference Security Architecture: [Global Information Security Architecture](https://example.com/security-architecture)
Reference the Global Information Classification and Handling Policy: [Information Classification Policy](https://wexinc.policytech.com/dotNet/documents/?docid=2268)

| Security & Technology Area                                          | Answer                                                                                                                               |
| :------------------------------------------------------------------ | :----------------------------------------------------------------------------------------------------------------------------------- |
| 1. Where is the new architecture going to live?                     | *Answer: Network location*                                                                                                    |
|    Is this network existing or a new one?                           | *Answer: Existing/New*                                                                                                        |
|    What will it connect to? Other WEX networks? External Services?  | *Answer: Connectivity details (e.g., Other WEX networks, External Services like Aiven)*                                        |
|    What is the protocol and port?                                   | *Answer: Protocol and Port details*                                                                                             |
| 2. Who or what services are going to consume/connect to this architecture? | *Answer: List consuming services/users*                                                                                     |
| 3. Any sensitive data processed, transmitted, or stored?            | *Answer: Yes/No*                                                                                                                |
|    *Defined as Confidential data in the Wex Data Classification and Handling Policy:* [https://wexinc.policytech.com/dotNet/documents/?docid=2268] Examples include SSN, DOB, credit card PAN, or Protected Health Information (PHI). | *If Yes, describe the sensitive data*                                                                                                |
|    Are there any users or applications accessing sensitive data that were not previously? | *Answer: Yes/No, and explain*                                                                                                 |
| 4. Please link to any existing documentation describing the architecture (if not included in the CAD). Please indicate what's new and what's pre-existing. | *Answer: Link to existing docs, clarify new vs. pre-existing components*                                                        |
| 5. Identify authentication points and how authentication occurs.    | *Answer: Describe authentication mechanisms and points*                                                                         |
| 6. Who will own it operationally? Is that team represented in the approval? | *Answer: Operational owner team. State if they are in the RACI/Approvals.*                                                      |
|    How is logging and monitoring (operational and security) handled? Does this follow WEX standards or are new patterns being introduced? | *Answer: Describe logging and monitoring approach. Note adherence to WEX standards or new patterns.*                            |

---

## 5.0 Risks, Assumptions, Issues, and Dependencies (Required)

| Question | Answer |
|----------|--------|
| **Business Risks** | *What Business Risks may be encountered through this effort* |
| **Technology Risks** | *What Technology Risks may be encountered through this effort* |
| **Assumptions** | *Key Assumptions that are critical to the application design.* |
| **Issues** | *What issues will you encounter that would be an impediment to the success of your design* |
| **Dependencies** | *Key Dependencies that are critical to the application design.* |

---

## 6.0 Solution Measurement & KPIs

This section outlines how the design choices within this CAD contribute to improvements across key measurement areas.

To ensure alignment and visibility, we will be implementing the following process:

- Identify and flag any work or proposed solutions that are expected to influence one or more of our five core vectors.
- Evaluate the anticipated impact of each flagged initiative on the relevant vectors.
- Review these flagged items and their impact assessments with leadership and other relevant stakeholders to ensure accuracy and agreement.
- Document all identified initiatives and their assessed impact on the vectors within Jira for tracking.
- Generate regular reports that summarize ongoing work and its effect on each vector, providing leadership with clear visibility into our progress.
  
### Key Measurement Areas (Vectors)

Technology Transformation Measurement focus on defining and implementing KPIs in key areas (Reliability, Security, AI, SaaS Maturity, Product Innovation Velocity), aligning strategic vectors and providing actionable insights for leadership.

- [Reliability Vector](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155172602241/Reliability+Vector)
- [Product Security](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155173978186/Product+Security)
- [Product Innovation Velocity (PiV)](https://wexinc.atlassian.net/wiki/spaces/TISO/pages/154810155024/Tech+Transformation+KPIs)
- [AI Maturity](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155174338612/AI+Maturity+Vector)
- [SaaS Maturity](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154867007780/RFC-351+SaaS+Maturity+Vector)
<!--  -->
---

<!-- Parent: Practices -->
<!-- Parent: Delivery Practices -->
<!-- Title: RFC122-Feature Flags -->
#

```text
Author: <PERSON><PERSON>
Publish Date: 2024-08-30
Category: Practices
Subtype: Delivery
```

## Overview

Feature Flags are a lightweight method to incrementally release changes to customers and end users. Flags are usually enabled via a deployment automation (i.e. pipeline) or ad hoc as part of a release or operational management procedure. They facilitate de-coupling between deployment (shipping changes) and release (enabling features to users).

There are two types of feature flags that can be used: temporary and permanent.

A temporary feature flag is used to rollout changes as part of the delivery cycle. They are typically implemented as a guard against breaking changes in an application. Temporary feature flags and old code paths should be removed as part of a feature before it can be considered “done”.

A permanent feature flag is used to enable or disable a cohesive feature area for users. These flags may be used to make an optional component available to users or to control feature access during a maintenance window.

## Reference Material

[Microsoft - Feature Flags](https://docs.microsoft.com/en-us/dotnet/architecture/cloud-native/feature-flags)
[Feature Flags - Theory](https://wexinc.atlassian.net/wiki/spaces/WH/pages/153302665149)
[LaunchDarkly - Best practices](https://launchdarkly.com/blog/best-practices-short-term-permanent-flags/)
[Fowler - Feature Toggles (aka Feature Flags)](https://martinfowler.com/articles/feature-toggles.html)
[LaunchDarkly - Architectural Migrations](https://docs.launchdarkly.com/guides/best-practices/infrastructure-migration)
[LaunchDarkly - User Experience Entitlements](https://docs.launchdarkly.com/guides/flags/entitlements/?q=best+pr#best-practices)

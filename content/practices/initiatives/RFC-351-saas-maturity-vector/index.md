<!-- Parent: Practices -->
<!-- Parent: Practice Initiatives -->
<!-- Title: RFC-351 SaaS Maturity Vector -->

<!-- Include: macros.md -->
# SaaS Maturity Vector

```text
Author: <PERSON>
Title: SaaS Maturity Vector
Publish Date: 2024-11-07
Category: Practices
Subtype: Initiatives
```

## Executive Summary

The [Tech Transformation Measurement](https://wexchange.wexinc.com/home/<USER>"spider chart" format for clear and actionable insights. Responses will contribute to this spider chart's SaaS Maturity vector.

The purpose of the SaaS Maturity Assessment is to measure a product's potential for improvements across multiple value streams. It does not judge whether design choices are good or bad, as these decisions are influenced by various factors beyond architectural maturity.

The SaaS Maturity Assessment will be completed by ring-fenced Solution Architects from the Architecture organization for their respective areas of responsibility. These architects should consult with technical leads for the product to ensure accurate assessments.

**Estimated completion time:** 20-30 minutes per product assessment

**Contact information for questions:**

- <PERSON> (Architecture Evolution)
- <PERSON> (Architecture)

**Published Results:**

- [2024 Q4 Results](https://docs.google.com/spreadsheets/d/1z7mcRUZA8X_VsLCSDdjGKrej1jdm5qr7h_0SykwWS7I)

### Table of Contents

:toc:

## Assessment Methodology

### Assessment Granularity

Assessments are conducted at either a product-level or subsystem-level, depending on logical system boundaries. For large products with multiple distinct components or services, separate assessments may be performed for each significant subsystem to provide more accurate insights.

### Scoring Methodology

- Each question is scored on a 1-5 scale, with 1 representing the lowest value-based outcome and 5 representing the highest value-based outcome.
- Questions within each vector component (API Maturity, Decoupled Architecture, Canonical System Adoption) are averaged together to produce a vector component score.
- The vector component scores together form the dimensions of the SaaS Maturity Vector.

> <!-- Info -->
> Info
> **Improvements Under Consideration:**
>
> - **AI maturity assessment** based on compliance with REST API Design Standard and HATEOAS using OpenAPI json specifications
> - Using observability service maps to produce **afferent and efferent decoupling metrics** and calculate instability score
> - Adding a **Code Health vector component** that uses tooling to calculate a code health score

```mermaid
graph TD
  subgraph "Vector Components"
    API["API Maturity<br>Questions API.1-API.4<br>(Avg Score)"]
    DA["Decoupled Architecture<br>Questions DA.1-DA.4<br>(Avg Score)"]
    CS["Canonical System Adoption<br>Questions CS.1-CS.3<br>(Avg Score)"]
  end

  subgraph "Scoring Process"
    Q["Individual Question Scores (1-5)"] --> |Average| VC["Vector Component Scores"]
    VC --> |Form dimensions of| SMV["SaaS Maturity Vector"]
  end

  VC --> API
  VC --> DA
  VC --> CS

  API --> |Dimension 1| Spider["Spider Chart<br>Visualization"]
  DA --> |Dimension 2| Spider
  CS --> |Dimension 3| Spider

  classDef process fill:#f9f,stroke:#333,stroke-width:2px;
  classDef component fill:#bbf,stroke:#333,stroke-width:1px;
  classDef visual fill:#bfb,stroke:#333,stroke-width:1px;
  
  class Q,VC,SMV process;
  class API,DA,CS component;
  class Spider visual;
```

### Reporting Approach

When a product consists of multiple subsystems that have been assessed separately:

- Product scores may be calculated using the average score for each vector component across all reported subsystems for the product.
- This approach ensures that areas requiring the most improvement receive appropriate attention and resources.

## Visual Response Scale

To help standardize responses and provide clarity on the progression of maturity, please use the following scale when answering assessment questions:

```mermaid
graph LR
  classDef level1 fill:#FF8080,stroke:#333,stroke-width:1px;
  classDef level2 fill:#FFD580,stroke:#333,stroke-width:1px;
  classDef level3 fill:#FDFF8F,stroke:#333,stroke-width:1px;
  classDef level4 fill:#C1FF9B,stroke:#333,stroke-width:1px;
  classDef level5 fill:#9EFFFF,stroke:#333,stroke-width:1px;
  
  L1[Level 1: INITIAL<br>Ad-hoc processes<br>High manual effort<br>Limited documentation]
  L2[Level 2: DEVELOPING<br>Basic processes defined<br>Significant manual effort<br>Some documentation]
  L3[Level 3: DEFINED<br>Standardized processes<br>Moderate manual effort<br>Adequate documentation]
  L4[Level 4: MANAGED<br>Optimized processes<br>Minimal manual effort<br>Comprehensive documentation]
  L5[Level 5: OPTIMIZED<br>Automated processes<br>Self-service capabilities<br>Exemplary documentation]
  
  L1 --> L2 --> L3 --> L4 --> L5
  
  class L1 level1;
  class L2 level2;
  class L3 level3;
  class L4 level4;
  class L5 level5;
```

## Vector Reference

This document covers the SaaS Maturity Vector.  The full list of vectors has been included below for reference:

- **SaaS Maturity**
- [Product Innovation Velocity (ProdIV)](https://wexinc.atlassian.net/wiki/spaces/TISO/pages/154810155024/Tech+Transformation+KPIs)
- [AI Maturity](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155174338612/AI+Maturity)
- [Product Security](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155173978186/Product+Security)
- [Reliability](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155172602241/Reability)

## Products

The following products are prioritized for assessment and included in reporting to the Technical Leadership Team:

- Corporate Payments (Payments)
- eNett (Payments)
- NAM (Mobility)
- OTR (Mobility)
- CDH (Benefits)
- Cobra (Benefits)
- BenAdmin (Benefits)

## SaaS Maturity Vector components

Vector components are categorized value streams associated with the Vector.  Each question is associated with one vector component.  The vector components are used as dimensions in the SaaS Maturity Vector spider chart.

The SaaS maturity vector components are:

- **API Maturity**
  - Assess the level of API maturity within the product. This includes evaluating how seamlessly the product **integrates with other systems**, the ease of **onboarding new users or clients**, and the efficiency of **operational support processes**.
- **Decoupled Architecture**
  - Assess the degree to which the product's architecture allows for independent development, deployment, and scaling of its components. This includes evaluating **the team's autonomy** in making changes without impacting other systems, the ability to **deploy and release** applications independently, and the effectiveness of the team's **testing and operational processes**.
- **Canonical System Adoption**
  - Assess the extent to which the product leverages standardized, reusable services and artifacts that support core business processes. This includes evaluating the team's ability to **adopt existing technologies, tools, and services** efficiently, and the impact of these adoptions on the product's architecture and delivery timelines.

## Survey

Please complete the survey to provide your input on the SaaS Maturity Vector components. Your responses will be incorporated into the Vector Maturity spider charts according to the line of business and product.

The most recent assessment form is available here:

- [Q1 2025](https://docs.google.com/forms/d/e/1FAIpQLScSLbw7b9Q2KE9xzEhj9Q1XEpJqOPxmBo6oDmqQsx4ul9AOyQ/viewform?usp=header)

> <!-- Info -->
> Info
> **Context to guide your responses:**
>
> - Questions are based on value propositions to Product Teams, Customers, and other WEX teams (e.g., AI, Data, Finance, Digital)
> - When answering about "Product Teams," consider all teams ring-fenced to the product who are accountable for its development and operations (Engineering, SRE, DBAs, Release Management, etc.)
> - Base your responses on the worst outcome experienced across all applications within the Product to identify areas with the most potential for improvement

## Questions

### API Maturity

> This section evaluates the level of API maturity within the product. This includes evaluating how seamlessly the product integrates with other systems, the ease of onboarding new users or clients, and the efficiency of operational support processes.
>
> In this context, "API" refers to all types of interfaces (web services, file-based integrations, event streams, etc.).

#### [API.1] Is there any knowledge sharing, training, or other required action needed by your team for a user or client to be setup and begin using the product's APIs?

1. Product Teams spend significant effort onboarding users and clients without any available documentation
2. Product Teams spend significant effort (e.g. deployment tasks) onboarding users and clients with documentation available by request
3. Product Teams spend moderate effort (e.g. consultation) onboarding users and clients with documentation available by request
4. Product Teams spend minimal effort (e.g. configuration) onboarding users and clients with documentation published and accessible
5. No effort is needed from Product Teams for user and client onboarding

> <!-- Info -->
> Info
> This question is measuring how well customers, vendors, and other WEX teams can onboard to the product through configuration & automation without assistance from an engineering team.
>
> **Example:** In a mature Payments product, adding a new merchant account would be fully self-service through a configuration portal with clear documentation, rather than requiring engineering teams to manually configure database entries, update routing tables, or deploy new instances. Teams from other parts of WEX needing to integrate with the payment processing system would similarly be able to onboard through documentation and configuration rather than requiring developer support.

#### [API.2] What is the average REST API Design Standard AI Compliance score for the Product's principle REST APIs?

> **Note:** This question is updated for the 2025 Q2 assessment to address redundancy in value measurement and subjectivity in scoring.

1. More than 5 violations of MUST requirements
2. Between 1 and 5 violations of MUST requirements
3. No violations of MUST requirements, Less than 5 violations of SHOULD requirements with most exceptions documented
4. No violations of MUST requirements, No undocumented violations of SHOULD requirements, advanced capabilities are not leveraged
5. No violations of MUST requirements, No undocumented violations of SHOULD requirements, advanced capabilities are leveraged

> <!-- Info -->
> Info
> This question is using AI to scan REST API code repositories and OpenAPI definitions for compliance with [WEX REST API Design Standards](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155342569626/RFC-489+REST+API+Design+Standard).
>
> **Self-Assessment:** Teams can self-assess their code repositories using [RFC-491 REST API Standards Audit using GitHub Copilot](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155370061837/RFC-491+REST+API+Standards+Audit+using+GitHub+Copilot).  Compliance Reports include detailed findings and recommendations for remediation and tracking.

#### [API.3] Are operational support teams able to quickly identify issue root cause and escalate to your team directly for remediation if necessary?

1. The Product Teams often struggle with identifying root cause independently
2. The Product Teams can identify root cause, but lack support documentation or the capacity to do it without impacting delivery commitments
3. The Product Teams have operational support documentation for the products published and available, but operational support roles often require assistance or lack a consistent path for escalation
4. Operational support roles are able to remediate issues related to scale & load and can escalate directly to the engineering teams, but are not actively consulted during solution design
5. Operational support roles are able to remediate most operational issues, can escalate directly to the engineering teams, and are actively engaged during solution design

> <!-- Info -->
> Info
> This question is measuring how efficiently the team collaborates with operational teams and how responsive the product is to service disruptions.
>
> **Example:** In a mature Payments system, operational support teams would have access to detailed monitoring dashboards and runbooks that allow them to diagnose common payment processing issues (like failed transactions or gateway timeouts) without involving the engineering team. When a new issue is detected, there would be a clear escalation path directly to the appropriate engineering team with all relevant diagnostic information automatically collected.

#### [API.4] What is the risk & impact to users and clients of your applications during a typical planned deployment?

1. Users & clients experience significant or complete loss of functionality during normal deployment procedures
2. Users & clients experience partial or limited loss of functionality during normal deployment procedures
3. Users & clients do not receive errors, but do receive a maintenance page or response during deployments
4. Users & clients may need to take a trivial action (e.x. re-authentication), but otherwise experience no loss of functionality
5. Users & clients experience no noticeable impact during planned deployments

> <!-- Info -->
> Info
> This question is measuring the potential impact to users and clients during typical planned deployments, not catastrophic failures due to human error or malicious actions. We're evaluating whether the system architecture supports non-disruptive updates.
>
> **Example:** In a mature Payments system, deploying an update to the transaction processing service would use techniques like blue-green deployments or canary releases to ensure zero downtime. For instance, a new version of the authorization service would be deployed alongside the existing one, and transactions would be gradually routed to the new version after validation, with no customer impact during the transition process.

### Decoupled Architecture

> This section evaluates the degree to which the product's architecture allows for independent development, deployment, and scaling of its components. This includes evaluating the team's autonomy in making changes without impacting other systems, the ability to deploy and release applications independently, and the effectiveness of the team's testing and operational processes.

#### [DA.1] To what degree do your systems' interfaces allow you to make implementation changes inside the system without communicating with others?

1. The Product Teams are dependent on many code or design reviews from teams outside the product team inside the system boundary. Clients and Users may experience significant outage. Support resources are significantly impacted.
2. The Product Teams are dependent on some code or design reviews from teams outside the product team inside the system boundary. Clients and Users may experience moderate delay or outage. Support resources are moderately impacted.
3. The Product Teams are dependent on some code or design reviews from teams outside the product team inside the system boundary. Clients and Users may experience marginal delay. Support resources are informed but moderately impacted.
4. The Product Teams are fully autonomous within the system boundary. Clients and Users are not impacted. Support resources are informed but slightly impacted.
5. The Product Teams are fully autonomous within the system boundary. Clients and Users are not impacted. Support resources are informed and not impacted.

> <!-- Info -->
> Info
> This question is measuring the team's ability to make changes within their system boundaries without needing to coordinate extensively with other teams.
>
> **Example:** In a mature Payments ecosystem, the fraud detection service team could update their risk scoring algorithm without requiring approvals or coordination with the transaction processing team. The teams would have agreed on a stable interface contract, so the fraud service could evolve independently while still integrating properly with the transaction flow, enabling greater team autonomy and faster innovation cycles.

#### [DA.2] Does your team have the people with skills and knowledge necessary to accomplish daily tasks without frequent conversations and close coordination with other teams?

1. The Product Teams lack core competencies, requiring significant communication and coordination with other teams for most changes
2. The Product Teams have skills and knowledge that are siloed within the team, requiring significant communication and coordination for approximately half of all changes
3. The Product Teams are capable but depend on other teams for some tasks, resulting in significant communication and coordination for all major and some minor changes
4. The Product Teams are capable but depend on other teams for a few tasks, requiring communication and coordination only for major changes
5. The Product Teams are fully self-sufficient and can deliver without roadblocks

> <!-- Info -->
> Info
> This question is measuring the team's self-sufficiency and the extent to which they can operate independently without relying on other teams for skills and knowledge.
>
> **Example:** In a mature Payments product, the team managing the credit card processing service would have members with expertise in payment gateway integrations, security compliance (PCI-DSS), transaction processing, and database optimization. This breadth of knowledge allows them to implement new payment features or troubleshoot complex transaction issues without having to wait for specialists from other teams, significantly improving team autonomy and reducing delivery time.

#### [DA.3] What level of autonomy does your team have over and between the deployment & release of applications & databases?

1. The Product Teams can deploy at a fixed frequency in a large groups of apps/DBs and cannot resolve issues quickly
2. The Product Teams can deploy at a fixed frequency in small group of apps/DBs, and cannot resolve issues quickly
3. The Product Teams can deploy apps/DBs independently at fixed frequencies and can resolve issues within SLAs
4. The Product Teams can deploy apps/DBs independently and resolve issues quickly; Releases are coupled with deployments
5. The Product Teams can deploy apps/DBs independently and resolve issues quickly; Releases are decoupled from deployments

> <!-- Info -->
> Info
> This question is measuring the team's ability to independently manage the deployment and release processes, ensuring minimal disruption and efficient resolution of issues.
>
> **Example:** In a mature Payments system, the team responsible for the payment authorization service could deploy updates to their component independently from the settlement service team. This autonomy would allow them to fix critical issues in the authorization flow immediately without being blocked by coordinating a full system deployment. Additionally, they could use feature flags to decouple releases from deployments, enabling them to gradually roll out a new payment method to specific merchant segments without requiring a new deployment.

#### [DA.4] What level of autonomy does the team have for writing, executing, and instrumenting tests?

1. The Product Teams do not write any tests, teams outside the product write and run all tests or no tests exist
2. The Product Teams write few tests in coordination with teams outside the product who write most tests, tests require coordination for many downstream dependencies
3. The Product Teams write most tests in coordination with teams outside the product who write few tests, tests require one-time setup for many downstream dependencies
4. The Product Teams write all tests which are run adhoc and within build & deploy automations. Tests require one-time setup for downstream dependencies
5. The Product Teams write all tests which are run by the team adhoc and within build & deploy automations. Tests can be run without a Service Request (SR) for downstream dependencies

> <!-- Info -->
> Info
> This question is measuring the team's ability to write, execute, and maintain tests autonomously, ensuring that testing processes are efficient and do not rely heavily on external teams.
>
> **Example:** In a mature Payments system, the transaction processing team would have the autonomy to create and run tests without waiting for other teams. They could independently set up test scenarios that simulate different payment flows (credit card, ACH, real-time payments) with mocked external payment gateways. This would allow the team to validate changes quickly and autonomously, without creating dependencies on specialized QA teams or having to coordinate test environments with other teams.

### Canonical System Adoption

> This section evaluates the extent to which the product leverages standardized, reusable services and artifacts that support core business processes. This includes evaluating the team's ability to adopt existing technologies, tools, and services efficiently, and the impact of these adoptions on the product's architecture and delivery timelines.

#### [CS.1] How does your team prioritize the usage of existing technologies, tools, and services in your solution designs?

1. The Product Teams do not perform active discovery & analysis of existing solutions and existing solutions are rarely adopted
2. The Product Teams regularly perform discovery & analysis of existing solutions, but adoption barriers are not shared and are only adopted when convenient
3. The Product Teams always perform discovery & analysis of existing solutions early in design phase and adoption barriers are informally shared, but adoption is often delayed due to lead time
4. The Product Teams always perform discovery & analysis of existing solutions early in design phase and adoption barriers are shared & prioritized, but adoption is sometimes delayed due to lead time
5. The Product Teams always perform discovery & analysis of existing solutions early in design phase, adoption barriers are shared & prioritized, and adoption does not impact release timelines

> <!-- Info -->
> Info
> This question is measuring how well existing technologies, tools, and services are adopted.  Cost savings are realized by maintaining a fewer instances of a solution by a fewer number of teams and increasing a team's delivery capacity.
>
> **Example:** In a mature Payments system, the team would assess if they can use the company's existing API gateway (like Apigee) before building their own routing solution. Using the established gateway would save development time and provide immediate benefits like standardized security, rate limiting, and monitoring. Similarly, for a new notification requirement, they would integrate with the existing Notification Hub service instead of creating a custom email/SMS solution, allowing them to focus on core payment capabilities.

#### [CS.2] Which canonical systems have been adopted? (select all that apply)

> **Note:** Select only systems where your team has adopted the canonical system for at least 65% of applicable use cases in production environments. Systems in pilot or limited use should not be selected.

- 🔲 WEX Fabric
- 🔲 Splunk
- 🔲 DataDog
- 🔲 ArgoCD
- 🔲 Aiven Kafka
- 🔲 API Gateway (Apigee)
- 🔲 WEX Data Platform
- 🔲 OneWEX Identity (Okta CIC)
- 🔲 Phoenix Design Components
- 🔲 LaunchDarkly
- 🔲 Notification Hub
- 🔲 Tokenizer

> <!-- Info -->
> Info
> This question is measuring the adoption rate of prioritized canonical systems.
>
> **Example:** In a mature Payments system, teams would leverage canonical systems like API Gateway for managing API access control, Kafka for event-driven transaction processing, and LaunchDarkly for feature flag management. For instance, using the standardized tokenization service would ensure that sensitive payment data is consistently secured across all payment products, rather than each team implementing their own tokenization solution with varying security implementations.

#### [CS.3] What reasons have canonical services and artifacts not been adopted? (select all that apply)

- 🔲 Awareness/Knowledge
- 🔲 Technology Compatibility
- 🔲 Missing Capabilities
- 🔲 Implementation Lead Time
- 🔲 Project Prioritization
- 🔲 Product Relevancy
- 🔲 Other

> <!-- Info -->
> Info
> The above question is not used for scoring. Its purpose is to provide additional relevant information that will help us to target areas for future improvements.
>
> **Example:** A Payments team might note that they haven't adopted the company's standard API Gateway because their legacy payment processing service has tight coupling with a custom authorization mechanism. Understanding these specific barriers helps the organization provide targeted support, such as creating migration paths or offering specialized integration patterns for legacy systems.

<!-- Parent: Practices -->
<!-- Parent: Practice Initiatives -->
<!-- Title: RFC5-Team Outcome Improvement Program -->
# Team Outcome Improvement Program

```text
Author: 
    <PERSON><PERSON>
    Phil <PERSON>
    Vish <PERSON>
Publish Date: 2024-07-11
Category: Practices
Subtype: Team Improvement
Source URL: https://docs.google.com/spreadsheets/d/1nZpacPrLjhMZI0IIw7Y8jCM0XZ78pkeqiEZJ5IO6dic
```

## Overview

This document outlines the process WEX IT uses to measure desired outcomes for teams and drive improvements for strategic objectives.

The Team Outcome Improvement Program consists of 2 processes: Outcome Assessment and Improvement Planning.

Outcome Assessment is a short light-weight survey that identifies which desired outcomes should be prioritized for improvement for a team. The Outcome Assessment should be completed by tech leaders across all engineering teams.

Improvement Planning consists of detailed practice surveys and are used to build an iterative evaluation and remediation work stream for that team. Improvement Planning is strategic and will require a budgeted project for teams that need it or aren't seeing improvements based on their own efforts in the Outcome Assessment.

This program will be an incremental evolution.  Adopting a comprehensive maturity model framework has a high lead time so we will be using incremental delivery and refine as needed over time.  

The initial phases of this program are focused on drafting a basic framework to measure to prioritized desired outcomes.  The goals of these initial phases are to:

- build a shared understanding of our strengths and opportunities for improvement across each line-of-business (LoB)
- identify improvements that teams can prioritize
  - e.x. training, practice adoption, development, etc
- be able to extend and improve this program over time

The program design and implementation are managed in Jira: [TISO-290](https://wexinc.atlassian.net/browse/TISO-290)

The current components that make up the assessment include:

- `Desired Outcomes`: The value-driven qualities and capabilities all teams should have
- `Key Performance Indicators (KPIs)`: Quantitative metrics captured and assessed objectively
- `Development & Delivery Practices`: Qualitative detail-oriented surveys focused on ways of working.  These may be used as placeholders for KPIs or road-mapping to drive improvements towards desired outcomes.

## Desired Outcomes

There are several desired outcomes for teams as they apply this process.  These outcomes are focused on value-driven qualities and capabilities of a team and their applications.

- Teams can make large-scale changes to the design of their systems without the permission of somebody outside the team or depending on other teams.
- Teams are able to complete work without needing fine-grained communication and coordination with people outside the team.
- Teams deploy and release their product or service on demand, independently of the services it depends on or of other services that depend on it.
- Teams are able to perform testing as needed without needing to wait for somebody outside the team
- Teams can deploy during normal business hours with negligible downtime.
- Other teams and external customers can interact with a team's application without needing communication with the team
- Internal and external customers can interact with a team's application data in an independent, secure and intentional way
- Internal and external customers can use a team's applications in a consistent and expected way without errors
- Teams prioritize the use of existing technologies and services when designing solutions
- Teams are able to quickly identify the source of reported issues and has the necessary context to respond accordingly

These outcomes are centered around the following design qualities:

- Security
- Stability
- Throughput
- Usability
- Maintainability

## Process

Desired Outcome Assessments will be done on a recurring basis by all engineering in order to measure progress and plan for the next round of improvements along with any instrumented KPIs.

The `scope of the applications*` reflected in the assessment should be a set of applications which are deployed together.
The `scope of the team*` reflected in the assessment should be the collective group of individuals who own one or more of the scoped applications and report to the survey taker.

> <!-- Note -->
> Warning
> These definition needs to be refined/solidified

To take the survey, fill out the [Outcome Assessment Survey](https://docs.google.com/forms/d/e/1FAIpQLSdm1Vhmff4SINmDUvEfXbowCfj5aRnF1KpngUU5c92cosse-Q/viewform) on Google Forms

Assessment results will be associated with both a team and applications so that comparative analysis can occur regardless of how team ownership, application ownership, or solution architecture changes over time.

> <!-- Info -->
> Note
> The Improvement Planning process described below is still being designed.  Information is included below to document current plans.

Teams may choose to either prioritize & implement improvements on their own or leverage the Improvement Planning process. The Improvement Planning process involves:

  1. Setup metric tracking and reporting
  2. Complete relevant practice surveys with proctors
  3. Proctors will score practices and provide a prioritized backlog of changes based on outcome alignment, KPIs, and current practices
  4. Schedule and implement next round of changes (development, practices, training, etc)
  5. Complete the Outcome Assessment again, compare results to prior scores and iterate again

This process will be continually refined:

- Replacing qualitative survey questions with quantitative KPIs
- Adoption of maturity model framework (e.g. OWASP SAMM)
- Additional surveys for design, delivery, and support practices
- Changes to WEX IT strategic objectives or prioritized outcomes

## Scoring

### Outcome Alignment

The [Outcome Alignment Assessment](https://docs.google.com/forms/d/1L_gS0sOjAQSLbtJAdrWpHO1Za23AgEkrBOqFDhqQXQk/edit) assesses how closely a team demonstrates the current set of prioritized qualities and capabilities.

The assessment consists of a small number of questions where each question is associated to a desired outcome.  Each question has a corresponding progressive scale which outlines milestones required to reach that level.

#### Levels

1. `Initial  / Ad Hoc` Little to no standardization in the technology architecture.
2. `Repeatable` Some processes are repeatable, but they are often still reactive. Basic standards and procedures are starting to emerge.
3. `Defined Processes` are documented and standardized across the organization. There is proactive management and defined roles and responsibilities.
4. `Managed Processes` are measured and controlled. There is a focus on process improvement and performance metrics.
5. `Optimized Processes` are continually improved based on quantitative feedback and new technologies. The organization is highly agile and adaptable.
6. `Elite` is when a team has achieved the desired outcome

#### Questions

> [!faq]- To what degree do your systems' interfaces allow you to make implementation changes inside the system without communicating with others?
>
> 1. My team is dependent on many external code or design reviews inside the system boundary. Clients and Users may experience significant outage. Support resources are significantly impacted.
> 2. My team is dependent on some external code or design reviews inside the system boundary. Clients and Users may experience moderate delay or outage. Support resources are moderately impacted.
> 3. My team is dependent on some external code or design reviews  inside the system boundary. Clients and Users may experience marginal delay. Support resources are informed but moderately impacted.
> 4. My team is fully autonomous within the system boundary. Clients and Users experience marginal delay. Support resources are informed but slightly impacted
> 5. My team is fully autonomous within the system boundary. Clients and Users are not impacted. Support resources are informed but slightly impacted.
> 6. My team is fully autonomous within the system boundary. Clients and Users are not impacted. Support resources are informed and not impacted.

***
> [!faq]- Does your team have the people with skills and knowledge necessary to accomplish daily tasks without frequent conversations and close coordination with other teams?
>
> 1. My team has missing core competencies and most changes require significant communication and coordination
> 2. My team has siloed skills and knowledge on the team with around half of changes require significant communication and coordination
> 3. My team is prepared and able but dependent on others for some tasks resulting in significant communication and coordination for all major and some minor changes
> 4. My team is prepared and able but dependent on others for a few tasks resulting in some communication and coordination for only major changes
> 5. My team is prepared, able, and actively delivering resulting in some communication for major changes, but no active coordination is needed
> 6. My team is prepared, able, and actively delivering without roadblocks

***
> [!faq]- What level of autonomy does your team have over and between the deployment & release of applications & databases?
>
> 1. My team can deploy at a fixed frequency in a large groups of apps/DBs, and we can not resolve issues quickly
> 2. My team can deploy at a fixed frequency in small group of apps/DBs, and we can not resolve issues quickly
> 3. My team can deploy apps/DBs independently at fixed frequencies, and we can resolve issues within SLAs
> 4. My team can deploy apps/DBs independently, however some limitations apply, and we can resolve issues within SLAs
> 5. My team can deploy apps/DBs independently and resolve issues quickly; Releases are coupled with deployments
> 6. My team can deploy apps/DBs independently and resolve issues quickly; Releases are decoupled from deployments

***
> [!faq]- What level of autonomy does the team have for writing, executing, and instrumenting tests?
>
> 1. My team does not write any tests, external teams write and run all tests or no tests exist
> 2. My team writes few tests in coordination with external teams who write mosts tests, tests require coordination for many downstream dependencies
> 3. My team writes mosts tests in coordination with external teams who write few tests, tests require one-time setup for many downstream dependencies
> 4. My team writes most tests which are run adhoc, tests require one-time setup for many downstream dependencies
> 5. My team writes all tests which are run adhoc within build & deploy automations while tests require one-time setup for downstream dependencies
> 6. My team writes all tests which are run by the team adhoc within build & deploy automations and tests can be run without SRs for downstream dependencies

***
> [!faq]- What is the risk & impact to users and clients of your applications if a deployment were to occur during the business day?
>
> 1. Users & clients experience significant or complete loss of functionality and assistance is required to correct issues (ex. data loss, corruption, etc)
> 2. Users & clients experience significant or complete loss of functionality, but all errors are recoverable without assistance
> 3. Users & clients experience partial or limited loss of functionality, but all errors are recoverable without assistance
> 4. Users & clients do not receive errors, but do receive a maintenance page or response
> 5. Users & clients may need to take a trivial action (e.x. authentication), but do not experience any errors or loss of functionality
> 6. Users & clients are not impacted during a deployment

***
> [!faq]- Is there any knowledge sharing, training, or other required action needed by your team for a user or client to be setup and begin using the application?
>
> 1. My team spends significant effort to onboard users and clients for my applications without available documentation
> 2. My team spends significant effort (e.g. deployment) to onboard users and clients for my applications with documentation available by request
> 3. My team spends some effort (e.g. collaboration) to onboard users and clients for my applications with documentation available by request
> 4. My team spends little effort (e.g. configuration) to onboard users and clients for my applications with documentation available by request
> 5. My team spends little effort (e.g. configuration) to onboard users and clients for my applications with documentation published and accessible
> 6. No effort is needed by my team to onboard users and clients for my applications

***
> [!faq]- Is your team able to adhere to all WEX security policies & standards, detect & remediate security issues quickly with a low number of incidents in production, and have minimal overhead cost for identity management?
>
> 1. My team lacks knowledge about WEX security policies & standards
> 2. My team understands WEX security policies & standards, but not all systems are compliant or has a high number of incidents in production
> 3. My team adheres to all WEX security policies & standards, but lacks the ability to track and report on detection or resolution KPIs
> 4. My team adheres to all WEX security policies & standards, but detection and remediation of moderate issues have high lead times
> 5. My team adheres to all WEX security policies & standards, but detection and remediation of low issues have high lead times or the team maintains a dedicated identity solution
> 6. My team adheres to all WEX security policies & standards, detects & resolves security issues quickly, has a low number of incidents in production, and does not have to maintain a dedicated identity solution

***
> [!faq]- Is your team able to detect and remediate issues (defects, code quality, performance, etc) reliably and quickly before they impact end users & clients?
>
> 1. Issues are detected after changes are merged, customers often submit SRs caused by an issue, or applications do not perform well
> 2. Most issues are detected after changes are merged, customers often submit SRs caused by an issue, or applications only perform well at light loads
> 3. Significant number of issues are detected after changes are merged but before they impact most users & clients. Customers sometimes submit SRs caused by an issue, but applications perform well at moderate and light loads
> 4. Significant number of issues are detected after changes are merged, but before they impact most users & clients. Customers infrequently submit SRs caused by an issue and applications perform well under most conditions
> 5. Most issues are detected and resolved before changes are merged, customers rarely submit SRs caused by an issue, and applications perform well under most conditions
> 6. Most issues are detected and resolved before changes are merged, customers rarely submit SRs caused by an issue, and applications perform well regardless of current load

***
> [!faq]- How does your team prioritize the usage of existing technologies and services in your solution designs?
>
> 1. My team does not perform active discovery & analysis of existing solutions and existing solutions are rarely adopted
> 2. My team sometimes performs discovery & analysis of existing solutions, but adoption barriers are not shared and are only adopted when convenient
> 3. My team always performs discovery & analysis of existing solutions early in design phase, but adoption barriers are not shared and are adopted when convenient
> 4. My team always performs discovery & analysis of existing solutions early in design phase and adoption barriers are informally shared, but adoption is often delayed due to lead time
> 5. My team always performs discovery & analysis of existing solutions early in design phase and adoption barriers are shared & prioritized, but adoption is sometimes delayed due to lead time
> 6. My team always performs discovery & analysis of existing solutions early in design phase, adoption barriers are shared & prioritized, and adoption does not impact release timelines

***
> [!faq]- Are operational support teams able to quickly identify issue root cause and escalate to your team directly for remediation if necessary?
>
> 1. My team often struggles with identifying root cause independently
> 2. My team can identify root cause, but lacks support documentation or the capacity to do it without impacting delivery commitments
> 3. My team has operational support documentation for our solutions published and available, but operational support roles are not on-boarded consistently
> 4. My team actively on-boards operational support roles for our applications, but often need to assist in identifying root cause or lack a consistent path for escalation
> 5. Operational support roles are able to remediate most issues related to scale & load and can escalate directly to my team, but are not actively consulted during solution design
> 6. Operational support roles are able to remediate most operational issues, can escalate directly to my team, and are actively engaged during solution design

### KPIs

#### Throughput - Change lead time (DORA)
>
> Time it takes for a code commit or change to be successfully deployed to production.

This component reflects the efficiency of your delivery pipeline.

#### Throughput - Deployment frequency (DORA)
>
> How often application changes are deployed to production.

Higher deployment frequency indicates a more agile and responsive delivery process.

#### Stability - Change fail percentage (DORA)
>
> % of deployments that cause service impairments or failures in production, requiring hot fixes or rollbacks.

A lower change failure rate indicates a more reliable delivery process

#### Stability - Mean time to recover (DORA)
>
> Time it takes to recover from a service impairment or failure.

A lower recovery time indicates a more resilient and responsive system.

#### Practice Assessment
>
> Measures adoption of design, delivery, and support practices

A higher percentage indicates a more complete adoption of practices.  Practice effectiveness can be measured by how adoption impacts the quantitative KPIs.

## Delivery Practices

- Design Discovery
- Branching
- [DevOps Survey](https://docs.google.com/spreadsheets/d/1zOcxfPxoIjFe-sX2bJ-90sR-hF0_iooSrCsQcPkRLfE/edit#gid=0)
- Testing
- Maintenance
- AI Enablement
- Release Management
- Work Management

## Design Practices

- [API Design Survey](https://docs.google.com/spreadsheets/d/1-mAJszuqFK3Tnv-eJRr8II_aUifIH0olOwnVe6RgwD4/edit#gid=0)
- [Security Survey](https://docs.google.com/spreadsheets/d/15cyzcE1TNY20ZF2LayyCvpksssn66_7foSDeuBKOaJ4/edit#gid=0)
- AI Capabilities
- WEX AI Platform
- WEX Data Platform
- WEX PaaS
- Tokenization
- API Gateway
- Customer Identity

## Maturity Model Frameworks

> To be introduced at a later phase in the project

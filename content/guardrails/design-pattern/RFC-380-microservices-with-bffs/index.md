<!-- Parent: Guardrails -->
<!-- Parent: Design Pattern -->
<!-- Title: RFC-380 Microservices With BFFs -->
# Design Pattern Template

```text
Author: <PERSON>
Publish Date: 2024-12-11
Category: Guardrails
Subtype: Design Pattern
```

<!--
Required sections for ratification: 
-->

## Introduction

This RFC proposes a design for software that enables stable, consistent, and intentional microservice-driven design, while supporting a variety of evolving product needs that can vary wildly over the lifetime of the product. Example "expressions" of the product (aka "frontends") include (but are not limited to) a published public API, internal system integrations/APIs, and various user interfaces such as mobile applications and microsites. Each frontend brings its own set of requirements and challenges, so it is advantageous to leverage a flexible design that can easily adapt.

Microservices are a solid choice when it comes to encapsulating a particular software domain. When architected well, they can accelerate a product team's ability to create reliable software that's able to adapt to a variety of changes. It is common, however, to focus microservice design on the needs of the backend software; applying similar concepts to the requirements of multiple frontends (clients) of the microservice(s) can provide similar benefits to the product that are easy to overlook while building.

## Conceptual Overview

For the purposes of this RFC, consider a scenario: an existing monolithic system has become difficult to maintain and enhance. It currently serves the needs of a web application, a mobile application, and a handful of external API integrations. This is fairly common with home-grown software, especially for agile / start-up teams.

Over time, amassed technical debt and system bloat have begun to plague the ability of the development team to reliably and quickly bring new features to market. The team has decided that breaking the monolith into smaller domain-bound components (services) would bring many long-term benefits.

To be successful, the new design must be able to support needs such as:

- **Web Application:** The primary SaaS product used by customers
- **Mobile Application:** A companion mobile application offering many features of the *Web Application* (and possibly some that are unique to the mobile app)
- **Additional Microsite(s):** Purpose-built, secondary web properties that extend the core product (example: a self-service portal offered to customers' customers)
- **Partner API:** A well-documented, non-public API delivered to one or more integration partners (ex. data exchanges, linkages between ERP systems)
- **Public API:** A well-documented, developer-friendly Public API that exposes most of the product's functionality to third-party developers. It is possible (and probable) that there are some capabilities that would not be exposed publicly

## Pattern Structure

This RFC offers a design that meets the needs outlined above. It proposes the adoption of an internal microservices architecture exposed with a layer of Backends-for-Frontends (BFFs) that address the unique challenges of each "frontend."

- *Domain Microservices:* Decomposing the software into smaller, independent services with well-defined domains of responsibility (e.g. user management, product catalog, order processing) allows independent product domains to grow and evolve independently over time. It allows the underlying backend technology stack to morph as product needs dictate change.
- *Backends For Frontends (BFFs):* Creating separate backend services specifically designed to be entry points for each "frontend" (web application, mobile application, public API, etc.) enables a great degree of flexibility when the requirements of a single frontend change over time. These services can help provide benefits such as user authentication/authorization, data format translations, frontend-specific request/response semantics, to name a few.

Since BFFs and Domain Microservices are both "back-end services," the difference between them can be confusing. One way to think of it is that Domain Microservices are the home for business logic and "source of truth." Much like object-oriented code promotes encapsulation, Domain Microservices work to encapsulate the data and logic for a particular area of the product.

On the other hand, BFFs can be viewed as "entry points" or "gateways" into the Domain Microservices. Care should be taken to *not* include domain business logic within them; they exist to support and enable their corresponding frontend.

## Implementation

This design introduces a handful of architectual "points of flexibility." This is advantageous in that the design is quite adaptable to future needs, and it helps avoid "painting ourselves into a corner." However, one common worry of a more complex architecture is that it can lead to longer overall development times and system fragmentation. Additionally, it can take longer to get a distributed system "off the ground" since initial release cycles likely include breaking a lot of "new ground" as the new architecture is built out. Therefore, it is critical to find good targets of opportunity to slowly adopt and adapt to this architecture, while incrementally delivering small chunks of business value.

![Microservices With BFFs](./microservices-with-bffs.jpg)

## Advantages and Disadvantages

Some advantages of this design are:

- It creates natural "lines of separation" that can enable a development team to divide-and-conquer.
  - One team of developers can work on frontend needs of the product, which would include ownership of the BFF layer. Allowing the frontend team to own their BFF grants them more overall ownership of the entire frontend, while establishing solid boundaries and options for automated testing (examples include contract-driven testing and mocked data/services).
  - Meanwhile, a separate team of developers can work on the microservice layer, focusing on core business logic that is frontend-agnostic. They can expose the logic via an API contract they own; this enables them to focus on automated testing that is focused on business/domain logic rather than user interface scenarios.
- It allows different system components to evolve and improve independently from one another

Some potential disadvantages of this design are:

- *Public API as a BFF:* It can be argued that all frontends, including internal ones, should consume a unified "Product API" which is also exposed as the "Public API". While this approach has merit, this can also add complication (due to the variety in API clients) and reduce flexibility.
  - This RFC proposes treating the public API as another BFF, allowing internal clients to access microservices directly.
  - Internal clients often have different needs and security requirements than external consumers of a public API. By keeping public API concerns within a Public API BFF, these can be separated as necessary.
  - By having separate BFFs, we gain fine-grained control over data access, authentication, and authorization for each client.
  - The public API might have constraints due to backward compatibility or external dependencies. Internal BFFs can evolve more freely, allowing for faster iteration and adoption of new technologies, while the public API is free to move more slowly.
  - Exposing internal functionalities through the public API can increase the overall attack surface of the system. BFFs provide an additional layer of security by ensuring internal services are not unnecessarily exposed.
- *Code Duplication:* Multiple BFFs that solve similar problems (user authentication, for example) can quickly lead to code duplication across BFFs. This could be viewed as a feature, as it allows for future independent flexibility. That being said, it is likely wise to mitigate potential code duplication by creating shared libraries and components for common functionalities across BFFs.
- *Complexity:* This architecture can definitely add complexity throughout the delivery chain (additional code repos, CI/CD delivery pipelines, deployed assets, costs, to name a few). That being said, the benefits in terms of scalability, flexibility, and security very likely outweigh these costs.

## FAQs

1. Why expose the Public API as another BFF, instead of having other BFFs consume the Public API directly?
    - Internal clients often have different needs and security requirements than external consumers of a public API.
    - By having separate BFFs, we gain fine-grained control over data access, authentication, and authorization for each client.
    - The public API might have constraints due to backward compatibility or external dependencies. Internal BFFs can evolve more freely, allowing for faster iteration and adoption of new technologies.
    - Exposing internal functionalities through the public API can increase your attack surface. BFFs provide an additional layer of security, ensuring that internal services are not inadvertently exposed.

## Further Reading

- [Amazon Web Sevices: Backends for Frontends Pattern](https://aws.amazon.com/blogs/mobile/backends-for-frontends-pattern/)
- [WEX Solutions Architecture: Microservices](https://wexinc.atlassian.net/wiki/spaces/PTA/pages/153792349451/Microservices)
- [WEX Solutions Architecture: API-First and Loose Coupling](https://wexinc.atlassian.net/wiki/spaces/PTA/pages/153788679601/API+First+and+Loose+Coupling)

## Glossary

- Backend for Frontend (BFF): An architectural pattern in web development where a dedicated backend is created for each frontend application.
- Microservices: An architectural and organizational approach to software development where software is composed of small independent services that communicate over well-defined APIs.

<!-- Parent: Guardrails -->
<!-- Parent: Design Pattern -->
<!-- Title: RFC-387 Decoupled Deployments using Expand and Contract -->

# Decoupled Deployments using Expand and Contract

```text
Author: <PERSON>
Publish Date: 2024-12-17
Category: Guardrails
Subtype: Design Pattern
```

## Introduction

The Expand and Contract pattern, also known as Parallel Change, is a design pattern used to decouple deployments and enable smooth transitions between different versions of a system. This pattern allows for incremental changes without disrupting the existing functionality.

## Conceptual Overview

The Expand and Contract pattern involves three main phases: Expand, Migrate, and Contract. During the Expand phase, new functionality is added alongside the existing one. In the Migrate phase, data and traffic are gradually shifted to the new functionality. Finally, in the Contract phase, the old functionality is removed.

This pattern addresses problems such as minimizing downtime, reducing deployment risks, and ensuring backward compatibility during transitions.

### Glossary

- **Expand Phase**: The phase where new functionality is added alongside the existing one.
- **Migrate Phase**: The phase where data and traffic are gradually shifted to the new functionality.
- **Contract Phase**: The phase where the old functionality is removed.

## Pattern Structure

Participants in this pattern include:

- **Old System**: The existing functionality that needs to be replaced or updated.
- **New System**: The new functionality that is being introduced.
- **Migration Layer**: A temporary layer that facilitates the transition from the old system to the new system.

Visual representations such as UML diagrams can illustrate the interactions between these participants during each phase.

![uml sample diagram](uml.png)

## Implementation

1. **Expand Phase**:
   - Introduce the new functionality alongside the existing one.
   - Ensure both old and new systems can operate simultaneously.

2. **Migrate Phase**:
   - Gradually shift data and traffic to the new system.
   - Monitor the performance and correctness of the new system.

3. **Contract Phase**:
   - Remove the old functionality once the new system is fully operational.
   - Clean up any temporary migration layers.

```typescript
// Example code snippet in TypeScript
class OldSystem {
  // ...existing code...
}

class NewSystem {
  // ...new functionality...
}

// Migration layer
class MigrationLayer {
  // ...code to facilitate transition...
}
```

## Use Cases

- Migrating from a monolithic architecture to a microservices architecture.
- Upgrading a database schema without downtime.
- Introducing a new API version while maintaining backward compatibility.
- Updating application parameters of a scheduled job

## Advantages and Disadvantages

**Advantages**:

- Minimizes downtime during deployments.
- Reduces deployment risks by allowing incremental changes.
- Ensures backward compatibility.

**Disadvantages**:

- Increased complexity due to maintaining both old and new systems temporarily.
- Requires careful planning and monitoring during the migration phase.

## Refactoring to Pattern

Guidelines for refactoring existing code to use the Expand and Contract pattern:

1. Identify the functionality to be replaced or updated.
2. Implement the new functionality alongside the existing one.
3. Gradually migrate data and traffic to the new functionality.
4. Remove the old functionality once the new system is stable.

### Code Examples

#### Example 1: Database Schema Migration

```sql
-- Expand Phase: Add new columns to the existing table
ALTER TABLE users ADD COLUMN new_email VARCHAR(255);

-- Migrate Phase: Copy data from old column to new column
UPDATE users SET new_email = email;

-- Contract Phase: Remove old column
ALTER TABLE users DROP COLUMN email;
```

#### Example 2: API Versioning

```javascript
// Expand Phase: Introduce new API endpoint alongside the old one
app.get('/api/v1/resource', oldHandler);
app.get('/api/v2/resource', newHandler);

// Migrate Phase: Gradually shift traffic to the new endpoint
// This can be done using feature flags or canary releases

// Contract Phase: Remove the old API endpoint
app.get('/api/v1/resource', (req, res) => res.status(410).send('Gone'));
```

#### Example 3: Feature Toggle in a Web Application

```typescript
// Expand Phase: Introduce new feature alongside the old one
function renderPage() {
    if (featureToggle.isEnabled('newFeature')) {
        renderNewFeature();
    } else {
        renderOldFeature();
    }
}

// Migrate Phase: Gradually enable the new feature for more users
featureToggle.enableForPercentage('newFeature', 50);

// Contract Phase: Remove the old feature
function renderPage() {
    renderNewFeature();
}
```

## Testing the Pattern

Strategies for testing the implementation:

- Unit tests for both old and new functionalities.
- Integration tests to ensure smooth transition during the migration phase.
- Monitoring and logging to detect any issues during the migration.

## FAQs

### What other patterns are typically used with the Expand and Contract pattern?

- **Feature Flag Management**: This pattern allows you to enable or disable features dynamically, providing a way to control the rollout of new functionality and facilitate testing during the Expand and Migrate phases.
- **Canary Releases**: Deploying new functionality to a small subset of users before a full rollout to monitor performance and catch issues early.
- **Blue-Green Deployments**: Running two identical production environments to switch between them, reducing downtime and risk during deployments.
- **Strangler Fig Pattern**: Incrementally replacing parts of a legacy system with new functionality until the old system is completely replaced.

## Further Reading

- [Refactoring](https://martinfowler.com/books/refactoring.html) by Martin Fowler
- [Parallel Change](https://martinfowler.com/bliki/ParallelChange.html) by Danilo Sato
- [Expand and Contract](https://www.tim-wellhausen.de/papers/ExpandAndContract/ExpandAndContract.html) by Tim Wellhausen
- "Continuous Delivery" by Jez Humble and David Farley

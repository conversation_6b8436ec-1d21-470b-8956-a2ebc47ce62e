<!-- Parent: Guardrails -->
<!-- Parent: Design Pattern -->
<!-- Title: RFC-470 API Pattern Selection Guide -->

# API Pattern Selection Guide

```text
Author: <PERSON><PERSON>
Publish Date: 2024-01-09
Category: Guardrails
Subtype: Design Pattern
```

## Introduction

This design pattern provides guidance for selecting between REST, GraphQL, or a hybrid approach for API development, including decision criteria, implementation patterns, and real-world use cases.

## Conceptual Overview

Modern API architectures can be implemented using:

- REST (Resource-oriented)
- GraphQL (Query-oriented)
- Hybrid (Combined approach)

## Pattern Structure

### Decision Flow

```mermaid
flowchart TD
    A[Start] --> B{Data Complexity?}
    B -->|Simple| C{Cache Important?}
    B -->|Complex| D{Client Variety?}
    
    C -->|Yes| E[REST API]
    C -->|No| F{Multiple Endpoints?}
    
    D -->|High| G[GraphQL API]
    D -->|Low| F
    
    F -->|Yes| H{Performance Critical?}
    F -->|No| E
    
    H -->|Yes| I[Hybrid Approach]
    H -->|No| G

    classDef decision fill:#f9f,stroke:#333,stroke-width:2px;
    classDef solution fill:#bbf,stroke:#333,stroke-width:2px;
    
    class B,C,D,F,H decision;
    class E,G,I solution;
```

### Selection Criteria

1. **Choose REST When:**
   - CRUD operations on well-defined resources
   - Heavy caching requirements
   - Simple data hierarchies
   - Backend-to-backend communication
   - Team expertise in REST

2. **Choose GraphQL When:**
   - Complex data relationships
   - Diverse client requirements
   - Mobile applications with bandwidth constraints
   - Flexible query requirements
   - Need to avoid over-fetching/under-fetching

3. **Choose Hybrid When:**
   - Mix of simple CRUD and complex queries
   - Different performance requirements per endpoint
   - Gradual migration scenario
   - Team has mixed expertise

## Implementation Patterns

### Pure REST Implementation

![REST API Usage](./diagrams/rest_api.png)
*REST API showing direct resource access pattern with clear endpoints per resource*

```json
GET /api/members/123/benefits
{
  "memberId": "123",
  "planType": "PPO",
  "effectiveDate": "2024-01-01",
  "coverageDetails": {
    "medical": true,
    "dental": true,
    "vision": false
  }
}
```

### Pure GraphQL Implementation

![GraphQL API Usage](./diagrams/graphql_api.png)
*GraphQL API showing subgraphs*

```graphql
query {
  member(id: "123") {
    currentBenefits {
      planType
      effectiveDate
      coverageDetails {
        medical
        dental
        vision
      }
    }
  }
}
```

### Hybrid Implementation

![Hybrid API Usage](./diagrams/hybrid_api.png)
*Hybrid architecture showing coexistence of REST and GraphQL endpoints*

```text
/api/v1/claims           # REST endpoint for claims processing
api/v1/graphql         # GraphQL endpoint for benefits queries
```

### Bounded Context Implementation

1. **REST Bounded Contexts**

   ```text
   /api/enrollment/       # Enrollment Management Context
   /api/claims/          # Claims Processing Context
   /api/benefits/        # Benefits Management Context
   ```

2. **GraphQL Bounded Contexts**

   ```graphql
   type Query {
     # Enrollment Context
     enrollment(id: ID!): Enrollment
     eligibility(memberId: ID!): EligibilityStatus
     
     # Claims Context
     claim(id: ID!): Claim
     claimsHistory(memberId: ID!): [Claim]
     
     # Benefits Context
     benefits(memberId: ID!): BenefitsCoverage
     planDetails(planId: ID!): HealthcarePlan
   }
   ```

3. **Hybrid Domain APIs**

   ```text
   # Claims Domain
   /api/claims/v1/              # REST for claim submissions
   /api/claims/graphql          # GraphQL for claim queries

   # Benefits Domain
   /api/benefits/v1/            # REST for benefit updates
   /api/benefits/graphql        # GraphQL for benefit queries
   ```

### Domain-Focused API Patterns

1. **Capability-Based REST APIs**

   ```text
   # Enrollment Capability
   POST   /api/enrollment/commands/enroll-member
   POST   /api/enrollment/commands/add-dependent
   GET    /api/enrollment/queries/coverage-status

   # Claims Capability
   POST   /api/claims/commands/submit-claim
   GET    /api/claims/queries/claim-status
   ```

2. **Domain-Oriented GraphQL Schema**

   ```graphql
   # Benefits Domain
   type HealthcarePlan {
     id: ID!
     type: PlanType!
     coverage: Coverage!
     network: ProviderNetwork! @domain(name: "provider")
     claims: ClaimsHistory! @domain(name: "claims")
   }

   # Claims Domain
   type Claim {
     id: ID!
     memberId: ID!
     serviceDate: Date!
     provider: Provider!
     diagnosis: [Diagnosis!]!
     status: ClaimStatus!
   }
   ```

### API Composition Strategies

1. **REST Domain Composition**

   ```text
   # API Gateway Level
   GET /api/customer-orders/123    # Aggregates data from:
                                  # - /orders/{id}
                                  # - /customers/{customerId}
                                  # - /shipping/{orderId}
   ```

2. **GraphQL Domain Stitching**

   ```graphql
   # Schema Stitching
   type Order {
     id: ID!
     items: [OrderItem!]!
     customer: Customer! @resolver(domain: "customer-service")
     shipping: ShipmentInfo! @resolver(domain: "shipping-service")
   }
   ```

### Best Practices for Domain APIs

1. **Boundary Definition**
   - Align API boundaries with business domains
   - Use consistent naming across domains
   - Maintain separate versioning per domain

2. **Domain Integration**
   - REST: Use API composition at gateway
   - GraphQL: Implement schema stitching
   - Hybrid: Domain-specific protocol selection

3. **Consistency Patterns**
   - Use event-driven patterns for cross-domain updates
   - Implement CQRS where appropriate
   - Maintain domain-specific data ownership

## Use Cases

### REST Examples

1. **Benefits Enrollment Service**

   ```text
   POST /api/enrollments           # Submit enrollment
   GET /api/enrollments/{id}       # Check enrollment status
   PUT /api/enrollments/{id}       # Update dependent info
   GET /api/plans/{planId}         # Get plan details
   ```

2. **Claims Processing**

   ```text
   POST /api/claims                # Submit claim
   GET /api/claims/{id}/status     # Check claim status
   PUT /api/claims/{id}/documents  # Upload supporting documents
   GET /api/claims/history         # View claims history
   ```

### GraphQL Examples

1. **Plan Benefits Query**

   ```graphql
   query {
     healthcarePlan(id: "PPO-2024") {
       planName
       coverage {
         inNetwork {
           deductible
           outOfPocketMax
           coinsurance
         }
         outOfNetwork {
           deductible
           outOfPocketMax
           coinsurance
         }
       }
       benefits {
         type
         coverageLevel
         priorAuthRequired
       }
       providers {
         name
         specialty
         locations
       }
     }
   }
   ```

2. **Member Benefits Dashboard**

   ```graphql
   query {
     memberBenefits(memberId: "M123") {
       activePlans {
         planType
         effectiveDate
         coverageLevel
         dependents {
           name
           relationship
           coverageStatus
         }
       }
       claimsSummary {
         totalSubmitted
         pending
         processed
         yearToDateSpend
         remainingDeductible
       }
       eligibilityStatus
       networkProviders {
         nearby(distance: 10)
         inNetwork
       }
     }
   }
   ```

### Hybrid Examples

1. **Benefits Administration Platform**
   - REST for:

     ```text
     POST /api/enrollment/submit        # Process enrollment
     POST /api/dependents/verify        # Verify dependent eligibility
     PUT /api/coverage/update           # Update coverage options
     ```

   - GraphQL for:

     ```graphql
     query {
       benefitsSummary(employeeId: "E123") {
         availablePlans {
           name
           cost
           coverage
           comparisons
         }
         currentEnrollment {
           plan
           dependents
           payrollDeductions
           flexSpending
         }
         openEnrollment {
           deadline
           changes
           recommendations
         }
       }
     }
     ```

2. **Provider Network Management**
   - REST for:

     ```text
     POST /api/providers               # Add new provider
     PUT /api/network-status          # Update network status
     POST /api/credentials/verify     # Verify provider credentials
     ```

   - GraphQL for:

     ```graphql
     query {
       providerNetwork {
         provider(npi: "**********") {
           credentials
           specialties
           locations {
             address
             availability
           }
           acceptedPlans {
             planType
             networkTier
             contractStatus
           }
           qualityMetrics {
             patientSatisfaction
             outcomes
             costEfficiency
           }
         }
       }
     }
     ```

## Best Practices

1. **API Versioning**
   - REST: Use URL or header versioning
   - GraphQL: Schema versioning
   - Hybrid: Consistent versioning strategy across both

2. **Error Handling**
   - REST: HTTP status codes
   - GraphQL: Error object in response
   - Hybrid: Consistent error format

3. **Security**
   - Implement consistent authentication
   - Use HTTPS for all endpoints
   - Apply rate limiting uniformly

4. **Performance**
   - REST: Leverage HTTP caching
   - GraphQL: Implement field-level caching
   - Hybrid: Cache based on query patterns

## Migration Strategies

1. **REST to GraphQL**
   - Start with high-value complex queries
   - Maintain REST endpoints for simple operations
   - Gradually transition based on client needs

2. **GraphQL to Hybrid**
   - Identify performance-critical operations
   - Create REST endpoints for simple, frequent operations
   - Keep GraphQL for complex queries

## Conclusion

The choice between REST, GraphQL, or a hybrid approach should be based on:

- Data complexity and relationships
- Client requirements and diversity
- Performance needs
- Team expertise
- Maintenance considerations

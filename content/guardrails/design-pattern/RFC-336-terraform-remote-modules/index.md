<!-- Parent: Guardrails -->
<!-- Parent: Design Pattern -->
<!-- Title: RFC-336 Adopting Terraform Remote Modules for Infrastructure Management -->
# Adopting Terraform Remote Modules for Infrastructure Management

```text
Author: <PERSON>ues
Publish Date: 2024-11-01
Category: Guardrails
Subtype: Design Pattern
```

<!--
Required sections for ratification: 
-->

## Introduction

This RFC proposes the adoption of Terraform remote modules to streamline our infrastructure-as-code (IaC) practices. By creating and utilizing reusable, versioned Terraform modules stored in separate Git repositories, we aim to increase consistency, reduce code duplication, and improve collaboration across teams.

### Background

Currently, some of our Terraform codebase is organized with configuration files directly defining the infrastructure resources within each project. While effective for isolated deployments, this setup leads to:

- **Code Duplication**: Similar configurations are rewritten across multiple projects and environments.
- **Inconsistent Resource Configurations**: Lack of standardization results in resources being configured differently, leading to potential misconfigurations.
- **Increased Maintenance Effort**: Updates and patches must be applied individually to each project, consuming valuable time and resources.

By using remote Terraform modules stored in GitHub repositories, we can modularize reusable components (e.g., VPCs, IAM roles, S3 buckets) and promote consistency and maintainability across our infrastructure.

### Motivation

The key drivers for this change include:

- **Consistency**: Centralized modules ensure standardized setup across all projects.
- **Reusability**: Modularized configurations allow teams to reuse the same component across multiple environments (e.g., staging, production).
- **Version Control**: Versioned modules provide a stable interface, allowing safe upgrades and rollbacks.
- **Efficiency**: Simplifies management, enabling teams to focus on project-specific requirements rather than duplicating infrastructure code.
- **Isolation for Testing**: Modules can be tested independently, facilitating thorough validation without risking existing infrastructure stability.

### Benefits of Isolated Testing

- **Independent Testing**: Modules can be tested separately, enabling faster troubleshooting.
- **Simplified Integration Tests**: Known module behaviors reduce unexpected interactions.
- **Enhanced CI/CD**: Automated tests can provision and teardown resources without affecting other projects.

### Goals

- **Define a Module Structure**: Establish a standardized way to create, organize, and version Terraform modules.
- **Identify Best Practices**: Develop guidelines for using remote modules within different projects.

## Conceptual Overview

To better understand the role of Terraform child modules, consider the analogy to functions in software development:

1. **Reusability**:
   - **Child Modules** encapsulate reusable infrastructure components, similar to how functions encapsulate reusable code. For example, a VPC module can be applied in both `staging` and `production` environments with different input parameters.
   - This reduces duplication and enforces consistent configurations across projects.
2. **Idempotency**:
   - Just as idempotent functions produce the same result when called with the same inputs, **Terraform child modules should be idempotent**. Running `terraform apply` with unchanged inputs should not introduce unexpected changes.
   - This ensures predictable infrastructure management and minimizes the risk of unintended side effects.
3. **Parameterization and Encapsulation**:
   - **Child Modules** use input variables to enable customization without exposing internal details, much like how functions use parameters.
   - Teams can specify module behavior (e.g., CIDR blocks, subnet ranges) without modifying the module code itself.

Well-structured Terraform modules lead to:

- **Consistency**: Reduces errors from manual configurations across environments.
- **Ease of Maintenance**: Updating a central module updates all instances where it's used just by upgrading the version.
- **Predictable Changes**: Ensures idempotency, minimizing unexpected resource alterations.

## Pattern Structure

Each module will have its own dedicated Git repository that provides native versioning through tags.

Each module repository will contain:

- ***.tf files**: Configuration files defining resources.
- **provider.tf**: Defining necessary providers and version constraints.
- **variables.tf**: Input variables for parameterization.
- **outputs.tf**: Outputs exposed to the calling module if necessary.
- **README.md**: Documentation on usage, inputs, and outputs.
- **examples/**: Sample configurations demonstrating module usage.
- **mock/ or tests/**: Mocked environment and automated tests for validation.

### Example Child Module Structure

![terraform_child_module](./terraform_child_module.png)

### Example Usage in Mock Module

```go
module "database" {
  source = "../."

  name                        = "TEST_DB"
  comment                     = "THIS IS A TEST DATABASE"
  data_retention_time_in_days = 0
}
```

### Example Usage in Root Module

```go
module "database" {
  for_each   = { for _, database in local.database_config : database.database_name => database }

  source = "github.com/wexinc/ps-ds-platform-eng-snowflake-modules//modules/database?ref=1.0.0"

  name                        = each.value.database_name
  comment                     = each.value.comment
  data_retention_time_in_days = each.value.data_retention_days
}
```

### Module Maintenance and Lifecycle

1. **Versioning and Updates:**
   - Follow **semantic versioning** (major.minor.patch).
   - Test and validate before updating module consumers.
   - Implement a **deprecation policy** for outdated modules.
2. **Ownership and Contribution:**
   - Assign module ownership to specific teams.
   - Encourage contributions through guidelines and code reviews.

### Testing Strategy

1. **Testing Tools:**
   - **Terratest** for integration testing.
   - **tflint** and **terraform validate** for syntax and best practices.
   - **DeepSource** for security scanning.
   - **OPA** for policy testing.

## Use Cases Examples

1. **VPC Module for Environment Segmentation:**
   - Reuse across staging, development, and production environments.
2. **IAM Role Module for Variable Permissions:**
   - Customize roles with necessary permissions for different teams.
3. **RDS or S3 Module for Compliance and Consistency:**
   - Enforce encryption, logging, and tagging standards.

## Advantages and Disadvantages

- **Initial Setup Cost**: Requires time to modularize existing resources.
- **Dependency Management**: Must avoid circular dependencies and drift.
- **Learning Curve**: Teams need time to adapt to new workflows.

## Refactoring to Pattern

1. **Module Development:**
    - Identify and create modules for common components (e.g., S3, VPC, EC2 instances).
2. **Pilot Project:**
    - Implement the approach in a non-critical environment to gather feedback.
3. **Documentation and Training:**
    - Develop guides and conduct workshops for team onboarding.
4. **Gradual Migration:**
    - Migrate existing projects incrementally, monitoring for issues.
5. **Continuous Improvement:**
    - Collect feedback and refine modules and practices accordingly.

## Further Reading

- [terraform modules](https://developer.hashicorp.com/terraform/language/modules)
- [semantic versioning](https://semver.org/)
- [terratest](https://terratest.gruntwork.io/)
- [tflint](https://github.com/terraform-linters/tflint)
- [opa](https://www.openpolicyagent.org/docs/latest/terraform/)

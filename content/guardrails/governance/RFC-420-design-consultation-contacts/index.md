<!-- Parent: Guardrails -->
<!-- Parent: Governance -->
<!-- Title: RFC-420 Design Consultation Contacts -->

<!-- Include: macros.md -->
# Design Consultation Contacts

:draft:

```text
Author: <PERSON>
Title: Design Consultation Contacts
Publish Date: 2025-01-21
Category: Guardrails
Subtype: Governance
```

## Introduction

This document provides a comprehensive list of principal contacts, referred to as coaches, available to consult on high-level solution designs during the discovery phase of development. Each coach is responsible for specific areas of design consultation, ensuring that all aspects of the architecture, infrastructure, and services are managed effectively. These coaches must be consulted before initiating a formal design review with the iSARB governance body. This guide aims to facilitate communication and collaboration across different teams by providing clear points of contact for each domain.

> <!-- Info -->
> Info
> The content provided in this document is tentative and subject to change pending review and approval by leadership in Q1 2025. This disclaimer will be removed when all principal contact information has been confirmed and updated.

## Additional Resources

For a complete overview of the design collaboration process, please refer to [RFC15-Design Collaboration](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593984673/RFC15-Design+Collaboration).

For detailed information on the formal review process, please refer to [RFC12-Formal Review Process](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154594017461/RFC12-Formal+Review+Process).

### Responsibility Overlap

While each coach has a scope of responsibility, there is overlap in some scopes to ensure comprehensive coverage and redundancy. This overlap allows for multiple perspectives on complex issues, fostering collaboration and innovation. Additionally, it ensures that if one coach is unavailable, another can provide the necessary guidance, thereby minimizing delays and maintaining continuity in the consultation process.

## Information Security - Application Security

**Principal Contact:** David Silveira
**Secondary Contact:** Jason Langston
**Responsibilities:** Ensuring the security of application and data design, including identifying potential vulnerabilities, implementing security best practices, conducting regular security reviews, and ensuring compliance with WEX IT policy.

## Information Security - Security Operations

**Principal Contact:** Kyle Thomas
**Responsibilities:** Overseeing security operations, incident response, and ongoing security monitoring across WEX systems and infrastructure.

## Infrastructure Engineering - Cloud Engineering

**Principal Contact:** Dan Delauro
**Responsibilities:** Designing, implementing, and maintaining the cloud infrastructure and services that support WEX products, ensuring scalability, reliability, and alignment with cloud best practices.

## Infrastructure Engineering - Cloud Operations AWS

**Principal Contact:** Doug Roberts
**Responsibilities:** Managing and optimizing AWS cloud operations, ensuring reliability, performance, and cost-effectiveness of AWS-based infrastructure.

## Infrastructure Engineering - Global Tech Ops

**Principal Contact:** David Zevac
**Responsibilities:** Overseeing global technology operations and ensuring seamless operation of infrastructure across international locations.

## Infrastructure Engineering - Network Engineering

**Principal Contact:** John Foley
**Responsibilities:** Designing, implementing, and maintaining network infrastructure to ensure reliable connectivity, performance, and security across WEX environments.

## PaaS Engineering - Fabric

**Principal Contact:** Jered Philippon
**Responsibilities:** Maintaining and evolving the golden path of paved roads, integrating existing standards, and streamlining the provisioning and configuration of multiple paved road standards to ensure consistency, efficiency, and compliance across WEX systems.

## PaaS Engineering - Enterprise

**Principal Contact:** Vish Meduri
**Responsibilities:** Evaluating, adopting, and integrating enterprise technologies to ensure alignment with organizational goals, improve efficiency, and drive innovation.

## PaaS Engineering - CI-CD

**Principal Contact:** Erik Englund
**Responsibilities:** Designing, implementing, and maintaining Continuous Integration and Continuous Deployment (CI/CD) pipelines that are scalable, reliable, and reusable.

## Infrastructure Engineering - End User Engineering

**Principal Contact:** Adam Moreno
**Responsibilities:** Managing end user computing environments, including physical workstations, virtual desktops, software installations, and enterprise authentication through Active Directory.

## WEX Data Platform

**Principal Contact:** Mohamed Battisha
**Responsibilities:** Managing the canonical data warehouse and reporting platform, ensuring data integrity, accessibility, and providing insights through comprehensive reporting and analytics.

## Infrastructure Engineering - Database Administration

**Principal Contact:** Matthew Hemming (he | him)
**Responsibilities:** Managing and maintaining database systems, ensuring data integrity, performance optimization, backup and recovery, and compliance with data governance policies.

## AI Platform Architecture

**Principal Contact:** Ron Gonzalez
**Responsibilities:** Developing and maintaining AI policies and standards, ensuring ethical use, compliance, and integration of AI technologies within WEX systems.

## Corporate Payments Architecture

**Principal Contact:** Sapan Swain
**Secondary Contact:** Matt Rudd
**Responsibilities:** Designing and overseeing the architecture of solutions specifically for products within the Payments line of business, ensuring alignment with strategic objectives and technical standards.

## Benefits Product Architecture

**Principal Contact:** Umesh Asaigoli
**Secondary Contact:** Sonny Truong
**Responsibilities:** Designing and overseeing the architecture of solutions specifically for products within the Benefits line of business, ensuring alignment with strategic objectives and technical standards.

## Mobility OTR Product Architecture

**Principal Contact:** John Garbarino
**Secondary Contact:** Sumit Amar
**Responsibilities:** Designing and overseeing architecture solutions for the OTR (Over-the-Road) line of business, ensuring alignment with business objectives and technical standards.

## Mobility Fleet Architecture

**Principal Contact:** Heather Violette
**Secondary Contact:** Sumit Amar
**Responsibilities:** Designing and overseeing the architecture of solutions specifically for products within the Mobility line of business, ensuring alignment with strategic objectives and technical standards.

## Architecture - Principal DART Architect

**Principal Contact:** Derek Gilbert
**Responsibilities:** Designing and overseeing architecture solutions for the DART (Data, Analytics, Reporting and Tools) domain, ensuring data-driven solutions align with business needs and technical standards.

## Architecture - Architecture Evolution

**Principal Contact:** Phillip Jenkins
**Responsibilities:** Solutions that enable communication, collaboration, and governance processes at WEX.

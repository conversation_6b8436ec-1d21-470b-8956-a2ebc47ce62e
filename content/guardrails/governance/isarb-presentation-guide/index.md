<!-- Parent: Guardrails -->
<!-- Parent: Governance -->
<!-- Title: RFC-509 iSARB Presentation Guide -->

<!-- Include: macros.md -->
# iSARB Presentation Guide

:draft:

```text
Author: <PERSON> <PERSON>
Publish Date: 2025-05-14
Category: Guardrails
Subtype: Governance
```

## Introduction

This document provides guidance for individuals and teams preparing to present design proposals to the WEX Integrated Solutions and Architecture Review Board (iSARB). The goal is to help you effectively communicate your design, address key architectural concerns, and facilitate a smooth review process, ultimately leading to a successful outcome.

> **Info**
> The iSARB is primarily focused on ensuring that critical non-functional concerns are properly addressed in your design. While functional objectives provide important context for understanding your solution, the approval decision hinges on how effectively you've addressed security, reliability, scalability, compliance, and other architectural quality attributes.
>
> Familiarity with [RFC-12 Formal Review Process](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154594017461/RFC-12-Formal+Review+Process) is crucial, as it outlines the comprehensive requirements and procedures for iSARB submissions. This guide complements RFC-12 by focusing on the presentation aspect.

:toc:

## 🛠️ Before the Presentation: Preparation is Key

> **Tip**
> Thorough preparation is the foundation of a successful iSARB review. The following checklist will help ensure you've covered all essential aspects before your presentation.

* **Understand RFC-12:** Thoroughly review RFC-12 to understand the review conditions, design acceptance criteria, and overall process flow. This will ensure your design document and presentation cover all necessary aspects.

* **Leverage Office Hours and Coaches:** Take advantage of iSARB office hours and schedule informal design consultations with coaches early in your process. These sessions provide valuable guidance and can help identify potential issues before your formal presentation.

* **Complete Your Design Review Document:** Your presentation should be a concise summary of your high-level solution design. Ensure your design is complete, well-documented, and has been reviewed by your Tech Partner and relevant Coaches as per the process outlined in RFC-12.

* **Study Approved Designs:** Review approved designs in the [Designs RFC Folder](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155174174942/Designs). Pay attention to their structure, level of detail, clarity of diagrams, and how they address evaluation criteria.

* **Know Your Audience:** iSARB members represent various domains (Architecture, Global Technology, Information Security, etc.). Tailor your language to be clear and understandable to a diverse technical audience. Avoid overly niche jargon where possible, or be prepared to explain it.

* **Anticipate Questions:** Think about potential questions the board might ask regarding security, cost, alignment with WEX standards, operational impact, and alternatives considered. Prepare concise answers.

### Pre-presentation Checklist

* 🔲 Review RFC-12 requirements thoroughly  
* 🔲 Complete comprehensive design document  
* 🔲 Gather feedback from Tech Partner and Coaches  
* 🔲 Study approved designs  
* 🔲 Prepare presentation slides  
* 🔲 Rehearse within time constraints  

## ⏱️ Presentation Structure and Timing (20-Minute Slot)

A typical iSARB presentation slot is limited. Adhering to the following structure will help you cover the essential information effectively:

```mermaid
sequenceDiagram
    participant Presenter
    participant iSARB
    Note over Presenter,iSARB: 20-Minute Presentation Slot
    Presenter->>iSARB: Project Context & Purpose (5 min)
    Note right of iSARB: Executive Summary<br/>Business Need<br/>Scope & Impact
    Presenter->>iSARB: Proposed Solution & Evaluation Criteria (10 min)
    Note right of iSARB: Architecture<br/>Key Design Decisions<br/>Security & Compliance
    Presenter->>iSARB: Q&A Session (5 min)
    Note right of iSARB: Board Questions<br/>Clarifications<br/>Follow-ups
    iSARB-->>Presenter: Decision (Post-Presentation)
```

### 💼 Project Context & Purpose (Approx. 5 minutes)

* **Executive Summary:** Briefly state what the project is and what problem it solves.

* **Business Need/Opportunity:** Explain the "why" behind the proposal. What business drivers, pain points, or strategic objectives does this design address?

* **Scope:** Clearly define what is in and out of scope for this specific design.

* **Impacted Products/Teams:** Identify which WEX products, systems, or teams will be affected.

* **Key Stakeholders:** Mention the primary stakeholders involved.

* **Alignment with Strategic Objectives:** Briefly touch upon how this design aligns with WEX's broader strategic IT and business goals (refer to RFC-12, "Alignment with Strategic Objectives").

* **Tech Transformation Vectors:** Indicate which tech transformation vectors are expected to improve with this design and why

> **Tip**
> Present functional objectives concisely as essential context, not as the primary focus. While understanding what the solution does is important, remember that the iSARB vote is based on how well your design addresses non-functional concerns such as security, reliability, and compliance.
>
> Consider using AI (Copilot, Gemini, Atlassian Intelligence) to summarize your completed design proposal. This summary can be used as an outline for your presentation.

### 💡 Proposed Solution & Evaluation Criteria (Approx. 10 minutes)

This is the core of your presentation. Focus on how your design meets the iSARB's evaluation criteria as outlined in RFC-12 ("Design Acceptance Criteria").

> **Tip**
> While you should briefly outline functional aspects to provide context, concentrate the majority of your presentation time on non-functional requirements (security, reliability, scalability, compliance, etc.). These are the primary factors that will determine approval.

```mermaid
mindmap
  root((Successful<br/>Presentation))
    High-Level Architecture
      System Context Diagram
      Cloud Architecture Diagram
      Component Interaction
    Key Design Decisions
      Critical Choices
      Justifications
      Alternatives Considered
    Evaluation Criteria
      Product Security
        Authentication
        Data Protection
      Reliability
        HA/DR Strategy
      Standards Compliance
        Adherence
        Deviations
        Mitigations
```

* **High-Level Architecture:**
  * Present clear **System Context and Cloud Architecture Diagrams**. Ensure they are readable and accurately represent the solution.
  * Explain the main components and their interactions.

* **Key Design Decisions & Justifications:**
  * Highlight critical design choices.
  * Explain *why* these choices were made.
  * **Alternatives Considered:** Briefly discuss other options you evaluated and why the proposed solution is superior.

* **Addressing Key Evaluation Criteria:**

  * 🛡️ **Product Security:**
    * **Authentication & Authorization:** Detail mechanisms for WEX employees, customers, and service principals.
    * **Data Ingress/Egress:** Explain network traffic patterns, security controls (e.g., WAF, encryption).
    * **Data Compliance:** Specify data classification (Class 1, 2, 3), and how it's protected at rest, in transit, and in use. Address PII, PHI, PCI if applicable.
    * **Secure Coding (if applicable):** Mention technology stack and security controls in the development lifecycle.

  * 🔄 **Reliability:**
    * **High Availability (HA):** Describe HA controls (e.g., redundancy, failover).
    * **Disaster Recovery (DR):** Describe DR strategy.

  * 📊 **Standards & Compliance:**
    * **Compliance with WEX IT Policy:** Confirm adherence.
    * **Adoption of Standards:** State how the design adheres to WEX technical standards (canonical systems, reference architectures, etc.).
    * **Deviation from WEX Standards:** If any, clearly state the deviation, provide a robust justification, and outline any mitigation or remediation plans.
    * **Logging & APM:** Describe how logging and monitoring (e.g., Splunk, DataDog) are incorporated.

  * ☁️ **Cloud & SaaS Components (if applicable):**
    * Detail key cloud services or SaaS components and integration points.
    * Address security controls specific to cloud/SaaS implementation.

  * 🤖 **AI/ML Components (if applicable):**
    * Address compliance and data protection of AI/ML components.
    * Explain model governance, training data considerations, and monitoring approach.

  * 📋 **Additional Context:**
    * **Risk Analysis & Dependencies:**
      * Summarize key risks (technical, security, operational), their potential impact, and mitigation strategies.
      * List critical assumptions and system dependencies.
    * **New Third-Party Software (if applicable):**
      * Specify if it's a PoC, Pilot, or Approved Usage.
      * Confirm engagement with Finance and completion of risk assessments.
      * Be prepared to summarize build/buy/partner decisions.
    * **Delivery Timeline:** Briefly present key milestones.

### ❓ Follow-up Questions (Approx. 5 minutes)

* Be prepared to answer questions from the board members.

* Listen carefully to the questions and provide clear, concise answers.

* If you don't know an answer, it's okay to say so and offer to follow up.

> **Note**
> Common pitfalls during Q&A include:
>
> * Becoming defensive when challenged on design choices
> * Providing overly complex explanations that confuse the board
> * Making promises that haven't been vetted by your team
> * Not acknowledging valid concerns raised by board members

## ⭐ Presentation Best Practices

* **Clarity and Conciseness:** Focus on the most critical information. Avoid getting bogged down in excessive detail that is already in your RFC document.

* **Visual Aids:**
  * Use simple, clear slides. Avoid clutter and excessive text.
  * Ensure diagrams are large enough to be easily read and understood. The diagrams in the approved Design RFCs are good references.

* **Tell a Story:** Structure your presentation logically to guide the audience through the problem, your proposed solution, and its benefits.

* **Confidence and Ownership:** Be confident in your design and demonstrate a thorough understanding of it.

* **Engage Your Tech Partner/Coaches:** They are there to support you. Ensure they have reviewed your presentation beforehand.

* **Practice:** Rehearse your presentation to ensure it fits within the allotted time and flows smoothly.

### Presentation Day Checklist

* 🔲 Confirm presentation slot time and location  
* 🔲 Prepare concise answers to anticipated questions  
* 🔲 Time your presentation during rehearsal  

## 🏁 Post-Presentation

* Be prepared to address any follow-up actions or requests for additional information from the iSARB.

* The Committee Coordinator will communicate the review decision.

> **Info**
> If your design is not approved, understand that the iSARB evaluates designs primarily on non-functional criteria. Rejection or conditional approval typically indicates insufficient attention to security, reliability, compliance, or other architectural quality attributes rather than functional concerns.
>
> Remember that the iSARB process is designed to improve the overall quality and alignment of IT solutions at WEX. Even if your design requires revisions, the feedback provided is valuable for creating a stronger end result.

```mermaid
sequenceDiagram
    participant Presenter as You
    participant iSARB as Review Board
    participant Coordinator as Committee Coordinator
    
    Note over Presenter,iSARB: After Presentation
    iSARB ->> Coordinator: Deliberation & Decision
    Coordinator -->> Presenter: Communicates Decision
    
    alt Approved
        Presenter ->> Presenter: Proceed with Implementation
    else Approved with Conditions
        Presenter ->> Presenter: Address Conditions
        Presenter ->> Coordinator: Submit Evidence
        Coordinator -->> Presenter: Document Evidence Collection
    else Rejected
        Presenter ->> Presenter: Revise Design
        Presenter ->> Coordinator: Schedule New Review
    end
```

<!-- Parent: Guardrails -->
<!-- Parent: Governance -->
<!-- Title: RFC-434 iSARB Membership -->
# iSARB Membership

```text
Author: <PERSON>
Title: iSARB Membership
Publish Date: 2025-01-30
Category: Guardrails
Subtype: Governance
```

## Introduction

The iSARB Membership page provides an overview of the current members, their roles, and responsibilities within the organization. This document serves as a reference for understanding the structure and key contacts within the iSARB, ensuring effective communication and collaboration across various architectural domains.

## Board Composition

- **Chair / Co-chair:** Responsible for setting the vision & strategy, presiding over meetings, establishing priority, and providing tie-breaking votes
- **Committee Coordinator:** Responsible for managing meeting agendas, emergency escalation, formal communications from iSARB to WEX employees, drafting iSARB material for leadership reporting, and managing current lists for membership, notifications, coaches, and tech partners
- **Coaching Member:** Responsible for advising Technical Architect in design consultations and communicating design confidence to organizational Voting Member
- **Tech Partner:** Responsible for ensuring that a high-level solution design is known, well-understood, and has completed informal design consultations before proceeding with a formal review
- **Voting Member:** Responsible for collaborating with related Coaching Members and vote accordingly to approve or reject high-level solution designs.

> <!-- Info -->
> Voting and Coaching members are representatives of WEX stakeholder organizations, including Architecture, Global Technology, Information Security, Tech Services, Digital Channels, AI, Mobility Architecture, Corporate Payments Architecture, and Benefits Architecture.

## Additional Resources

For a complete overview of the design collaboration process, please refer to [RFC15-Design Collaboration](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593984673/RFC15-Design+Collaboration).

For detailed information on the formal review process, please refer to [RFC12-Formal Review Process](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154594017461/RFC12-Formal+Review+Process).

For specific information on coaches available for design consultations, please refer to [RFC-420 Design Consultation Contacts](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155035730254/RFC-420+Design+Consultation+Contacts).

For specific information on iSARB Tech Partners for each organization, please refer to [RFC-421 iSARB Tech Partners](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155036287549/RFC-421+iSARB+Tech+Partners).

## Membership

- **Chair:** Nick Krosschell
- **Co-Chair:** Terry Delmonte
- **Committee Coordinator:** Adriana Rodrigues

> <!-- Info -->
> Info
>
> - There are 12 voting members which requires 8 to be present for a quorum necessary for passing any motions.
> - The Chair and Co-Chair are available for tie-breaking votes

### Architecture

- **Voting Member:** Phillip Jenkins (Architecture - Architecture Evolution)
- **Coaching Member:** Umesh Asaigoli (Architecture - Principal Benefits Architect)
- **Coaching Member:** Sapan Swain (Architecture - Principal CPS Architect)
- **Coaching Member:** Heather Violette (Architecture - Principal NAF Architect)
- **Coaching Member:** John Garbarino (Architecture - Principal OTR Architect)
- **Coaching Member:** Derek Gilbert (Architecture - Principal DART Architect)

**Sponsor:** Nick Krosschell

### Infrastructure Engineering

- **Voting Member:** Edward Taggart (Infrastructure Engineering - Cloud Services)
- **Coaching Member:** David Zevac (Infrastructure Engineering - Global Tech Ops)
- **Coaching Member:** John Foley (Infrastructure Engineering - Network Engineering)
- **Coaching Member:** Adam Moreno (Infrastructure Engineering - End User Engineering)
- **Coaching Member:** Matt Hemming (Infrastructure Engineering - Database Administration)
- **Coaching Member:** Dan DeLauro (Infrastructure Engineering - Cloud Engineering)
- **Coaching Member:** Doug Roberts (Infrastructure Engineering - Cloud Operations AWS)

**Sponsor:** Terry Delmonte

### Information Security

- **Voting Member:** Jason Langston (Information Security - Application Security)
- **Coaching Member:** Kyle Thomas (Information Security - Security Operations)
- **Coaching Member:** David Silveira (Information Security - Application Security)

**Sponsor:** Michael Leonhirth

### PaaS Engineering

- **Voting Member:** Vish Meduri (PaaS Engineering - Enterprise)
- **Coaching Member:** Jered Philippon (PaaS Engineering - Fabric)
- **Coaching Member:** Erik Englund (PaaS Engineering - CI-CD)

**Sponsor:** Christian Oestreich

### Tech Services

- **Voting Member:** Jim Brady (Employee Tech Services)

**Sponsor:** Jim Brady

### Digital Channels

- **Voting Member:** Alexandre Page-Relo

**Sponsor:** Barry Driscol

### AI Platform Architecture

- **Voting Member:** Ron Gonzalez

**Sponsor:** Zijian Zheng (ZJ)

### Site Reliability Engineering

- **Voting Member:** Gyan Trivedi

**Sponsor:** Barry Driscol

### Mobility Architecture

- **Voting Member:** Sumit Amar

**Sponsor:** Ian Peters-Campbell

### Corporate Payments Architecture

- **Voting Member:** Matt Rudd

**Sponsor:** Ian Peters-Campbell

### Benefits Architecture

- **Voting Member:** Sonny Truong

**Sponsor:** Prashant Desale

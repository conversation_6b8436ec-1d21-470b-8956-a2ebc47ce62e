<!-- Parent: Guardrails -->
<!-- Parent: Governance -->
<!-- Title: RFC12-Formal Review Process -->

<!-- Include: macros.md -->
# Formal Review Process

:draft:

```text
Author: Integrated Solutions and Architecture Review Board (iSARB)
Title: Formal Review Process
Publish Date: 2024-08-19
Category: Guardrails
Subtype: Governance
```

## Executive Summary

> <!-- Info -->
> Note
> This document has not been formally ratified yet.

This document describes how formal technical reviews of high-level solution designs are handled at WEX. Formal reviews are required evaluations by the Integrated Solutions and Architecture Review Board (iSARB) which must be approved when specific design conditions exist before a solution can be implemented.

Technical review and audit serve as governance controls and opportunities for broad collaboration at WEX. Teams should be agile and innovate within their system boundaries with minimal barriers. However specific types of changes do require a review to evaluate conformance of applicable policies, standards, practices, plans, specifications, and procedures.

### Table of Contents

:toc:

### Goals

This process prioritizes the following concerns for formal technical reviews:

- Portable technical documents for collaboration
- A single streamlined process for formal solution design reviews
- Clarity on when a review is required and what is needed for approval
- Reducing capacity demands on individual SMEs for review preparation assistance
- Early engagement of relevant stakeholders on solution design and standards
- Participation from leaders across WEX IT
- Enable teams to innovate and be autonomous while also ensuring compliance with IT policies, standards, and strategic objectives

## 🏛️ Board Composition

- **Chair / Co-chair:** Responsible for setting the vision & strategy, presiding over meetings, establishing priority, and providing tie-breaking votes
- **Committee Coordinator:** Responsible for managing meeting agendas, emergency escalation, formal communications from iSARB to WEX employees, drafting iSARB material for leadership reporting, and managing current lists for membership, notifications, coaches, and tech partners
- **Coaching Member:** Responsible for advising Technical Architect in design consultations and communicating design confidence to related Voting Member
- **Tech Partner:** Responsible for ensuring that a high-level solution design is known, well-understood, and has completed informal design consultations before proceeding with a formal review
- **Voting Member:** Responsible for collaborating with related Coaching Members and vote accordingly to approve or reject high-level solution designs

Voting and Coaching members are representatives of WEX stakeholder organizations, including Architecture, Global Technology, Information Security, Tech Services, Digital Channels, AI, Mobility Architecture, Corporate Payments Architecture, and Benefits Architecture.

> <!-- Info -->
> Info
> Refer to [RFC-420 Design Consultation Contacts](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155035730254/RFC-420+Design+Consultation+Contacts) for the current list of coaches for design consultations
>
> Refer to [RFC-434 iSARB Membership](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155073609787/RFC-434+iSARB+Membership) for the current list of representatives on the board.
>
> Refer to [RFC-421 iSARB Tech Partners](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155036287549/RFC-421+iSARB+Tech+Partners) for the current list of Tech Partners for each organization.

### Notification groups

Google contact distribution groups have been created to facilitate awareness and transparency of board activities. Interested parties may request to be notified when a design is ready for review and/or when a design has been approved.  Added individuals will be included as optional attendees for review meetings and scheduled office hours.

Contact the Committee Coordinator if you wish to be notified of board activities.

## ⚙️ Process Flow

The process flow described here is intended to complement and enhance a team's existing design process. It provides a structured approach to ensure that any formal governance reviews are conducted efficiently and effectively.

```mermaid
%% Mermaid sequence diagram for formal design review process
%% Represents stakeholders, responsibilities, and tasks
sequenceDiagram
    participant Team
    participant Coaches
    participant TechPartner as Tech Partner
    participant iSARB
    participant Coordinator as Committee Coordinator

    TechPartner-->>Team: May facilitate Technical Architect to draft design
    Team->>Coaches: Engage for Informal Design Consultations
    Coaches-->>Team: Provide Feedback
    Coaches->>Team: Ensure Design Addresses Architectural Concerns
    Team->>TechPartner: Inform and Update
    TechPartner-->>Team: Confirm Design is Ready for Formal Review
    Team->>iSARB: Submit Design for Review
    iSARB-->>Coaches: Solicit design feedback
    iSARB->>iSARB: Vote to Approve or Reject Design
    iSARB->>Coordinator: Notify Results
    Coordinator-->>Team: Communicate Review Decision
    
    alt Design Rejected
        Team->>Coaches: Re-engage to Address Issues
        Coaches-->>Team: Help Refine Design
        Team->>TechPartner: Review Refined Design
        TechPartner-->>Team: Approve Updated Design
        Team->>iSARB: Resubmit Revised Design
        iSARB->>iSARB: Re-evaluate Design
        iSARB->>Coordinator: Notify Updated Results
        Coordinator-->>Team: Communicate Final Decision
    end
```

The steps in this process include:

1. **Design Review Preparation**: A solution design document is prepared which includes all necessary information about architectural concerns.  This design is evaluated to confirm the design is satisfies all relevant standards, requirements, and IT policies.

2. **Collaboration and Engagement**: Early and ongoing engagement is crucial. Teams are strongly encouraged to utilize the weekly iSARB Coaches Office Hours for consolidated feedback from multiple coaching disciplines. Adhoc informal design consultations with coaches are an option if deeper dives are needed outside of this forum.

3. **Leadership Alignment**: The process requires that teams engage with their organizational Tech Partner to ensure leadership is informed, supportive, and confident that all architectural concerns have been addressed through design consultations with Coaches.

4. **Submitting Review Requests**: A design review can be requested via the Employee Service Portal once the solution design is complete and ready for review. Refer to [iSARB Home](https://wexchange.wexinc.com/home/<USER>

5. **Formal Review and Approval**: The process outlines the steps for formal review and approval. Conditional Approval may be granted by iSARB if the design meets architectural acceptance criteria but is pending the successful completion of concurrent external reviews (e.g., final procurement, security risk assessment for new third-party software). The solution cannot be implemented until all contingencies are met and final approval is confirmed. If a design is not approved (or conditions are not met), it must either be changed or abandoned.

### Collaboration and Engagement

It is critical that informal design discussions occur during the discovery and design phases to ensure optimal efficiency. These informal reviews should identify any conditions in the design that would necessitate a formal review. Additionally, they should help draft all necessary documents required for the formal review. This proactive approach ensures that the formal governance review process is smooth and well-prepared.

> <!-- Info -->
> Info
> Refer to [RFC15-Design Collaboration](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593984673) for more detailed information on design consultations with coaches and technical walkthroughs.
>
> Refer to [RFC-420 Design Consultation Contacts](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155035730254/RFC-420+Design+Consultation+Contacts) for the current list of coaches available for design consultations.

### Leadership Alignment

It is essential to inform the Tech Partner for your organization about the project, indicating that a governance design review will be required. Additionally, ensure that the status of relevant design consultations with coaches is communicated clearly.

- Notify the Tech Partner about the project and the upcoming governance design review.
- Provide updates on the status of design consultations with coaches to ensure all necessary preparations are in place.

> <!-- Info -->
> Info
> Refer to [RFC-421 iSARB Tech Partners](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155036287549/RFC-421+iSARB+Tech+Partners) for the current list of iSARB Tech Partners for each organization.

### Submitting Review Requests

Refer to [iSARB Home](https://wexchange.wexinc.com/home/<USER>

Solution and Security Architects routinely audit prioritized development work in Jira and Azure DevOps as a verification measure to ensure relevant design consultations and formal reviews are initiated when appropriate.

### Formal Review and Approval

Voting members are notified about upcoming reviews via iSARB chat space and iSARB meeting attachments.  Voting members should solicit design feedback on solution designs from coaches in their organization ahead of iSARB meetings.

Voting members should base their votes on the overall value to WEX and specific, concrete concerns related to one or more of the evaluation criteria outlined in this document. Ensure that your decision is informed by the design's compliance with WEX IT policies, alignment with strategic objectives, and adherence to secure coding practices, among other criteria.

Reviews may be conducted through an asynchronous vote instead of a meeting presentation depending on the complexity of the solution.  Contact the Committee Coordinator for questions on asynchronous reviews.

> <!-- Note -->
> Warning
> If a design is not approved it must either be changed or abandoned.  If the altered design no longer contains formal review conditions, then a formal review of the new design is not required.

## 🔍 Review Conditions

A formal governance review is required for:

- Changes to Standards
- Solution Designs that include new third-party software
- Solution Designs that include significant architecture changes

```mermaid
mindmap
    root((Conditions))
        New Third-Party Software
            Proof-of-Concept
            Pilot
            Approved Usage
        Architecture Changes
            Technology
            Long Projects
            HA/DR
            New Patterns
            New Product/Capability
            WEX Product Integrations
            Accessibility
            Standards
        Standards Changes
            Canonical Systems
            Reference Architectures
            Design Specifications
            Design Patterns
            Design Practices
            Delivery Practices
            Support Practices
```

### 📦 New Third-Party Software

When introducing new third-party software into the solution architecture, a formal governance review is required to ensure compliance with WEX's technical policies, standards, and best practices.

Designs that incorporate new third-party software fall under one of three categories:

1. Proof-of-Concept (PoC)
2. Pilot
3. Approved Usage

> <!-- Info -->
> Note
> Note that approval for third-party software does not establish a standard for future use.  Adoption of a new or changed standard requires a separate review and should be considered only after a successful limited rollout.
>
> For designs involving new third-party software, the formal iSARB review of the solution architecture may proceed concurrently with Finance, Procurement, and Information Security risk assessment processes. iSARB approval in such cases may be conditional upon the favorable outcome of these external reviews. This allows architectural planning to advance while due diligence is performed by respective teams.

#### Types of Third-Party Software

iSARB reviews third-party software that interfaces with or impacts WEX's systems, network infrastructure, or data architecture. This includes but is not limited to:

- Cloud services and SaaS applications
- Server applications and middleware
- Databases and data processing tools
- Development frameworks and libraries
- API services and integrations
- Network and security software

> <!-- Info -->
> Note
> **Desktop Applications and Browser Extensions** are not reviewed by iSARB. These software types still require Risk Assessment and undergo external reviews by End User Computing and Information Security teams before approval for use within the WEX environment.

When proposing third-party software that falls outside of iSARB's purview, teams should follow the process for [requesting new software](https://wexinc.atlassian.net/servicedesk/customer/portal/6/article/154096108088).

All third-party software, regardless of type, must comply with WEX's procurement policies and information security standards.

#### Proof-of-Concept

A **preliminary evaluation** phase where new software is tested as part **of an artificial solution** to determine its feasibility and potential impact on the existing architecture. This phase requires a formal review to assess risks, compatibility, and alignment with WEX's strategic goals. The proof-of-concept should be isolated from production systems and data to prevent any unintended impact. This may require the generation or sanitization of test data to ensure a safe and controlled testing environment.

> <!-- Info -->
> Note
> Solution designs for proof-of-concept solutions have less stringent acceptance criteria for Authentication, High Availability, and Disaster Recovery concerns. While these elements may be simplified or not fully implemented in a PoC design, they must be completely addressed before the solution can be approved for production workloads. Any PoC selected for transition to production must return to iSARB for approval with a complete production design that fully addresses these requirements.
>
> Proof-of-concept designs should include the success criteria for the project.

#### Pilot

A **limited deployment** of new software in a controlled production environment as part **of a real-world solution** to validate its performance, scalability, and integration with other systems. A formal review is necessary to ensure that the pilot meets all technical and security requirements before broader implementation.

> <!-- Info -->
> Note
> Solution designs for Pilots of new software have less stringent acceptance criteria for High Availability, and Disaster Recovery concerns. While these elements may be simplified or not fully implemented in a Pilot design, they must be completely addressed before the solution can be approved for broader production workloads. All successful pilots must return to iSARB with a complete production design if these concerns were not included in the Pilot design or significant changes to the approved Pilot design are identified.
>
> Pilot designs should include the success criteria for the project.

#### Approved Usage

A **formal authorization** phase where new software is approved for use within a **defined scope or purpose**. This phase requires a formal review to ensure that the software meets all necessary technical, security, and compliance requirements.

### 🏗️ Architecture Changes

Significant changes to a product's architecture must undergo a formal design review to ensure that the modifications align with organizational goals, technical standards, and best practices.

A formal design review is required if the design contains:

- Core Tech Stack Changes
- Projects with significant implementation (>= 6 months)
- HA/DR Strategy Changes
- New Pattern Adoption
- New Product or Capability
- WEX Product Integrations
- Accessibility Change (Internal/External)
- Deviation from WEX Standards

#### Core Tech Stack Changes

Changes to the core technology stack require a formal review to ensure alignment with WEX's policies and standards. This includes modifications to programming languages, frameworks, databases, and cloud services.

> <!-- Info -->
> Info
> Examples include:
>
> - **Programming Languages**: Introducing a new language like Rust for performance-critical components.
> - **Database Management Systems**: Migrating from MySQL to PostgreSQL for better scalability.
> - **Cloud Services**: Using a new AWS or Azure native cloud service type
> - **Frameworks**: Adopting a new web framework like Next.js for better performance and developer experience.

#### Projects with significant implementation (>= 6 months)

Significant projects or initiatives that span six months or more require a formal review to ensure alignment with WEX's strategic goals and technical standards.

> <!-- Info -->
> Info
> Examples include:
>
> - **Enterprise-Wide Application Development**: Developing a new enterprise-wide application to streamline operations.
> - **Customer Management System Overhaul**: Overhauling the existing customer management system to enhance user experience and scalability.

#### HA/DR Strategy Changes

Changes to High Availability (HA) or Disaster Recovery (DR) strategies need a formal review to ensure that they meet WEX's resilience and recovery requirements.

> <!-- Info -->
> Info
> Examples include:
>
> - **New Disaster Recovery Site Implementation**: Implementing a new DR site to enhance system resilience.
> - **Critical System HA Design Change**: Modifying the existing design for critical systems to improve uptime.

#### New Pattern Adoption

Adopting new design or architectural patterns that have not been used before at WEX requires a formal review to ensure they align with WEX's best practices and standards.

> <!-- Info -->
> Info
> Examples include:
>
> - **Digital Twin Architecture**: Implementing digital twin pattern to create virtual replicas of physical systems for advanced simulation, monitoring, and predictive analysis.
> - **Edge Computing Pattern**: Deploying computation and data storage closer to the data source to reduce latency and bandwidth use for IoT applications.

#### New Product or Capability

Introducing new products or capabilities into the WEX ecosystem requires a formal review to ensure they meet technical, security, and compliance standards.

> <!-- Info -->
> Info
> Examples include:
>
> - **Mobile Payment Solution Launch**: Introducing a new mobile payment platform that enables customers to make secure transactions through smartphones and wearable devices, requiring evaluation of security protocols, integration capabilities, and compliance with payment industry standards.
> - **AI-Driven Analytics Platform**: Building a new artificial intelligence system that analyzes customer transaction data to provide insights on spending patterns and fraud detection, requiring evaluation of data processing capabilities, machine learning framework compatibility, and privacy compliance.

#### WEX Product Integration

Enabling new integrations between WEX products requires a formal review to ensure seamless and secure integration.

> <!-- Info -->
> Info
> Examples include:
>
> - **Real-Time Fuel Expense Tracking and Route Optimization**: Integrating a fuel card payment system with a fleet management application to enable real-time tracking of fuel expenses and optimize route planning based on fuel consumption data.
> - **Streamlined Health and Wellness Expense Reimbursement**: Connecting a corporate expense management system with an employee benefits platform to streamline the reimbursement process for health and wellness expenses, ensuring compliance with company policies.
> - **Mobile App for Commuting Benefits and Ride-Sharing Services**: Developing a mobile app that combines transportation benefits with ride-sharing services, allowing employees to use pre-tax dollars for commuting expenses while tracking their usage and remaining benefits balance.
> - **Unified Dashboard for Comprehensive Expense and Benefits Analysis**: Creating a unified dashboard that aggregates data from Mobility, Corporate Payments, and Benefits systems to provide a comprehensive view of employee expenses, benefits utilization, and travel patterns, helping organizations make informed decisions on cost-saving measures and employee satisfaction initiatives.

#### Deviation from WEX Standards

Any deviation from established WEX technical standards requires a formal review to assess the impact and ensure that exceptions are justified and documented.  

When deviating from established WEX technical standards, the documented justification should include the following key pieces of information:

1. **Rationale for Deviation**: Clearly explain why the deviation is necessary and how it benefits the project or organization.
2. **Impact Analysis**: Assess the potential impact of the deviation on the existing systems, processes, and stakeholders.
3. **Risk Assessment**: Identify and evaluate any risks associated with the deviation, including security, compliance, and operational risks.
4. **Mitigation Strategies**: Outline the steps that will be taken to mitigate identified risks and ensure the deviation does not negatively affect the overall system.
5. **Stakeholder Approval**: Document the approval from relevant stakeholders, including any governance bodies or committees.
6. **Implementation Plan**: Provide a detailed plan for implementing the deviation, including timelines, resources, and responsibilities.
7. **Monitoring and Review**: Describe how the deviation will be monitored and reviewed to ensure it remains effective and aligned with organizational goals.

> <!-- Info -->
> Info
> Examples include:
>
> - **Tool Deviation:** Deviating from the standard Artifactory service for package management
> - **Service Deviation:** Deviating from the standard Tokenizer service for tokenization
> - **Reference Architecture Deviation:** Deviating from the standard GoAnywhere SFTP reference architecture
> - **Design Specification Deviation:** Deviating from the standard REST API design specification

#### Accessibility Change (Internal/External)

Changes to an application's accessibility, whether making it available to external WEX customers and vendors or restricting it to internal WEX employees, require a formal review to confirm compliance with security and data protection policies, especially regarding sensitive payment data.

> <!-- Info -->
> Info
> Examples include:
>
> - **Internal Payment Portal Extension**: Extending an internal payment processing portal to be accessible by authorized merchant partners, requiring thorough evaluation of PCI DSS compliance measures and implementation of additional controls to protect cardholder data.
> - **Fleet Card Management System Exposure**: Modifying a previously internal-only fleet card management system to be accessible to external fleet managers, necessitating review of PII protection mechanisms and segmentation of sensitive payment data.
> - **Healthcare Payment Processing API**: Creating an external API for a previously internal-only healthcare payment processing system, requiring comprehensive review of PHI data handling protocols and ensuring HIPAA compliance in the new access model.
> - **Vendor Payment Portal Restriction**: Converting a vendor-accessible payment portal to internal-only use after identifying potential PCI compliance risks, requiring review of authentication mechanisms and data access controls.

### 📐 Standards Changes

> <!-- Info -->
> Info
> Refer to [RFC14-Technical Standards Taxonomy](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593657230/RFC14-Technical+Standards+Taxonomy) for detailed information on the different types of standards at WEX, enforcement levels, and scopes of responsibility.

A formal review and ratification is required when adopting, replacing, or revoking a standard. Standards available for consideration include:

- **Canonical Systems**: Standardized, approved components such as common tools, services, and built artifacts.
- **Reference Architectures**: Standardized templates or blueprints that guide the design and implementation of systems within a specific domain.
- **Design Specifications**: Detailed requirements that outline the necessary criteria and constraints for the design and implementation of a technical solution.
- **Design Patterns**: Reusable abstract solutions to commonly occurring problems in software design.
- **Design Practices**: Systematic approaches that guide developers in creating well-structured, maintainable, and efficient software systems.
- **Delivery Practices**: Systematic approaches that guide teams in planning, executing, and delivering software projects.
- **Support Practices**: Systematic approaches that guide teams in maintaining, troubleshooting, and enhancing software systems post-deployment.

## ⚖️ Design Acceptance Criteria

High-level designs and standards are evaluated based on specific, concrete concerns to remove subjectivity and provide clarity for teams drafting designs. This ensures that all designs meet WEX's strategic goals, technical standards, and compliance requirements, facilitating a consistent and transparent review process.

```mermaid
mindmap
  root((Criteria))
    Product Security
      Authentication
      Authorization
      Data Compliance
      Data Ingress / Egress
    Reliability
      High Availability
      Disaster Recovery
    AI / ML
      Fundamental
      Advanced
      Integrated
      GenAI
    SaaS
      WEX IT Policy
      Strategic Objectives
      Standards Adoption
      Logging & APM
```

### 🛡️ Product Security

#### Authentication & Authorization

> <!-- Info -->
> Info
> The standard for WEX employee authentication is Okta Workforce Identity, which can be integrated using Active Directory, Microsoft Entra, and Identity IQ SAML SSO. This integration ensures secure and efficient management of user identities and access controls.
>
> The standard for WEX customer authentication is Okta Customer Identity (CIC). This service provides secure and scalable authentication for WEX customers, ensuring that their identities are managed separately from internal WEX employee identities.

Detail the authentication and authorization mechanisms. Ensure they meet WEX's security standards and best practices.

- **Standards Adoption**: Utilize standardized authentication protocols and frameworks to ensure consistency and security across all systems.
- **Managed Service Accounts**: Implement managed service accounts to control access and automate account management processes.
- **Multi-Factor Authentication (MFA)**: Enforce MFA to add an extra layer of security, requiring users to provide multiple forms of verification.
- **Single Sign-On (SSO)**: Use SSO to streamline the authentication process, allowing users to access multiple applications with a single set of credentials.
- **Auto-Deprovisioning**: Ensure that accounts are automatically deactivated when no longer needed, reducing the risk of unauthorized access.

> <!-- Info -->
> Info
> If standard integrations are available they should be used. Teams must be prepared to speak to any deviations from these standards.
>
> **Example:** Do not assume local identity management is acceptable for a small number of users when SSO SAML integration is available.

#### Data Ingress / Egress

Ensure that the high-level solution design includes detailed information about the cloud or data center networks, network security components, and network connectivity requirements. Additionally, specify whether protected data is stored, transmitted, or accessed within the network. This information is crucial for assessing the security and compliance of the solution.

#### Data Compliance

Ensure that the design indicates whether Class 1 (Confidential) and Class 2 (Internal) data is present and how it is protected at-rest, in-transit, and in-use. The design must be compliant with all relevant data protection regulations and WEX's data governance policies.

> <!-- Info -->
> Info
>
> **Class 1 (Confidential):** Sensitive information that, if disclosed to unauthorized parties, would likely cause significant harm to WEX.  This includes PII, PHI, and PCI data.  It also includes source code, specifications, detailed network diagrams, and information about WEX-owned systems.
> **Class 2 (Internal):** Information that, if disclosed to unauthorized parties, would likely have a minor and/or short term negative impact to WEX.  This includes WEX policies, standards, procedures, market analysis, budgets, phone numbers, or other information not intended for public viewing.
> **Class 3 (Public):** Information that if disclosed will not cause any harm to WEX.  This includes publicly posted marketing information, press releases, or other information that has been intentionally published for public consumption.

### 🔄 Reliability

#### High Availability & Disaster Recovery

Ensure that the design incorporates robust availability and disaster recovery strategies to maintain system resilience and minimize downtime. Consider the following approaches:

- **Multi-Region Active-Active**: Deploy the application across multiple geographic regions in an active-active configuration. This ensures that if one region fails, the other regions can continue to serve traffic without interruption.
- **Availability Zone Redundancy**: Utilize multiple availability zones within a region to distribute the application components. This provides protection against failures in a single availability zone.
- **Automatic Regional Failover**: Implement automatic failover mechanisms to redirect traffic to a healthy region in case of a regional outage. This can be achieved using DNS-based routing, load balancers, or cloud-native services.
- **Data Replication**: Ensure that data is replicated across regions and availability zones to prevent data loss and enable quick recovery. Use database replication, object storage replication, or other data synchronization methods.
- **Monitoring and Alerts**: Implement comprehensive monitoring and alerting to detect and respond to failures promptly. Use tools and services that provide real-time insights into system health and performance.
- **Failover Testing**: Periodically test failover mechanisms to ensure they work as expected. Simulate regional outages and validate that the system can recover without data loss or significant downtime.

### ☁️ SaaS

#### Compliance with WEX IT Policy

Ensure that the design adheres to all relevant WEX IT policies. Review the latest policy documents and incorporate necessary controls and guidelines.

> <!-- Info -->
> Info
> Official WEX policies can be found in [AoDocs](https://wexchange.wexinc.com/home/<USER>/content/6557755175121083/aodocs-replaces-policytech-as-document-repository) as of February 2025

#### Alignment with Strategic Objectives

Verify that the solution aligns with WEX's strategic goals. Highlight how the design supports key business objectives and initiatives.

#### Adoption of Standards

Verify that the solution adopts and follows established WEX technical standards while balancing team autonomy and innovation. The design should:

- **Follow Established Standards**: Demonstrate adherence to approved WEX standards, reference architectures, design specifications, and practices.
- **Document Deviations**: Clearly identify any deviations from established standards with detailed justifications that explain why the deviation is necessary.
- **Provide Remediation Plans**: For temporary deviations, include concrete plans with timelines for eventual alignment with standards.
- **Balance Standardization and Innovation**: When introducing innovative approaches that may conflict with current standards, demonstrate how the innovation provides significant value that justifies the deviation.

> <!-- Info -->
> Info
> Justifications for standard deviations should address:
>
> - Business or technical needs that cannot be met by existing standards
> - Analysis of alternative approaches considered
> - Risk assessment and mitigation strategies
> - Long-term implications and sustainability of the deviation
>
> Innovation that proves broadly valuable may be considered for adoption as a new standard through the standards ratification process.

#### Logging & APM

Integrate comprehensive logging and Application Performance Monitoring (APM) into the high-level design using standard tools such as Splunk and DataDog. Ensure that logging best practices are followed, including consistent log formatting, capturing relevant metrics, and setting up alerts for critical events. This integration will provide real-time insights into system performance, facilitate troubleshooting, and enhance overall system reliability.

### 🤖 AI/ML

Ensure that any AI/ML components comply with all WEX policies and standards. Ensure all AI components and data protection strategies are documented in the design.

### 📊 Diagrams

#### System Context Diagram

Provide a high-level system context diagram. This should illustrate the system's interactions with external entities and other systems.

```mermaid
flowchart TB
    classDef systemBoundary fill:#e6f2ff,stroke:#0066cc,stroke-width:2px
    classDef externalEntity fill:#f9f9f9,stroke:#999999
    
    subgraph WEXProduct["WEX Product"]
        class WEXProduct systemBoundary
        WebUI["Web UI"]
        APIs["APIs"]
    end
    
    Users["Users"]
    WEXProducts["Other WEX Products"]
    Partners["Partner Systems"]
    ExternalAPIs["Third-Party Services"]
    
    Users -->|"Browser"| WEXProduct
    WEXProducts -->|"API Calls"| WEXProduct
    WEXProduct -->|"API Responses"| WEXProducts
    Partners -->|"Integration APIs"| WEXProduct
    WEXProduct -->|"Data Exchange"| Partners
    WEXProduct -->|"API Calls"| ExternalAPIs
    ExternalAPIs -->|"API Responses"| WEXProduct
    
    class Users,WEXProducts,Partners,ExternalAPIs externalEntity
```

Guidance for drafting a system context diagram:

- **Identify the system boundary**: Clearly define what is inside and outside the system.
- **Identify external entities**: Determine all external entities (e.g., users, external systems) that interact with the system.
- **Define interactions**: Illustrate how the system interacts with each external entity, including data flows and communication channels.
- **Use standard notation**: Utilize standard diagramming notations (e.g., rectangles for entities, arrows for interactions) for clarity.
- **Keep it high-level**: Focus on the main components and interactions without going into too much detail.
- **Ensure readability**: Make sure the diagram is easy to read and understand, avoiding clutter and overly complex structures.

#### Cloud Architecture Diagram

```mermaid
flowchart LR
    classDef aws fill:#FF9900,stroke:#232F3E,color:#232F3E
    classDef security fill:#FF5252,stroke:#B71C1C,color:white
    classDef k8s fill:#326CE5,stroke:#254AA5,color:white
    classDef db fill:#4CAF50,stroke:#388E3C,color:white
    classDef lb fill:#03A9F4,stroke:#0288D1,color:white

    Internet((Internet))
    Imperva["Imperva WAF"]:::security
    
    subgraph AWS["AWS Cloud"]
        subgraph VPC["Virtual Private Cloud (VPC)"]
            ELB["Elastic Load Balancer"]:::lb
            
            subgraph K8s["Kubernetes Service"]
                Pod1["Pod 1"]
                Pod2["Pod 2"]
                Pod3["Pod 3"]
            end
            
            RDS[("Postgres")]:::db
        end
    end
    subgraph SaaS
        Splunk
        DataDog
        SaaSApi["External API"]
    end

    K8s --> SaaS
    
    Internet --> Imperva
    Imperva --> ELB
    ELB --> K8s
    K8s --> RDS
    
    class AWS,VPC aws
```

When drafting a cloud architecture diagram, consider the following key guidelines:

- **Identify Key Components**: List all major components of your cloud architecture, including compute resources, storage solutions, databases, networking elements, and any managed services.
- **Define Interactions**: Clearly illustrate how these components interact with each other. Use arrows to indicate data flow and communication paths between services.
- **Use Standard Icons**: Utilize standard icons provided by cloud service providers (e.g., AWS, Azure, Google Cloud) when possible to represent different services and components. This helps in maintaining consistency and improving readability.
- **Highlight Security Measures**: Indicate security measures such as firewalls, encryption, identity and access management (IAM) roles, and security groups. This ensures that security considerations are clearly communicated.
- **Show Redundancy and Failover**: Illustrate redundancy and failover mechanisms, such as multi-region deployments, load balancers, and backup solutions. This demonstrates the resilience of your architecture.
- **Include Third-Party SaaS Products**: Clearly represent any third-party SaaS products integrated into your architecture. Use appropriate icons and labels to show how these external services interact with your cloud components. Ensure that data flow and security measures involving these third-party services are well-documented.
- **Incorporate Edge Devices and Services**: Include edge devices and services such as Imperva WAF, express routes, and proxies. Show how these components enhance security, performance, and connectivity by positioning them appropriately within the architecture diagram.

### 📋 Relevant Context

#### Tech Transformation Vectors

Indicate which [Tech Transformation Vectors](https://wexchange.wexinc.com/home/<USER>/content/7585675489301477/tech-transformation-measurement) this solution design is targeting for improvement and how that vector is impacted.

#### Risk Analysis & Dependencies

Provide additional context and analysis that could impact delivery such as significant risks, critical assumptions, known issues, and system dependencies.

> <!-- Info -->
> Info
> **Example**: A payment gateway integration depends on a third-party API with a history of intermittent outages. Highlighting this dependency allows the team to plan for retries, caching, or alternative failover mechanisms to ensure uninterrupted payment processing.

#### Design Patterns

Provide information about standard design patterns incorporated into the high-level design for the solution.

Using standard design patterns can achieve faster design approvals and accelerate delivery timelines, ensuring efficient and effective project execution.

> <!-- Info -->
> Info
> **Example**: A design uses the "Circuit Breaker" pattern to handle failures in external payment services. This ensures that repeated failures do not overwhelm the system, maintaining stability and improving user experience during outages.

#### Secure Coding

Provide contextual information about coding languages, frameworks, and package dependencies for risk transparency as well as secure coding practices adopted that ensure compliance with WEX IT security policies, standards, and best practices.

When documenting secure coding practices and environment, include the following key information:

- **Coding Standards and Guidelines**: Outline the coding standards and guidelines followed by the team, such as OWASP Secure Coding Practices, CERT Secure Coding Standards, or language-specific guidelines.
- **Security Testing**: Outline the security testing practices, including penetration testing, dynamic application security testing (DAST), and static application security testing (SAST). Ensure to highlight any deviations from WEX standard tooling.
- **Dependency Management**: Detail how dependencies are managed and kept up-to-date to avoid known vulnerabilities. Mention tools like Dependabot or OWASP Dependency-Check.
- **Threat Modeling**: Document any threat modeling exercises used to identify and mitigate potential security risks in the design phase. Mention any frameworks or tools used, such as STRIDE or Microsoft Threat Modeling Tool.

> <!-- Info -->
> Info
> This concern is only relevant when a design includes resources that execute WEX applications built from source code.
>
> **Example**: The payment processing service uses Java with Spring Boot and includes OWASP Dependency-Check to identify vulnerabilities in third-party libraries. Secure coding practices such as input validation and output encoding are implemented to prevent injection attacks, ensuring compliance with PCI DSS standards.

#### Procurement & Risk Assessment

If a design includes new third-party software, the team MUST engage the Finance team for procurement considerations and Information Security for risk assessment. These processes can run concurrently with the iSARB architectural review.

iSARB's approval of the solution design may be contingent upon the successful completion and approval from these respective teams. The design document should note the status of these external reviews.

While these external reviews can be concurrent, early engagement with Finance, Procurement, and Information Security is still highly recommended to identify potential showstoppers early in the design lifecycle.

> <!-- Info -->
> Info
> This concern is only relevant to designs that incorporate new third-party software.

#### Build / Buy / Partner Decisions

If a design includes new third-party software, the design should include a summary of the decision-making process. This summary should include:

- **Alternatives Considered**: List all third-party products that were evaluated as part of the selection process, including any existing WEX solutions that could potentially meet the requirements.

- **Selection Criteria**: Clearly define the technical, operational, financial, and strategic criteria used to evaluate each alternative. Include specific requirements such as performance metrics, scalability needs, security requirements, and integration capabilities.

- **Build Analysis**: Explain why building the solution in-house was not pursued, including:
  - Development time and resource requirements
  - Technical expertise availability within WEX
  - Maintenance and support implications
  - Total cost of ownership comparison

- **Partner Analysis**: Explain why partnering with an existing WEX team or vendor was not pursued, including:
  - Assessment of existing WEX capabilities
  - Evaluation of current vendor relationships and capabilities
  - Benefits or drawbacks of leveraging internal resources

This information helps iSARB understand the strategic reasoning behind the technology choice and ensures that all viable options have been thoroughly considered.

> <!-- Info -->
> Info
> This concern is only relevant to designs that incorporate new third-party software.

#### Standards Documentation

If a change to standards is being proposed:

- Ensure that all necessary documentation for the type of standard is complete and up-to-date. This includes design documents, user guides, and operational manuals.
- Clearly state the benefits of the solution for all teams at WEX. If the solution impacts or is only used by a single team, it does not need to be a standard.

> <!-- Info -->
> Info
> This concern is only relevant when a change to standards is being proposed.

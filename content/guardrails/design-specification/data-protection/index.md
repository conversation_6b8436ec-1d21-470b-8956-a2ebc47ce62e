<!-- Parent: Guardrails -->
<!-- Parent: Design Specification -->
<!-- Title: RFC-488 Data Protection -->

<!-- Include: macros.md -->
# Data Protection

:draft:

```text
Author: <PERSON>
Publish Date: 2025-04-14
Category: Guardrails
Subtype: Design Specification
```

<!--
Required sections for ratification: 
-->

## Overview

WEX's rapid growth through acquisitions has created a complex and fragmented technical environment, posing significant challenges to protecting PCI, PHI, and PII data. Despite having strong security tools, the lack of a unified, enterprise-wide data protection strategy has resulted in inconsistent sensitive data handling practices and a proliferation of disparate solutions.

This inconsistency severely slows innovation. For instance, integrating data into the Data Platform requires laborious manual analysis to identify and manage sensitive data, hindering scalability and automation. While essential data governance measures, like a comprehensive data catalog, are needed, a clear data protection strategy is critical to achieving speed, scalability, and cost efficiency.

Our broad regulatory compliance scope, driven by widespread access to decrypted sensitive data, creates a substantial burden. The constant expansion of systems into compliance scope consumes resources and limits agility, while also increasing attack surfaces and escalating third-party security assessment costs. While robust security across all systems is important, strategically reducing the compliance scope would free up resources and help to minimize project disruptions.

To address data protection challenges, WEX requires a comprehensive and adaptable data protection strategy to solidify its market leadership and safeguard entrusted information. This strategy will foster customer trust, protect WEX's brand reputation, and accelerate innovation by exceeding mere compliance.

## Goals

- **Categorize Data Protection**: Organize security methods into logical groups based on data state (rest/transit) and type (PCI, general), creating a structured approach.

- **Provide Layered Security**: Implement multiple security measures at different points in the data's journey to create a robust defense, ensuring protection even if one layer fails.

- **Address Specific Compliance Requirements**: Acknowledge and cater to unique security standards, particularly PCI DSS, by highlighting specific methods like tokenization for sensitive data.

- **Offer a Range of Protection Techniques**: Present a variety of security tools (encryption, tokenization, secure protocols) to accommodate diverse data types and security needs.

- **Guide Decision-Making**: Serve as a roadmap for the WEX, helping technologists choose the right protection measures based on data state and requirements.

- **Promote Best Practices**: Encourage the use of industry-recommended security practices, like strong encryption and tokenization, for optimal data protection.

## Scope

### In-Scope

- **Governance & Compliance**: This establishes the organizational framework for data protection by defining how data is classified by sensitivity, who is responsible for its security, and how access to sensitive data is monitored through audits to ensure compliance.

- **Data Protection Decision**: This outlines the use of a structured decision tree process to guide the selection of the most appropriate encryption methods based on data characteristics and context.

- **Data Protection Mechanisms**: This covers the technical methods used to secure data, both when it is stored (at the storage, logical, or field level) and when it is being transmitted between systems or locations.

- **Other Data Protection Methods**: Beyond encryption, this includes strategies to reduce data exposure by limiting data collection and retention (data minimization), as well as technologies and processes (DLP) to prevent sensitive data from leaving the organization's control.

### Out-of-Scope

- **Detailed Legal and Regulatory Compliance Frameworks**: While adherence is a guiding principle, the specific details of individual legal and regulatory frameworks are addressed in a broader organizational compliance strategy.

- **General Publicly Available Information**: The primary focus is on protecting non-public, sensitive data, regardless of whether it's internal or being shared externally.

- **Marketing and Public Relations Data Handling (unless specifically sensitive)**: Standard handling of general marketing and PR data is typically covered under separate guidelines.

- **Physical Shipment of Data (unless highly sensitive and specifically approved)**: The primary focus is on electronic data. Physical transfers require separate, specific protocols for highly sensitive cases.

## Governance & Compliance

### Data Classification

This data protection strategy emphasizes the protection of Class 1 data, categorized as "Confidential," which includes sensitive information that, if disclosed, would likely cause significant harm to WEX. Examples of Class 1 data are Personally Identifiable Information (PII) such as Social Security numbers, driver's license numbers, and financial information, as well as Payment Card Information (PCI) like Primary Account Numbers (PAN). The current policy that defines and governs this classification is maintained in the [Global Information Classification and Handling Policy | Doc ID: 02610](https://aodocs.altirnao.com/?locale=en_GB&aodocs-domain=wexinc.com#Menu_viewDoc/LibraryId_UaBPCN63arqiBS4iO9/ViewId_UaBParq0TMEuM5J1ov/DocumentId_UdTZ4e1wCAVY0dkJse/VersionId_COMMITTED).

### Roles & Responsibilities

Effective data protection requires clearly defined roles and responsibilities. The following outlines the key roles involved in WEX's data protection strategy, emphasizing their distinct accountabilities:

- **Data Owner**: Primarily accountable for defining data sensitivity, authorizing access, and approving tokenization strategies. They are consulted and informed on other data management activities.

- **Data Custodian**: Responsible for implementing and maintaining the technical controls, managing tokenization systems, and ensuring data security.

- **Data User**: Responsible for adhering to data policies, utilizing data for authorized purposes, and reporting security incidents.

- **Compliance Officer**: Accountable for ensuring regulatory compliance, conducting audits, developing documentation, and providing guidance.

- **Security Officer**: Responsible for designing and implementing security measures, managing encryption, and responding to incidents.

The below RACI (Responsible, Accountable, Consulted, Informed) helps to define and clarify the roles and responsibilities as it pertains to sensitive data management:

| Responsibility                                  | Data Owner | Data Custodian | Data User | Compliance Officer | Security Officer |
| :---------------------------------------------- | :--------- | :------------- | :-------- | :----------------- | :--------------- |
| Adhere to Data Policies & Procedures            | I          | I              | R         | I                  | I                |
| Approve Tokenization Strategy                   | A          | I              | I         | I                  | I                |
| Authorize Data Access & Usage                   | A          | I              | I         | I                  | I                |
| Conduct Compliance Audits & Risk Assessments   | I          | I              | I         | R                  | I                |
| Conduct Security Assessments & Penetration Testing | I          | I              | I         | I                  | R                |
| Define Data Classification & Sensitivity Levels  | R          | I              | I         | I                  | I               |
| Design & Implement Security Controls            | I          | I              | I         | I                  | R                |
| Develop & Maintain Compliance Documentation    | I          | I              | I         | R                  | I                |
| Ensure Backup & Recovery Procedures             | I          | R              | I         | I                  | I                |
| Ensure Regulatory Compliance                    | I          | I              | I         | R                  | I                |
| Ensure Security of Tokenization APIs            | I          | I              | I         | I                  | R                |
| Ensure Tokenization Strategy Meets Requirements | A          | I              | I         | R                  | I                |
| Follow Detokenization Procedures                | I          | I              | R         | I                  | I                |
| Implement Data Security Controls                | I          | R              | I         | I                  | R                |
| Manage Encryption Keys & Access Controls        | I          | I              | I         | I                  | R                |
| Manage Tokenization & Detokenization Systems    | I          | R              | I         | I                  | I                |
| Maintain Audit Logs                             | I          | R              | I         | I                  | R                |
| Monitor Changes in Regulations                | I          | I              | I         | R                  | I                |
| Monitor Data Access & Usage                     | I          | R              | R         | I                  | R                |
| Monitor Security Logs & Respond to Incidents   | I          | I              | I         | I                  | R                |
| Participate in Data Lifecycle Management        | C          | I              | I         | I                  | I                |
| Protect Access Credentials                      | I          | I              | R         | R                  | I                |
| Provide Regulatory Guidance & Training        | I          | I              | I         | R                  | I                |
| Report Security Incidents                       | I          | I              | R         | I                  | R                |
| Review Effectiveness of Data Strategy           | R          | I              | I         | I                  | I                |
| Utilize Data Only for Authorized Purposes       | I          | I              | R         | I                  | I                |

### Auditing Sensitive Data Activities

Detailed audit trails are crucial for maintaining accountability and ensuring compliance. Robust logging, secure storage, and regular review of these specific actions are critical for security.

Measures for auditing sensitive data activities include:

- **Comprehensive Logging of Token/Key Usage**: Record all instances of tokenization, detokenization, encryption, and decryption actions.

- **Detailed Log Information**: Capture timestamps, user/system IDs, specific tokens/keys used, and the data affected within each log entry.

- **Secure Log Storage for Cryptographic Operations**: Implement measures to prevent tampering and ensure log integrity, especially for logs relating to key usage.

- **Regular Audit Log Reviews for Cryptographic Events**: Conduct periodic reviews to detect potential security breaches or misuse of tokenization/encryption functions.

- **Anomaly Alerting for Token/Key Activity**: Establish alerts for unusual patterns or unauthorized attempts involving tokenization, detokenization, encryption, or decryption.

- **Regulatory-Compliant Retention of Cryptographic Logs**: Maintain logs relating to cryptographic operations for a duration that satisfies relevant legal and regulatory obligations, which often require stricter retention.

## Data Protection Strategies Across Different Layers

This chart illustrates various data encryption methods categorized by the layer at which they are typically implemented within a system or during data transmission. Moving from the bottom "Storage" layer upwards through "Logical/Application" and "Field-Level" to "Transit," the diagram highlights how different encryption techniques address data security at different stages and granularities. This layered approach provides a comprehensive strategy for protecting data both at rest and in motion, ensuring confidentiality and integrity across the data lifecycle.

![alt text](layers.png)

## Decision Flow for Applying Data Protection

The terminal nodes of this decision tree define the absolute minimum security controls required for data protection. Be aware that comprehensive risk assessments, organizational policies, and specific regulatory mandates may necessitate the implementation of additional, more robust safeguards on a case-by-case basis.

```mermaid
graph TD
    A[START] --> B{Transit, At Rest, or Both?};
    B -- Transit --> C[TLS-Enabled Protocols];
    B -- Both --> D{Contains Class 1 Data?};
    D -- Yes --> E[PGP Encrypt + SFTP];
    D -- No --> F[Secure File Transfer Protocols];
    B -- Rest --> G{Contains Class 1 Data?};
    G -- No --> H[Storage-Level Encryption];
    G -- Yes --> I{PCI Data?};
    I -- Yes --> J[Tokenization];
    I -- No --> K{Do logical/application-level encryption mechanisms satisfy all relevant regulatory requirements?};
    K -- Yes --> L{Filesystem, Object Storage, or Database?};
    K -- No --> M[Granular Encryption];
    L -- Filesystem --> N[PGP Encryption];
    L -- Object Storage --> O[Object Storage Native Encryption];
    L -- Database --> P[Transparent Data Encryption];
```

## Transit-Level Encryption

Secure communication over networks relies heavily on in-transit encryption, primarily achieved through the Transport Layer Security (TLS) protocol. TLS provides a secure channel for data transmission by encrypting the communication between two endpoints. This encryption ensures confidentiality, integrity, and authentication, protecting sensitive information from eavesdropping and tampering. While commonly associated with HTTPS for secure web browsing, TLS can secure various other protocols, including SMTP (email), FTP (file transfer), and custom application protocols.

### Example Process

```mermaid
sequenceDiagram
    Client->>Server: HTTPS Request (ClientHello)
    Server->>Client: ServerHello, Certificate, ServerHelloDone
    Client->>Server: ClientKeyExchange, ChangeCipherSpec, Encrypted Finished
    Server->>Client: ChangeCipherSpec, Encrypted Finished
    Client->>Server: HTTPS Request (Encrypted Application Data)
    Server->>Client: HTTPS Response (Encrypted Application Data)
    Client--xServer: Close Connection
    Server--xClient: Close Connection
```

1. **HTTPS Request (ClientHello)**: Client initiates TLS handshake with the server.

2. **ServerHello, Certificate, ServerHelloDone**: Server responds with handshake parameters and its certificate.

3. **ClientKeyExchange, ChangeCipherSpec, Encrypted Finished**: Client sends encrypted pre-master secret and confirms encrypted communication.

4. **ChangeCipherSpec, Encrypted Finished**: Server confirms encrypted communication and verifies handshake.

5. **HTTPS Request (Encrypted Application Data)**: Client sends encrypted HTTP request.

6. **HTTPS Response (Encrypted Application Data)**: Server sends encrypted HTTP response.

7. **Close Connection**: Client or server initiates connection closure.

8. **Close Connection**: Other party completes connection closure.

## Storage-Level Encryption

Storage-level encryption secures data at rest by encrypting it directly at the storage controller or drive. This transparent approach, handled by the storage infrastructure, minimizes performance impact and simplifies key management. It's ideal for shared storage and cloud environments, providing robust protection against unauthorized physical access and aiding regulatory compliance. Encryption granularity is at the storage device level.

### Storage-Level Encryption Example Process

```mermaid
sequenceDiagram
    Application->>DBServer: Insert/Update Data (Plaintext)
    DBServer->>Controller: Write Data
    Controller->>KMS: Retrieve Encryption Key
    KMS-->>Controller: Encryption Key
    Controller->>Controller: Encrypt Data
    Controller->>Device: Store Encrypted Data
    Device-->>Controller: Confirmation
    Controller-->>DBServer: Confirmation
    DBServer-->>Application: Confirmation
    Application->>DBServer: Retrieve Data
    DBServer->>Controller: Read Data
    Controller->>Device: Retrieve Encrypted Data
    Device-->>Controller: Encrypted Data
    Controller->>KMS: Retrieve Decryption Key
    KMS-->>Controller: Decryption Key
    Controller->>Controller: Decrypt Data
    Controller-->>DBServer: Plaintext Data
    DBServer-->>Application: Plaintext Data
```

#### Storage-Level Encryption - Write Operations (Encrypt)

1. **Data Submission**: The application sends plaintext data to the database server.

2. **Data Transfer**: The database server forwards the data to the storage controller for writing.

3. **Key Retrieval**: The storage controller requests an encryption key from the Key Management System (KMS).

4. **Key Delivery**: The KMS securely delivers the encryption key to the storage controller.

5. **Data Encryption**: The storage controller encrypts the data using the retrieved encryption key.

6. **Encrypted Storage**: The storage controller sends the encrypted data to the storage device for storage.

7. **Confirmation**: The storage device confirms the successful storage of the encrypted data, and the storage controller relays this confirmation back to the database server and then to the application.

#### Storage-Level Encryption - Read Operations (Decrypt)

1. **Data Retrieval Request**: The application requests data retrieval from the database server.

2. **Data Transfer**: The database server forwards the request to the storage controller for reading.

3. **Encrypted Data Retrieval**: The storage controller retrieves the encrypted data from the storage device.

4. **Key Retrieval**: The storage controller requests a decryption key from the KMS.

5. **Key Delivery**: The KMS securely delivers the decryption key to the storage controller.

6. **Data Decryption**: The storage controller decrypts the retrieved data using the decryption key.

7. **Plaintext Delivery**: The storage controller returns the decrypted (plaintext) data to the database server, which then returns it to the application.

## Logical/Application-Level Encryption

### Object Storage Encryption

---
Object storage encryption is a critical security measure employed to protect data at rest within cloud-based object storage services like AWS S3, Azure Blob Storage, and MinIO. It ensures that data is stored in an unreadable format, safeguarding it from unauthorized access. Encryption can be configured at the bucket level, automatically applying to all objects within that bucket, or specified on a per-object basis during upload. These services typically offer server-side encryption (where the storage service manages the encryption and decryption) using either service-managed keys, customer-managed keys (often integrated with key management services), or customer-provided keys. When a user retrieves an encrypted object, the storage service transparently handles the decryption, provided the user has the necessary access permissions, thus ensuring data confidentiality throughout its lifecycle in the cloud.

#### Object Storage Encryption Example Process (SSE-KMS)

```mermaid
sequenceDiagram
    alt Upload
          Client->>OSS: PutObject (bucketName, objectKey, data)
          OSS->>OSS: Check Bucket Encryption Configuration
          OSS->>KMS: GenerateDataKey (KeyId=key-identifier)
          KMS-->>OSS: DataKey (plaintext), DataKey (ciphertext)
          OSS->>OSS: Encrypt Object Data (using plaintext DataKey)
          OSS->>OSS: Store Encrypted Data and Ciphertext DataKey
          OSS-->>Client: Success
    else Download
          Client->>OSS: GetObject (bucketName, objectKey)
          OSS->>OSS: Check Bucket Encryption Configuration
          OSS->>OSS: Retrieve Encrypted Object Data and Ciphertext DataKey
          OSS->>KMS: DecryptDataKey (Ciphertext DataKey)
          KMS-->>OSS: DataKey (plaintext)
          OSS->>OSS: Decrypt Object Data (using plaintext DataKey)
          OSS-->>Client: Object Data
    end
```

#### Bucket Upload Process (Encrypt)

1. **Client requests object upload**: The client application sends a `PutObject` request to the Object Storage Service (OSS), including the bucket name, object key, and the data to be uploaded.

2. **OSS checks bucket encryption configuration**: The OSS examines the encryption settings configured for the target bucket. This determines if and how the uploaded object should be encrypted.

3. **OSS requests a data key from KMS**: The OSS communicates with the Key Management Service (KMS) to generate a unique data encryption key. The request includes a key identifier.

4. **KMS provides plaintext and ciphertext data keys**: The KMS returns two versions of the data key: a plaintext version for immediate encryption and a ciphertext (encrypted) version for secure storage.

5. **OSS encrypts the object data**: The OSS uses the plaintext data key received from KMS to encrypt the object's data.

6. **OSS stores the encrypted data and ciphertext data key**: The OSS persists the encrypted object data along with the ciphertext of the data key. The ciphertext data key is stored so that the object can be decrypted later.

7. **OSS confirms successful upload**: The OSS sends a success response back to the client, indicating that the object has been successfully stored (in its encrypted form).

#### Bucket Download Process (Decrypt)

1. **Client requests object download**: The client application sends a `GetObject` request to the Object Storage Service (OSS), specifying the bucket name and the key of the object to be downloaded.

2. **OSS checks bucket encryption configuration**: The OSS retrieves the encryption settings associated with the requested bucket to understand how the object was originally encrypted.

3. **OSS retrieves the encrypted data and ciphertext data key**: The OSS fetches the encrypted object data and the corresponding ciphertext of the data key from storage.

4. **OSS requests decryption of the data key from KMS**: The OSS sends the ciphertext of the data key to the KMS for decryption.

5. **KMS provides the plaintext data key**: The KMS decrypts the ciphertext data key and returns the plaintext version to the OSS.

6. **OSS decrypts the object data**: The OSS uses the plaintext data key received from KMS to decrypt the object's data.

7. **OSS sends the decrypted object data to the client**: The OSS transmits the original, decrypted object data back to the client application.

### Transparent Data Encryption

---

Transparent Data Encryption (TDE) automatically encrypts entire databases or tablespaces at rest, at the I/O level, using a master key (often managed by an HSM). This protects data on storage devices from unauthorized access, without application changes. While effective for data at rest and useful for regulatory compliance, TDE doesn't secure data in transit or memory, and may introduce minor performance overhead.

#### Transparent Data Encryption Example Process

```mermaid
sequenceDiagram
    Application->>Server: Insert/Update Data (Plaintext)
    Server->>Server: Encrypt Data (Entire Database/Tablespace)
    Server->>Storage: Store Encrypted Data
    Storage-->>Server: Confirmation
    Server-->>Application: Confirmation
    Application->>Server: Retrieve Data
    Server->>Storage: Retrieve Encrypted Data
    Storage-->>Server: Encrypted Data
    Server->>Server: Decrypt Data (Entire Database/Tablespace)
    Server-->>Application: Plaintext Data
```

#### Transparent Data Encryption - Write Operations (Encrypt)

1. **Data Submission**: The application sends plaintext data to the database server for insertion or update.

2. **Automatic Encryption**: The database server, configured with TDE, automatically encrypts the entire database or tablespace before writing data to storage. This encryption occurs at the I/O level.

3. **Encrypted Storage**: The database server writes the encrypted data to the underlying storage system.

4. **Confirmation**: The storage system confirms the successful storage of the encrypted data, and the database server relays this confirmation to the application.

#### Transparent Data Encryption - Read Operations (Decrypt)

1. **Data Retrieval Request**: The application requests data retrieval from the database server.

2. **Encrypted Data Retrieval**: The database server retrieves the encrypted data from the underlying storage system.

3. **Automatic Decryption**: The database server, configured with TDE, automatically decrypts the entire database or tablespace after reading data from storage. This decryption occurs at the I/O level.

4. **Plaintext Delivery**: The database server returns the decrypted (plaintext) data to the application.

### File Encryption

---
File-level encryption, like PGP, secures individual files, offering granular control. Applications encrypt and decrypt files using cryptographic tools and managed keys. This method protects sensitive data on local or shared storage, even if the storage is compromised. However, robust key management and user awareness are crucial for effective security.

Below is an example process flow illustrating the encryption of a file using PGP encryption, transmitting over SFTP, and decryption on the receiver end:

#### Sender Process

```mermaid
sequenceDiagram
    App->>FileSystem: Write File (Plaintext)
    App->>KMS: Retrieve Encryption Key
    KMS-->>App: Encryption Key
    App->>App: Encrypt File
    App->>FileSystem: Store Encrypted File
    FileSystem->>SFTP: Upload Encrypted File
```

1. **File Creation**: The sender application creates or modifies a plaintext file.

2. **Key Retrieval (Sender)**: The sender application retrieves an encryption key from the sender's Key Management System (KMS) or uses locally stored keys.

3. **File Encryption**: The sender application encrypts the file using the retrieved encryption key.

4. **Encrypted Storage (Sender)**: The sender application stores the encrypted file in the sender's file system.

5. **SFTP Upload**: The sender application uploads the encrypted file to the SFTP server.

#### Receiver Process

```mermaid
sequenceDiagram
    SFTP->>App: Download Encrypted File
    App->>FileSystem: Store Encrypted File
    App->>KMS: Retrieve Decryption Key
    KMS-->>App: Decryption Key
    App->>App: Decrypt File
    App->>FileSystem: Read File (Plaintext)
```

1. **SFTP Download**: The receiver application downloads the encrypted file from the SFTP server.

2. **Encrypted Storage (Receiver)**: The receiver application stores the encrypted file in the receiver's file system.

3. **Key Retrieval (Receiver)**: The receiver application retrieves the decryption key from the receiver's Key Management System (KMS) or uses locally stored keys.

4. **File Decryption**: The receiver application decrypts the file using the retrieved decryption key.

5. **Plaintext Access (Receiver)**: The receiver application can now read and process the decrypted (plaintext) file.

## Field-Level Encryption

### Tokenization

---

Tokenization is a security process that significantly improves data protection by replacing sensitive information with non-sensitive substitutes known as tokens. This substitution effectively decontextualizes the original data, minimizing the risk of exposure by isolating it from its original context. Two main methodologies are employed in tokenization: vault-based and vault-less. Vault-based tokenization relies on a secure, dedicated datastore to maintain the mappings between tokens and their corresponding original values. Conversely, vault-less tokenization uses cryptographic techniques to generate tokens that can be reversed back to the original data without the need for persistent mapping storage.

Both vault-based and vault-less tokenization frequently utilize Format-Preserving Encryption (FPE) to ensure that the generated tokens retain the format of the original sensitive data. This is a key differentiator from traditional encryption methods, which produce ciphertext that bears little resemblance to the input. To enhance the security of tokenization systems, Hardware Security Modules (HSMs) are often integrated to provide secure management of cryptographic keys and to perform cryptographic operations. Furthermore, robust authentication and authorization controls are implemented to restrict access to tokenization and detokenization functions, ensuring that only authorized parties can perform these critical actions.

Although WEX has standardized on **vault-less tokenization**, this section provides a comparative overview of both vault-based and vault-less methods. This comparison illustrates the advantages of the chosen approach, particularly in terms of performance and reduced infrastructure complexity. It also highlights the critical role of secure key management, authentication, and auditing in maintaining the integrity and confidentiality of tokenized data.

### Vault-Based Tokenization

During vault-based tokenization, sensitive data is replaced with a non-sensitive token, and both values are securely stored in a highly protected database, known as a “vault”. In this setup, the token acts as a reference pointer to the original data value. When the value is detokenized, the original data is simply retrieved from the vault and returned to the requestor.

#### Vault-Based Tokenization Benefits

- **Enhanced Security**: The sensitive data is isolated in a secure vault, minimizing the risk of exposure in operational systems.

- **Stronger Control**: Centralized control over sensitive data and detokenization processes.

- **Regulatory Compliance**: Simplifies compliance with regulations that require strict data isolation (e.g., PCI DSS, GDPR).

- **Flexibility**: Allows for more complex tokenization schemes and data management.

- **Reduced Scope**: Reduces the scope of systems that must be PCI compliant.

#### Vault-Based Tokenization Drawbacks

- **Infrastructure Overhead**: Requires a separate, secure vault infrastructure, adding complexity and cost.

- **Performance Impact**: Detokenization requires accessing the vault, which can introduce latency.

- **Single Point of Failure**: If the vault is compromised, all sensitive data is at risk.

- **Increased Complexity**: The vault must be maintained, backed up, and secured.

#### Vault-Based Tokenization Example Process

```mermaid
sequenceDiagram
    Card Generation Service->>Tokenization Service: Tokenization Request (PAN)
    Tokenization Service->>Tokenization Service: Authenticate/Authorize Request
    Tokenization Service->>Tokenization Service: Validate PAN Format
    Tokenization Service->>Key Management System: Retrieve FPE Key
    Key Management System-->>Tokenization Service: FPE Key
    Tokenization Service->>Tokenization Service: Apply FPE and Masking
    Tokenization Service->>Token Vault: Store Token -> PAN Mapping
    Token Vault-->>Tokenization Service: Confirmation
    Tokenization Service->>Audit Log: Log Tokenization Request
    Tokenization Service-->>Card Generation Service: Tokenized PAN

    Card Generation Service->>Tokenization Service: Detokenization Request (Token)
    Tokenization Service->>Tokenization Service: Authenticate/Authorize Request
    Tokenization Service->>Token Vault: Retrieve PAN using Token
    Token Vault-->>Tokenization Service: PAN
    Tokenization Service-->>Card Generation Service: PAN
    Tokenization Service->>Audit Log: Log Detokenization Request
```

#### Vault-Based Tokenization Process

1. **Request Initiation**: The Card Generation Service sends a tokenization request, including the plaintext PAN, to the Tokenization Service.

2. **Authentication/Authorization**: The Tokenization Service authenticates and authorizes the request, verifying the requestor's permissions.

3. **Data Validation**: The Tokenization Service validates the PAN format to ensure it conforms to expected standards.

4. **Key Retrieval**: The Tokenization Service retrieves the necessary FPE key from the Key Management System.

5. **Token Generation**: The Tokenization Service applies Format-Preserving Encryption (FPE) and any configured masking rules to generate the token.

6. **Vault Storage**: The Tokenization Service securely stores the mapping between the generated token and the original PAN in the Token Vault.

7. **Audit Logging**: The Tokenization Service logs the tokenization request and associated details to the Audit Log.

8. **Token Return**: The Tokenization Service returns the generated token to the Card Generation Service.

#### Vault-Based Detokenization Process

1. **Request Initiation**: The Card Generation Service sends a detokenization request, including the tokenized PAN, to the Tokenization Service.

2. **Authentication/Authorization**: The Tokenization Service authenticates and authorizes the request.

3. **Vault Retrieval**: The Tokenization Service retrieves the corresponding original PAN from the Token Vault using the provided token.

4. **Audit Logging**: The Tokenization Service logs the detokenization request and associated details to the Audit Log.

5. **PAN Return**: The Tokenization Service returns the original PAN to the Card Generation Service.

### Vaultless Tokenization

During vault-less tokenization, cryptographic algorithms are used to generate tokens directly from the sensitive data without storing the original value. Detokenization leverages the same algorithms and keys as used in tokenization, effectively exchanging a token for the plaintext value.

#### Vaultless Tokenization Benefits

- **Reduced Infrastructure**: Eliminates the need for a separate vault, simplifying implementation and reducing costs.

- **Improved Performance**: Detokenization is faster because it doesn't involve accessing a remote vault.

- **Simpler Implementation**: Easier to integrate into existing systems.

- **Portability**: Tokens can be generated and detokenized anywhere, as long as the keys and algorithms are available.

#### Vaultless Tokenization Drawbacks

- **Higher Risk of Key Compromise**: The security relies heavily on the secrecy of the cryptographic keys. If the keys are compromised, all tokens are vulnerable.

- **Limited Flexibility**: Less flexibility in tokenization schemes compared to vault tokenization.

- **Algorithm Vulnerability**: If the cryptographic algorithm is compromised, the tokens are vulnerable.

- **Data Residency concerns**: The keys must be stored somewhere.

- **Compliance challenges**: Depending on the compliance regulations, the vault-less method might not meet the required standards.

#### Vaultless Tokenization Example Process

```mermaid
sequenceDiagram
    Card Generation Service->>Tokenization Service: Tokenization Request (PAN)
    Tokenization Service->>Tokenization Service: Authenticate/Authorize Request
    Tokenization Service->>Tokenization Service: Validate PAN Format
    Tokenization Service->>Key Management System: Retrieve FPE Key
    Key Management System-->>Tokenization Service: FPE Key
    Tokenization Service->>Tokenization Service: Apply FPE and Masking
    Tokenization Service->>Audit Log: Log Tokenization Request
    Tokenization Service-->>Card Generation Service: Tokenized PAN

    Card Generation Service->>Tokenization Service: Detokenization Request (Token)
    Tokenization Service->>Tokenization Service: Authenticate/Authorize Request
    Tokenization Service->>Key Management System: Retrieve FPE Key
    Key Management System-->>Tokenization Service: FPE Key
    Tokenization Service->>Tokenization Service: Apply Reverse FPE and Unmasking
    Tokenization Service-->>Card Generation Service: PAN
    Tokenization Service->>Audit Log: Log Detokenization Request
```

#### Vaultless Tokenization Process

1. **Request Initiation**: The Card Generation Service sends a tokenization request, including the plaintext PAN, to the Tokenization Service.

2. **Data Validation**: The Tokenization Service validates the PAN format to ensure it conforms to expected standards.

3. **Authentication/Authorization**: The Tokenization Service authenticates and authorizes the request, verifying the requestor's permissions.

4. **Key Retrieval**: The Tokenization Service retrieves the necessary FPE key from the Key Management System.

5. **Token Generation**: The Tokenization Service applies Format-Preserving Encryption (FPE) and any configured masking rules to generate the token.

6. **Audit Logging**: The Tokenization Service logs the tokenization request and associated details to the Audit Log.

7. **Token Return**: The Tokenization Service returns the generated token to the Card Generation Service.

#### Vaultless Detokenization Process

1. **Request Initiation**: The Card Generation Service sends a detokenization request, including the tokenized PAN, to the Tokenization Service.

2. **Authentication/Authorization**: The Tokenization Service authenticates and authorizes the request.

3. **Key Retrieval**: The Tokenization Service retrieves the necessary FPE key from the Key Management System.

4. **Reverse Tokenization**: The Tokenization Service applies the reverse FPE decryption and unmasking to retrieve the original PAN.

5. **Audit Logging**: The Tokenization Service logs the detokenization request and associated details to the Audit Log.

6. **PAN Return**: The Tokenization Service returns the original PAN to the Card Generation Service.

### Granular Encryption

---
Granular encryption gives developers more control over data security, independent of database structures or platforms. It protects data in transit and at rest, but requires robust KMS integration for key management. While offering flexibility and enhanced security, it adds development complexity and potential performance overhead. Granular encryption is typically regarded as a last resort if other levels of encryption are not suitable to support regulatory compliance needs.

#### Granular Encryption Example Process

```mermaid
sequenceDiagram
    Application->>Application: Prepare Data (Plaintext)
    Application->>KMS: Retrieve Encryption Key
    KMS-->>Application: Encryption Key
    Application->>Application: Encrypt Data
    Application->>Storage: Store Encrypted Data
    Application->>Storage: Retrieve Encrypted Data
    Application->>KMS: Retrieve Decryption Key
    KMS-->>Application: Decryption Key
    Application->>Application: Decrypt Data
    Application->>Application: Process Data (Plaintext)
```

#### Encryption Process

1. **Data Preparation**: The application prepares the data to be stored or transmitted. This data is in plaintext.

2. **Key Retrieval**: The application requests an encryption key from the Key Management System (KMS).

3. **Key Delivery**: The KMS securely delivers the encryption key to the application.

4. **Data Encryption**: The application encrypts the data using the retrieved encryption key. This can involve encrypting specific fields, entire data structures, files, or messages.

5. **Encrypted Storage**: The application stores the encrypted data in the data storage system (e.g., database, file system, cloud storage).

#### Decryption Process

1. **Encrypted Data Retrieval**: The application retrieves the encrypted data from the data storage system.

2. **Key Retrieval**: The application requests a decryption key from the KMS.

3. **Key Delivery**: The KMS securely delivers the decryption key to the application.

4. **Data Decryption**: The application decrypts the data using the retrieved decryption key.

5. **Plaintext Processing**: The application can now process the decrypted (plaintext) data.

### Code Example

The example demonstrates application-layer encryption by encrypting and decrypting sensitive data using AWS Key Management Service (KMS). The encryptData method receives a sensitive value, encrypts it using the specified KMS key, and stores the Base64-encoded ciphertext. The decryptData method retrieves the encrypted data, decrypts it using the same KMS key, and returns the original sensitive value. This process ensures that the sensitive data is encrypted before storage and decrypted only when explicitly requested, illustrating application-layer encryption with external key management.

**NOTE**: This example uses in-memory storage for simplicity, which is not suitable for production use. It's crucial to replace the in-memory storage with a secure persistent storage solution in a production environment and ensure that the KMS key and access permissions are correctly configured.

```java
import org.springframework.web.bind.annotation.*;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.kms.KmsClient;
import software.amazon.awssdk.services.kms.model.DecryptRequest;
import software.amazon.awssdk.services.kms.model.EncryptRequest;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/sensitive")
public class SensitiveDataController {

    private static final String KMS_KEY_ID = "arn:aws:kms:us-east-1:12345:key/abcdef01-2345-6789-abcd-ef0123456789"; 
    private static final Map<String, String> encryptedData = new HashMap<>();
    private final KmsClient kmsClient;

    public SensitiveDataController() {
        this.kmsClient = KmsClient.create();
    }

    @PostMapping("/encrypt")
    public String encryptData(@RequestBody Map<String, String> request) {
        String sensitiveValue = request.get("value");
        if (sensitiveValue == null) {
            return "Value is required";
        }

        try {
            EncryptRequest encryptRequest = EncryptRequest.builder()
                    .keyId(KMS_KEY_ID)
                    .plaintext(SdkBytes.fromUtf8String(sensitiveValue))
                    .build();

            SdkBytes encryptedBytes = kmsClient.encrypt(encryptRequest).ciphertextBlob();
            String encryptedString = Base64.getEncoder().encodeToString(encryptedBytes.asByteArray());
            String id = UUID.randomUUID().toString();
            encryptedData.put(id, encryptedString);

            return "Encrypted data stored with ID: " + id;
        } catch (Exception e) {
            e.printStackTrace();
            return "Encryption failed: " + e.getMessage();
        }
    }

    @GetMapping("/decrypt/{id}")
    public String decryptData(@PathVariable String id) {
        String encryptedString = encryptedData.get(id);
        if (encryptedString == null) {
            return "Data not found for ID: " + id;
        }

        try {
            DecryptRequest decryptRequest = DecryptRequest.builder()
                    .ciphertextBlob(SdkBytes.fromByteArray(Base64.getDecoder().decode(encryptedString)))
                    .keyId(KMS_KEY_ID)
                    .build();

            SdkBytes decryptedBytes = kmsClient.decrypt(decryptRequest).plaintext();
            return decryptedBytes.asUtf8String();
        } catch (Exception e) {
            e.printStackTrace();
            return "Decryption failed: " + e.getMessage();
        }
    }
}
```

## Other Data Protection Methods

### Data Minimization

Data minimization is fundamental to sensitive data strategies, reducing risk by limiting breach impact and simplifying compliance. It lowers storage and security costs, enhances user trust through responsible data handling, and minimizes the attack surface. By collecting only essential data, data security is streamlined and overall protection is strengthened.

#### Data Inventory and Classification

For established ecosystems, a comprehensive data inventory and classification is the foundational step. This process involves meticulously identifying all sensitive data assets, classifying them according to a defined sensitivity scale, and clearly documenting their intended purpose. Furthermore, detailed use cases should be recorded within an enterprise data catalog to ensure transparency and maintain a clear understanding of data utilization.

#### Purpose Limitation

Following data identification, implementing purpose-driven data retention is essential. To counter the tendency of indiscriminate data accumulation, key stakeholders from business, data science, and data governance must collaboratively define explicit purposes for data collection and storage. This process should consider specific research objectives, modeling requirements, potential future analyses, and the associated costs of maintaining data for those future needs.

#### Data Retention Policies

Each identified data set should be governed by a clearly defined retention policy that specifies the duration for which the data is actively needed and outlines the procedures for secure deletion or anonymization to support potential future use cases. When anonymization is not feasible, consider archiving data to a lower-cost storage tier with stricter access controls, thereby balancing data preservation with security and cost efficiency.

#### Data Anonymization and Pseudonymization

Anonymization permanently prevents identification by techniques like averaging age, grouping locations, or removing highly sensitive data elements. Pseudonymization uses reversible pseudonyms (e.g.; tokens, hashed IDs) instead of direct identifiers. Both methods enable data analysis while minimizing re-identification risks, enhancing privacy and security.

#### Regular Data Audits

Leveraging the established sensitive data catalog, conduct regular audits to ensure adherence to data minimization principles. These audits should verify the removal or anonymization of unnecessary data, maintaining a lean and secure data environment.

#### Just-in-Time Data Collection

Where operationally feasible, adopt a just-in-time data collection strategy. This approach, exemplified by credit adjudication, involves acquiring data closer to its point of use, minimizing the storage duration and reducing the potential attack surface.

### Data Loss Prevention

Data Loss Prevention (DLP) is crucial for sensitive data strategies, combining technology with policies and employee training. DLP tools discover and classify data, enabling monitoring and preventing unauthorized exfiltration across various channels. Instead of solely blocking, DLP can trigger encryption or other security measures. It aids compliance, enhances data visibility, and proactively mitigates risks.

WEX employs Palo Alto Networks' DLP for comprehensive data discovery and classification across its network, cloud, and endpoints. This facilitates real-time monitoring, granular policy enforcement, and insider threat mitigation. The integrated platform extends protection to cloud applications, ensuring compliance and robust data control.

## Appendices
>
> Any additional information or reference material.

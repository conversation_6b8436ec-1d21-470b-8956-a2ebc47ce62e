<!-- Parent: Guardrails -->
<!-- Parent: Design Specification -->
<!-- Title: RFC156-Idempotency Key in HTTP POST Request Header -->

<!-- Include: macros.md -->
# Idempotency Key in HTTP POST Request Header

:isarb:

```text
Author: <PERSON>
Publish Date: 2024-08-28
Category: Guardrails
Subtype: Design Specification
Ratified By: SARB (2024-09-25)
```

## Overview

This design specification proposes an extension to the [WEX Restful Design Standards and Guidelines on Idempotency Key](https://docs.google.com/document/d/1HdP3MZ71yI3yZZ5ZFNGOiVl5MRa9Hgptvi3BM1-vIjY/edit#heading=h.uz9apa90yjus) that for HTTP POST requests:

* You MUST have an idempotency key for actions that are not inherently idempotent and are not otherwise protected from unintended state modifications
* You SHOULD place the idempotency key in the HTTP Request Header
* You MAY place the idempotency key in the HTTP Request Body

## Reasoning

* The Draft IEFT RFC propose the use of the `Idempotency-Key` HTTP header field to provide a unique identifier for an idempotent request
* POST requests are not inherently idempotent so it will require an idempotency key
* Requests such as PUT or DELETE are inherently idempotent and do not require an idempotency key
* This is to align with existing HTTP conventions on metadata, as the idempotency is a property of the request, not the resource
* Currently, for Kafka events, the idempotency key is stored in the event header. Placing the idempotency key in the HTTP request header will keep it consistent across both the Kafka events and HTTP request

## Reference

* [Draft IEFT RFC: Idempotency Key HTTP Header Field](https://datatracker.ietf.org/doc/html/draft-ietf-httpapi-idempotency-key-header-05)
* [RFC 9110: HTTP Semantics](https://www.rfc-editor.org/rfc/rfc9110.html#name-idempotent-methods)

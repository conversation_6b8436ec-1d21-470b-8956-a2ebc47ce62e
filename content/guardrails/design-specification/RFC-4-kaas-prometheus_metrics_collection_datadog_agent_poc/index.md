<!-- Parent: Guardrails -->
<!-- Parent: Design Specification -->
<!-- Title: RFC4-KaaS: Prometheus Metrics Collection with Datadog Agent Proof of Concept -->
# KaaS: Prometheus Metrics Collection with Datadog Agent Proof of Concept

```text
Author: <PERSON>
Publish Date: 2024-08-14
Group: KaaS Engineering
Category: Guardrails
Subtype: Design Specification
```

## Overview

This project aims to explore approaches for deploying a Datadog Agent for Prometheus metrics collection using different resources such as ECS and EKS. The objective is to establish a consistent and reliable deployment method for the Datadog Agent across multiple KaaS environments.

## Goals

- Outline the benefits and limitations of each approach
- Provide a detailed design specification for each approach
- Establish a deployment plan for each approach, considering a MVP and future iterations

## Scope

This RFC will cover the following approaches:

- ECS
- EKS

The document will detail every step, from resource instance setup to the Datadog Agent installation process. This includes the necessary steps to launch the chosen resource instance, associated pricing, the Datadog installation process, and configuration for Prometheus metrics collection.

Areas considered out of scope for this RFC:

- Environment promotion strategies
- Multi-region Prometheus metrics collection

## Functional Requirements

1. The Datadog Agent must be deployed using the ECS or EKS approaches
2. The Datadog Agent must be able to collect Prometheus metrics
3. The Datadog Agent must be able to redirect logs to the Datadog platform

## Non-Functional Requirements

- Performance
- Security
- Usability

## Design Constraints

### ECS (Elastic Container Service)

#### Resource Instance Setup

Since the Datadog Agent will function independently and won't need to collect metrics from any containers or tasks within the same ECS context, it should operate as a standalone instance, focusing solely on redirecting Prometheus logs ingested from Aiven's endpoint to the Datadog platform.

To simplify management and scaling, the Datadog Agent should be run in Fargate mode, which avoids the need to manage underlying EC2 instances. Follow these steps to set up ECS Fargate with the Datadog Agent:

1. Choose the Fargate launch type.
2. Add the Datadog Agent image to the task definition.
3. Configure the necessary environment variables in the task definition.

Detailed steps for setting up the Datadog Agent in ECS Fargate can be found in the [Datadog documentation](https://docs.datadoghq.com/integrations/ecs_fargate/?tab=webui#create-an-ecs-fargate-task).

##### IaC approach

For the ECS approach, we will use Terraform to manage the infrastructure as code. The Terraform configuration will handle the creation of all required resources for the ECS task definition and service, including IAM roles and policies necessary for the Datadog Agent to collect metrics and logs. Terraform’s AWS provider offers native support for ECS task provisioning through the `aws_ecs_task_definition` resource, making it easier to automate and manage the deployment process.

For container definitions specifications, a similar structure to the following example can be used:

```hcl
resource "aws_ecs_task_definition" "datadog_agent" {
  family                   = "datadog-agent"
  network_mode             = "awsvpc"
  cpu                      = 256
  memory                   = 512
  requires_compatibilities = ["FARGATE"]

  execution_role_arn = aws_iam_role.datadog_agent_execution_role.arn
  task_role_arn      = aws_iam_role.datadog_agent_task_role.arn

  container_definitions = jsonencode([
    {
    "containerDefinitions": [
        {
        "name": "datadog-agent",
        "image": "public.ecr.aws/datadog/agent:latest",
        "cpu": 100,
        "memory": 512,
        "essential": true,
        "mountPoints": [
            {
            "containerPath": "/var/run/docker.sock",
            "sourceVolume": "docker_sock",
            "readOnly": true
            },
            {
            "containerPath": "/host/sys/fs/cgroup",
            "sourceVolume": "cgroup",
            "readOnly": true
            },
            {
            "containerPath": "/host/proc",
            "sourceVolume": "proc",
            "readOnly": true
            }
        ],
        "environment": [
            {
            "name": "DD_API_KEY",
            "value": "<YOUR_DATADOG_API_KEY>"
            },
            {
            "name": "DD_SITE",
            "value": "datadoghq.com"
            }
        ]
        }
    ],
    "volumes": [
        {
        "host": {
            "sourcePath": "/var/run/docker.sock"
        },
        "name": "docker_sock"
        },
        {
        "host": {
            "sourcePath": "/proc/"
        },
        "name": "proc"
        },
        {
        "host": {
            "sourcePath": "/sys/fs/cgroup/"
        },
        "name": "cgroup"
        }
    ],
    "family": "datadog-agent"
    }
  ])
}
```

#### Pricing

With AWS Fargate, there are no upfront costs and you pay only for the resources you use. You pay for the amount of vCPU, memory, and storage resources consumed by your containerized applications running on Amazon Elastic Container Service (ECS). More details on pricing can be found in the [AWS Fargate pricing documentation](https://aws.amazon.com/fargate/pricing/).

#### Prometheus Metrics Collection

For ingesting Prometheus metrics into Datadog, the Datadog Agent must be configured to collect metrics from the Prometheus endpoint. The Datadog Agent can be configured to collect Prometheus metrics by adding the following configuration to the `openmetrics.d/conf.yaml`  file in the `conf.d/` folder at the root of the Agent’s configuration directory:

```yaml
init_config:

instances:
    - openmetrics_endpoint: http://localhost:9090/metrics
      namespace: 'documentation_example'
      metrics:
          - promhttp_metric_handler_requests_total: prometheus.handler.requests.total
```

The Agent needs to be restarted after adding the configuration to start collecting metrics from the Prometheus endpoint.

Additional parameters and configurations can be found in the [Datadog documentation](https://docs.datadoghq.com/integrations/guide/prometheus-host-collection/).

### EKS (Elastic Kubernetes Service)

#### Resource Setup

For installing the Datadog Agent in an EKS cluster, there are two primary methods: installing the Datadog Operator as an EKS add-on or deploying the Datadog Agent using a Helm chart. The preferred approach is to use the Helm chart, as it offers greater flexibility and control over the deployment. Additionally, Helm is a more widely adopted tool, making it easier to manage and customize the Datadog Agent setup.

Here’s an example of the Helm chart that can be used to deploy the agent:

```yaml
datadog:
  apiKey: <DATADOG_API_KEY>
  appKey: <DATADOG_APP_KEY>
  criSocketPath: /run/dockershim.sock
  env:
    - name: DD_AUTOCONFIG_INCLUDE_FEATURES
      value: "containerd"
```

> The deployment of Kubernetes resources without using Fabric's app-of-apps isn't recommended.

##### Arch App-of-Apps deploy

The app-of-apps pattern is an effective method for organizing applications within ArgoCD, allowing users to manage multiple deployments declaratively. In this pattern, a single "ArgoCD Application" acts as a container for multiple "business applications." By utilizing this approach, we can streamline and scale the deployment of the Datadog Agent within the EKS fabric cluster.

Since the KaaS team currently lacks a fabric ID, we will need to use the fabric ID provided by the Fabric team to create the app-of-apps repository through the `fabric_argocd` Terraform resource. Here's an example of how to create this repository:

```hcl
resource "fabric_argocd" "kaas" {
  fabric_id       = fabric_id.fabric.id
  name            = "kaas"
}
```

This resource will establish an app-of-apps repository named `fabric-kaas-app-of-apps`, enabling the management of multiple applications within the EKS fabric cluster.

The next step involves creating a Helm template within the KaaS app-of-apps repository. This Helm template will specify the Helm chart to be deployed within the EKS fabric cluster. The following is an example of a Helm chart provided by Datadog that can be used to deploy the Datadog Agent in an EKS cluster:

```yaml
datadog:
  apiKey: <DATADOG_API_KEY>
  appKey: <DATADOG_APP_KEY>
  criSocketPath: /run/dockershim.sock
  env:
    - name: DD_AUTOCONFIG_INCLUDE_FEATURES
      value: "containerd"
```

For more Datadog Helm Chart details, refer to the [Datadog Helm Charts repository on GitHub](https://github.com/DataDog/helm-charts/blob/main/charts/datadog/README.md).

#### Pricing estimates

By using EKS, you pay only for the AWS resources you use. You pay for the EC2 instances or Fargate resources you create to run your EKS cluster. More details on pricing can be found in the [Amazon EKS pricing documentation](https://aws.amazon.com/eks/pricing/). As this approach doesn't involves creating a new cluster to run the Datadog Agent, the pricing will be the same as the existing EKS cluster.

#### Prometheus Metrics Collection Configuration

To collect Prometheus metrics using the Datadog Agent, the file `conf.d/openmetrics.d/conf.yaml` located at the root of the Agent’s configuration directory must be updated with the following configuration:

```yaml
init_config:

instances:
    - openmetrics_endpoint: 'localhost:<PORT>/<ENDPOINT>'
      namespace: '<NAMESPACE>'
      metrics:
          - '<METRIC_TO_FETCH>': '<DATADOG_METRIC_NAME>'
```

The Agent needs to be restarted after adding the configuration to start collecting metrics from the Prometheus endpoint.

More details can be found in the [Datadog documentation](https://docs.datadoghq.com/integrations/guide/prometheus-host-collection/).

## System Architecture

- **ECS Approach**

![ECS Approach](./ECS_Approach.png)

- **ECS Approach with IaC**

![ECS Approach with IaC](./ECS_IaC_Approach.png)

- **EKS Approach**

![EKS Approach](./EKS_Approach.png)

- **EKS Approach with Arch App-of-Apps deploy**

![EKS Approach with Arch App-of-Apps deploy](./EKS_App_of_Apps.png)

## User Interface Design

No associated user interface design required.

## Data Model

No associated data model required.

## Dependencies

- Datadog Agent
- AWS ECS or EKS
- Terraform (if using IaC)
- Helm (if using EKS)
- Aiven Prometheus endpoint
- Fabric App-of-Apps (if using EKS)

## Testing Strategy

Manual tests will be conducted to ensure that the Datadog Agent is correctly deployed and configured to collect Prometheus metrics. The tests will cover the following areas:

- Datadog Agent deployment
- Prometheus metrics collection
- Log redirection to the Datadog platform

If the team decides to pursue IaC-based approaches, automated tests will be implemented to validate the Terraform configuration and ensure that the Datadog Agent is correctly deployed.

## Deployment Plan

As this RFC is a Proof of Concept, the deployment plan will be divided into two phases: MVP and future iterations.

## Maintenance and Support

Maintenance and support will be provided by the KaaS Engineering team. The team will be responsible for ensuring that
the agent is up and running and that the metrics are being received normally.  Version updates may need to be applied
periodically.

## Open questions / risks

- How will the Datadog Agent configuration handle multiple Prometheus endpoints? (Multi-region support)

### References

- [Datadog - Amazon ECS](https://docs.datadoghq.com/containers/amazon_ecs/)
- [Terraform - AWS Provider (ECS Task Definition)](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecs_task_definition)
- [AWS Fargate Pricing](https://aws.amazon.com/fargate/pricing/)
- [Datadog - Prometheus Host Collection](https://docs.datadoghq.com/integrations/guide/prometheus-host-collection/)
- [Datadog Helm Charts](https://github.com/DataDog/helm-charts)

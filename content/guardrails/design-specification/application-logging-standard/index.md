<!-- Parent: Guardrails -->
<!-- Parent: Design Specification -->
<!-- Title: RFC-490 Application Logging Standard -->

<!-- Include: macros.md -->
# Application Logging Standard

```text
Author: Core Standards Board
Title: Application Logging Standard
Publish Date: 2025-04-18
Category: Guardrails
Subtype: Design Specification
```

:draft:

## Introduction

This design specification establishes standards for implementing application logging across the organization. The standard provides clear guidance organized by requirement levels (MUST, SHOULD, MAY) as defined in [IETF RFC 2119](https://www.ietf.org/rfc/rfc2119.txt), allowing engineers to easily assess and remediate compliance based on priority. These standards aim to create a consistent logging approach regardless of the team or department that develops the applications.

> <!-- Info -->
> Info
> **Changelog**
>
> This RFC-490 standard adopts a significantly improved organization compared to the [previous application logging documentation](https://docs.google.com/document/d/119KpwTpVE97MLiOrg1kk6LMrleYTdMOnFkl9yGRHRhM):
>
> - **Requirements organized by priority level** (MUST, SHOULD, MAY) rather than by topic, enabling teams to address critical requirements first
> - **Consistent ID system** with clear prefixes (M-xxx, S-xxx, Y-xxx) for easier reference and tracking
> - **Structured tables** for each requirement section with standardized columns for ID, requirement, and detailed description
> - **Enhanced examples** with implementation patterns
> - **Implementation checklist** to simplify compliance verification

:toc:

## Overview

The Application Logging Standard aims to:

- Establish consistent logging practices across all applications
- Ensure proper collection and handling of log data
- Support operational monitoring, troubleshooting, and debugging
- Enable security audits and compliance reporting
- Provide business context for events when applicable
- Facilitate incident response and root cause analysis

## Compliance Requirements

This standard follows [IETF RFC 2119](https://www.ietf.org/rfc/rfc2119.txt) terminology for specifying requirement levels:

- **MUST**: This means that the requirement is an absolute requirement of the specification.
- **SHOULD**: This means that there may exist valid reasons in particular circumstances to ignore a particular requirement, but the full implications must be understood and carefully weighed before choosing a different course.
- **MAY**: This means that an item is truly optional. One vendor may choose to include the item because a particular marketplace requires it or because the vendor feels that it enhances the product while another vendor may omit the same item.

Engineers should address requirements in order of priority:

1. First, ensure all MUST requirements are satisfied
2. Next, address SHOULD requirements where possible
3. Finally, consider MAY requirements as enhancements

## Scope

### In Scope

- Applies to any software services built by the organization
- For COTS products, it is recommended that the logging is configured meeting the must-have requirements outlined in the documentation. If the must-haves cannot be met, they need to be raised and approved by Security Architecture and Compliance teams as part of the implementation and security reviews.

### Out of Scope

- Taxonomy of business events

## MUST Requirements

These requirements are mandatory for all application logging implementations. Non-compliance with any MUST requirement represents a critical deficiency that should be addressed immediately.

### Frameworks and Implementation

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-001</td>
      <td><strong>Use an approved logging framework</strong></td>
      <td>• Use only logging frameworks approved by the Solution Architecture Review Boards<br>• Follow framework-specific best practices<br>• Configure frameworks according to organizational standards<br>• Avoid custom logging implementations unless approved</td>
    </tr>
    <tr>
      <td>M-002</td>
      <td><strong>Do not use deprecated libraries and frameworks</strong></td>
      <td>• Avoid using deprecated logging libraries<br>• Migrate away from unsupported logging frameworks<br>• Keep logging dependencies up-to-date<br>• Follow security advisories for logging components</td>
    </tr>
    <tr>
      <td>M-003</td>
      <td><strong>Conform to common log file schema</strong></td>
      <td>• Follow the schema defined by your Solution Architecture Review Board<br>• Use standard formats for general concerns (e.g., JSON, base/common schema)<br>• Follow event-specific formats for different concerns (security, business, job, host events)<br>• Maintain consistency across applications</td>
    </tr>
  </tbody>
</table>

### Log Levels and Time-Related Requirements

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-004</td>
      <td><strong>Use a standard set of log level categories</strong></td>
      <td>• Implement standardized log levels across all applications<br>• Configure appropriate default log levels for each environment<br>• Document log level usage in application documentation<br>• Use levels appropriately for their intended purpose</td>
    </tr>
    <tr>
      <td>M-005</td>
      <td><strong>Timebox when enabling log collection at trace and debug log levels</strong></td>
      <td>• Do not leave trace or debug logging enabled permanently in production<br>• Restore log levels prior to resolving an incident<br>• Implement time-limited debug logging capability<br>• Document procedures for enabling/disabling verbose logging</td>
    </tr>
    <tr>
      <td>M-006</td>
      <td><strong>Use NTP to ensure server time is accurate</strong></td>
      <td>• Configure all systems to synchronize time with approved NTP servers<br>• Monitor for time drift<br>• Correct time discrepancies promptly<br>• Ensure accurate timestamps for event correlation</td>
    </tr>
  </tbody>
</table>

### Security and Compliance

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-007</td>
      <td><strong>Log for auditing purposes</strong></td>
      <td>• Log all identity life cycle, authentication and authorization workflow events<br>• Record whether authentication/authorization succeeded or failed<br>• Follow the log retention policy and global event logging and monitoring policy<br>• Include relevant details for compliance requirements</td>
    </tr>
    <tr>
      <td>M-008</td>
      <td><strong>Have well-documented policies around the logging of authentication information</strong></td>
      <td>• Document what authentication information is logged<br>• Specify how authentication data is secured in logs<br>• Define retention policies for authentication logs<br>• Ensure compliance with security standards</td>
    </tr>
    <tr>
      <td>M-009</td>
      <td><strong>Do not include logging in the critical path for business processing</strong></td>
      <td>• Design logging to be non-blocking for critical business flows<br>• Implement asynchronous logging where appropriate<br>• Ensure logging failures don't affect core application functions<br>• Consider performance impact of logging operations</td>
    </tr>
    <tr>
      <td>M-010</td>
      <td><strong>Do not contain sensitive information that would fall under PCI, HIPAA, or PII scope</strong></td>
      <td>• Never log payment card details, passwords, SSNs, or PHI<br>• Mask, hash or tokenize sensitive data when contextual information is required<br>• Follow data classification and handling policies<br>• Review logs regularly for sensitive data</td>
    </tr>
  </tbody>
</table>

### Log Management

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-011</td>
      <td><strong>Aggregate and transmit logs to centralized logging platform</strong></td>
      <td>• Send all application logs to the approved central logging solution<br>• Configure appropriate log shipping mechanisms<br>• Ensure logs arrive with all required metadata<br>• Monitor log pipeline for delivery issues</td>
    </tr>
    <tr>
      <td>M-012</td>
      <td><strong>Monitor, detect, and retry failed log transmissions if the logs contain security-related data</strong></td>
      <td>• Implement monitoring for log transmission failures<br>• Configure automatic retry mechanisms<br>• Alert on persistent log delivery failures<br>• Maintain local buffer for logs pending delivery</td>
    </tr>
    <tr>
      <td>M-013</td>
      <td><strong>Follow the Global Information Handling and Classification Policy</strong></td>
      <td>• Classify log data according to organizational policies<br>• Apply appropriate controls based on data classification<br>• Review classification regularly<br>• Train team members on proper data handling procedures</td>
    </tr>
    <tr>
      <td>M-014</td>
      <td><strong>Follow the Global Event Logging and Monitoring Policy</strong></td>
      <td>• Adhere to organization-wide logging standards<br>• Implement required monitoring for log events<br>• Configure alerts for security-relevant log entries<br>• Conduct periodic compliance reviews</td>
    </tr>
    <tr>
      <td>M-015</td>
      <td><strong>Log host lifecycle events</strong></td>
      <td>• Record application started/stopped/paused events<br>• Log unhandled runtime exceptions<br>• Document critical errors/exceptions<br>• Include relevant context with lifecycle events</td>
    </tr>
  </tbody>
</table>

### Audit Requirements

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-016</td>
      <td><strong>Implement automated audit logs for critical security events</strong></td>
      <td>• Log individual access to sensitive data<br>• Record all actions taken with administrative privileges<br>• Track access to audit logs<br>• Log invalid access attempts<br>• Record changes to authentication mechanisms<br>• Track audit log status changes (initialization, stopping, pausing)<br>• Log creation or deletion of system objects<br>• Record system and application startup/shutdown errors<br>• Track file and security policy changes<br>• Log system configuration changes<br>• Record use of elevated privileges and system utilities<br>• Track files accessed and access types<br>• Log security alerts from protection systems</td>
    </tr>
    <tr>
      <td>M-017</td>
      <td><strong>Include required fields in audit trail records</strong></td>
      <td>• Include unique user identification<br>• Record event type/ID<br>• Include precise date and time<br>• Record success/failure indication<br>• Document event origin<br>• Identify affected data/component/resource<br>• Include device identity or location<br>• Record network addresses and protocols where applicable<br>• Log HTTP referrers and browser information for web applications<br>• Record system access attempts (successful and rejected)<br>• Track resource access attempts</td>
    </tr>
  </tbody>
</table>

### Identity and Access Management Events

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-018</td>
      <td><strong>Emit Identity and Access Management events</strong></td>
      <td>• Log all user identity lifecycle events with appropriate event IDs and logging levels<br>• Include administrative changes (username, password, MFA)<br>• Track authentication and authorization events<br>• Record user session states<br>• Log security token operations<br>• Track self-service account operations<br>• Include appropriate context properties with IAM events</td>
    </tr>
  </tbody>
</table>

#### Required IAM Event Details

The following user identity lifecycle events **MUST** be emitted:

<table>
  <colgroup>
    <col style="width: 300px;">
    <col style="width: 100px;">
    <col style="width: 150px;">
    <col style="width: 250px;">
  </colgroup>
  <thead>
    <tr>
      <th>Event</th>
      <th>Event ID</th>
      <th>Level</th>
      <th>Properties</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Administrative username changed</td>
      <td>2018</td>
      <td>Information</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>Administrative password reset</td>
      <td>2020</td>
      <td>Information</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>Administrative MFA challenge reset</td>
      <td>2022</td>
      <td>Information</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>Duplicate user session detected</td>
      <td>2029</td>
      <td>Warning</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>Forgot Username requested</td>
      <td>2012</td>
      <td>Trace</td>
      <td>• UserContext<br>• PreviousUser<br>• CurrentUser</td>
    </tr>
    <tr>
      <td>Forgot Username completed</td>
      <td>2013</td>
      <td>Trace</td>
      <td>• UserContext<br>• PreviousUser<br>• CurrentUser</td>
    </tr>
    <tr>
      <td>Forgot Username challenge failed</td>
      <td>2014</td>
      <td>Warning</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>Inbound User SSO established</td>
      <td>2009</td>
      <td>Trace</td>
      <td>• TokenHash</td>
    </tr>
    <tr>
      <td>Inbound User SSO failure</td>
      <td>2010</td>
      <td>Warning</td>
      <td>• TokenHash</td>
    </tr>
    <tr>
      <td>Invalid user session state</td>
      <td>2028</td>
      <td>Error</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>MFA challenge succeeded</td>
      <td>2015</td>
      <td>Information</td>
      <td>• MFAProvider</td>
    </tr>
    <tr>
      <td>MFA challenge failed</td>
      <td>2016</td>
      <td>Warning</td>
      <td>• MFAProvider</td>
    </tr>
    <tr>
      <td>Outbound User SSO initiated</td>
      <td>2011</td>
      <td>Trace</td>
      <td>• Resource</td>
    </tr>
    <tr>
      <td>Security token issued</td>
      <td>2005</td>
      <td>Information</td>
      <td>• TokenType<br>• TokenHash</td>
    </tr>
    <tr>
      <td>Security token consumed</td>
      <td>2006</td>
      <td>Information</td>
      <td>• TokenType<br>• TokenHash</td>
    </tr>
    <tr>
      <td>Security token rejected</td>
      <td>2007</td>
      <td>Warning</td>
      <td>• TokenType<br>• TokenHash<br>• Reason</td>
    </tr>
    <tr>
      <td>Self-service username changed</td>
      <td>2017</td>
      <td>Information</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>Self-service password changed</td>
      <td>2019</td>
      <td>Information</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>Self-service MFA challenge changed</td>
      <td>2021</td>
      <td>Information</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>User authenticated</td>
      <td>2001</td>
      <td>Information</td>
      <td>• Scheme<br>• IdP<br>• UserType<br>• UserAgent</td>
    </tr>
    <tr>
      <td>User authentication failure</td>
      <td>2002</td>
      <td>Warning</td>
      <td>• Scheme<br>• IdP<br>• RequestedUser</td>
    </tr>
    <tr>
      <td>User authorized</td>
      <td>2003</td>
      <td>Information</td>
      <td>• Resource<br>• Policy</td>
    </tr>
    <tr>
      <td>User authorization failed</td>
      <td>2004</td>
      <td>Warning</td>
      <td>• Resource<br>• Policy</td>
    </tr>
    <tr>
      <td>User External Site Redirect</td>
      <td>2008</td>
      <td>Information</td>
      <td>• Resource<br>• RedirectSSO</td>
    </tr>
    <tr>
      <td>User added</td>
      <td>2023</td>
      <td>Information</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>User disabled</td>
      <td>2024</td>
      <td>Information</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>User enabled</td>
      <td>2025</td>
      <td>Information</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>User removed</td>
      <td>2026</td>
      <td>Information</td>
      <td>• UserContext</td>
    </tr>
    <tr>
      <td>User authorization changed</td>
      <td>2027</td>
      <td>Information</td>
      <td>• UserContext</td>
    </tr>
  </tbody>
</table>

### Data Protection Events

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-019</td>
      <td><strong>Emit data protection events</strong></td>
      <td>• Log file uploads with metadata (size, type, hash)<br>• Record protected data operations<br>• Track data access and modification<br>• Include appropriate context with data protection events</td>
    </tr>
  </tbody>
</table>

#### Required Data Protection Event Details

The following data protection events **MUST** be emitted:

<table>
  <colgroup>
    <col style="width: 300px;">
    <col style="width: 100px;">
    <col style="width: 150px;">
    <col style="width: 250px;">
  </colgroup>
  <thead>
    <tr>
      <th>Event</th>
      <th>Event ID</th>
      <th>Level</th>
      <th>Properties</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>File uploaded</td>
      <td>2100</td>
      <td>Information</td>
      <td>• FileSize<br>• FileType<br>• FileHash</td>
    </tr>
    <tr>
      <td>File upload failure</td>
      <td>2101</td>
      <td>Warning</td>
      <td>• FileSize<br>• FileType<br>• FileHash</td>
    </tr>
    <tr>
      <td>Protected data revealed</td>
      <td>2102</td>
      <td>Information</td>
      <td>• UserContext<br>• TargetProperty</td>
    </tr>
    <tr>
      <td>Protected data reveal failure</td>
      <td>2103</td>
      <td>Warning</td>
      <td>• UserContext<br>• TargetProperty<br>• Reason</td>
    </tr>
    <tr>
      <td>Protected data changed</td>
      <td>2104</td>
      <td>Information</td>
      <td>• UserContext<br>• TargetProperty</td>
    </tr>
  </tbody>
</table>

## SHOULD Requirements

These requirements represent best practices that should be followed unless there's a good reason not to. Non-compliance should be justified and documented.

### Content and Format

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>S-001</td>
      <td><strong>Write log events asynchronously</strong></td>
      <td>• Implement non-blocking logging patterns<br>• Use buffered logging when appropriate<br>• Consider performance impact of synchronous logging<br>• Ensure asynchronous logs are properly flushed during application shutdown</td>
    </tr>
    <tr>
      <td>S-002</td>
      <td><strong>Add useful context to log messages</strong></td>
      <td>• Avoid terse messaging like "USER OPERATION FAILED"<br>• Include specific details about operations and failures<br>• Add context for debugging and troubleshooting<br>• Use structured logging to facilitate searching and analysis</td>
    </tr>
    <tr>
      <td>S-003</td>
      <td><strong>Standardize on timezone across all logs</strong></td>
      <td>• Use a consistent timezone for all log entries<br>• Document timezone usage in application documentation<br>• Consider UTC for systems spanning multiple time zones<br>• Ensure timezone handling is consistent across all components</td>
    </tr>
    <tr>
      <td>S-004</td>
      <td><strong>Standardize on a datetime format</strong></td>
      <td>• Use consistent datetime format across all logging<br>• Follow ISO 8601 or another organization-approved format<br>• Include milliseconds when appropriate<br>• Document datetime format in application specifications</td>
    </tr>
    <tr>
      <td>S-005</td>
      <td><strong>Use a rotation pattern for file log sinks</strong></td>
      <td>• Implement log file rotation based on size or time intervals<br>• Configure appropriate retention periods for rotated logs<br>• Use standardized naming patterns for rotated log files<br>• Follow SARB guidance on rotation strategies</td>
    </tr>
    <tr>
      <td>S-006</td>
      <td><strong>Send logs to the logging solution within a reasonable timeframe</strong></td>
      <td>• Transmit logs in near-real time when possible<br>• Configure batch sending with appropriate intervals<br>• Define reasonable timeframe based on LOB requirements<br>• Balance performance and timeliness</td>
    </tr>
  </tbody>
</table>

### Log Content and Security

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>S-007</td>
      <td><strong>Add event attributes</strong></td>
      <td>• Include "when, where, who and what" for each event<br>• Record log date/time, application identifiers, service details<br>• Include user identity information when available<br>• Add event severity, status, and relevant object information<br>• Include stack traces for errors when available</td>
    </tr>
    <tr>
      <td>S-008</td>
      <td><strong>Perform sanitization on all event data</strong></td>
      <td>• Sanitize user input before logging<br>• Remove/escape control characters (CR, LF)<br>• Escape delimiter characters<br>• Prevent log injection attacks</td>
    </tr>
    <tr>
      <td>S-009</td>
      <td><strong>Make log levels changeable at runtime</strong></td>
      <td>• Allow dynamic modification of log levels<br>• Avoid requiring application restart to change logging detail<br>• Implement secure mechanisms for log level changes<br>• Document procedures for changing log levels</td>
    </tr>
    <tr>
      <td>S-010</td>
      <td><strong>Encrypt logs in transit</strong></td>
      <td>• Use secure channels for log transmission (HTTPS, Syslog over TLS)<br>• Configure appropriate TLS versions and cipher suites<br>• Validate certificates for logging endpoints<br>• Follow organizational encryption standards</td>
    </tr>
  </tbody>
</table>

### Log Storage and Collection

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>S-011</td>
      <td><strong>Put logs in standard directories</strong></td>
      <td>• Use industry-standard locations for log files<br>• For Linux: /var/log<br>• For Windows: Windows Event Log or application-specific standard locations<br>• For web servers: Follow server-specific standards (IIS, Nginx, etc.)<br>• Document log locations in application documentation</td>
    </tr>
    <tr>
      <td>S-012</td>
      <td><strong>Store and collect logs in appropriate places</strong></td>
      <td>• Use appropriate log storage mechanisms (files, HEC)<br>• <strong>Explicitly avoid storing logs in database tables</strong><br>• Follow organizational guidance on log storage<br>• Consider performance and operational impact of storage choices</td>
    </tr>
    <tr>
      <td>S-013</td>
      <td><strong>Use a logging proxy class</strong></td>
      <td>• Implement abstraction layer for logging frameworks<br>• Facilitate future logging framework changes<br>• Standardize logging format and behavior<br>• Enable centralized logging configuration</td>
    </tr>
    <tr>
      <td>S-014</td>
      <td><strong>Log the X-REQUEST-ID header</strong></td>
      <td>• Include X-REQUEST-ID in log entries for web requests<br>• Enable cross-service request tracing<br>• Maintain consistent request ID across service boundaries<br>• Sanitize header value to prevent log injection</td>
    </tr>
    <tr>
      <td>S-015</td>
      <td><strong>Emit HTTP client events</strong></td>
      <td>• Log successful HTTP responses<br>• Record 4XX and 5XX result codes with context<br>• Track exceptions during HTTP client operations<br>• Include appropriate request and response details</td>
    </tr>
  </tbody>
</table>

#### Required HTTP Client Event Details

The following HTTP client events **SHOULD** be emitted when they occur:

<table>
  <colgroup>
    <col style="width: 300px;">
    <col style="width: 100px;">
    <col style="width: 150px;">
    <col style="width: 250px;">
  </colgroup>
  <thead>
    <tr>
      <th>Event</th>
      <th>Event ID</th>
      <th>Level</th>
      <th>Properties</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Successful HTTP response</td>
      <td>5000</td>
      <td>Trace</td>
      <td>• RemoteUri<br>• HttpMethod<br>• ResultCode</td>
    </tr>
    <tr>
      <td>4XX HTTP Result Code</td>
      <td>5001</td>
      <td>Trace</td>
      <td>• RemoteUri<br>• HttpMethod<br>• Parameters<br>• ResultCode<br>• Reason</td>
    </tr>
    <tr>
      <td>5XX HTTP Result Code</td>
      <td>5002</td>
      <td>Trace</td>
      <td>• RemoteUri<br>• HttpMethod<br>• Parameters<br>• ResultCode<br>• Reason</td>
    </tr>
    <tr>
      <td>Runtime exception during HTTP client operation</td>
      <td>5003</td>
      <td>Error</td>
      <td>• RemoteUri<br>• Exception</td>
    </tr>
  </tbody>
</table>

## MAY Requirements

These requirements are optional but recommended considerations. They provide enhancements to your logging implementation.

### Optional Enhancements

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Y-001</td>
      <td><strong>Use a logging sidecar for containerized applications</strong></td>
      <td>• Consider sidecar pattern for container logging<br>• Evaluate direct logging versus sidecar approach<br>• Design appropriate log collection for container environments<br>• Follow container orchestration best practices for logging</td>
    </tr>
    <tr>
      <td>Y-002</td>
      <td><strong>Use the following logical log level categories</strong></td>
      <td>• Trace: For verbose granular events useful for debugging<br>• Debug: For occasional events useful for debugging<br>• Info: For occasional events useful for troubleshooting or monitoring<br>• Warning: For abnormal or unexpected events which do not indicate an error condition<br>• Error: For "unit of work" exceptions that do not trigger an application-wide failure<br>• Fatal/Critical: For exceptions that cause the application to terminate execution</td>
    </tr>
  </tbody>
</table>

## Best Practices & Examples

### Logging Architecture Diagrams

#### Logging Architecture Flow

The following diagram illustrates how logs flow from applications to the centralized logging platform:

```mermaid
flowchart LR
    subgraph "Applications"
        A[Web Service] --> |"emit logs"| L
        B[Mobile API] --> |"emit logs"| L
        C[Batch Jobs] --> |"emit logs"| L
        L[Logger Abstraction]
    end
    L --> |"asynchronous"| B1[Buffer]
    B1 --> T[Log Transport]
    T --> |"encrypted"| C1[Central Log Platform]
    C1 --> S1[Storage]
    C1 --> S2[Search]
    C1 --> S3[Alerting]
    C1 --> S4[Dashboards]
    
    classDef critical fill:#f96,stroke:#333,stroke-width:2px;
    classDef secure fill:#6b8e23,stroke:#333,stroke-width:1px;
    class B1,T secure;
    class C1 critical;
```

#### Log Level Decision Tree

This diagram helps developers choose the appropriate log level for different types of events:

```mermaid
flowchart TD
    start[Event to Log] --> q1{Is this granular detail for debugging?}
    q1 -->|Yes| trace[TRACE]
    q1 -->|No| q2{Is this occasional info useful for debugging?}
    q2 -->|Yes| debug[DEBUG]
    q2 -->|No| q3{Is this useful for troubleshooting?}
    q3 -->|Yes| info[INFO]
    q3 -->|No| q4{Is this abnormal but not an error?}
    q4 -->|Yes| warn[WARNING]
    q4 -->|No| q5{Does this indicate a failure of a unit of work?}
    q5 -->|Yes| error[ERROR]
    q5 -->|No| q6{Does this cause application termination?}
    q6 -->|Yes| fatal[FATAL/CRITICAL]
    q6 -->|No| info
    
    classDef default fill:#f9f9f9,stroke:#333,stroke-width:1px;
    classDef level1 fill:#d1e7dd,stroke:#333,stroke-width:1px;
    classDef level2 fill:#cfe2ff,stroke:#333,stroke-width:1px;
    classDef level3 fill:#fff3cd,stroke:#333,stroke-width:1px;
    classDef level4 fill:#f8d7da,stroke:#333,stroke-width:1px;
    classDef level5 fill:#842029,stroke:#333,stroke-width:1px,color:#fff;
    class trace,debug level1;
    class info level2;
    class warn level3;
    class error level4;
    class fatal level5;
```

#### Logging Lifecycle

This sequence diagram shows the complete lifecycle of a log event:

```mermaid
sequenceDiagram
    participant App as Application
    participant Logger as Logger Library
    participant Buffer as Log Buffer
    participant Transport as Log Transport
    participant Central as Central Logging
    participant Storage as Log Storage
    
    App->>Logger: Log Event
    Logger->>Logger: Sanitize Data
    Logger->>Logger: Format Message
    Logger->>Buffer: Write Event
    Note over Buffer: Asynchronous
    Buffer->>Transport: Batch Events
    Transport->>Central: Encrypted Transmission
    Central->>Central: Parse & Normalize
    Central->>Central: Enrich Metadata
    Central->>Storage: Store Log
    Central->>Central: Index for Search
```

#### Event Category Classification

This class diagram visualizes how different types of events are categorized:

```mermaid
classDiagram
    class LogEvent {
        timestamp: DateTime
        level: LogLevel
        message: String
        context: Context
    }
    
    LogEvent <|-- AuthEvent
    LogEvent <|-- SystemEvent
    LogEvent <|-- BusinessEvent
    LogEvent <|-- DataAccessEvent
    LogEvent <|-- ApiEvent
    
    class AuthEvent {
        event_id: 2000-2099
        user_id: String
        auth_method: String
        success: Boolean
    }
    
    class SystemEvent {
        event_id: 3000-3099
        component: String
        state: String
    }
    
    class BusinessEvent {
        event_id: 4000-4099
        transaction_id: String
        operation: String
    }
    
    class DataAccessEvent {
        event_id: 2100-2199
        resource: String
        operation: String
    }
    
    class ApiEvent {
        event_id: 5000-5099
        endpoint: String
        http_method: String
        status_code: Integer
    }
```

#### Central Logging Architecture

This diagram illustrates the components of the central logging solution:

```mermaid
graph TB
    subgraph "Application Layer"
        A1[Web Apps]
        A2[APIs]
        A3[Services]
    end
    
    subgraph "Collection Layer"
        B1[Log Forwarders]
        B2[Log Shippers]
        B3[Direct Integration]
    end
    
    subgraph "Processing Layer"
        C1[Parsing]
        C2[Enrichment]
        C3[Filtering]
        C4[Normalization]
    end
    
    subgraph "Storage Layer"
        D1[Hot Storage]
        D2[Warm Storage]
        D3[Cold Storage]
    end
    
    subgraph "Analysis Layer"
        E1[Search]
        E2[Dashboards]
        E3[Alerts]
        E4[Reports]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    
    B1 --> C1
    B2 --> C1
    B3 --> C1
    
    C1 --> C2
    C2 --> C3
    C3 --> C4
    
    C4 --> D1
    D1 --> D2
    D2 --> D3
    
    D1 --> E1
    D1 --> E2
    D1 --> E3
    D1 --> E4
    
    classDef appLayer fill:#d1e7dd,stroke:#333,stroke-width:1px;
    classDef colLayer fill:#cfe2ff,stroke:#333,stroke-width:1px;
    classDef procLayer fill:#fff3cd,stroke:#333,stroke-width:1px;
    classDef storLayer fill:#f8d7da,stroke:#333,stroke-width:1px;
    classDef anaLayer fill:#e2e3e5,stroke:#333,stroke-width:1px;
    
    class A1,A2,A3 appLayer;
    class B1,B2,B3 colLayer;
    class C1,C2,C3,C4 procLayer;
    class D1,D2,D3 storLayer;
    class E1,E2,E3,E4 anaLayer;
```

### Example Log Formats

#### Structured JSON Log Example

```json
{
  "timestamp": "2025-04-18T14:30:00.123Z",
  "level": "ERROR",
  "logger": "com.example.PaymentService",
  "thread": "http-nio-8080-exec-5",
  "message": "Transaction 234853 failed: cc number checksum incorrect",
  "context": {
    "transaction_id": "234853",
    "user_id": "user_12345",
    "service": "payment_processor",
    "request_id": "7b15094c-eb99-4fa3-9ea1-8933b9b7954a"
  },
  "exception": {
    "class": "com.example.ValidationException",
    "message": "Credit card validation failed: invalid checksum",
    "stacktrace": "com.example.ValidationException: Credit card validation failed: invalid checksum\n\tat com.example.CreditCardValidator.validate(CreditCardValidator.java:42)..."
  },
  "host": "payment-svc-pod-5dfc7b8f9d-2klnm",
  "app_version": "1.5.2"
}
```

#### Authentication Event Example

```json
{
  "timestamp": "2025-04-18T10:15:32.451Z",
  "level": "INFO",
  "event_id": 2001,
  "message": "User authenticated successfully",
  "context": {
    "user_id": "john.smith",
    "scheme": "password",
    "idp": "internal",
    "user_type": "customer",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "request_id": "550e8400-e29b-41d4-a716-446655440000",
    "source_ip": "*************"
  },
  "host": "auth-service-01",
  "app_name": "user-auth-service",
  "app_version": "2.3.1"
}
```

#### Failed API Request Log Example

```json
{
  "timestamp": "2025-04-18T16:20:45.781Z",
  "level": "WARN",
  "event_id": 5001,
  "message": "HTTP request failed with 403 Forbidden",
  "context": {
    "remote_uri": "https://api.payment-processor.com/v1/transactions",
    "http_method": "POST",
    "result_code": 403,
    "reason": "Invalid API key",
    "request_id": "550e8400-e29b-41d4-a716-446655440000",
    "parameters": {
      "transaction_type": "purchase",
      "amount": 129.99
    }
  },
  "host": "order-service-pod-7b9c8d6f5e-3klnp",
  "app_name": "order-service",
  "app_version": "3.2.1"
}
```

### Context Variables for Common Events

For consistent logging across applications, use these standard context variables for common events:

#### Authentication Events

<table>
  <colgroup>
    <col style="width: 200px;">
    <col style="width: 300px;">
    <col style="width: 350px;">
  </colgroup>
  <thead>
    <tr>
      <th>Context Variable</th>
      <th>Description</th>
      <th>Example</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>user_id</td>
      <td>The identifier of the user</td>
      <td>"john.smith"</td>
    </tr>
    <tr>
      <td>user_type</td>
      <td>The type/role of the user</td>
      <td>"admin", "customer"</td>
    </tr>
    <tr>
      <td>authentication_method</td>
      <td>How the user authenticated</td>
      <td>"password", "oauth", "mfa"</td>
    </tr>
    <tr>
      <td>source_ip</td>
      <td>IP address of the user</td>
      <td>"*************"</td>
    </tr>
    <tr>
      <td>success</td>
      <td>Whether authentication succeeded</td>
      <td>true, false</td>
    </tr>
    <tr>
      <td>failure_reason</td>
      <td>Reason for authentication failure</td>
      <td>"invalid_credentials", "account_locked"</td>
    </tr>
  </tbody>
</table>

#### Application Lifecycle Events

<table>
  <colgroup>
    <col style="width: 200px;">
    <col style="width: 300px;">
    <col style="width: 350px;">
  </colgroup>
  <thead>
    <tr>
      <th>Context Variable</th>
      <th>Description</th>
      <th>Example</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>lifecycle_event</td>
      <td>Type of lifecycle event</td>
      <td>"startup", "shutdown", "config_change"</td>
    </tr>
    <tr>
      <td>process_id</td>
      <td>Process ID of the application</td>
      <td>12345</td>
    </tr>
    <tr>
      <td>startup_time</td>
      <td>Time taken for startup</td>
      <td>4231 (milliseconds)</td>
    </tr>
    <tr>
      <td>components</td>
      <td>List of components affected</td>
      <td>["database", "cache", "messaging"]</td>
    </tr>
    <tr>
      <td>environment</td>
      <td>Deployment environment</td>
      <td>"production", "staging", "development"</td>
    </tr>
  </tbody>
</table>

#### Data Access Events

<table>
  <colgroup>
    <col style="width: 200px;">
    <col style="width: 300px;">
    <col style="width: 350px;">
  </colgroup>
  <thead>
    <tr>
      <th>Context Variable</th>
      <th>Description</th>
      <th>Example</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>resource_type</td>
      <td>Type of resource accessed</td>
      <td>"customer_record", "payment_data"</td>
    </tr>
    <tr>
      <td>resource_id</td>
      <td>Identifier of the resource</td>
      <td>"customer_12345"</td>
    </tr>
    <tr>
      <td>operation</td>
      <td>Type of operation performed</td>
      <td>"read", "update", "delete"</td>
    </tr>
    <tr>
      <td>affected_fields</td>
      <td>Fields that were modified</td>
      <td>["email", "phone_number"]</td>
    </tr>
    <tr>
      <td>success</td>
      <td>Whether the operation succeeded</td>
      <td>true, false</td>
    </tr>
    <tr>
      <td>failure_reason</td>
      <td>Reason for operation failure</td>
      <td>"permission_denied", "not_found"</td>
    </tr>
  </tbody>
</table>

## Implementation Checklist

To ensure compliance with this design specification, review your application logging implementation against these criteria:

### MUST Requirements Checklist

- [ ] All MUST requirements (M-001 through M-019) are satisfied

### SHOULD Requirements Checklist

- [ ] All SHOULD requirements (S-001 through S-015) are satisfied or exceptions documented

### MAY Requirements Considerations

- [ ] Applicable MAY requirements (Y-001 through Y-002) have been considered

## References

- [IETF RFC 2119](https://www.ietf.org/rfc/rfc2119.txt) - Key words for use in RFCs
- [Global Information Handling and Classification Policy](https://wexinc.policytech.com/dotNet/documents/?docid=1651)
- [Global Event Logging and Monitoring Policy](https://wexinc.policytech.com/dotNet/documents/?docid=1534)
- [Log Retention Policy](https://wexinc.policytech.com/dotNet/documents/?docid=1428)
- [OWASP Logging Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Logging_Cheat_Sheet.html)
- [Previous Application Logging Standards (v1.0.2)](https://docs.google.com/document/d/119KpwTpVE97MLiOrg1kk6LMrleYTdMOnFkl9yGRHRhM)
- [WEX Secure Logging Standard](https://wexinc.atlassian.net/wiki/spaces/ESA/pages/472416371/WEX+Secure+Logging+Standard)

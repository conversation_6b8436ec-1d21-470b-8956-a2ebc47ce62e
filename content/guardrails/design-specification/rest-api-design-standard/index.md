<!-- Parent: Guardrails -->
<!-- Parent: Design Specification -->
<!-- Title: RFC-489 REST API Design Standard -->

<!-- Include: macros.md -->
# REST API Design Standard

```text
Author: Core Standards Board
Title: REST API Design Standard
Publish Date: 2025-04-17
Category: Guardrails
Subtype: Design Specification
```

:draft:

## Introduction

This design specification establishes standards for designing and implementing RESTful APIs across the organization. The standard provides clear guidance organized by requirement levels (MUST, SHOULD, MAY) as defined in [IETF RFC 2119](https://www.ietf.org/rfc/rfc2119.txt), allowing engineers to easily assess and remediate compliance based on priority. These standards aim to create a consistent API design approach regardless of the team or department that develops them.

> <!-- Info -->
> Info
> **Changelog**
>
> This RFC-489 standard adopts a significantly improved organization compared to the [previous REST API design documentation](https://docs.google.com/document/d/1HdP3MZ71yI3yZZ5ZFNGOiVl5MRa9Hgptvi3BM1-vIjY):
>
> - **Requirements organized by priority level** (MUST, SHOULD, MAY) rather than by topic, enabling teams to address critical requirements first
> - **Consistent ID system** with clear prefixes (M-xxx, S-xxx, Y-xxx) for easier reference and tracking
> - **Structured tables** for each requirement section with standardized columns for ID, requirement, and detailed description
> - **Enhanced examples** with JSON samples for error responses and implementation patterns
> - **Implementation checklist** to simplify compliance verification
> - **Best practices sections** with detailed guidance on error handling, fault tolerance, and optimistic locking

:toc:

## Overview

The REST API Design Standard aims to:

- Establish consistent RESTful API design practices
- Ensure backward compatibility and interoperability
- Improve developer experience for API consumers
- Support API evolution without breaking client implementations
- Enhance security, performance, and maintainability

## Compliance Requirements

This standard follows [IETF RFC 2119](https://www.ietf.org/rfc/rfc2119.txt) terminology for specifying requirement levels:

- **MUST**: This means that the requirement is an absolute requirement of the specification.
- **SHOULD**: This means that there may exist valid reasons in particular circumstances to ignore a particular requirement, but the full implications must be understood and carefully weighed before choosing a different course.
- **MAY**: This means that an item is truly optional. One vendor may choose to include the item because a particular marketplace requires it or because the vendor feels that it enhances the product while another vendor may omit the same item.

Engineers should address requirements in order of priority:

1. First, ensure all MUST requirements are satisfied
2. Next, address SHOULD requirements where possible
3. Finally, consider MAY requirements as enhancements

## MUST Requirements

These requirements are mandatory for all REST API implementations. Non-compliance with any MUST requirement represents a critical deficiency that should be addressed immediately.

### API Design Fundamentals

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-001</td>
      <td><strong>Follow the API First principle</strong></td>
      <td>• Define APIs outside the code first using standard specification language<br>• Get early review feedback from peers and client developers<br>• Focus on domain understanding and generalized business entities/resources<br>• Clearly separate WHAT vs HOW concerns</td>
    </tr>
    <tr>
      <td>M-002</td>
      <td><strong>Provide API specification using OpenAPI/Swagger</strong></td>
      <td>• Use OpenAPI (formerly Swagger) as the standard format<br>• Provide API specification files in YAML for improved readability<br>• Include all endpoints, parameters, responses, and error definitions</td>
    </tr>
    <tr>
      <td>M-003</td>
      <td><strong>Write APIs in U.S. English</strong></td>
      <td>• Use U.S. English for all API components including properties and URIs<br>• Follow consistent terminology across documentation<br>• Localized textual information may be provided in addition to U.S. English</td>
    </tr>
    <tr>
      <td>M-004</td>
      <td><strong>Contain API meta information</strong></td>
      <td>• Include OpenAPI meta information like title, version, description<br>• Provide contact information for API maintainers<br>• Document API purpose and scope</td>
    </tr>
    <tr>
      <td>M-005</td>
      <td><strong>Use Semantic Versioning</strong></td>
      <td>• Follow Semantic Versioning 2.0 rules (MAJOR.MINOR.PATCH)<br>• Increment MAJOR for incompatible API changes<br>• Increment MINOR for backward-compatible new functionality<br>• Increment PATCH for backward-compatible bug fixes</td>
    </tr>
  </tbody>
</table>

### Security and Compatibility

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-006</td>
      <td><strong>Secure API requests</strong></td>
      <td>• Use secure channels (HTTPS) for all API traffic<br>• For internal APIs: implement basic auth and JWT<br>• For customer-facing APIs: implement basic auth, JWT, and bearer tokens<br>• Never transmit credentials in URLs</td>
    </tr>
    <tr>
      <td>M-007</td>
      <td><strong>Don't break backward compatibility</strong></td>
      <td>• Keep all API clients running when changing APIs<br>• Follow rules for compatible extensions<br>• Consider using API Gateway/Proxy for maintaining backward compatibility<br>• Test with existing clients before deploying changes</td>
    </tr>
    <tr>
      <td>M-008</td>
      <td><strong>Always return JSON objects as top-level data structures</strong></td>
      <td>• Return JSON objects (not arrays) as top-level data structures<br>• Support extensibility through additional attributes<br>• Avoid primitive types at the root level<br>• Ensure consistent response format</td>
    </tr>
    <tr>
      <td>M-009</td>
      <td><strong>Use URI versioning when required</strong></td>
      <td>• Include /vN with the major version as a prefix (e.g., /v1/resource)<br>• Don't use minor version in URI (no /v1.2/resource)<br>• Only increment version for backward compatibility breaking changes<br>• Document version changes clearly</td>
    </tr>
  </tbody>
</table>

### JSON Naming and Structure

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-010</td>
      <td><strong>Property names must be ASCII snake_case</strong></td>
      <td>• Format: `^[a-z_][a-z_0-9]*$`<br>• Never use camelCase for JSON properties<br>• Start with lowercase letter or underscore<br>• Use only letters, numbers, and underscores</td>
    </tr>
    <tr>
      <td>M-011</td>
      <td><strong>Use lowercase with hyphens for path segments</strong></td>
      <td>• Example: /shipment-orders/{shipment-order-id}<br>• Use kebab-case (lowercase with hyphens) for paths<br>• Path parameters may use snake_case<br>• Keep paths simple and readable</td>
    </tr>
    <tr>
      <td>M-012</td>
      <td><strong>Use snake_case for query parameters</strong></td>
      <td>• Example: ?filter_type=customer&sort_by=date<br>• Maintain consistency across all query parameters<br>• Follow same pattern as JSON property names</td>
    </tr>
    <tr>
      <td>M-013</td>
      <td><strong>Pluralize resource names</strong></td>
      <td>• Use plural forms for resource names (e.g., /customers instead of /customer)<br>• Keep collection endpoints consistent<br>• Represent resources as collections</td>
    </tr>
    <tr>
      <td>M-014</td>
      <td><strong>Avoid trailing slashes</strong></td>
      <td>• Don't include trailing slashes in URLs<br>• Example: use /resources not /resources/<br>• Maintain consistent URL structure</td>
    </tr>
    <tr>
      <td>M-015</td>
      <td><strong>Avoid actions – think about resources</strong></td>
      <td>• Focus on resources rather than actions<br>• Model state changes as fields on resources<br>• Map actions to appropriate HTTP methods<br>• Use standard CRUD operations when possible</td>
    </tr>
    <tr>
      <td>M-016</td>
      <td><strong>Use domain-specific resource names</strong></td>
      <td>• Avoid CRUD-only resource names<br>• Use domain terminology for resource naming<br>• Reflect business entities in resource names<br>• Name resources based on business concepts</td>
    </tr>
    <tr>
      <td>M-017</td>
      <td><strong>Identify resources and sub-resources via path segments</strong></td>
      <td>• Use path structure to reflect resource hierarchy<br>• Example: /customers/{customer-id}/accounts/{account-id}<br>• Keep nesting reasonable (see S-015)<br>• Express relationships through path structure</td>
    </tr>
  </tbody>
</table>

### HTTP Methods and Status Codes

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-018</td>
      <td><strong>Use HTTP methods correctly</strong></td>
      <td>• GET: Read-only operations<br>• POST: Create new resources or process operations<br>• PUT: Full update of existing resources<br>• PATCH: Partial update of existing resources<br>• DELETE: Remove resources</td>
    </tr>
    <tr>
      <td>M-019</td>
      <td><strong>Fulfill safeness and idempotency properties</strong></td>
      <td>• Safe methods: GET, HEAD, OPTIONS<br>• Idempotent methods: PUT, DELETE, and safe methods<br>• Non-idempotent methods require idempotency key<br>• Design operations with idempotency in mind</td>
    </tr>
    <tr>
      <td>M-020</td>
      <td><strong>Use specific HTTP status codes</strong></td>
      <td>• Use appropriate status codes for different scenarios<br>• 2xx for success (200 OK, 201 Created, 204 No Content)<br>• 3xx for redirection (301 Moved, 304 Not Modified)<br>• 4xx for client errors (400 Bad Request, 404 Not Found)<br>• 5xx for server errors (500 Internal Server Error)</td>
    </tr>
    <tr>
      <td>M-021</td>
      <td><strong>Provide error documentation</strong></td>
      <td>• Document error responses with specific status codes<br>• Include error codes, messages, and details<br>• Document validation errors for inputs<br>• Provide clear error messages and resolution guidance</td>
    </tr>
    <tr>
      <td>M-022</td>
      <td><strong>Use 429 with headers for rate limits</strong></td>
      <td>• Use 429 "Too Many Requests" for rate limiting<br>• Include Retry-After header with retry time in seconds<br>• Add X-RateLimit-Limit, X-RateLimit-Remaining headers<br>• Document rate limit policies in API documentation</td>
    </tr>
  </tbody>
</table>

### Data Format and Idempotency

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-023</td>
      <td><strong>Use JSON to encode structured data</strong></td>
      <td>• Use application/json as the default media type<br>• Follow JSON structure guidelines<br>• Ensure properly formatted JSON in all responses<br>• Handle JSON parsing errors gracefully</td>
    </tr>
    <tr>
      <td>M-024</td>
      <td><strong>Use standard date and time formats</strong></td>
      <td>• Use RFC 3339 format for date and time<br>• Example: 2025-04-17T14:30:00Z<br>• Be consistent with timezone handling<br>• Document date/time format requirements</td>
    </tr>
    <tr>
      <td>M-025</td>
      <td><strong>Define format for type Number and Integer</strong></td>
      <td>• Specify minimum/maximum constraints<br>• Document precision requirements<br>• Use strings for decimal numbers requiring exact precision<br>• Include validation rules in API specification</td>
    </tr>
    <tr>
      <td>M-026</td>
      <td><strong>Enforce submission of an idempotency key</strong></td>
      <td>• Require idempotency key for POST requests and non-idempotent actions<br>• Place the key in HTTP Request Header using `Idempotency-Key` header field<br>• Alternatively, place the key in HTTP Request Body<br>• Note: GET, PUT, DELETE don't require an idempotency key<br>• Enable safe retries with consistent state outcomes</td>
    </tr>
  </tbody>
</table>

### Headers and Error Handling

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-027</td>
      <td><strong>Follow hypertext control conventions</strong></td>
      <td>• Use _links for linking to resources<br>• Follow HATEOAS principles when implemented<br>• Maintain consistent link relation structure<br>• Support self-references and navigation links</td>
    </tr>
    <tr>
      <td>M-028</td>
      <td><strong>Do not expose stack traces</strong></td>
      <td>• Never return stack traces in API responses<br>• Provide appropriate error details instead<br>• Log errors server-side with full context<br>• Return standardized error objects with codes</td>
    </tr>
    <tr>
      <td>M-029</td>
      <td><strong>Use content headers correctly</strong></td>
      <td>• Set appropriate Content-Type headers<br>• Handle content negotiation properly<br>• Include accurate Content-Length when available<br>• Support charset specifications where needed</td>
    </tr>
    <tr>
      <td>M-030</td>
      <td><strong>Use only specified proprietary headers</strong></td>
      <td>• Restrict to approved custom headers<br>• Follow naming conventions for custom headers<br>• Prefix custom headers with X-<br>• Document all proprietary headers properly</td>
    </tr>
    <tr>
      <td>M-031</td>
      <td><strong>Propagate specified headers</strong></td>
      <td>• Forward designated headers in service-to-service calls<br>• Preserve trace context throughout request chain<br>• Maintain header values unchanged<br>• Include headers in logging for traceability</td>
    </tr>
  </tbody>
</table>

### Compatibility and Deprecation

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-032</td>
      <td><strong>Obtain approval from clients before deprecating</strong></td>
      <td>• Get client consent before API shutdown<br>• Provide migration support to new endpoints<br>• Document migration paths clearly<br>• Allow sufficient time for client transitions</td>
    </tr>
    <tr>
      <td>M-033</td>
      <td><strong>External partners must agree on deprecation timespan</strong></td>
      <td>• Define reasonable timespan for maintaining deprecated APIs<br>• Get external partner agreement before API adoption<br>• Document deprecation policies in contracts<br>• Provide clear communication channels for deprecation notices</td>
    </tr>
    <tr>
      <td>M-034</td>
      <td><strong>Reflect deprecation in API definition</strong></td>
      <td>• Mark deprecated APIs with deprecated=true in specification<br>• Document what clients should use instead<br>• Include timeline for removal when available<br>• Explain migration path in documentation</td>
    </tr>
    <tr>
      <td>M-035</td>
      <td><strong>Monitor usage of deprecated APIs</strong></td>
      <td>• Track usage of deprecated endpoints<br>• Align deprecation timeline with actual usage<br>• Provide usage reports to stakeholders<br>• Adjust timelines based on adoption of alternatives</td>
    </tr>
    <tr>
      <td>M-036</td>
      <td><strong>Not start using deprecated APIs</strong></td>
      <td>• Do not implement new clients against deprecated APIs<br>• Choose current supported versions for new development<br>• Educate teams about deprecation status<br>• Update documentation to discourage use of deprecated APIs</td>
    </tr>
    <tr>
      <td>M-037</td>
      <td><strong>Publish OpenAPI specification</strong></td>
      <td>• Make API specifications publicly available<br>• Keep specifications updated<br>• Ensure specifications match implementation<br>• Version specification documents appropriately</td>
    </tr>
  </tbody>
</table>

### Hypermedia Requirements

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-038</td>
      <td><strong>Use REST Maturity Level 2</strong></td>
      <td>• Use HTTP verbs and URIs according to REST patterns<br>• Implement standard status codes and response formats<br>• Enable resource manipulation through representations<br>• Follow HTTP semantics consistently</td>
    </tr>
    <tr>
      <td>M-039</td>
      <td><strong>Use full, absolute URIs</strong></td>
      <td>• Always use complete URIs including scheme, host, and path<br>• Don't use relative URIs in responses<br>• Ensure URIs are navigable as-is<br>• Include all necessary path components</td>
    </tr>
    <tr>
      <td>M-040</td>
      <td><strong>Use common hypertext controls</strong></td>
      <td>• Follow consistent link relation patterns<br>• Structure links predictably for machine consumption<br>• Maintain uniform link formats across APIs<br>• Use standard relation names when applicable</td>
    </tr>
    <tr>
      <td>M-041</td>
      <td><strong>Not use link headers with JSON entities</strong></td>
      <td>• Don't use HTTP Link headers with JSON responses<br>• Include link information directly in JSON body<br>• Use _links structure for hypermedia controls<br>• Keep navigation controls within the resource representation</td>
    </tr>
    <tr>
      <td>M-042</td>
      <td><strong>Use 207 for batch or bulk requests</strong></td>
      <td>• Use HTTP status code 207 (Multi-Status) for batch operations<br>• Return detailed status information for each item in the batch<br>• Include individual status codes for each operation<br>• Provide meaningful error messages for failed operations</td>
    </tr>
    <tr>
      <td>M-043</td>
      <td><strong>Specify error response format</strong></td>
      <td>• Include standardized error schema with code, message, details array, and trace_id fields<br>• Never expose internal implementation details or stack traces<br>• Provide specific error codes for different error conditions<br>• Include field-level validation errors when applicable</td>
    </tr>
  </tbody>
</table>

### Custom Headers Requirements

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>M-044</td>
      <td><strong>Use only specific proprietary headers</strong></td>
      <td>Only use the following proprietary headers: X-Request-ID (required), X-Correlation-ID (optional), X-Tenant-ID (conditional), X-Source-System (required), X-Original-Request-Time (required).</td>
    </tr>
    <tr>
      <td>M-045</td>
      <td><strong>Propagate specified headers</strong></td>
      <td>When services call other services: All designated proprietary headers MUST be propagated, the X-Request-ID value must remain unchanged throughout the request chain, headers must be propagated regardless of protocol or transport layer, header values should be logged at each service boundary.</td>
    </tr>
  </tbody>
</table>

## SHOULD Requirements

These requirements represent best practices that should be followed unless there's a good reason not to. Non-compliance should be justified and documented.

### Documentation and Design

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>S-001</td>
      <td><strong>Provide API user manual</strong></td>
      <td>• Document API scope, purpose, and use cases<br>• Include examples of API usage<br>• Describe edge cases and error resolution<br>• Provide getting-started guides for new consumers</td>
    </tr>
    <tr>
      <td>S-002</td>
      <td><strong>Prefer compatible extensions</strong></td>
      <td>• Add only optional, never mandatory fields<br>• Maintain semantic consistency of fields<br>• Keep validation logic compatible<br>• Consider backward compatibility in all changes</td>
    </tr>
    <tr>
      <td>S-003</td>
      <td><strong>Design APIs conservatively</strong></td>
      <td>• Be specific in what your API accepts<br>• Define clear input constraints<br>• Provide meaningful error feedback<br>• Implement comprehensive input validation</td>
    </tr>
    <tr>
      <td>S-004</td>
      <td><strong>Avoid versioning when possible</strong></td>
      <td>• Design for compatibility to minimize versioning<br>• Consider alternative approaches before versioning<br>• Use extension points for future functionality<br>• Plan for evolution without breaking changes</td>
    </tr>
  </tbody>
</table>

### Data Structure and Naming

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>S-005</td>
      <td><strong>Array names should be pluralized</strong></td>
      <td>• Use plural forms for array names (e.g., "items", "customers")<br>• Keep object names in singular form<br>• Be consistent with naming patterns<br>• Follow English language pluralization rules</td>
    </tr>
    <tr>
      <td>S-006</td>
      <td><strong>Boolean property values must not be null</strong></td>
      <td>• Always use true/false for boolean properties<br>• Use enumeration if a null-like state is needed<br>• Consider using absence of field instead of false<br>• Document boolean field behavior clearly</td>
    </tr>
    <tr>
      <td>S-007</td>
      <td><strong>Avoid producing and consuming null values</strong></td>
      <td>• Use absence of field rather than null values<br>• Handle undefined/missing fields appropriately<br>• Consider default values for missing fields<br>• Document field optionality clearly</td>
    </tr>
    <tr>
      <td>S-008</td>
      <td><strong>Avoid returning values as properties</strong></td>
      <td>• Return properties with structured values<br>• Don't use property names as value containers<br>• Structure data in predictable patterns<br>• Use value objects when appropriate</td>
    </tr>
    <tr>
      <td>S-009</td>
      <td><strong>Empty array values should not be null</strong></td>
      <td>• Always use empty array ([]) instead of null<br>• Be consistent with array representation<br>• Consider omitting array entirely if empty and optional<br>• Document array field behavior</td>
    </tr>
    <tr>
      <td>S-010</td>
      <td><strong>Enumerations should be represented as strings</strong></td>
      <td>• Use descriptive strings for enum values<br>• Follow naming convention for enums (e.g., UPPER_SNAKE_CASE)<br>• Document all possible enum values<br>• Consider extensibility when designing enums</td>
    </tr>
    <tr>
      <td>S-011</td>
      <td><strong>Prefer Hyphenated-Pascal-Case for HTTP headers</strong></td>
      <td>• Use consistent HTTP header capitalization<br>• Example: Content-Type, X-Request-ID<br>• Follow HTTP standard header conventions<br>• Be consistent across all custom headers</td>
    </tr>
  </tbody>
</table>

### API Design and Structure

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>S-012</td>
      <td><strong>Use conventional query strings</strong></td>
      <td>• Use standardized query parameter patterns<br>• Implement consistent parameter naming across all APIs<br>• Follow common patterns for filtering and sorting (e.g., filter_by, sort_by)<br>• Document all supported query parameters in API specification</td>
    </tr>
    <tr>
      <td>S-013</td>
      <td><strong>Only use UUIDs if absolutely necessary</strong></td>
      <td>• Prefer simple, readable IDs where possible<br>• Consider UUID drawbacks: verbosity, indexing complexity, readability<br>• Use UUIDs when global uniqueness or security by obscurity is required<br>• Document UUID format requirements (version, presentation format)</td>
    </tr>
    <tr>
      <td>S-014</td>
      <td><strong>Limit number of resources to maximum of 8</strong></td>
      <td>• Keep API surface area manageable and focused<br>• Group related functionality into logical resource collections<br>• Consider resource granularity and domain alignment<br>• Split overly large APIs into multiple domain-specific APIs</td>
    </tr>
    <tr>
      <td>S-015</td>
      <td><strong>Limit number of sub-resources to 3 levels</strong></td>
      <td>• Avoid deep nesting beyond 3 levels (e.g., /a/b/c/d)<br>• Maintain URL readability and usability<br>• Consider alternative representations for complex hierarchies<br>• Use query parameters for filtering instead of deep nesting</td>
    </tr>
  </tbody>
</table>

### Performance and Usability

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>S-016</td>
      <td><strong>Reduce bandwidth needs</strong></td>
      <td>• Minimize response payload size<br>• Use compression when appropriate<br>• Consider partial responses to reduce data transfer<br>• Remove unnecessary fields from responses</td>
    </tr>
    <tr>
      <td>S-017</td>
      <td><strong>Support filtering of resource fields</strong></td>
      <td>• Implement field selection parameters (e.g., fields=id,name,email)<br>• Allow clients to request only needed data<br>• Document field selection syntax<br>• Handle invalid field requests gracefully</td>
    </tr>
    <tr>
      <td>S-018</td>
      <td><strong>Allow optional embedding of sub-resources</strong></td>
      <td>• Enable inclusion of related resources in responses<br>• Use consistent parameter naming (e.g., expand=orders,customers)<br>• Document embedding limitations and performance implications<br>• Make embedding optional (never required for basic functionality)</td>
    </tr>
    <tr>
      <td>S-019</td>
      <td><strong>Prefer cursor-based pagination</strong></td>
      <td>• Use cursor tokens for stable pagination<br>• Support consistent page traversal even with changing data<br>• Include next/previous cursor values in responses<br>• Document pagination parameters clearly</td>
    </tr>
    <tr>
      <td>S-020</td>
      <td><strong>Use simple hypertext controls</strong></td>
      <td>• Implement _links for pagination navigation<br>• Include self, next, prev, first, last links where appropriate<br>• Follow consistent link relation patterns<br>• Make navigation intuitive and machine-processable</td>
    </tr>
  </tbody>
</table>

### Content and Headers

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>S-021</td>
      <td><strong>Prefer standard media type name application/json</strong></td>
      <td>• Use standard content types in Content-Type and Accept headers<br>• Avoid vendor-specific media types when possible<br>• Follow IANA Media Type registry conventions<br>• Maintain consistent content types across APIs</td>
    </tr>
    <tr>
      <td>S-022</td>
      <td><strong>Use Location header instead of Content-Location</strong></td>
      <td>• Follow HTTP standard practices for resource location<br>• Use Location header for newly created resources<br>• Avoid Content-Location unless required for specific use cases<br>• Include absolute URLs in Location headers</td>
    </tr>
    <tr>
      <td>S-023</td>
      <td><strong>Add warning header to deprecated responses</strong></td>
      <td>• Include Warning header when returning deprecated features<br>• Format: Warning: 299 - "Deprecated API - planned removal date 2026-01-01"<br>• Provide clear deprecation messaging with migration paths<br>• Include timeline information when available</td>
    </tr>
    <tr>
      <td>S-024</td>
      <td><strong>Add monitoring for warning headers</strong></td>
      <td>• Track warning header usage in monitoring systems<br>• Alert on high usage of deprecated features<br>• Monitor client adoption of replacement APIs<br>• Use data to inform deprecation timelines</td>
    </tr>
    <tr>
      <td>S-025</td>
      <td><strong>Monitor API usage</strong></td>
      <td>• Implement comprehensive API metrics collection<br>• Track usage patterns by endpoint, client, and version<br>• Use analytics to inform API evolution decisions<br>• Monitor performance, errors, and availability</td>
    </tr>
  </tbody>
</table>

### Collection Parameters and Array Constraints

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>S-026</td>
      <td><strong>Use standard collection formats for query parameters</strong></td>
      <td>• Explicitly define the collection format in the API specification<br>• Use standard collection formats (multi, csv, pipes, ssv, tsv)<br>• Document the chosen format clearly<br>• Be consistent across similar parameters<br>• Consider URL length limitations when choosing formats</td>
    </tr>
    <tr>
      <td>S-027</td>
      <td><strong>Specify maximum and minimum accepted array sizes</strong></td>
      <td>• Always specify maximum allowed items to prevent abuse<br>• Consider defining minimum required items when applicable<br>• Document performance implications of large arrays<br>• Test edge cases with maximum array sizes</td>
    </tr>
  </tbody>
</table>

## MAY Requirements

These requirements are optional but recommended considerations. They provide enhancements to your API implementation.

### URL Structure

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Y-001</td>
      <td><strong>Use /api as first path segment</strong></td>
      <td>• Consider adding /api prefix for API endpoints<br>• Apply consistently across all services if implemented<br>• Helps distinguish API endpoints from web content<br>• Enables shared gateway routing</td>
    </tr>
    <tr>
      <td>Y-002</td>
      <td><strong>Keep URLs verb-free</strong></td>
      <td>• Focus on resources rather than actions in URLs<br>• Map operations to appropriate HTTP methods instead<br>• Maintain RESTful design principles<br>• Express state transitions through resource properties</td>
    </tr>
    <tr>
      <td>Y-003</td>
      <td><strong>Consider using nested/non-nested URLs</strong></td>
      <td>• Choose URL structure based on resource relationships<br>• Use nested paths for strong containment relationships<br>• Consider flat structures for better scalability<br>• Balance between clarity and complexity</td>
    </tr>
  </tbody>
</table>

### Performance and Navigation

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Y-004</td>
      <td><strong>Use gzip compression</strong></td>
      <td>• Compress API responses to reduce bandwidth usage<br>• Apply to large payloads particularly<br>• Use standard Content-Encoding headers<br>• Balance compression vs. CPU overhead</td>
    </tr>
    <tr>
      <td>Y-005</td>
      <td><strong>Use pagination links where applicable</strong></td>
      <td>• Implement standardized navigation links<br>• Include first, last, next, prev links<br>• Enhance API discoverability<br>• Make pagination handling consistent</td>
    </tr>
    <tr>
      <td>Y-006</td>
      <td><strong>Use REST Maturity Level 3 (HATEOAS)</strong></td>
      <td>• Implement hypermedia controls when beneficial<br>• Enable dynamic client navigation<br>• Reduce client coupling to URL structure<br>• Support discoverable API workflows</td>
    </tr>
  </tbody>
</table>

### Content Format and Standards

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Y-007</td>
      <td><strong>Use non-JSON media types when appropriate</strong></td>
      <td>• Use binary formats for binary data when needed<br>• Consider specialized formats for specific use cases<br>• Document all supported media types<br>• Provide consistent content negotiation</td>
    </tr>
    <tr>
      <td>Y-008</td>
      <td><strong>Time durations could conform to ISO 8601</strong></td>
      <td>• Use standardized ISO 8601 format for durations<br>• Example: "P1Y2M3DT4H5M6S" (1 year, 2 months, 3 days, 4 hours, 5 minutes, 6 seconds)<br>• Be consistent with duration representation<br>• Document duration format requirements</td>
    </tr>
    <tr>
      <td>Y-009</td>
      <td><strong>Use standards for country/language/currency codes</strong></td>
      <td>• Use ISO 3166-1 alpha-2 for country codes<br>• Use ISO 639-1 for language codes<br>• Use ISO 4217 for currency codes<br>• Document standards used in API specification</td>
    </tr>
  </tbody>
</table>

### Headers

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Y-010</td>
      <td><strong>Use standardized headers</strong></td>
      <td>• Use standard HTTP headers before creating custom ones<br>• Follow HTTP header specifications and best practices<br>• Maintain consistent header usage across APIs<br>• Document all headers used by the API</td>
    </tr>
    <tr>
      <td>Y-011</td>
      <td><strong>Use Content-Location header</strong></td>
      <td>• Consider for specific access scenarios<br>• Use to indicate canonical URL of a resource<br>• Follow HTTP specification guidelines<br>• Use when response body represents a different resource than requested</td>
    </tr>
    <tr>
      <td>Y-012</td>
      <td><strong>Use Prefer header for processing preferences</strong></td>
      <td>• Consider for client preference indication<br>• Follow RFC 7240 specifications<br>• Support preferences like respond-async, return=minimal, return=representation<br>• Document supported preference tokens</td>
    </tr>
    <tr>
      <td>Y-013</td>
      <td><strong>Consider ETag with If-(None-)Match header</strong></td>
      <td>• Use for caching and concurrency control<br>• Implement optimistic locking with ETags<br>• Support conditional requests<br>• Reduce unnecessary data transfer with 304 responses</td>
    </tr>
  </tbody>
</table>

### Optimistic Locking

<table>
  <colgroup>
    <col style="width: 80px;">
    <col style="width: 400px;">
    <col style="width: 500px;">
  </colgroup>
  <thead>
    <tr>
      <th>ID</th>
      <th>Requirement</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Y-014</td>
      <td><strong>Implement optimistic locking for concurrent updates</strong></td>
      <td>• Consider using ETags with If-Match headers for concurrency control<br>• Include ETags in resource entities for better client experience<br>• Consider using Last-Modified/If-Unmodified-Since as alternative<br>• Consider embedding version numbers in resources for simpler concurrency control</td>
    </tr>
  </tbody>
</table>

## Best Practices & Examples

### Error Response Format

To ensure consistent error handling across APIs:

#### Error Response Schema (relates to M-043)

All error responses MUST include:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable message",
    "details": [
      {
        "field": "field_name",
        "code": "validation_code",
        "message": "Field-specific message"
      }
    ],
    "trace_id": "request-trace-identifier"
  }
}
```

Where:

- `code`: A standardized error code string (e.g., "VALIDATION_ERROR", "RESOURCE_NOT_FOUND")
- `message`: A human-readable description suitable for logging (not for end-user display)
- `details`: Array of specific errors when multiple issues exist
- `trace_id`: Identifier that correlates the error with logs for troubleshooting

#### Error Status Code Mapping (relates to M-020, M-021)

Map common application errors to specific HTTP status codes:

<table>
  <colgroup>
    <col style="width: 250px;">
    <col style="width: 250px;">
  </colgroup>
  <thead>
    <tr>
      <th>Situation</th>
      <th>HTTP Status Code</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Validation error</td>
      <td>400 Bad Request</td>
    </tr>
    <tr>
      <td>Authentication required</td>
      <td>401 Unauthorized</td>
    </tr>
    <tr>
      <td>Permission denied</td>
      <td>403 Forbidden</td>
    </tr>
    <tr>
      <td>Resource not found</td>
      <td>404 Not Found</td>
    </tr>
    <tr>
      <td>Method not allowed</td>
      <td>405 Method Not Allowed</td>
    </tr>
    <tr>
      <td>Conflict with current state</td>
      <td>409 Conflict</td>
    </tr>
    <tr>
      <td>Precondition failed</td>
      <td>412 Precondition Failed</td>
    </tr>
    <tr>
      <td>Request entity too large</td>
      <td>413 Payload Too Large</td>
    </tr>
    <tr>
      <td>Unsupported media type</td>
      <td>415 Unsupported Media Type</td>
    </tr>
    <tr>
      <td>Rate limit exceeded</td>
      <td>429 Too Many Requests</td>
    </tr>
    <tr>
      <td>Internal server error</td>
      <td>500 Internal Server Error</td>
    </tr>
    <tr>
      <td>Service unavailable</td>
      <td>503 Service Unavailable</td>
    </tr>
  </tbody>
</table>

Never expose internal implementation details, stack traces, or sensitive information in error responses. (relates to M-028)

#### Validation Error Example (400 Bad Request) (relates to M-021, M-043)

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The request contains invalid parameters",
    "details": [
      {
        "field": "payment_amount",
        "code": "min_value",
        "message": "Payment amount must be greater than zero"
      },
      {
        "field": "currency_code",
        "code": "invalid_value",
        "message": "Currency code 'XYZ' is not supported"
      }
    ],
    "trace_id": "1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed"
  }
}
```

#### Resource Not Found Example (404 Not Found) (relates to M-020, M-043)

```json
{
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "The requested resource does not exist",
    "details": [
      {
        "field": "payment_id",
        "code": "not_found",
        "message": "No payment found with ID 'pay_123456789'"
      }
    ],
    "trace_id": "5f6e8d7c-abcd-1234-ef56-789abcdef012"
  }
}
```

#### Rate Limiting Example (429 Too Many Requests) (relates to M-022, M-043)

```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Request rate limit exceeded",
    "details": [
      {
        "code": "quota_exceeded",
        "message": "Your API quota has been exceeded. You are allowed 100 requests per minute."
      }
    ],
    "trace_id": "9b8c7d6e-5f4e-3d2c-1b0a-9876abcdef12"
  }
}
```

#### Server Error Example (500 Internal Server Error) (relates to M-028, M-043)

```json
{
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "message": "An internal error occurred",
    "details": [],
    "trace_id": "0a1b2c3d-4e5f-6a7b-8c9d-0e1f2a3b4c5d"
  }
}
```

#### Conflict Error Example (409 Conflict) (relates to M-020, M-043)

```json
{
  "error": {
    "code": "RESOURCE_CONFLICT",
    "message": "The request conflicts with the current state of the resource",
    "details": [
      {
        "field": "account_number",
        "code": "already_exists",
        "message": "An account with this number already exists"
      }
    ],
    "trace_id": "5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b"
  }
}
```

#### Idempotency Error Example (422 Unprocessable Entity) (relates to M-026, M-043)

```json
{
  "error": {
    "code": "IDEMPOTENCY_ERROR",
    "message": "Idempotency key already used with different parameters",
    "details": [
      {
        "field": "idempotency_key",
        "code": "duplicate_with_different_body",
        "message": "Idempotency key '2f614682-7c55-4031-81b6-ebe1b8f79829' was used previously with different request parameters"
      }
    ],
    "trace_id": "1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"
  }
}
```

#### Best Practices for Error Responses

1. **Be specific but not revealing** (relates to M-028, M-043): Provide enough detail to help clients diagnose issues without exposing internal implementation details or security-sensitive information.

2. **Use standardized error codes** (relates to M-020, M-021, M-043): Maintain a company-wide registry of error codes to ensure consistency across all APIs.

3. **Include trace IDs in all responses** (relates to M-043): These IDs should be logged with the complete error details server-side and can be used by support teams to locate the full error context.

4. **Localization considerations** (relates to M-003): The `message` field should be considered for logging purposes, not direct display to end users. Client applications should handle localization based on error codes.

5. **Field-specific errors** (relates to M-021, M-043): When errors relate to specific fields in the request, always include the field name in the error details.

6. **Sort multiple errors logically** (relates to S-003): When returning multiple validation errors, consider sorting them in a logical order (e.g., by field name or by severity).

### Fault Tolerance and Resilience

APIs should be designed with fault tolerance and resilience in mind to ensure they remain operational under various conditions:

#### Circuit Breaking Pattern (relates to S-003, Y-004)

```mermaid
flowchart LR
    Client --> Gateway[API Gateway]
    Gateway --> CircuitBreaker[Circuit Breaker]
    
    subgraph Service
        CircuitBreaker --> ServiceLogic[Service Logic]
    end
    ServiceLogic --> Downstream[Downstream Services]
    
    CircuitBreaker -- "Circuit Open<br>(Service Unavailable)" --> Fallback[Fallback Response]
    Fallback --> Client
    
    classDef closed fill:#9f9,stroke:#484
    classDef open fill:#f99,stroke:#844
    classDef half fill:#ff9,stroke:#884
    
    class CircuitBreaker closed
```

Implementation recommendations:

- Open circuits when error thresholds are reached (e.g., 5 failures in 10 seconds)
- Provide fallback responses during outages
- Gradually test recovery with semi-open state
- Track circuit state with metrics for operational visibility

#### Retries with Backoff (relates to M-019, M-026)

Design clients to handle transient errors gracefully:

```json
{
  "retry_policy": {
    "max_attempts": 3,
    "initial_backoff_ms": 100,
    "max_backoff_ms": 1000,
    "backoff_multiplier": 2.0,
    "jitter_factor": 0.2
  }
}
```

Key considerations:

- Implement exponential backoff for retries
- Add jitter to prevent thundering herd problems
- Respect Retry-After headers when provided
- Limit maximum retries to prevent prolonged failures
- Consider timeout thresholds for each attempt
- Don't retry non-idempotent operations without idempotency keys

#### Bulkhead Pattern (relates to S-003)

Isolate failures to prevent system-wide impact:

```mermaid
flowchart TD
    Gateway[API Gateway] --> ServiceA[Service A <br> Connection Pool A]
    Gateway --> ServiceB[Service B <br> Connection Pool B]
    Gateway --> ServiceC[Service C <br> Connection Pool C]
    ServiceA --> DatabaseA[Database A]
    ServiceB --> DatabaseB[Database B]
    ServiceC --> DatabaseC[Database C]
```

Implementation details:

- Separate thread pools for different services
- Independent connection pools for different databases
- Request-specific timeouts for all external calls
- Resource limits per client/tenant

#### Graceful Degradation (relates to S-003, S-016)

Services should maintain basic functionality during partial outages:

```json
{
  "order": {
    "id": "12345",
    "status": "processing",
    "items": [
      { "id": "item-1", "name": "Widget", "quantity": 2 }
    ],
    "_metadata": {
      "pricing_service_unavailable": true,
      "shipping_estimates_unavailable": true
    }
  }
}
```

Implementation strategies:

- Provide reduced functionality rather than complete failure
- Return cached data when real-time data is unavailable
- Clearly communicate degraded service state to clients
- Cache critical data for offline access
- Implement feature flags for selective disabling
- Return limited data rather than errors when possible
- Clearly communicate degraded state through response metadata

#### Health Check Implementation (relates to M-020, S-025)

Expose standardized health check endpoints:

```json
GET /health

200 OK
{
  "status": "OK",
  "components": {
    "database": "OK",
    "cache": "OK",
    "payment_service": "DEGRADED",
    "shipping_service": "OK"
  },
  "version": "1.2.3",
  "timestamp": "2025-04-17T12:34:56Z"
}
```

Best practices:

- Implement both shallow (simple) and deep (dependency) health checks
- Include component-level status information
- Use standardized status values (OK, DEGRADED, DOWN)
- Cache health check results to prevent cascading load
- Consider security implications of health endpoint data

#### Idempotency Implementation (relates to M-026)

Ensure that repeated identical requests have the same effect as a single request:

- Generate idempotency keys client-side
- Store idempotency keys with request state
- Return cached responses for duplicate requests
- Implement expirations for idempotency keys

##### Idempotency Key Best Practices

- Use UUID v4 for idempotency keys
- Client should generate the key, not the server
- Keys should expire after an appropriate period (e.g., 24 hours)
- Document idempotency key requirements clearly in API documentation
- Include handling for duplicate idempotency keys with different request bodies
- Log idempotency key with each request for debugging
- Include idempotency key in response headers for confirmation

Implementing proper idempotency allows clients to safely retry operations without worrying about duplicate processing, which is especially important for payment operations, resource creation, and other state-changing actions.

##### Using the Idempotency-Key Header (Preferred)

```json
POST /payments HTTP/1.1
Content-Type: application/json
Idempotency-Key: 2f614682-7c55-4031-81b6-ebe1b8f79829

{
  "amount": 100.00,
  "currency": "USD",
  "payment_method": "card",
  "card": {
    "number": "****************",
    "exp_month": 12,
    "exp_year": 2025,
    "cvc": "123"
  }
}
```

##### Using Idempotency Key in Request Body

```json
POST /payments HTTP/1.1
Content-Type: application/json

{
  "idempotency_key": "2f614682-7c55-4031-81b6-ebe1b8f79829",
  "amount": 100.00,
  "currency": "USD",
  "payment_method": "card",
  "card": {
    "number": "****************",
    "exp_month": 12,
    "exp_year": 2025,
    "cvc": "123"
  }
}
```

##### Server-Side Processing of Idempotency Keys

Server implementation should:

1. Generate a cache key from the idempotency key + request path
2. Check if a response exists for this cache key
3. If exists, return cached response with original status code
4. If not exists:
   - Process the request
   - Cache the response with the idempotency key
   - Return the response

```javascript
function processRequest(request) {
  const idempotencyKey = request.headers['idempotency-key'] || request.body.idempotency_key;
  
  if (!idempotencyKey && !isIdempotentMethod(request.method)) {
    return errorResponse(400, 'IDEMPOTENCY_KEY_REQUIRED');
  }
  
  if (idempotencyKey) {
    const cacheKey = generateCacheKey(idempotencyKey, request.path);
    const cachedResponse = responseCache.get(cacheKey);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const response = processActualRequest(request);
    responseCache.set(cacheKey, response, EXPIRATION_TIME);
    return response;
  }
  
  return processActualRequest(request);
}
```

### API First Principle Elaboration

The API First principle is fundamental to our API design approach and deserves additional clarification:

#### Relationship with Agile Development (relates to M-001, M-002, M-004)

API First is not in conflict with agile development principles. APIs should evolve incrementally along with their services, starting with:

- Draft specifications with early peer review
- Iterative enhancements based on implementation feedback
- Breaking changes only for non-production features
- Changes aligned with client implementation cycles

#### Benefits of API First (relates to M-001, M-002, M-037)

Following API First principles provides several key benefits:

- Clear separation between interface design and implementation concerns
- Improved API stability even with complete technology stack changes
- Single source of truth for API specifications
- Enablement of tooling for API discovery, documentation, and quality checks
- Early alignment between service providers and API consumers
- Decoupling of client and provider development cycles

### Optimistic Locking Implementation

Optimistic locking is essential when multiple clients might update the same resource concurrently. The wex standard provides detailed guidance on implementing optimistic locking in RESTful APIs:

#### ETag Header with If-Match Header (relates to Y-013, Y-014)

This approach uses the HTTP ETag mechanism:

```json
GET /orders/123 HTTP/1.1

HTTP/1.1 200 OK
ETag: "xyz123"
{
  "id": "123",
  "total": 39.99,
  "status": "pending"
}

PUT /orders/123 HTTP/1.1
If-Match: "xyz123"
{
  "id": "123",
  "total": 39.99,
  "status": "completed"
}
```

If another client has modified the resource, the server responds with:

```json
HTTP/1.1 412 Precondition Failed
```

#### ETags in Result Entities (Recommended) (relates to Y-013, Y-014)

For better client experience, include the ETag value in the resource representation:

```json
{
  "id": "123",
  "total": 39.99,
  "status": "pending",
  "etag": "xyz123"
}
```

This allows clients to store and use the ETag without parsing HTTP headers.

#### Last-Modified / If-Unmodified-Since (Recommended) (relates to Y-013, Y-014)

Another approach using timestamp-based concurrency control:

```json
GET /orders/123 HTTP/1.1

HTTP/1.1 200 OK
Last-Modified: Wed, 15 Apr 2025 16:00:00 GMT
{
  "id": "123",
  "total": 39.99,
  "status": "pending"
}

PUT /orders/123 HTTP/1.1
If-Unmodified-Since: Wed, 15 Apr 2025 16:00:00 GMT
{
  "id": "123",
  "total": 39.99,
  "status": "completed"
}
```

#### Version Numbers (relates to Y-014)

A simpler approach embeds a version number directly in the resource:

```json
{
  "id": "123",
  "total": 39.99,
  "status": "pending",
  "version": 5
}
```

When updating, the client must include the version number, and the server verifies it matches the current version before updating and incrementing it:

```json
PUT /orders/123
{
  "id": "123",
  "total": 39.99,
  "status": "completed",
  "version": 5
}
```

This approach is simpler but less standardized than using HTTP headers.

### HTTP Methods - Advanced Usage

#### GET with Body (relates to M-018, M-019)

While HTTP semantics technically allow GET requests with bodies, this practice is discouraged:

- Many clients, servers, and proxies don't support or properly handle GET with body
- GET with body can cause unpredictable caching behavior
- Query parameters should be used for filtering and search criteria
- For complex search operations that exceed URL length limitations, consider:
  - POST method with a custom search endpoint
  - SEARCH method (WebDAV) if your infrastructure supports it

If GET with body must be implemented, ensure:

- Documentation clearly states this non-standard behavior
- Fallback options are available for clients that can't support it
- Caching implications are thoroughly understood and documented

## Implementation Checklist

To ensure compliance with this design specification, review your API implementation against these criteria:

### MUST Requirements Checklist

- [ ] All MUST requirements (M-001 through M-041) are satisfied

### SHOULD Requirements Checklist

- [ ] All SHOULD requirements (S-001 through S-025) are satisfied or exceptions documented

### MAY Requirements Considerations

- [ ] Applicable MAY requirements (Y-001 through Y-013) have been considered

## References

- [IETF RFC 2119](https://www.ietf.org/rfc/rfc2119.txt) - Key words for use in RFCs
- [OpenAPI Specification](https://spec.openapis.org/oas/v3.0.3)
- [RFC 7231](https://tools.ietf.org/html/rfc7231) - HTTP/1.1 Semantics and Content
- [Semantic Versioning 2.0](http://semver.org/spec/v2.0.0.html)
- [Prior Version REST API Standards (v1.0.2)](https://docs.google.com/document/d/1HdP3MZ71yI3yZZ5ZFNGOiVl5MRa9Hgptvi3BM1-vIjY)

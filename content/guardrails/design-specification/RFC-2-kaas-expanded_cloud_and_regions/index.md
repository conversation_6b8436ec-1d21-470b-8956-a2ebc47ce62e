<!-- Parent: Guardrails -->
<!-- Parent: Design Specification -->
<!-- Title: RFC2-KaaS: Expanding Regions & Cloud Providers -->
# KaaS: Expanding Regions & Cloud Providers

```text
Author: <PERSON>
Title: KaaS: Expanding Regions & Cloud Providers
Group: KaaS Engineering
Publish Date: 2024-06-01
Category: Guardrails
Subtype: Design Specification
```

## Overview

This document describes a mechanism to define the cloud and region for where kafka resources should be provisioned.

## Goals

The outcome we seek with this RFC is a better technical decision than starting with code first.

To define conventions used to provision kafka resources to a specific set of services.

## Motivation

As a primary motivator, this rfc aims to provide a clear path for provisioning kafka resources to expanded regions and
cloud providers.

As a supporting motivator, there are some concerns, like noisy neighbor, compliance and data segmentation, temporary
sandboxing, that may require a dedicated solution. The standard operating procedure for provisioning kafka resources
will remain on shared infrastructure, and this RFC allows for expansion into dedicated solutions.

## Scope

This RFC considers cloud, locale, and disaster recovery as they relate to provisioning kafka clusters and specialized
configurations.

The proposed solution design extends the work in `kaas-eventing-resources` repository to provision resources in expanded
clouds and regions.

While the solution must consider how to choose the infrastructure services kafka resources will be provisioned on,
this RFC does not consider how to provision the infrastructure services. This is envisioned to be its own RFC.

Additionally, Fabric id integration has been suggested. Fabric id considerations are envisioned to be their own RFC
built on top of the proposal within this document.

Additional areas considered out-of-scope include:

* Provisioning databases as part of an active-active or active-passive kafka technical stack
* Provisioning routing as part of an active-active or active-passive technical stack
* Improved Splunk Logging mechanism
* Replication
* Defaulted cloud settings for when a cloud resource is not defined

## Other Designs/Approaches

We discussed an approach to augment existing resources with the region they could be provisiononed on, e.g. a field on
each topic, user, etc...  [It was decided by the team](https://wexinc.atlassian.net/wiki/spaces/AEAE/whiteboard/************)
that at this time, a simple declaritive approach would suffice.

## Functional Requirements

Some general requirements include:

* Services must be self-service.
* Services must not be single-cluster.
* Services should not require knowledge of the full hostname.
* Services must have logging enabled.
* Services must have monitoring enabled.

## Non-Functional Requirements

Some guiding principles for how this RFC has approached design and prototyping include:

* [Pragmatism](https://en.wikipedia.org/wiki/Pragmatism)
* [DRY](https://en.wikipedia.org/wiki/Don%27t_repeat_yourself)
* [Modularity](https://en.wikipedia.org/wiki/Modular_design)
* [Abstraction](https://en.wikipedia.org/wiki/Abstraction_(computer_science))
* [Explicit is better than implicit](https://peps.python.org/pep-0020/#the-zen-of-python)
* [Hub and Spoke](https://en.wikipedia.org/wiki/Spoke%E2%80%93hub_distribution_paradigm)
* [Keep it simple](https://en.wikipedia.org/wiki/KISS_principle#In_software_development)

## Design Constraints

### New Cloud Kind

To model multi-cloud, we should introduce a new kind, `cloud`, which will define how resources for team domains are
provisioned.

An idea behind the `cloud` kind is that it becomes a dedicated place to define cloud provider-specific details separate
from the resource definitions.

Some defining characteristics of the `cloud` kind are:

1. A team domain is represented in the kafka-eventing-resources repository by a team name, like EPEC.
2. The `cloud` resource must define a field _cloud_ with either an AWS cloud configuration or an AZURE resource.
3. The `cloud` resource must define a field _locale_ to express the continent or geographical region (not to be confused
   with cloud provider regions). This value should be one value of: `us`, `emea`, and `apac`.
4. If multiple _locale_ resources are desired, they must be a new `cloud` resource. e.g. _locale_ should not be a list.
5. Valid _locale_ values should follow the following guidelines, per Cloud Engineering (conversational, not documented):
   * Cloud Utilization: Benefits => Azure, Everything else => AWS
   * AWS has 6 governed regions
     * US => us-east-1, us-west-2
     * EMEA => eu-central-1, eu-west-1
     * APAC => ap-southeast-1, ap-southeast-2
   * AZURE is only available in the US
     * US => eastus, westus2
6. When _cloud_ is combined with _locale_ it becomes more clear that the `cloud` resource must not define both clouds
   as this would result in mixed expressions that would yield invalid configurations.

    For example, if we allowed the _cloud_ field to be an array containing possible values of **aws** and **azure**, but set
    our _locale_ field to **emea** this must result in an error because **emea** is an invalid configuration for **azure**.
7. Configurations defined within a `cloud` resource must be assumed to apply uniformly across three environments, currently defined as:

* DEV
* STAGE
* PROD

### Disaster Recovery

Significant work has already been done to define the disaster recovery strategy[^1] for a set of regions within an
environment. Because provisioning is not a part of this RFC, many details around disaster recovery may more
appropriately apply to a cluster provisioning RFC.

It is worth mentioning that our DR setup results in a symmetrical configuration which is why _locale_ is the unit of
definition that spans multiple regions.

### Shared Tenancy vs Unshared Tenancy

There are cases where a team may want to provision and use a dedicated solution. Dedicated solutions are suitable for
organizations needing high performance, strict compliance, and custom configurations.

A team should have the capability to provision a dedicated solution.

There are cases where shared tenancy is more appropriate. Shared tenancy is ideal for cost-sensitive scenarios,
scalability, rapid deployment, and resource optimization.

A team must have the capability to provision resources on a shared tenancy basis.

### Naming

To cleanly organize resources, we should consider a naming convention that is both human-readable and machine-friendly.
[Reverse Domain Name Notation (RDNN)](https://en.wikipedia.org/wiki/Reverse_domain_name_notation) is a common convention
for naming resources. We should consider this convention for our purposes.

|   Element    | required |                      Description                       |          Example          |
|:------------:|:--------:|:------------------------------------------------------:|:-------------------------:|
|     root     |    X     |  The root domain name of the organization or project.  |            wex            |
|    domain    |    X     |        The domain name of the team or project.         |    shared or team_name    |
|    cloud     |    X     |                  The cloud provider.                   |       aws or azure        |
| environment  |    X     | The environment in which the resource is provisioned.  | sandbox, dev, stage, prod |
|   service    |    X     |       The service of resource being provisioned.       |           cloud           |
|    region    |    X     |    The region in which the resource is provisioned.    |  us-east-1, eu-central-1  |
|    number    |          | The instance number of the resource being provisioned. |        0, 1, 2, 3         |

### Hub and Spoke

The hub and spoke model is a network design where a central hub is connected to multiple spokes. We'll use this
to model how our cloud kafka resources are provisioned.  We'll then scale this model out to support dedicated tenancy
implementations.  Through the naming convention, resources can be logically grouped together.

![hub-spoke](./hub_spoke.png)

We're able to create a logical abstraction around related infrastructures, not only through implicit use of network
definitions but also through the naming convention.

## User Interface Design

User interactions are through yaml configurations submitted to a git repository. The following are examples for valid
and invalid cloud resource specifications provided by users of the system.

The interface design uses some elements of abstraction to make the system more user-friendly. Specifically the use of a
_locale_ field to define the regions in which the resource is provisioned.

This approach tries to balance implementation vs what's theoretically possible with yaml. (e.g. pragmatism)  It also
aims to reduce duplication by normalizing cloud definitions away from resource definitions (e.g. DRY).

Example 1: default.yml

```yaml
---
kind: cloud
description: define the default cloud settings
cloud: aws
locale: us
```

Example 2: multi-locale.yml

```yaml
---
- kind: cloud
  description: define a multi-locale scenario which builds resourcing in both the US and AMEA
  cloud: aws
  locale: us

- kind: cloud
  description: define a multi-locale scenario which builds resourcing in both the US and AMEA
  cloud: aws
  locale: emea
```

Example 3: multi-cloud.yml

```yaml
---
- kind: cloud
  description: define a multi-locale scenario in aws
  cloud: aws
  locale: us

- kind: cloud
  description: define a multi-locale scenario in aws
  cloud: aws
  locale: emea

- kind: cloud
  description: and also define a multi-cloud scenario
  cloud: azure
  locale: us
```

Example 4: invalid.yml

```yaml
---
- kind: cloud
  description: non-us azure regions aren’t supported and therefore should produce an error
  cloud: azure
  locale: emea
```

To model disaster recovery, we should extend the `cloud` kind to define a set of configurations that describe how
disaster recovery is set up. We do so in a way that should be straight forward for any engineer to understand.

1. `cloud` resource must define a _disaster-recovery_ field. It must have only one value and must be one of
   **active-active** or **active-passive**.

Example 1: default.yml

```yaml
- kind: cloud
  description: defines how the stack should handle disaster-recovery
  cloud: aws
  locale: us
  disaster-recovery: active-active
```

To model shared tenancy, we should extend the `cloud` kind to define shared and unshared tenancy.

1. `cloud` resource must define a _tenancy_ field. It must have only one value and must be one of **shared** or
   **dedicated**.

Example 1: default.yml

```yaml
- kind: cloud
  description: defines how the stack should handle tenancy
  cloud: aws
  locale: us
  disaster-recovery: active-active
  tenancy: shared
```

Example 2: dedicated.yml

```yaml
- kind: cloud
  description: defines how the stack should handle tenancy
  cloud: aws
  locale: us
  disaster-recovery: active-active
  tenancy: dedicated
```

## System Architecture

The following are diagrams of the established design for active-active[^2] and active-passive[^3] architectures.

### Active-Active

![active-active](./active_active.png)

### Active-Passive

![active-passive](./active_passive.png)

### Component Diagram

The following diagrams an additional `cloud` specification and business logic. The business logic would be used to
determine the cloud and locale to determine a target name for the desired infrastructure.

Dependent modules would then utilize the values stored within a cloud mapping that's returned as a map of cloud
configurations. The ideal case is that the configurations are organized by region so that they can be passed cleanly
to other submodules which operate at the regional level.

The thought here is that we will use modularity and abstraction to hide away some of the complexity for manipulating
cloud definition data.

![component.png](component.png)

## Prototype

A prototype version for this can be found in the `kaas-eventing-resources` repository here: <https://github.com/wexinc/kaas-eventing-resources/pull/157>

Notable changes to enable this capability include:

* Data driven approach to defining cloud and locale.
* Modification of Pipeline to trigger changes on non-matrixed (team/env) file.
* Modification of Kafka Spec to allow `cloud` resource
* Modification to ingest `cloud` kinds
* Addition of Cloud module with map handling
* Modification of service name sources

## Data Model

Strict data modeling hasn't been done for this RFC.

## Dependencies

This RFC is considered a foundational RFC, of which, other RFCs will depend on.

## Testing Strategy

The test strategy should fall somewhere within the test automation pyramid[^4].

Unit tests could be considered and would most likely be used to test new validation code, if any.

Component tests most likely won't apply to this project as component isolation of things like terraform components
would prove to be difficult without the underlying saas to support it.

Integration/System tests could be considered to ensure that basic core functionality exists and continues to exist
throughout the life of the system.

## Deployment Plan

No deployment plan has been put forward for this RFC.

## Maintenance and Support

No additional maintenance or support considerations have been put forward for this RFC and suspect that it could be
informed by a deployment plan.

## Questions

> Will users need a cloud.yml file to define the cloud and locale for their resources?

For simplicity cloud.yml is a good starting point, however we could be flexible in the implementation. The important piece
is that the resources defined are specified as `kind: cloud`.

> What repositories does this RFC affect?

The current thought is that it would only affect `kaas-eventing-resources` and would assist in tarting topics, schemas,
and users to the appropriate locale.  It would not include provisioning of the repository which is currently occuring in
`fabric-eventing-infrastructure` or the newer prototype repo `kaas-infrastructure-pilot`.

> Are we giving too much freedom to users to define their cloud and locale?

The current thought is no, however we could put a validation step in place that restricts the number of locales that can
be defined if it becomes a problem.

> What if a team wants to specify as set of resources to one locale and another set to another locale?

The current thought is that we could create two `teams` directories, say one for `us` and one for `emea`.  Teams would
then manage their resources using a singular CODEOWNERs file. This would effectively allow for more complicated
configurations.

## References

[^1]: <https://docs.wexfabric.com/reference/kafka/replication_strategy/#replication-strategy-disaster-recovery>
[^2]: <https://docs.wexfabric.com/reference/kafka/design_considerations/#activeactive>
[^3]: <https://docs.wexfabric.com/reference/kafka/design_considerations/#activepassive-warm-standby>
[^4]: <https://blog.cleancoder.com/uncle-bob/2017/05/05/TestDefinitions.html>

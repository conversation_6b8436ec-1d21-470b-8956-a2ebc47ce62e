<!-- Parent: Guardrails -->
<!-- Parent: Design Specification -->
<!-- Title: RFC3-KaaS: Provisioning Cloud Resources -->
# KaaS: Provisioning Cloud Resources

```text
Author: <PERSON>
Title: KaaS: Provisioning Cloud Resources
Group: KaaS Engineering
Publish Date: 2024-07-19
Category: Guardrails
Subtype: Design Specification
```

<!--
Required sections for ratification:
-->

## Overview

This document describes a mechanism for provisioning kafka cloud infrastructure in expanded regions and clouds.

## Goals

The outcome we seek with this RFC is a better technical decision than starting with code first.

This rfc aims to extend [RFC2-KaaS: Expanding Regions & Cloud Providers](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593755310) by providing an approach to provisioning resources.

## Motivation

As a primary motivator, this rfc aims to provide a clear path for provisioning cloud resources.  The acute need is to
provision resources for the ICFS/Vin mesh project and requires resourcing in AWS EMEA regions. The hope is that this
proposal can scale beyond AWS US and EMEA regions and include a solution for Azure as well.

As a supporting motivator, our cloud infrastructure project has grown beyond its initial scope and is generally accepted
that a v2 version of the repository or solution is needed. This RFC is a step in that direction.

## Scope

The scope for this RFC is limited to provisioning resources for EMEA and APAC regions in AWS and US region in Azure.

As components are built out, we should think in vertical layers. Not only should we think about delivering functionality
within new locales or regions, but we should also think about how to deliver the functionality to our end users.

These additional areas include:

* Kafka
* Kafka Registry
* Mirror Maker
* CDC
* Logging (Splunk)
* Monitoring (Datadog/Prometheus)
* Self-service Documentation
* User Provisioning

The scope for this RFC does not include things like legal requirements or compliance concerns for data restrictions.
This will most likely be handled in later RFCs to document knowledge

The scope for this RFC does not include things like the Datadog Prometheus integration. As new regions are brought online,
a solution for this will need to be considered.

## Functional Requirements

Some general requirements include:

* Services must be self-service.
* Services must not be single-cluster.
* Services should not require knowledge of the full hostname.
* Services must have logging enabled.
* Services must have monitoring enabled.

## Non-Functional Requirements

Some guiding principles for how this RFC has approached design and prototyping include:

* [Pragmatism](https://en.wikipedia.org/wiki/Pragmatism)
* [DRY](https://en.wikipedia.org/wiki/Don%27t_repeat_yourself)
* [Modularity](https://en.wikipedia.org/wiki/Modular_design)
* [Abstraction](https://en.wikipedia.org/wiki/Abstraction_(computer_science))
* [Convention over Configuration](https://en.wikipedia.org/wiki/Convention_over_configuration)
* [Hub and Spoke](https://en.wikipedia.org/wiki/Spoke%E2%80%93hub_distribution_paradigm)
* [Keep it simple](https://en.wikipedia.org/wiki/KISS_principle#In_software_development)

## Design Constraints

Aiven Solution Architects have suggested that WEX utilize a Hub and Spoke model for provisioning resources. In the
[provisioning RFC](../kaas-provisioning_expanded_cloud_and_regions/index.md#hub_and_spoke) we mentioned that hub and
spoke model is a network design where a central hub is connected to multiple spokes. Through a logical abstraction and
the use of implicit network definitions we can enable the hub and spoke architecture.

![hub-spoke](./hub_spoke.png)

Module design is a key constraint. We should design modules that are reusable and can be used across any region to
enable the hub and spoke architecture.  Ideally, this should be done in a way that's not overly complex and is fairly
intuitive.

A strawman module invocation hierarchy is proposed as follows and can be read as "is composed of":

  project
    |- kafka
    |- connect
    |- flink
    |- mirrormaker
    |- dashboard
    |- healthchecks
    |- dns
    |- \<future regional\> component

> **_NOTE:_** This is a proposal for a module composition and does not intend to imply an inheritance hierarchy.

Locale design is another constraint. We should have a way to set configurations for each region separately. This is
important because we may have different requirements for different regions to support active-active designs.

> **_NOTE:_** The following example is how a secondary region within a locale requires configuration to set the
> schema_registry_config.leader_eligibility, preventing the schema registry from being a leader effectively
> making the primary a centralized schema registry.
>
> See: <https://github.com/wexinc/fabric-eventing-infrastructure/pull/53>

Environment design is another constraint. To ensure that we have user access controls built into regional segments, we
must consider how to manage multiples of Aiven projects within an environment.

Naming conventions are another constraint. We should have a way to name resources in a way that align with the
provisioning RFC. This is important so that we can target resources to a specific set of cloud services. See:
[provisioning RFC](../kaas-provisioning_expanded_cloud_and_regions/index.md#naming)

## User Interface Design

The user interface is through a GitOps process. Because there are so many configurations available, it is not feasible
to set each one through a configuration file explicitly. The proposal is to use
[convention over configuration](https://en.wikipedia.org/wiki/Convention_over_configuration) so that users of the
repository can set the configurations they need and the system will have reasonable defaults for the rest.

The following is an example of how the configuration might look:

```yaml
- kind: project
  description: This is a project definition for the WEX Eventing AWS EMEA Dev project
  name: wex-eventing-dev
  domain: shared
  cloud: aws
  locale: us
  regions:
    - name: us-east-1
      kafka:
        version: "3.7"
        plan: "startup-2"
        # TODO: spike additional fields
      connect:
        - name_suffix: 0
          plan: "startup-2"
          # no version specified in config
          # TODO: spike additional fields
      flink:
        - name_suffix: 0
          plan: "startup-2"
          # no version specified in config
          # TODO: not implementing
      mirror_maker:
        - name_suffix: 0
          plan: "startup-2"
          # no version specified in config
          # TODO: spike additional fields
    - name: us-west-2
      kafka:
        version: "3.7"
        plan: "startup-2"
        # TODO: spike additional fields
      connect:
        - name_suffix: 0
          plan: "startup-2"
          # no version specified in config
          # TODO: spike additional fields
      flink:
        - name_suffix: 0
          plan: "startup-2"
          # no version specified in config
          # TODO: not implementing
      mirror_maker:
        - name_suffix: 0
          plan: "startup-2"
          # no version specified in config
          # TODO: spike additional fields
```

An initial prototype is implemented in this pilot branch: <https://github.com/wexinc/kaas-infrastructure-pilot>

Projects should be represented as a set of objects.

* Each project must have a name and domain. Together these must be unique.
* Each project must have a set of regions.
* Each region must have a set of configurations for each of the hub and spoke services
* Each broker must follow the naming convention to ensure provisioning target alignment
* Kafka Connect, Flink, and Mirror Maker must support zero or more instances
* Each broker must specify the major version of kafka it's running.  This helps support maintenance activities.
* Each service instance must specify the plan it intends to use.  This way we can scale specific instances up and down.

> **_NOTE:_** Assumption: The network configuration "cloud_name" must follow the following convention: (custom)-(cloud)-(region)
>
> Examples:
>
> * custom-wex-pcidss-aws-us-east-1
> * custom-wex-pcidss-aws-us-west-2
> * custom-wex-pcidss-aws-eu-west-1
> * custom-wex-pcidss-aws-eu-central-1
>
> Assuming that the cloud_name continues to follow this convention, we can calculate the value making the configuration
> simpler.

## System Architecture

Some architectural and design considerations include:

* The system must be designed to be modular and extensible. The way we can do this is through the use of [terraform modules.](https://developer.hashicorp.com/terraform/language/modules#using-modules).
* The system modules should mirror the hub and spoke architecture, meaning at a minimum there should be modules for Kafka, MirrorMaker, Connect, and Flink.
* Ancillary services like logging and monitoring should be modularized as well. As services are brought online, they should show up in dashboards and monitoring tools.
* The system must be designed so that any part of the terraform code can be run locally. This is important for debugging, testing, and maintenance.
* The system should retain a common interface, e.g. a Makefile interface used by both CI and Users.
* The system should utilize a common configuration files, e.g. a `project`.yaml
* The `project`.yaml should conform to yaml standards and be validated as part of the CI/CD pipeline.

## Implementation Plan

* Architect and pilot a working prototype that demonstrates the ability to provision resources in AWS EMEA regions.
* Spindown the sandbox environment to make room in the US for a prototyping project.
* Spike and implement which remaining fields should be configurable in the Kafka part of the file. This should be conservative.
* Write a terraform module that provisions Kafka resources in AWS US regions in dev. Shift to EU once available.
* Spike which remaining fields should be configurable in the Connect part of the file. This should be conservative.
* Write a terraform module that provisions Connect resources in AWS us regions in dev. Shift to EU once available.
* Spike which remaining fields should be configurable in the MirrorMaker part of the file. This should be conservative.
* Write a terraform module that provisions MirrorMaker resources in AWS us regions in dev. Shift to EU once available.
* Write a module that integrates a project into datadog to ship metrics.
* Write a module that integrates the services in a project building datadog healthchecks.
* Write a module that integrates the services in a project building a dashboard.
* Setup terraform backends for the modules.
* Setup a CI/CD pipeline that builds and tests the terraform modules.
* Setup .github templates and workflows to model after monorepov2.
  * pull-request
  * bug_report
  * feature_request
  * auto-update
  * ci
  * linter
  * opslevel
  * pull-request-task-checker
  * release
* Write a terraform configuration that provisions resources in AWS EMEA regions in dev.
* Write a terraform configuration that provisions resources in AWS EMEA regions in stage.
* Write a terraform configuration that provisions resources in AWS EMEA regions in prod.
* Setup a standard change process for adding new features and configuration to regions.
* Integrate provisioning of kafka resources into utilization of the new clusters.
* Write documentation that describes the elements of a project kind and how to add a new one.

## Data Model

See [User Interface Design](./index.md#user-interface-design)

## Logging

Logging at WEX is a Security Architecture requirement. A proposal for how to implement logging is provided in the
dependencies section.

## Monitoring

Monitoring will be setup using the [Datadog<->Aiven integration](https://aiven.io/docs/integrations/datadog), which is
the existing approach.

## Dependencies

* [Splunk Logging of Kafka using HTTP Event Collector (HEC)](https://docs.google.com/document/d/1SmrbA-tuLYUERO5DSJSDkVYo1jKEpAQhYIpBWUvASfQ/edit)

## Testing Strategy

Testing will be done through the CI/CD pipeline. The CI/CD pipeline will be triggered by pull requests and merges to the
main branch. The CI/CD pipeline will run tests on the terraform modules and configurations.

## Deployment Plan

Deployment of services and resourcess will be done through the CI/CD pipeline. The CI/CD pipeline will be triggered by
pull requests and merges to the main branch.

## Maintenance and Support

Maintenance and support will be provided by the KaaS Engineering team. The team will be responsible for ensuring that
the services are up and running and that the services are monitored and logged.  Version updates may need to be applied
periodically.

## References

Terraform Resource Links:

* <https://registry.terraform.io/providers/aiven/aiven/latest/docs/resources/kafka>
* <https://registry.terraform.io/providers/aiven/aiven/latest/docs/resources/kafka_connect>
* <https://registry.terraform.io/providers/aiven/aiven/latest/docs/resources/kafka_mirrormaker>
* <https://registry.terraform.io/providers/aiven/aiven/latest/docs/resources/flink>

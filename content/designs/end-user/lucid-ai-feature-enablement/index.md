<!-- Parent: Designs -->
<!-- Parent: End User Technology -->
<!-- Title: RFC-477 Lucid AI Feature Enablement -->

<!-- Include: macros.md -->
# Lucid AI Feature Enablement

:draft:

```text
Author: <PERSON>
Publish Date: 2024-03-19
Category: Designs
Subtype: End User Technology
Ratified By: iSARB (2025-03-05)
```

## Executive Summary

Enabling AI functionalities within the existing Lucid platform to enhance user productivity through intelligent diagram modeling and content refinement features. This integration leverages certified tools within our operational environment and requires minimal implementation effort while providing immediate value to approximately 712 users.

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## Change Context

* **Organization:** End User Productivity & AI Technology
* **Purpose:** Enhance user productivity in diagram creation through AI assistance
* **Users:** WEX employees using Lucid (~712 users)
* **Integration Points:** Okta SSO
* **Third-Party Software:** Lucid (Approved Usage)
* **Impacted Products:**
  * Lucid Platform
  
* **Design Review Qualities:**
  * 🔲 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * ✅ 🟡 New Pattern Adoption
  * ✅ 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Feature Enablement | 2024-02 | None |
| Usage Monitoring | 2024-03 | Feature Enablement |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

* ✅ Completed without concerns

#### Data Classification & Protection

Data Protection Controls:

* ✅ Data is protected at rest, in transit, and in use per WEX IT policy
* ✅ HTTPS using TLS 1.2 or higher
* ✅ Access control (permission, role, policy)

#### AI/ML Compliance

* ✅ Yes - includes new AI/ML components
* ✅ Yes - reviewed for AI/ML compliance

### 💼 Business & Enterprise Impact

#### Design Impact Scope

* ✅ Impacts or used by more than 25 users
* ✅ Vendor & Product Risk Assessment completed

#### Procurement Details

Software Type:

* ✅ SaaS

Additional Considerations:

* ✅ Solution is part of Target State Architecture

### 🔄 Operations & Reliability

#### Security Controls

Select the code quality scanning services that are active:

* ✅ Other: `Microsoft Azure OpenAI Service Security Controls`

### Authentication & Authorization

Select the authentication components for WEX Employees:

* ✅ Okta Workforce Identity

Indicate if access is automatically provisioned & deprovisioned:

* ✅ Yes

### Paved Road Adoption

No deviations from standard tools and services.

## Risk Analysis & Dependencies

* 📋 **Critical Assumptions:**
  * Users will require minimal training to utilize AI features
  * Existing Lucid infrastructure will support AI functionality

* ⚠️ **Known Issues:**
  * None identified

## Reference Links

* **Documentation:**
  * [Lucid AI iSARB Presentation](https://docs.google.com/presentation/d/1SvflU0GVEy4ilTBW42ft0R5gi9PDWZ0SY7-weuYMXb0)
  * [Lucid AI Platform](https://lucid.co/platform/ai)
  * [Lucid AI Diagrams](https://www.lucidchart.com/blog/ai-diagram)
  * [Lucid AI Help Documentation](https://help.lucid.co/hc/en-us/articles/31463501699476-AI-in-Lucid)

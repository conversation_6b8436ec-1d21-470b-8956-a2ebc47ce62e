<!-- Parent: Designs -->
<!-- Parent: End User Technology -->
<!-- Title: RFC-500 Apple Intelligence on macOS Implementation -->

<!-- Include: macros.md -->
# Apple Intelligence on macOS Implementation

:isarb:

```text
Author: <PERSON> & Fab<PERSON><PERSON> Go<PERSON>
Publish Date: 2025-05-06
Category: Designs
Subtype: End User Technology
Ratified By: iSARB (2025-04-16)
```

## Executive Summary

Apple's new "Apple Intelligence" in macOS Sequoia integrates AI with personal data through a hybrid system, combining on-device processing, Apple's Private Cloud Compute, and OpenAI's ChatGPT to enhance Siri and writing tools with features like rewrite, proofread, summarization and image creation. However, this raises data privacy concerns, including the security of sensitive information across Apple's systems and third-party integrations.

This solution proposes enabling the use of Apple Intelligence on macOS while disabling External "ChatGPT" Sign-in with MDM restrictions via JAMF. The design includes a plan to reevaluate Apple Intelligence features and settings with every macOS release, as Apple may introduce new functionalities or security enhancements.

This solution drives improvements towards the following Tech Transformation Vectors:

| Vector | Impact |
|--------|--------|
| 🔲 Product Security | N/A |
| 🔲 Reliability | N/A |
| ☑️ Product Innovation Velocity (PiV) | Provides enhanced productivity tools to macOS users |
| 🔲 SaaS Maturity | N/A |
| ☑️ AI/ML Maturity | Enables controlled adoption of Apple's AI features |

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## 💼 Change Context

* **Organization:** End User Technology
* **Purpose:** Enable Apple Intelligence features on macOS while maintaining control over potential privacy concerns
* **Users:** Over 500 WEX employees using macOS computers
* **Integration Points:** Apple Private Cloud Compute, On-device AI processing
* **Third-Party Software:** Apple Intelligence (Approved Usage)
* **Impacted Products:**
  * macOS Sequoia (15.0+) on Apple Silicon devices
* **Design Review Qualities:**
  * 🔲 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * ☑️ 🟡 New Pattern Adoption
  * ☑️ 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Implementation | 2025-05 | iSARB approval |

## Evaluation Criteria

### 🛡️ Product Security

#### Risk Assessment

The current status of risk assessments for this vendor and product:

* 🔲 Not Requested
* 🔲 In Progress
* ☑️ Completed without concerns
* 🔲 Completed with concerns

#### Data Compliance

The following types of Class 1 Protected Data is transmitted, processed, or stored in this solution:

* 🔲 PCI
* 🔲 PII
* 🔲 PHI

Data Protection Controls:

* ☑️ Data is protected at rest, in transit, and in use per WEX IT policy
* ☑️ HTTPS using TLS 1.2 or higher
* 🔲 Message encryption
* 🔲 Database Connections using TLS 1.2 or higher
* 🔲 Row-level data encryption
* 🔲 Transparent data encryption
* 🔲 De-identification (tokenization, hashing, masking, redaction)
* 🔲 Digital Signatures
* ☑️ Access control (permission, role, policy)

Data Retention:

* Some tasks are completed on-device
* For Private Cloud Compute tasks, data is not stored or made accessible to Apple
* For requests sent to ChatGPT, OpenAI must process requests solely for fulfillment and not store requests or responses unless required by law

#### Data Ingress / Egress

This solution uses the following data flow patterns:

* 🔲 Customer-to-WEX traffic
* 🔲 WEX-to-Customer traffic
* 🔲 Vendor-to-WEX traffic
* ☑️ WEX-to-Vendor traffic
* 🔲 Internal system-to-system traffic

For each data flow, provide:

| Origin System | Destination System | Protocol | Authentication | Data Classification | Encryption Methods |
|---------------|-------------------|----------|---------------|-------------------|-------------------|
| WEX macOS Device | Apple Private Cloud Compute | HTTPS | Apple ID | Class 2 | TLS 1.2+ |

```mermaid
flowchart LR
  WEX[WEX macOS Device] -->|On-device processing| LocalAI[Local AI Processing]
  WEX --> |Advanced requests| PCC[Apple Private Cloud Compute]
  
  subgraph "Data Protection Controls"
    PCC --> |Ephemeral Processing| Result[Results returned to device]
    PCC --> |No data storage| Privacy[Privacy-preserving design]
  end
```

#### Authentication & Authorization

WEX Employee Authentication Components:

* 🔲 IdentityIQ SAML
* 🔲 Okta Workforce Identity
* 🔲 Active Directory: `WEXPRODR`
* ☑️ Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* 🔲 Other: `Explain`

WEX Employee automated provisioning & deprovisioning:

* 🔲 Yes
* ☑️ No

#### Network Traffic Patterns

**Inbound Connectivity:**

* 🔲 Protected by Imperva WAF
* 🔲 Direct inbound (not WAF-protected)
* 🔲 Customer/vendor traffic
* 🔲 WEX business user traffic  
* 🔲 WEX support user traffic

**Outbound Connectivity:**

* ☑️ To customer/vendor systems
* 🔲 From WEX network (support)
* 🔲 Class 1 data transmission
* ☑️ Class 2 data transmission

**Security Controls:**

* 🔲 Bot protection enabled
* 🔲 Automated certificate renewal
* ☑️ TLS 1.2+ enforcement
* ☑️ Network traffic encryption

### 🤖 AI/ML

Indicate if this design includes any new AI/ML components:

* ☑️ Yes
* 🔲 No

Indicate if all AI/ML components have been reviewed for AI/ML compliance:

* ☑️ Yes
* 🔲 No

### 🔄 Reliability

#### High Availability Controls

* 🔲 Availability Zone Redundancy
* 🔲 Regional Failover
* 🔲 Automated Failover
* 🔲 Multi-Region Active-Active
* 🔲 Load Balancing
* 🔲 Content Delivery Network
* 🔲 Monitoring & Alerting
* ☑️ Other: `Service provided and managed by Apple`

### ☁️ SaaS

#### Key Components

| Component | Description | Technology | Purpose |
|-----------|-------------|------------|---------|
| Writing Tools | AI-powered writing assistant | Apple Intelligence | Rewrite, change tone, proofread, summarize |
| Image Creation | AI-generated image tools | Apple Intelligence | Image Playground, Image Wand, Genmoji |
| Siri | Voice assistant | Apple Intelligence | Rich language understanding, onscreen awareness, personal search, actions |

#### Design Impact Scope

* ☑️ Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* ☑️ Vendor & Product Risk Assessment completed

#### Logging & APM

* 🔲 Splunk Forwarding
* 🔲 DataDog Forwarding
* 🔲 Security Event Logging
* 🔲 Synthetic Testing
* 🔲 Log Archival
* 🔲 Log Purging
* 🔲 Class 1 Data Logging Controls

#### Standards Adoption

No deviations from standard tools, services, and reference architectures.

### Relevant Context

#### MDM Controls for Apple Intelligence

Apple has introduced updated MDM options that enable IT administrators to manage how Apple Intelligence features are used. The following MDM restrictions are available for controlling Apple Intelligence features:

| Restriction | Available in | Description |
|-----------|-------------|------------|
| Allow Image Playground | macOS 15.0.0 | If key value is set to FALSE, prohibits the use of image generation |
| Allow Writing Tools | macOS 15.0.0 | If key value is set to FALSE, allows only anonymous access to external services |
| Allow Genmoji | macOS 15.0.0 | If key value is set to FALSE, disables Genmoji |
| Allow Mail Summary | macOS 15.1.0 | If key value is set to FALSE, prohibits the ability to create email summaries |
| Allow External Intelligence Integrations | macOS 15.2.0 | If key value is set to FALSE, prohibits integrations with external services including ChatGPT |
| Allow External Intelligence Sign-Ins | macOS 15.2.0 | If key value is set to FALSE, prohibits the ability to sign in to external services |
| Allow External Intelligence Workspace IDs | macOS 15.3.0 | If key value is set to the correct workspace ID string, Apple Intelligence will only allow specific workspaces |
| Allow Notes Transcription Summary | macOS 15.3.0 | If key value is set to FALSE, disables transcription summarization in Notes |
| Allow Apple Intelligence Report | macOS 15.4.0 | If key value is set to FALSE, disables Apple Intelligence reports |
| Allow Mail Smart Replies | macOS 15.4.0 | If key value is set to FALSE, disables smart replies in Mail |
| Allow Notes Transcription | macOS 15.4.0 | If key value is set to FALSE, disables transcriptions in Notes |
| Allow Safari Summary | macOS 15.4.0 | If key value is set to FALSE, disables the ability to summarize content in Safari |

#### Feature Details

**On-Device Processing:**
A significant portion of Apple Intelligence processing occurs directly on the user's device. This minimizes the need to send sensitive data to external servers such as Apple's Private Cloud Compute.

**Private Cloud Compute:**
Private Cloud Compute uses Apple silicon servers, and data is only used to fulfill the user's request. Data is not stored, and Apple does not have access to it.

**Integration with ChatGPT:**
Apple has partnered with OpenAI to incorporate ChatGPT functionality into Apple Intelligence for advance requests from Siri and writing tools. ChatGPT access is not automatic. Users will be required to give their consent each time an application wants to use ChatGPT to answer a question. Inquiries made through ChatGPT will not be linked to a user's account. OpenAI will not store user requests nor use information for model training when not using a personal or enterprise ChatGPT login.

## Diagrams

### System Context Diagram

```mermaid
graph TB
    User[WEX User]
    macOS[macOS Device]
    LocalAI[Local AI Processing]
    PCC[Apple Private Cloud Compute]
    ChatGPT[OpenAI ChatGPT]
    MDM[JAMF MDM]

    User -->|Uses| macOS
    macOS -->|On-device Processing| LocalAI
    macOS -->|Advanced Processing| PCC
    macOS -.->|Blocked by MDM| ChatGPT
    MDM -->|Configures| macOS

    %% Add annotations for key components
    classDef secure fill:#e6ffe6,stroke:#006600
    classDef external fill:#f9f9f9,stroke:#666666
    classDef blocked fill:#ffe6e6,stroke:#990000
    class LocalAI,PCC external
    class MDM secure
    class ChatGPT blocked
```

### Cloud Architecture Diagram

```mermaid
flowchart TB
    subgraph "WEX Environment"
        direction TB
        User[WEX User]
        macOS[macOS Device]
        JAMF[JAMF MDM]
    end

    subgraph "Apple Infrastructure"
        LocalAI[On-device AI]
        PCC[Private Cloud Compute]
    end

    subgraph "Blocked Services"
        ChatGPT[OpenAI ChatGPT]
    end

    User -->|Interacts with| macOS
    JAMF -->|Manages settings| macOS
    macOS -->|Uses| LocalAI
    macOS -->|Advanced requests| PCC
    macOS -.-x|Blocked by MDM| ChatGPT
    
    %% Add annotations for data classification
    classDef blocked fill:#ffebeb,stroke:#990000
    classDef secure fill:#e6ffe6,stroke:#006600
    class JAMF secure
    class ChatGPT blocked
```

## Risk Analysis & Dependencies

### Key Risks

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Data privacy concerns | Medium | Low | Use MDM to disable ChatGPT integration and rely on Apple's Private Cloud Compute which does not store data |
| Security of sensitive information | Medium | Low | Use MDM controls to restrict features as needed and leverage Apple's on-device processing for sensitive tasks |
| New features in future macOS releases | Medium | High | Reevaluate with each macOS release and update MDM policies as needed |

### Critical Assumptions

* **Apple Privacy Practices:** Apple will continue to maintain strong privacy practices with its Private Cloud Compute services.
* **MDM Control Effectiveness:** JAMF MDM controls will effectively restrict features that pose security concerns.
* **User Adoption:** WEX users will find value in the approved Apple Intelligence features.

### System Dependencies

| Dependency | Type | Criticality | Contact Team |
|------------|------|-------------|--------------|
| JAMF MDM | System | High | Brian Taylor Endpoint Engineering Team |
| macOS Sequoia 15.1+ | System | High | Brian Taylor Endpoint Engineering Team |
| Apple Silicon Hardware | System | High | Brian Taylor Endpoint Engineering Team |

## Reference Links

* **Documentation:**
  * [iSARB Presentation](https://docs.google.com/presentation/d/1Ig74_9kyZBZIvQy9lk-EI_jdGjfN0t64oazPebx-7K0)

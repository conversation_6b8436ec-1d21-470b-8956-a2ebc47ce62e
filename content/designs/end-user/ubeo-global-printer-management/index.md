<!-- Parent: Designs -->
<!-- Parent: End User Technology -->
<!-- Title: RFC-478 UBEO Global Printer Management Solution -->

<!-- Include: macros.md -->
# UBEO Global Printer Management Solution

:draft:

```text
Author: <PERSON>
Publish Date: 2024-03-19
Category: Designs
Subtype: End User Technology
```

## Executive Summary

WEX is implementing a consolidated global printer management solution through UBEO Business Services to replace the current fragmented multi-vendor approach. This solution will centralize printer management, reduce costs by approximately $200K annually, and provide flexible contract terms to accommodate office space changes. The implementation includes deploying a Data Collection Agent (DCA) for automated monitoring and management of approximately 130 printers across global locations.

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## Change Context

* **Organization:** End User Technology
* **Purpose:** Consolidate and modernize WEX's global printer fleet management
* **Users:** All WEX employees using print services
* **Integration Points:** WEX Network, PrinterLogic, SNMP monitoring
* **Third-Party Software:** UBEO DCA (Approved Usage)
* **Impacted Products:**
  * End User Print Services
  * PrinterLogic
* **Design Review Qualities:**
  * ✅ 🟡 New Product or Capability
  * ✅ 🟡 WEX Product Integration
  * ✅ 🟡 Deviation from WEX Standards

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Data Gathering & Internal Alignment | 2025-01-31 | EUC and Networks team availability |
| iSARB Presentation & Approval | 2025-02-07 | Internal alignment completion |
| Procurement & Logistics | 2025-02-14 | iSARB approval |
| Portland HQ Deployment | 2025-02-28 | Procurement completion |
| Regional Deployments | 2025-03-21 | Successful HQ deployment |
| Project Completion | 2025-03-28 | Regional deployments |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

* ✅ Completed without concerns

#### Data Classification & Protection

* **PCI Records:** 0
* **PII Records:** 0
* **PHI Records:** 0

Data Protection Controls:

* ✅ Data is protected at rest, in transit, and in use per WEX IT policy
* ✅ HTTPS using TLS 1.2 or higher
* ✅ Access control (permission, role, policy)

Data Retention:

* Data archived after: 90 days
* Data purged after: 365 days
* Logs purged after: 90 days

#### AI/ML Compliance

* ✅ No AI/ML components

### 💼 Business & Enterprise Impact

#### Design Impact Scope

* ✅ Impacts or used by more than 25 users
* ✅ Software installation & configuration is automated
* ✅ Cost exceeds $25,000
* ✅ Solution is part of Target State Architecture

Alternatives Considered:

* **HPE:** Higher cost, complex implementation, strong global presence
* **BEU:** Limited geographic reach, higher cost, good local expertise
* **Ricoh/Flo-Tech:** Eliminated due to excessive pricing

### 🔄 Operations & Reliability

#### High Availability Controls

* ✅ Monitoring & Alerting
* ✅ Other: DCA redundancy with 3-hour offline tolerance

#### Observability

* ✅ Security Event Logging
* ✅ Log Archival
* ✅ Log Purging

### Data Ingress / Egress

#### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | AWS |
| Primary Region | US East |
| Failover Region | US West |

#### Network Traffic Patterns

**Inbound Connectivity:**

* ✅ WEX business user traffic
* ✅ WEX support user traffic

**Security Controls:**

* ✅ TLS 1.2+ enforcement
* ✅ Network traffic encryption

### Authentication & Authorization

WEX Employees:

* ✅ Built-in user management & authentication

Service Principals:

* ✅ Local user

### Secure Coding

#### Technology Stack

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | Java |
| Operating Systems | Windows |
| Client Platforms | Desktop |

## Diagrams

### System Context Diagram

```mermaid
graph TB
    User[WEX User]
    Printer[Printer Device]
    DCA[UBEO DCA]
    Portal[UBEO Portal]
    
    User -->|Print| Printer
    DCA -->|Monitor| Printer
    DCA -->|Report| Portal
    Portal -->|Manage| Printer
```

## Risk Analysis & Dependencies

* 🚨 **Key Risks:**
  * Network connectivity requirements for SNMP monitoring
  * Printer VLAN configuration changes needed
  * Contract buyout coordination with existing vendors

* 📋 **Critical Assumptions:**
  * SNMP access will be approved for printer monitoring
  * PrinterLogic integration will be maintained
  * Regional office locations will remain stable during deployment

* 🔗 **System Dependencies:**
  * PrinterLogic
  * WEX Network Infrastructure
  * SNMP monitoring services

## Reference Links

* **Documentation:**
  * [UBEO DCA iSARB Presentation](https://docs.google.com/presentation/d/1Z8N0-VrQzNrGjH-tB_KgB2YELZSxfLWx615SUglojtM)

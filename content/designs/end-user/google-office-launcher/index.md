<!-- Parent: Designs -->
<!-- Parent: End User Technology -->
<!-- Title: RFC-476 Google Office Launcher -->

<!-- Include: macros.md -->
# Google Office Launcher

:draft:

```text
Author: <PERSON>
Publish Date: 2025-03-05
Category: Designs
Subtype: End User Technology
Ratified By: iSARB (2025-03-05)
```

## Executive Summary

Microsoft Office 2019 and 2021 are approaching EOL, affecting a large portion of WEX's workforce. This solution proposes deploying a Google Office Launcher application that emulates Microsoft Office workflows using Google Workspace, allowing users to open local files directly while leveraging WEX's existing Google Workspace investment. The solution provides a seamless transition path from Microsoft Office to Google Workspace for document editing.

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## Change Context

* **Organization:** End User Technology
* **Purpose:** Enable Microsoft Office-like desktop integration for Google Workspace
* **Users:** WEX employees using Microsoft Office
* **Integration Points:** Google Workspace, Active Directory
* **Third-Party Software:** Google Office Launcher (Pilot)
* **Impacted Products:**
  * Microsoft Office 2019/2021
  * Google Workspace
* **Design Review Qualities:**
  * 🔲 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * 🔲 🟡 New Pattern Adoption
  * ✅ 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Pilot Deployment | 2025-04-01 | Google Cloud Project setup, SCCM package creation |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

* ✅ Completed with concerns: Client secret file access and storage requires careful handling

#### Data Classification & Protection

Data Protection Controls:

* ✅ Data is protected at rest, in transit, and in use per WEX IT policy
* ✅ HTTPS using TLS 1.2 or higher
* ✅ Access control (permission, role, policy)

### 💼 Business & Enterprise Impact

#### Design Impact Scope

* ✅ Impacts or used by more than 25 users
* ✅ Software installation & configuration is automated

#### Technology Stack

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | .NET Core |
| Operating Systems | Windows |
| Client Platforms | Desktop |

### Authentication & Authorization

Select the authentication components for WEX Employees:

* ✅ Okta Workforce Identity
* ✅ Active Directory: `WEXPRODR`

### Secure Coding

#### Security Controls

* ✅ Client secret encryption at rest
* ✅ OAuth 2.0 authentication flow
* ✅ Secure credential storage using Windows Credential Manager

## Diagrams

### System Context Diagram

```mermaid
graph TB
    User[WEX User]
    Launcher[Google Office Launcher]
    Drive[Google Drive API]
    Docs[Google Workspace]
    AD[Active Directory]
    
    User -->|Opens Document| Launcher
    Launcher -->|Authenticates| AD
    Launcher -->|Uploads| Drive
    Drive -->|Opens| Docs
```

## Risk Analysis & Dependencies

* 🚨 **Key Risks:**
  * Client secret exposure could allow unauthorized Drive API access
  * UNC paths are explicitly excluded from processing
  * Initial VPN connectivity required for first launch

* 📋 **Critical Assumptions:**
  * Users have Google Workspace accounts
  * Active Directory authentication available
  * SCCM deployment capability exists

* ⚠️ **Known Issues:**
  * First-time setup requires VPN connectivity
  * User authentication workflow may impact adoption

## Reference Links

* **Documentation:**
  * [Google Office Launcher iSARB Presentation](https://docs.google.com/presentation/d/1T1dZsb1HEWELadn8s5e7tw_EDc8kAKYmUMYPP7aTOM8)

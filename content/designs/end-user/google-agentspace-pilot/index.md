<!-- Parent: Designs -->
<!-- Parent: End User Technology -->
<!-- Title: RFC-475 Google Agentspace Enterprise AI Pilot -->

<!-- Include: macros.md -->
# Google Agentspace Enterprise AI Pilot

:isarb:

```text
Author: <PERSON>
Publish Date: 2025-03-05
Category: Designs
Subtype: End User Technology
Ratified By: iSARB (2025-03-05)
```

## Executive Summary

WEX is proposing a funded trial of Google Agentspace in partnership with 66 Degrees to accelerate AI agent solutions adoption. The pilot focuses on two key use cases: WEX Health Knowledge Base search and potential Moveworks replacement for JSM ticket information retrieval. This initiative aims to improve enterprise information discovery and automate support workflows, with projected annual productivity savings of 32,000+ hours.

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## Change Context

* **Organization:** End User Productivity & AI Technology
* **Purpose:** Evaluate Google Agentspace for enterprise AI agent capabilities and Moveworks replacement
* **Users:** Technical Services/Support Team, Partner Service Managers, GTS and Digital teams
* **Integration Points:** Google Workspace, Jira Service Management, Knowledge Bases
* **Third-Party Software:** Pilot/POC with Google and 66 Degrees
* **Impacted Products:**
  * WEX Health Knowledge Base
  * Jira Service Management
  * Enterprise Search
* **Design Review Qualities:**
  * ✅ 🟡 New Product or Capability
  * ✅ 🟡 WEX Product Integration
  * ✅ 🟡 New Pattern Adoption

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| POC Setup | 2025-03 | Google/66 Degrees Engagement |
| Initial Testing | 2025-04 | POC Setup |
| Evaluation | 2025-05 | Testing Completion |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

* ✅ Not Requested
* 🔲 In Progress
* 🔲 Completed without concerns
* 🔲 Completed with concerns

#### Data Classification & Protection

Data Protection Controls:

* ✅ Data is protected at rest, in transit, and in use per WEX IT policy
* ✅ HTTPS using TLS 1.2 or higher
* ✅ Access control (permission, role, policy)

### 💼 Business & Enterprise Impact

#### Design Impact Scope

* ✅ Impacts or used by more than 25 users
* ✅ Software installation & configuration is automated
* ✅ Solution is part of Target State Architecture

#### Procurement Details

Software Type:

* ✅ SaaS

Alternatives Considered:

* **Moveworks:** Incumbent solution, struggling with competitive features
* **Glean:** Strong technology but cost-prohibitive at $50 PPM
* **Microsoft Copilot:** Requires GWS migration
* **AWS Bedrock:** Strong contender but complex pricing model
* **Open Source:** Build vs buy decision favors buying

### 🔄 Operations & Reliability

#### High Availability Controls

* ✅ Regional Failover
* ✅ Load Balancing
* ✅ Monitoring & Alerting

#### Observability

* ✅ Security Event Logging
* ✅ Log Archival
* ✅ Log Purging

### Authentication & Authorization

Select the authentication components for WEX Employees:

* ✅ Okta Workforce Identity
* ✅ Other: Google Identity Integration

### Secure Coding

#### Technology Stack

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | Google Agentspace |
| Operating Systems | Cloud-based |
| Client Platforms | web/api |

## Diagrams

### System Context Diagram

```mermaid
graph TB
    User[WEX User]
    AGS[Google Agentspace]
    JSM[Jira Service Management]
    KB[Knowledge Base]
    GWS[Google Workspace]
    
    User -->|Queries| AGS
    AGS -->|Searches| KB
    AGS -->|Creates/Queries| JSM
    AGS -->|Integrates| GWS
```

## Risk Analysis & Dependencies

* 🚨 **Key Risks:**
  * Early-stage product maturity
  * Integration complexity with existing systems
  * Data privacy and compliance requirements

* 📋 **Critical Assumptions:**
  * Google Workspace integration will remain strategic
  * POC success metrics will be clearly defined
  * Existing security controls are sufficient

* 🔗 **System Dependencies:**
  * Google Workspace
  * Jira Service Management
  * Enterprise Knowledge Bases

## Reference Links

* **Documentation:**
  * [Google Agentspace iSARB Presentation](https://docs.google.com/presentation/d/12c_9EVJtZFH4HUE5hsVyZ_ZmfYnQwVWVSb4JD3QOxY0)

<!-- Parent: Designs -->
<!-- Parent: End User Technology -->
<!-- Title: RFC-486 UiPath Agentic Solution for Intelligent Automation -->

<!-- Include: macros.md -->
# UiPath Agentic Solution for Intelligent Automation

:isarb:

```text
Author: <PERSON> <PERSON>
Publish Date: 2025-04-09
Category: Designs
Subtype: End User Technology
```

## Executive Summary

* WEX's Intelligent Automation team proposes to implement UiPath's Agentic Solution to enhance the company's existing UiPath automation platform.
* The solution will leverage AI-powered agents to automate complex business processes, including call center email triage, AML fraud detection, and credit memo reporting.
* Expected business impact includes $1M in cost savings, enhanced employee productivity for 200+ WEX staff, and accelerated innovation velocity in 2025.

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## Change Context

* **Organization:** End User Productivity & AI Technology
* **Purpose:** Enhance WEX's automation capabilities with AI-powered agents to solve complex business problems
* **Users:** WEX Call Center, AML Team, Finance Team, and other business units (200+ employees total)
* **Integration Points:** Existing UiPath platform, email systems, fraud detection systems, financial data sources
* **Third-Party Software:** UiPath Agentic Solution (Extending existing approved UiPath platform)
* **Impacted Products:**
  * Call Center Email Processing
  * AML Fraud Detection
  * Credit Memo Reporting
* **Design Review Qualities:**
  * 🔲 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * ☑ 🟡 New Pattern Adoption
  * ☑ 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Perform Agent Preview Testing | 2025-03-01 | Access to UiPath Preview Environment |
| Formalize Initial Use Cases | 2025-03-21 | Business stakeholder alignment |
| Enable Agents in Dev and Prod Tenant | 2025-04-03 | iSARB approval |
| Begin Use Case Implementation | 2025-04-07 | Enabled Agents in environment |
| Begin Use Case UAT | 2025-Q2 | Completed implementation |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

Confirm the status of risk assessments for this vendor and product:

* 🔲 Not Requested
* 🔲 In Progress
* ☑ Completed without concerns
* 🔲 Completed with concerns: `Explain`

#### Data Classification & Protection

Estimate the number of Class 1 Data records that are transmitted, processed, or stored:

* **PCI Records:** `0`
* **PII Records:** `Varies by use case - PII filtering capability in place`
* **PHI Records:** `Varies by use case - PHI filtering capability in place`

Data Protection Controls:

* ☑ Data is protected at rest, in transit, and in use per WEX IT policy
* ☑ HTTPS using TLS 1.2 or higher
* ☑ Message encryption
* ☑ Database Connections using TLS 1.2 or higher
* ☑ Row-level data encryption
* ☑ Transparent data encryption
* ☑ De-identification (tokenization, hashing, masking, redaction)
* ☑ Digital Signatures
* ☑ Access control (permission, role, policy)

Data Retention:

* Data archived after: `Configurable based on use case requirements`
* Data purged after: `Configurable based on use case requirements`
* Logs purged after: `Configurable based on use case requirements`

#### AI/ML Compliance

Indicate if this design includes any new AI/ML components:

* ☑ Yes
* 🔲 No

Indicate if all AI/ML components have been reviewed for AI/ML compliance:

* ☑ Yes
* 🔲 No

### 💼 Business & Enterprise Impact

#### Design Impact Scope

* ☑ Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* ☑ Vendor & Product Risk Assessment completed

#### Procurement Details

Software Type:

* ☑ SaaS
* ☑ IaaS
* 🔲 AWS PaaS
* ☑ Azure PaaS
* 🔲 Code Dependency Package
* 🔲 Desktop
* 🔲 Mobile App
* 🔲 Browser - Web Browser

Additional Considerations:

* ☑ Software installation & configuration is automated
* 🔲 Cost exceeds $25,000
* ☑ Solution is part of Target State Architecture
* 🔲 Temporary solution planned for replacement

Alternatives Considered:

* **retool:** Limited capabilities in system RPA, data extraction, and agent functionality
* **n8n:** Limited in multi-model support and system RPA capabilities
* **Native AI Assistants (ChatGPT, Gemini):** Lack enterprise security framework and system RPA capabilities; limited to specific models

### 🔄 Operations & Reliability

#### High Availability Controls

* ☑ Availability Zone Redundancy
* ☑ Regional Failover
* ☑ Automated Failover
* 🔲 Multi-Region Active-Active
* ☑ Load Balancing
* 🔲 Content Delivery Network
* ☑ Monitoring & Alerting
* 🔲 Other: `Explain`

#### Outage Impact Assessment

Business Impact:

* 🔲 Critical customer business impact
* ☑ Partial loss of non-critical functionality
* 🔲 Customer recovery assistance required
* ☑ Multiple lines of business affected
* 🔲 Multiple products in LOB affected
* 🔲 Multiple customers affected
* 🔲 External SLA impact
* 🔲 Internal SLA impact
* ☑ Engineering support required

Recovery Actions:

* ☑ Infrastructure deployment/patching
* ☑ Application deployment
* ☑ Configuration updates (App/LB/DNS/Firewall/Gateway)
* 🔲 Data recovery operations
* 🔲 Other: `Explain`

#### Observability

Logging & APM Features:

* ☑ Splunk Forwarding
* ☑ DataDog Forwarding
* ☑ Security Event Logging
* ☑ Synthetic Testing
* ☑ Log Archival
* ☑ Log Purging
* ☑ Class 1 Data Logging Controls

### Data Ingress / Egress

#### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | `Azure` |
| Primary Region | `Azure US East` |
| Failover Region | `Azure US West` |
| Production VNet | `WEX-PROD-VNET` |
| WEX Fabric ID | `Not Applicable` |

#### Vendor Cloud Infrastructure (if applicable)

| Component | Value |
|-----------|--------|
| Vendor Name | `UiPath` |
| Cloud Provider | `Azure` |
| Primary Region | `Azure US East` |
| Failover Region | `Azure US West` |

#### Network Traffic Patterns

**Inbound Connectivity:**

* ☑ Protected by Imperva WAF
* 🔲 Direct inbound (not WAF-protected)
* 🔲 Customer/vendor traffic
* ☑ WEX business user traffic  
* ☑ WEX support user traffic

**Outbound Connectivity:**

* 🔲 To customer/vendor systems
* ☑ From WEX network (support)
* ☑ Class 1 data transmission
* ☑ Class 2 data transmission

**Security Controls:**

* ☑ Bot protection enabled
* ☑ Automated certificate renewal
* ☑ TLS 1.2+ enforcement
* ☑ Network traffic encryption

#### Network Connectivity Services

* 🔲 SDWAN
* 🔲 AWS Private Link
* ☑ Azure Private Link
* 🔲 Netskope Private Access (NPA)
* 🔲 Point-to-Point VPN
* 🔲 Network Peering
* 🔲 Client VPN
* 🔲 DMZ

> [!note]
> All data flows comply with WEX security standards for encryption, access control, and monitoring.

### Authentication & Authorization

Select the authentication components for WEX Employees (select all that apply):

* 🔲 IdentityIQ SAML
* ☑ Okta Workforce Identity
* 🔲 Active Directory: `WEXPRODR`
* 🔲 Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* 🔲 Other: `Explain`

Indicate if access for WEX employees is automatically provisioned & deprovisioned:

* ☑ Yes
* 🔲 No

Select the authentication components for WEX Customers (select all that apply):

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory: `DOMAIN FQDN`
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* ☑ WEX customers do not authenticate
* 🔲 Other: `Explain`

Select the types of Service Principals (select all that apply):

* 🔲 Amazon Managed Identity
* ☑ Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA: `DOMAIN FQDN`
* 🔲 Active Directory User Service Account: `DOMAIN FQDN`
* 🔲 Local user
* ☑ Digital certificates
* 🔲 Local administrator
* 🔲 Other: `Explain`

Indicate if all employee, customer, and service principal credentials require periodic rotation:

* ☑ Yes
* 🔲 No

Indicate if service principal credentials are automatically rotated:

* ☑ Yes
* 🔲 No

### Secure Coding

#### Technology Stack

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | `UiPath, .NET, Python` |
| Database Technologies | `Azure SQL` |
| Storage Solutions | `Azure Blob Storage` |
| Operating Systems | `Windows, Containers` |
| Client Platforms | `Web, API` |

#### Security Controls

Select the code quality scanning services that are active for all development repositories (select all that apply):

* ☑ Snyk
* ☑ Cycode
* 🔲 Mend Renovate
* 🔲 GitHub Copilot
* 🔲 Dependabot
* 🔲 Other: `Service Name`

### Design Patterns

List any design, delivery, or support patterns incorporated in this design:

* **Event-Driven Architecture:** Enables asynchronous processing of business events
* **Human-in-the-Loop AI:** Critical decisions require human validation
* **Multi-tenant SaaS:** Leverages UiPath's multi-tenant architecture

> [!note]
> Refer to [Architecture Outcomes & Patterns for Reuse](https://wexinc.atlassian.net/wiki/spaces/PTA/pages/153788613542/Architecture+Outcomes+Patterns+for+Reuse) for more information on recommended patterns.

### Paved Road Adoption

Identify all deviations from standard tools, services, and reference architectures with justification. (select all that apply)

Select the deviations from standard tools, services, and reference architectures with justification (select all that apply):

* 🔲 **WEX Fabric:** `Justification`
* 🔲 **SCM (GitHub Enterprise):** `Justification`
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `Justification`
* 🔲 **DFS (Panzura):** `Justification`
* 🔲 **Outbound Email (PowerMTA/Sendgrid):** `Justification`
* 🔲 **SFTP (GoAnywhere):** `Justification`
* 🔲 **WAF (Imperva):** `Justification`
* 🔲 **CDN (Imperva):** `Justification`
* 🔲 **Logging (Splunk):** `Justification`
* 🔲 **APM (DataDog):** `Justification`
* 🔲 **Auth Gateway:** `Justification`
* 🔲 **AI Gateway:** `Justification`
* 🔲 **Eventing Platform:** `Justification`
* 🔲 **WEX Data Platform:** `Justification`
* 🔲 **Tokenizer:** `Justification`
* 🔲 **Notification Hub:** `Justification`

## Diagrams

### System Context Diagram

```mermaid
graph TB
    User[WEX User]
    Agent[UiPath Agent]
    UiPathPlatform[UiPath Platform]
    DataSources[WEX Systems & Data Sources]
    
    User -->|Requests automation| UiPathPlatform
    UiPathPlatform -->|Orchestrates| Agent
    Agent -->|Interacts with| DataSources
    Agent -->|Retrieves results| UiPathPlatform
    UiPathPlatform -->|Presents results| User
```

### Cloud Architecture Diagram

```mermaid
graph TB
    subgraph "WEX Azure Environment"
        WAF[Imperva WAF]
        VMs[WEX Virtual Machines]
        DataStore[(WEX Data Sources)]
    end
    
    subgraph "UiPath Cloud (Azure)"
        AutomationCloud[UiPath Automation Cloud]
        AgentkHub[UiPath Agentic Hub]
        AICenter[UiPath AI Center]
        Orchestra[UiPath Orchestrator]
        subgraph "AI Models"
            GPT[OpenAI GPT]
            Gemini[Google Gemini]
            Claude[Anthropic Claude]
            DocPath[UiPath DocPath]
        end
    end
    
    User[WEX User] -->|HTTPS| WAF
    WAF -->|Secure access| AutomationCloud
    AutomationCloud <-->|Orchestration| Orchestra
    Orchestra <-->|Executes workflows| VMs
    VMs <-->|Processes data| DataStore
    Orchestra <-->|Agentic capabilities| AgentkHub
    AgentkHub <-->|AI processing| AICenter
    AICenter <-->|Model selection| GPT
    AICenter <-->|Model selection| Gemini
    AICenter <-->|Model selection| Claude
    AICenter <-->|Document intelligence| DocPath
```

## Risk Analysis & Dependencies

* 🚨 **Key Risks:**
  * Process dependency without proper oversight could lead to undetected errors
  * Data processing risks with sensitive information requiring proper controls
  * Future AI regulations may impact usage patterns and governance requirements

* 📋 **Critical Assumptions:**
  * UiPath platform continues to be supported at WEX
  * Business units will allocate resources for testing and implementation
  * Integration with existing systems will remain stable

* ⚠️ **Known Issues:**
  * OKTA integration is currently being scoped
  * Multi-model support requires careful validation for each use case
  * AI Trust layer configuration needs to be customized for WEX governance

* 🔗 **System Dependencies:**
  * Existing UiPath RPA infrastructure
  * Azure hosting environment
  * Email systems for Call Center use case
  * Financial data sources for Credit Memo use case
  * AML systems for fraud detection use case

## Reference Links

* **Epic/Feature:** `TECH-9531`
* **Support Tickets:** `N/A`
* **Documentation:**
  * `[UiPath Security Whitepaper](https://drive.google.com/file/d/1oLU0dX99yHvCmZ-YadwyErOSPVTQz4WY/view?usp=sharing)`
  * `[Existing RPA Architecture](https://drive.google.com/file/d/15w6T7owSMghMhnngobMFrDeJjfcra3b5/view?usp=sharing)`
* **Repositories:**
  * `[UiPath Orchestrator Repository](https://github.wexinc.com/automation/uipath-orchestrator)`
  * `[UiPath Process Repository](https://github.wexinc.com/automation/uipath-processes)`
  
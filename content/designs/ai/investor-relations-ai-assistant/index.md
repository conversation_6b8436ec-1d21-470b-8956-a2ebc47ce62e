<!-- Parent: Designs -->
<!-- Parent: AI -->
<!-- Title: RFC-485 Investor Relations AI Assistant using Google AI Studio -->

<!-- Include: macros.md -->
# Investor Relations AI Assistant using Google AI Studio

:isarb:

```text
Author: <PERSON>
Publish Date: 2025-04-08
Category: Designs
Subtype: AI
```

## Executive Summary

The Investor Relations AI Assistant leverages Google AI Studio to enhance investor communications by analyzing and refining earnings call scripts, identifying key investor concerns, providing objective feedback on message clarity, coaching the CEO on delivery techniques, and recommending optimal timing for news releases. This solution addresses current challenges in investor communications that can lead to misinterpretations and confusion around strategic initiatives.

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## Change Context

* **Organization:** AI
* **Purpose:** Enhance investor communications through AI-powered analysis and coaching
* **Users:** CEO, Investor Relations team
* **Integration Points:** Internal communications workflows, Investor Relations systems
* **Third-Party Software:** Google AI Studio (Pilot)
* **Impacted Products:**
  * Executive Communications Platform
  * Investor Relations Portal
* **Design Review Qualities:**
  * 🔲 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * ✅ 🟡 New Pattern Adoption
  * ✅ 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * ✅ 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Initial Prototype | 2025-05 | Google AI Studio access, Historical scripts |
| Full Implementation | 2025-08 | Prototype validation, Security review |
| CEO Training | 2025-09 | Full implementation |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

Confirm the status of risk assessments for this vendor and product:

* 🔲 Not Requested
* ✅ In Progress
* 🔲 Completed without concerns
* 🔲 Completed with concerns: `Explain`

The team plans to start with initial experiments using Gemini Gems and NotebookLM before progressing to a more comprehensive security review.

#### Data Classification & Protection

Estimate the number of Class 1 Data records that are transmitted, processed, or stored:

* **PCI Records:** `0`
* **PII Records:** `0`
* **PHI Records:** `0`

Data Protection Controls:

* ✅ Data is protected at rest, in transit, and in use per WEX IT policy
* ✅ HTTPS using TLS 1.2 or higher
* 🔲 Message encryption
* 🔲 Database Connections using TLS 1.2 or higher
* 🔲 Row-level data encryption
* 🔲 Transparent data encryption
* ✅ De-identification (tokenization, hashing, masking, redaction)
* 🔲 Digital Signatures
* ✅ Access control (permission, role, policy)

Data Retention:

* Data archived after: `90 days`
* Data purged after: `365 days`
* Logs purged after: `90 days`

#### AI/ML Compliance

Indicate if this design includes any new AI/ML components:

* ✅ Yes
* 🔲 No

Indicate if all AI/ML components have been reviewed for AI/ML compliance:

* 🔲 Yes
* ✅ No

### 💼 Business & Enterprise Impact

#### Design Impact Scope

* ✅ Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* 🔲 Vendor & Product Risk Assessment completed

#### Procurement Details

Software Type:

* ✅ SaaS
* 🔲 IaaS
* 🔲 AWS PaaS
* 🔲 Azure PaaS
* 🔲 Code Dependency Package
* 🔲 Desktop
* 🔲 Mobile App
* 🔲 Browser - Web Browser

Additional Considerations:

* ✅ Software installation & configuration is automated
* ✅ Cost exceeds $25,000
* 🔲 Solution is part of Target State Architecture
* 🔲 Temporary solution planned for replacement

Alternatives Considered:

* **Azure OpenAI Service:** Less specialized for the specific investor communications use case
* **Amazon Bedrock:** Less integration with WEX's Google Workspace environment
* **Anthropic Claude:** Good capabilities but lacks Google's enterprise-level compliance certifications

### 🔄 Operations & Reliability

#### High Availability Controls

* ✅ Availability Zone Redundancy
* 🔲 Regional Failover
* 🔲 Automated Failover
* 🔲 Multi-Region Active-Active
* 🔲 Load Balancing
* 🔲 Content Delivery Network
* ✅ Monitoring & Alerting
* 🔲 Other: `Explain`

#### Outage Impact Assessment

Business Impact:

* 🔲 Critical customer business impact
* ✅ Partial loss of non-critical functionality
* 🔲 Customer recovery assistance required
* 🔲 Multiple lines of business affected
* 🔲 Multiple products in LOB affected
* 🔲 Multiple customers affected
* 🔲 External SLA impact
* 🔲 Internal SLA impact
* ✅ Engineering support required

Recovery Actions:

* 🔲 Infrastructure deployment/patching
* 🔲 Application deployment
* ✅ Configuration updates (App/LB/DNS/Firewall/Gateway)
* 🔲 Data recovery operations
* 🔲 Other: `Explain`

#### Observability

Logging & APM Features:

* ✅ Splunk Forwarding
* 🔲 DataDog Forwarding
* ✅ Security Event Logging
* 🔲 Synthetic Testing
* ✅ Log Archival
* ✅ Log Purging
* ✅ Class 1 Data Logging Controls

### Data Ingress / Egress

#### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | `Google Cloud Platform` |
| Primary Region | `US Central1` |
| Failover Region | `None` |
| Production VNet | `N/A` |
| WEX Fabric ID | `N/A` |

#### Vendor Cloud Infrastructure (if applicable)

| Component | Value |
|-----------|--------|
| Vendor Name | `Google` |
| Cloud Provider | `Google Cloud Platform` |
| Primary Region | `US Central1` |
| Failover Region | `None` |

#### Network Traffic Patterns

**Inbound Connectivity:**

* 🔲 Protected by Imperva WAF
* ✅ Direct inbound (not WAF-protected)
* 🔲 Customer/vendor traffic
* ✅ WEX business user traffic  
* ✅ WEX support user traffic

**Outbound Connectivity:**

* 🔲 To customer/vendor systems
* ✅ From WEX network (support)
* 🔲 Class 1 data transmission
* ✅ Class 2 data transmission

**Security Controls:**

* ✅ Bot protection enabled
* ✅ Automated certificate renewal
* ✅ TLS 1.2+ enforcement
* ✅ Network traffic encryption

#### Network Connectivity Services

* 🔲 SDWAN
* 🔲 AWS Private Link
* 🔲 Azure Private Link
* 🔲 Netskope Private Access (NPA)
* 🔲 Point-to-Point VPN
* 🔲 Network Peering
* 🔲 Client VPN
* 🔲 DMZ

> [!note]
> All data flows must comply with WEX security standards for encryption, access control, and monitoring.

### Authentication & Authorization

Select the authentication components for WEX Employees (select all that apply):

* 🔲 IdentityIQ SAML
* 🔲 Okta Workforce Identity
* 🔲 Active Directory: `WEXPRODR`
* 🔲 Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* ✅ Other: `Google Workspace login`

Indicate if access for WEX employees is automatically provisioned & deprovisioned:

* ✅ Yes
* 🔲 No

Select the authentication components for WEX Customers (select all that apply):

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory: `DOMAIN FQDN`
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* ✅ WEX customers do not authenticate
* 🔲 Other: `Explain`

Select the types of Service Principals (select all that apply):

* 🔲 Amazon Managed Identity
* 🔲 Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA: `DOMAIN FQDN`
* 🔲 Active Directory User Service Account: `DOMAIN FQDN`
* 🔲 Local user
* 🔲 Digital certificates
* 🔲 Local administrator
* ✅ Other: `Google Cloud IAM Service Accounts`

Indicate if all employee, customer, and service principal credentials require periodic rotation:

* ✅ Yes
* 🔲 No

Indicate if service principal credentials are automatically rotated:

* ✅ Yes
* 🔲 No

### Secure Coding

#### Technology Stack

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | `python` |
| Database Technologies | `N/A` |
| Storage Solutions | `Google Cloud Storage` |
| Operating Systems | `containers` |
| Client Platforms | `web` |

#### Security Controls

Select the code quality scanning services that are active for all development repositories (select all that apply):

* ✅ Snyk
* 🔲 Cycode
* 🔲 Mend Renovate
* 🔲 GitHub Copilot
* 🔲 Dependabot
* 🔲 Other: `Service Name`

### Design Patterns

List any design, delivery, or support patterns incorporated in this design:

* **AI Prompt Engineering Pattern:** [AI Platform Patterns](https://wexinc.atlassian.net/wiki/spaces/PTA/pages/************/Architecture+Outcomes+Patterns+for+Reuse)
* **Continuous Prompt Improvement:** [AI Development Pattern](https://wexinc.atlassian.net/wiki/spaces/PTA/pages/************/Architecture+Outcomes+Patterns+for+Reuse)
* **Federated Security Model:** [Security Patterns](https://wexinc.atlassian.net/wiki/spaces/PTA/pages/************/Architecture+Outcomes+Patterns+for+Reuse)

### Paved Road Adoption

Identify all deviations from standard tools, services, and reference architectures with justification. (select all that apply)

Select the deviations from standard tools, services, and reference architectures with justification (select all that apply):

* 🔲 **WEX Fabric:** `Justification`
* 🔲 **SCM (GitHub Enterprise):** `Justification`
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `Justification`
* 🔲 **DFS (Panzura):** `Justification`
* 🔲 **Outbound Email (PowerMTA/Sendgrid):** `Justification`
* 🔲 **SFTP (GoAnywhere):** `Justification`
* ✅ **WAF (Imperva):** `Solution uses Google Cloud's built-in security controls instead of Imperva`
* 🔲 **CDN (Imperva):** `Justification`
* 🔲 **Logging (Splunk):** `Justification`
* ✅ **APM (DataDog):** `Google Cloud's native monitoring tools will be used instead of DataDog`
* ✅ **Auth Gateway:** `Google Workspace authentication will be used instead of standard WEX auth gateway`
* ✅ **AI Gateway:** `Direct access to Google AI Studio instead of through WEX AI Gateway`
* 🔲 **Eventing Platform:** `Justification`
* 🔲 **WEX Data Platform:** `Justification`
* 🔲 **Tokenizer:** `Justification`
* 🔲 **Notification Hub:** `Justification`

## Diagrams

### System Context Diagram

```mermaid
graph TB
    IR[Investor Relations Team]
    CEO[CEO]
    AI_Assistant[Google AI Studio Assistant]
    Scripts[Earnings Call Scripts]
    Analytics[Investor Analytics]
    
    IR -->|Uploads scripts| AI_Assistant
    AI_Assistant -->|Provides feedback| IR
    CEO -->|Receives coaching| AI_Assistant
    Scripts -->|Analyzed by| AI_Assistant
    AI_Assistant -->|Generates insights from| Analytics
```

### Cloud Architecture Diagram

```mermaid
graph TB
    subgraph "Google Cloud Platform"
        AI_Studio[Google AI Studio]
        IAM[Google Cloud IAM]
        Storage[Google Cloud Storage]
        Logging[Google Cloud Logging]
    end
    
    subgraph "WEX Environment"
        IR_Team[IR Team]
        CEO_Office[CEO Office]
        Confidential_Data[Confidential Data]
    end
    
    IR_Team -->|Authenticates via| IAM
    CEO_Office -->|Authenticates via| IAM
    Confidential_Data -->|Securely stored in| Storage
    AI_Studio -->|Processes data from| Storage
    AI_Studio -->|Logs activities to| Logging
```

## Risk Analysis & Dependencies

* 🚨 **Key Risks:**
  * AI may not properly understand nuanced financial communications
  * Potential exposure of confidential financial information
  * Public perception of AI use in investor relations
  * Potential SEC concerns with AI-augmented investor communications

* 📋 **Critical Assumptions:**
  * Google AI Studio can be effectively trained on financial and investor relations contexts
  * The solution complies with all relevant regulatory requirements
  * The CEO and IR team will adopt and effectively use the AI assistant

* ⚠️ **Known Issues:**
  * Integration with existing communication workflows requires refinement
  * Performance may vary with highly nuanced or specialized financial terminology
  * Training data quality directly impacts AI assistant effectiveness

* 🔗 **System Dependencies:**
  * Google Workspace authentication
  * Access to historical earnings call scripts and iterations
  * Access to analyst reports
  * Secure channel for financial information

## Reference Links

* **Epic/Feature:** `AIGENICS-1234`
* **Support Tickets:** `JSM-5678`
* **Documentation:**
  * `[Google AI Studio Documentation](https://cloud.google.com/vertex-ai)`
  * `[WEX AI Guidelines](https://wexinc.atlassian.net/wiki/spaces/AI/pages/guidelines)`
* **Repositories:**
  * `[IR AI Assistant Config](https://github.wexinc.com/aigenics/ir-assistant)`
  * `[IR AI Prompt Library](https://github.wexinc.com/aigenics/ir-prompt-library)`

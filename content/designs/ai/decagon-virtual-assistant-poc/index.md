<!-- Parent: Designs -->
<!-- Parent: AI -->
<!-- Title: RFC-497 Decagon Virtual Assistant POC -->

<!-- Include: macros.md -->
# Decagon Virtual Assistant POC

:isarb:

```text
Author: <PERSON><PERSON><PERSON>
Publish Date: 2025-05-06
Category: Designs
Subtype: AI
Ratified By: iSARB (2025-04-30)
```

## Executive Summary

Customers face long wait times, limited self-service options, and a lack of 24/7 availability, often leading to frustration and repeated contacts. At the same time, contact center agents are overwhelmed with high call volumes, repetitive inquiries, and fragmented tools—reducing efficiency, increasing operational costs, and limiting scalability. This fragmented experience undermines both customer satisfaction and brand trust, while putting pressure on internal teams to deliver more with less.

This solution drives improvements towards the following Tech Transformation Vectors:

| Vector | Impact |
|--------|--------|
| 🔲 Product Security | Not applicable for POC |
| 🔲 Reliability | Not applicable for POC |
| ☑️ Product Innovation Velocity (PiV) | Fast evaluation of AI capabilities for customer service |
| 🔲 SaaS Maturity | Not applicable for POC |
| ☑️ AI/ML Maturity | Evaluation of AI virtual assistant capabilities |

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

:toc:

## 💼 Change Context

* **Organization:** AI
* **Purpose:** To evaluate Decagon's AI-powered virtual assistant capabilities for transforming customer engagement
* **Users:** Internal evaluation team only for POC (no agents or customers)
* **Integration Points:** Test environment APIs with mock data
* **Third-Party Software:** Decagon (POC)
* **Impacted Products:**
  * None during POC phase
* **Design Review Qualities:**
  * 🔲 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * ☑️ 🟡 New Pattern Adoption
  * ☑️ 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| POC Evaluation | 2025-Q2 | None |

## Evaluation Criteria

### 🛡️ Product Security

#### Risk Assessment

The current status of risk assessments for this vendor and product:

* 🔲 Not Requested
* ☑️ In Progress
* 🔲 Completed without concerns
* 🔲 Completed with concerns: `Explain`

#### Data Compliance

The following types of Class 1 Protected Data is transmitted, processed, or stored in this solution:

* 🔲 PCI
* ☑️ PII
* 🔲 PHI

Data Protection Controls:

* ☑️ Data is protected at rest, in transit, and in use per WEX IT policy
* ☑️ HTTPS using TLS 1.2 or higher
* 🔲 Message encryption
* 🔲 Database Connections using TLS 1.2 or higher
* 🔲 Row-level data encryption
* 🔲 Transparent data encryption
* ☑️ De-identification (tokenization, hashing, masking, redaction)
* 🔲 Digital Signatures
* ☑️ Access control (permission, role, policy)

Data Retention:

* Data archived after: `N/A for POC`
* Data purged after: `N/A for POC`
* Logs purged after: `N/A for POC`

> [!note]
> POC is limited to test data and will be validated by internal team. No agents or customers will interact with the tool.

#### Data Ingress / Egress

This solution uses the following data flow patterns:

* 🔲 Customer-to-WEX traffic
* 🔲 WEX-to-Customer traffic
* ☑️ Vendor-to-WEX traffic
* ☑️ WEX-to-Vendor traffic
* 🔲 Internal system-to-system traffic

For each data flow, provide:

| Origin System | Destination System | Protocol | Authentication | Data Classification | Encryption Methods |
|---------------|-------------------|----------|---------------|-------------------|-------------------|
| WEX APIs | Decagon | HTTPS | OAuth | Class 2 (Test Data) | TLS 1.2+ |
| Decagon | WEX APIs | HTTPS | OAuth | Class 2 (Test Data) | TLS 1.2+ |

```mermaid
flowchart LR
  WEX[WEX Test Environment] <-->|API Calls| Decagon[Decagon SaaS]
  
  subgraph "Data Protection Controls"
    direction TB
    TLS["TLS 1.2+"]
    OAuth["OAuth Authentication"]
    DLP["Google DLP for Redaction"]
  end
```

#### Authentication & Authorization

WEX Employee Authentication Components:

* 🔲 IdentityIQ SAML
* 🔲 Okta Workforce Identity
* 🔲 Active Directory: `WEXPRODR`
* ☑️ Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* 🔲 Other: `Explain`

WEX Employee automated provisioning & deprovisioning:

* 🔲 Yes
* ☑️ No

WEX Customer Authentication Components:

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory: `DOMAIN FQDN`
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* ☑️ WEX customers do not authenticate
* 🔲 Other: `Explain`

WEX Service Principal Types:

* 🔲 Amazon Managed Identity
* 🔲 Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA: `DOMAIN FQDN`
* 🔲 Active Directory User Service Account: `DOMAIN FQDN`
* 🔲 Local user
* 🔲 Digital certificates
* 🔲 Local administrator
* ☑️ Other: `API Keys for POC only`

All employee, customer, and service principal credentials require periodic rotation:

* 🔲 Yes
* ☑️ No

Service principal credentials are automatically rotated:

* 🔲 Yes
* ☑️ No

#### Network Traffic Patterns

**Inbound Connectivity:**

* 🔲 Protected by Imperva WAF
* ☑️ Direct inbound (not WAF-protected)
* 🔲 Customer/vendor traffic
* 🔲 WEX business user traffic  
* ☑️ WEX support user traffic

**Outbound Connectivity:**

* 🔲 To customer/vendor systems
* ☑️ From WEX network (support)
* 🔲 Class 1 data transmission
* ☑️ Class 2 data transmission

**Security Controls:**

* 🔲 Bot protection enabled
* 🔲 Automated certificate renewal
* ☑️ TLS 1.2+ enforcement
* ☑️ Network traffic encryption

### 🤖 AI/ML

Indicate if this design includes any new AI/ML components:

* ☑️ Yes
* 🔲 No

Indicate if all AI/ML components have been reviewed for AI/ML compliance:

* 🔲 Yes
* ☑️ No

### 🔄 Reliability

#### High Availability Controls

* 🔲 Availability Zone Redundancy
* 🔲 Regional Failover
* 🔲 Automated Failover
* 🔲 Multi-Region Active-Active
* 🔲 Load Balancing
* 🔲 Content Delivery Network
* 🔲 Monitoring & Alerting
* ☑️ Other: `Not applicable for POC`

#### Outage Impact Assessment

Business Impact:

* 🔲 Critical customer business impact
* 🔲 Partial loss of non-critical functionality
* 🔲 Customer recovery assistance required
* 🔲 Multiple lines of business affected
* 🔲 Multiple products in LOB affected
* 🔲 Multiple customers affected
* 🔲 External SLA impact
* 🔲 Internal SLA impact
* 🔲 Engineering support required

Recovery Actions:

* 🔲 Infrastructure deployment/patching
* 🔲 Application deployment
* 🔲 Configuration updates (App/LB/DNS/Firewall/Gateway)
* 🔲 Data recovery operations
* ☑️ Other: `Not applicable for POC`

#### Vendor Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Vendor Name | `Decagon` |
| Cloud Provider | `Google Cloud Platform (GCP)` |
| Primary Region | `US` |
| Failover Region | `N/A for POC` |

### ☁️ SaaS

#### Key Components

| Component | Description | Technology | Purpose |
|-----------|-------------|------------|---------|
| Virtual Assistant | AI-powered customer service bot | Decagon SaaS | Automate customer interactions |
| API Integration | Interface for data exchange | REST APIs | Connect to WEX systems |
| Admin Portal | Management interface | Web application | Configure and monitor virtual assistant |

#### Design Impact Scope

* 🔲 Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* ☑️ Vendor & Product Risk Assessment completed

#### Logging & APM

* 🔲 Splunk Forwarding
* 🔲 DataDog Forwarding
* ☑️ Security Event Logging
* 🔲 Synthetic Testing
* 🔲 Log Archival
* 🔲 Log Purging
* 🔲 Class 1 Data Logging Controls

#### Standards Adoption

Deviations from standard tools, services, and reference architectures are:

* 🔲 **WEX Fabric:** `Justification`
* 🔲 **SCM (GitHub Enterprise):** `Justification`
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `Justification`
* 🔲 **DFS (Panzura):** `Justification`
* 🔲 **Outbound Email (PowerMTA/Sendgrid):** `Justification`
* 🔲 **SFTP (GoAnywhere):** `Justification`
* 🔲 **WAF (Imperva):** `Justification`
* 🔲 **CDN (Imperva):** `Justification`
* 🔲 **Logging (Splunk):** `Justification`
* 🔲 **APM (DataDog):** `Justification`
* 🔲 **Auth Gateway:** `Justification`
* 🔲 **AI Gateway:** `Justification`
* 🔲 **Eventing Platform:** `Justification`
* 🔲 **WEX Data Platform:** `Justification`
* 🔲 **Tokenizer:** `Justification`
* 🔲 **Notification Hub:** `Justification`

### Relevant Context

#### Procurement Details

Third-Party Software Type:

* ☑️ SaaS
* 🔲 IaaS
* 🔲 AWS PaaS
* 🔲 Azure PaaS
* 🔲 Code Dependency Package
* 🔲 Desktop
* 🔲 Mobile App
* 🔲 Browser - Web Browser

Additional Considerations:

* ☑️ Software installation & configuration is automated
* 🔲 Cost exceeds $25,000
* 🔲 Solution is part of Target State Architecture
* ☑️ Temporary solution planned for replacement

Alternatives Considered:

* **Build In-house:** Would require significant development time and resources
* **Other vendors:** Detailed evaluation to be part of POC outcomes

## Diagrams

### System Context Diagram

```mermaid
graph TB
    classDef secure fill:#e6ffe6,stroke:#006600
    classDef external fill:#f9f9f9,stroke:#666666
    
    WEXUser[WEX Test User]
    Decagon[Decagon Virtual Assistant]
    WEXApis[WEX Test APIs]
    Security[Security Controls]

    WEXUser -->|Evaluates| Decagon
    Decagon -->|Queries| WEXApis
    WEXApis -->|Returns Data| Decagon
    Security -->|Protects| WEXApis
    
    class Security secure
    class Decagon external
```

### Cloud Architecture Diagram

```mermaid
flowchart TB
    subgraph "POC Environment"
        direction TB
        subgraph "WEX Test Environment"
            TestApis[Test APIs]
            MockData[(Mock Data)]
            TestApis --> MockData
        end
    end

    subgraph "Decagon Cloud (GCP)"
        VirtualAssistant[Virtual Assistant]
        NLP[NLP Engine]
        AdminPortal[Admin Portal]
        DecagonDB[(Decagon Database)]
        
        VirtualAssistant --> NLP
        VirtualAssistant --> DecagonDB
        AdminPortal --> DecagonDB
    end

    WEXEvaluator[WEX Evaluator] -->|HTTPS/TLS 1.2+| AdminPortal
    VirtualAssistant <-->|API/OAuth| TestApis
    
    classDef class1data fill:#ffebeb,stroke:#990000
    classDef secure fill:#e6ffe6,stroke:#006600
    class DecagonDB class1data
```

## Risk Analysis & Dependencies

### Key Risks

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Data privacy concerns | Medium | Low | Using only test data, Google DLP for redaction |
| Integration complexity | Medium | Medium | Limited scope for POC, focusing on key capabilities |
| Performance issues | Low | Low | Vendor provides 99.9% uptime guarantee |

### Critical Assumptions

* **Assumption 1:** Test data will be sufficient to evaluate real-world performance and capabilities
* **Assumption 2:** API integration will be representative of production integration challenges
* **Assumption 3:** POC results will provide clear indicators for build vs. buy decision

### Known Issues

* **Issue 1:** Limited scope of testing may not reveal all potential production challenges
* **Issue 2:** Security assessment is still in progress during POC evaluation

### System Dependencies

| Dependency | Type | Criticality | Contact Team |
|------------|------|-------------|--------------|
| WEX Test APIs | API | High | Engineering Team |
| Mock Data | Data | High | Data Team |

## Reference Links

* **Documentation:**
  * [iSARB Presentation](https://docs.google.com/presentation/d/1CaYVBFP2vgzZ256aWoHdRuSeRJK4DOS0pXNZvjMqYgI)
* **Stakeholder Information:**
  * Product Manager: Kashif Merchant (AI Product - Tech)
  * Tech Partners: ZJ & Sonny T
  * Management: Karen Stroup, Sachin Dhawan, Eric Tracy
  * Procurement: Bill Felice
  * Product/Platform Solution Review Board: ZJ, Brian Hood
  * Architecture: Huaxing Wu, Umesh
  * Cloud Engineering: Dan DeLauro

<!-- Parent: Designs -->
<!-- Parent: GTS -->
<!-- Title: RFC279-Atlassian StatusPage -->

<!-- Include: macros.md -->
# Atlassian StatusPage

:draft:

```text
Author: <PERSON>
Publish Date: 2024-10-09
Category: Guardrails
Subtype: Design Specification
```

## Change Context

### Conditions

Select the type of new software engagement being proposed (select one):

- ✅ Proof-of-Concept
- 🔲 Pilot
- 🔲 GA Approved Software
- 🔲 WEX Product Integration

Please select all secondary conditions that apply:

- 🔲 Tech Changes (languages, frameworks, cloud SKUs, etc)
- 🔲 Disaster Recovery Strategy Changes
- 🔲 New Pattern Adoption
- 🔲 New Inbound/Outbound Route
- 🔲 Standard Service Deviation
- 🔲 Network Accessibility Change
- 🔲 Vulnerability Remediation
- 🔲 Authentication / Authorization Changes
- 🔲 Cryptography Changes
- 🔲 Class 1 Data Handling (PII / PCI / PHI)
- 🔲 Class 2 Data Handling (Internal)
- 🔲 Standards Change

### Informal Pre-Review

> An informal pre-review is a casual, often undocumented review process aimed at improving the quality of a document or project before it undergoes a formal review.

- ✅ I attest that I have consulted outside my team as due diligence for all applicable concerns:
  - Application Security & IT Compliance for compliance with WEX policies and security requirements
  - Cloud Engineering for WEX cloud architecture requirements
  - End User Computing for WEX desktop architecture requirements
  - Operations & Support for significant risk or impact to customers and/or WEX technical support staff
  - Architecture & PaaS Engineering for opportunities for re-use and adoption of standard tools & services

> <!-- Info -->
> Note- Expand for contact information...
>
> - Application Security
>   - Email:
>   - Chat:
>   - Principal SME:
> - IT Compliance
>   - Email:
>   - Chat:
>   - Principal SME:
> - Cloud Engineering
>   - Email: [Dan](mailto:<EMAIL>)
>   - Chat:
>   - Principal SME:
> - End User Computing
>   - Email:
>   - Chat:
>   - Principal SME:
> - Architecture & PaaS Engineering
>   - Ingestion: [Architecture](https://wexinc.atlassian.net/wiki/spaces/PTA/pages/153797001742/Welcome+to+the+Product+Technology+Solution+Architecture+Page) & [PaaS Engineering](https://docs.wexfabric.com/)
>   - Email:
>   - Chat:
>   - Principal SME:

### Scope

Which lines of business are impacted by this change:

- 🔲 Mobility
- 🔲 Payments
- 🔲 Benefits
- ✅ WEX Enterprise IT
- 🔲 WEX Enterprise Digital

Which products are impacted by this change:

- ✅ WEX Operations
- 🔲 WEX PaaS (Enterprise)
- 🔲 WEX Cloud Engineering (Enterprise)
- 🔲 WEX Support (Enterprise)
- 🔲 WEX Network Security (Enterprise)
- 🔲 WEX End User Computing (Enterprise)
- 🔲 WEX Middleware (Enterprise)

### Impact

Provide the problem statement this change is solving:
_(eg. Need to be able to produce/issue credit cards.)_

> We want to communicate the status and health of any group of applications or services to appropriate internal and external stakeholders.  This effort will evaluate the capabilities of Atlassian StatusPage as a potential long-term solution for this purpose.  The PoC will not make use of production data or be integrated with any AWS or Azure cloud landing zones.

What is the value proposition for WEX (select all that apply):

- 🔲 Cost Reduction
- 🔲 Customer Retention
- ✅ Customer Service Quality
- 🔲 Product Competitiveness
- 🔲 Security & Compliance
- 🔲 Delivery Velocity
- 🔲 Delivery Quality
- ✅ Operational Maturity
- 🔲 Other: `Explain`

Please list any assumptions that this solution is dependent on:

- **Assumption 1:** Solution will leverage existing WEX Atlassian account and integrations with WEX

## Concerns

### Procurement

What types of components are included with the software? (select all that apply)

- ✅ SaaS
- 🔲 IaaS
- 🔲 AWS PaaS
- 🔲 Azure PaaS
- 🔲 Code Dependency package
- 🔲 Desktop
- 🔲 Mobile App
- 🔲 Browser

Will the software installation & configuration be automated?

- 🔲 Yes
- ✅ No

Please list all alternative products considered and the deciding factors against:

- **No Other Products Evaluated:** `PoC is targeting Atlassian due to existing relationship with Vendor and product suite`

Does this software cost more than $25,000?

- 🔲 Yes
- ✅ No

Is this software a temporary solution or a planned component of the Target State Architecture?

- ✅ Long-term solution incorporated into the Product Target State Architecture
- 🔲 Temporary solution that will be replaced

### Risk Assessment

> **Risk assessments** are a systematic process used to identify potential hazards and determine what could happen if those hazards occur. It’s a crucial part of risk management, aiming to determine which measures should be implemented to eliminate or control risks, and prioritize them based on their likelihood and impact.

Select all that apply to this design:

- ✅ Impacts or used by more than 25 users
- 🔲 Dependent upon legacy or end of life software or hardware
- ✅ Vendor & Product Risk Assessment has been conducted by WEX Information Security

### Availability & Disaster Recovery

> **Availability** is a design quality focused on the ability of an application to remain running and respond to requests
> **Disaster Recovery (DR)** is a set of policies, tools, and procedures to re-establish availability in the event of an outage.

What controls are included in the design to satisfy availability requirements? (select all that apply)

- ✅ Availability Zone Redundancy (Same Region)
- ✅ Regional Failover
- ✅ Automated Failover
- 🔲 Multi-Region Active-Active
- 🔲 Load Balancing
- 🔲 Content Delivery Network
- 🔲 Monitoring & Alerting
- 🔲 Other: `Explain`

What is the impact of an outage to WEX and our Customers? (select all that apply)

- 🔲 Customer business is significantly impacted by outage
- 🔲 Customer experiences partial loss of functionality not critical to their operations
- 🔲 Customers require assistance to recover from outage (ex. data loss, corruption, etc)
- ✅ Outage may impact multiple lines of business
- ✅ Outage may impact multiple products in a line of business
- ✅ Outage may impact multiple customers of a product
- ✅ WEX cannot meet external SLAs
- 🔲 WEX cannot meet internal SLAs
- 🔲 WEX support teams require assistance from engineering teams to respond to outage

What actions may be required by WEX to respond to an outage? (select all that apply)

- 🔲 Deploy or Patch Infrastructure
- 🔲 Deploy Applications
- 🔲 Update Application configuration
- 🔲 Update Load Balancer configuration
- 🔲 Update DNS records
- 🔲 Update Firewall configuration
- 🔲 Update API Gateway configuration
- 🔲 Update WAF configuration
- 🔲 Update Production Data
- 🔲 Other: `Explain`

### AI/ML Compliance

> **AI and Machine Learning (ML)** are transforming the landscape of regulatory compliance across various industries.

Does this design include any new AI/ML components?

- 🔲 Yes
- ✅ No

Have all AI/ML components been reviewed for AI/ML compliance?

- 🔲 Yes
- ✅ No

### Logging & APM

> **Logging and Application Performance Monitoring (APM)** are crucial components for maintaining and optimizing the performance of applications.

What logging and APM capabilities are included in the design? (select all that apply)

- 🔲 Splunk forwarding
- ✅ DataDog forwarding
- 🔲 No external storage of log data
- 🔲 Log data is routinely archived
- 🔲 Log data is routinely purged
- 🔲 Security events are logged
- ✅ Class 1 data is not logged
- 🔲 Synthetic testing

### Data Ingress & Egress

> **Data Ingress** refers to the process of data entering a system, network, or application from external sources. This can include user requests, network traffic, or any other form of information sent to a system from outside sources.
> **Data Egress** refers to the process of data leaving a system, network, or application. This can include data being sent to external systems, users, or other networks.

Describe WEX Cloud Architecture: `WEX Cloud infrastructure not present in design`

Describe Vendor Cloud Architecture:

- **Vendor Name:** `Atlassian StatusPage`
- **Cloud Provider:** `AWS`
- **Cloud Primary Region:** `US West 2`
- **Cloud Failover Region:** `US East 1`

What data ingress & egress conditions are present in the design? (select all that apply)

- 🔲 Inbound connectivity proxied by Imperva WAF
- 🔲 Inbound connectivity **NOT** proxied by Imperva WAF
- 🔲 Inbound connectivity from a customer or vendor
- 🔲 Inbound connectivity from internal WEX business users
- 🔲 Inbound connectivity from internal WEX support users
- 🔲 Outbound connectivity to a customer or vendor
- 🔲 Outbound connectivity from the WEX network for support resources
- 🔲 Class 1 data is being transmitted
- ✅ Class 2 data is being transmitted
- 🔲 Bot protection enabled
- 🔲 Digital certificate renewal is automated

What network connectivity services are used in the design? (select all that apply)

- 🔲 SDWAN
- 🔲 AWS Private Link
- 🔲 Azure Private Link
- 🔲 Netskope Private Access (NPA)
- 🔲 Point-to-Point VPN
- 🔲 Network Peering
- 🔲 Client VPN
- 🔲 DMZ
- ✅ Public Internet

### Authentication & Authorization

> **Authentication** is the process of verifying the identity of a user, device, or system before granting access to resources.
> **Authorization** is the process of evaluating the users level of access to a resource.

What authentication components are used for WEX employees?

- ✅ IdentityIQ SAML
- 🔲 Okta Workforce Identity
- 🔲 Active Directory: `WEXPRODR`
- 🔲 Built-in user management & authentication
- 🔲 WEX custom-built IAM solution (fit-to-purpose)
- 🔲 WEX employees do not authenticate
- 🔲 Other: `Explain`

Is access for WEX employees automatically provisioned & deprovisioned?

- ✅ Yes
- 🔲 No

What authentication components are used for WEX customers?

- 🔲 Okta Customer Identity (OWI / Auth0)
- 🔲 Built-in user management & authentication
- 🔲 Active Directory: `DOMAIN FQDN`
- 🔲 WEX custom-built IAM solution (fit-to-purpose)
- ✅ WEX customers do not authenticate
- 🔲 Other: `Explain`

What types of service principals are used? (select all that apply)

- 🔲 Amazon Managed Identity
- 🔲 Microsoft Entra Managed Identity
- 🔲 Active Directory GMSA or MSA: `DOMAIN FQDN`
- 🔲 Active Directory User Service Account: `DOMAIN FQDN`
- ✅ Local user
- 🔲 Digital certificates
- 🔲 Local administrator
- 🔲 Other: `Explain`

Do all employee, customer, and service principle credentials require periodic rotation?

- 🔲 Yes
- ✅ No

Are service principle credentials automatically rotated?

- 🔲 Yes
- ✅ No

### Data Compliance

> **Data Compliance** involves managing and handling personal and sensitive data in accordance with regulatory requirements, industry standards, and internal policies.

Estimate the number of Class 1 Data records that are transmitted, processed, or stored in this solution design:

- PCI Records: `0`
- PII Records: `0`
- PHI Records: `0`

Is data protected at rest, in transit, and in use according to WEX IT policy?

- ✅ Yes
- 🔲 No

What forms of data protection are used in this solution design? (select all that apply)

- ✅ HTTPS using TLS 1.2 or higher
- 🔲 Message encryption
- 🔲 Database Connections using TLS 1.2 or higher
- 🔲 Row-level data encryption
- 🔲 Transparent data encryption
- 🔲 De-identification (tokenization, hashing, masking, redaction, generalization, etc)
- 🔲 Digital Signatures
- ✅ Access control (permission, role, policy)

Describe how data is retained.

- Data is archived after `0` Days
- Data is purged after `60` Days
- Logs are purged after `60` Days

### Standard Services, Tools & Reference Architectures

Identify all deviations from standard tools, services, and reference architectures with justification. (select all that apply)

> <!-- Info -->
> Note
> Every standard tool, service and reference architecture should have a published technical document that explains what the software does, when to use it, technical assistance contact, value from standardization, and how to use it.

- 🔲 **WEX Fabric:** `Justification`
- 🔲 **SCM (GitHub Enterprise):** `Justification`
- 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `Justification`
- 🔲 **DFS (Panzura):** `Justification`
- 🔲 **Outbound Email (PowerMTA/Sendgrid):** `Justification`
- 🔲 **SFTP (GoAnywhere):** `Justification`
- 🔲 **WAF (Imperva):** `Justification`
- 🔲 **CDN (Imperva):** `Justification`
- 🔲 **Logging (Splunk):** `Justification`
- 🔲 **APM (DataDog):** `Justification`
- 🔲 **Auth Gateway:** `Justification`
- 🔲 **AI Gateway:** `Justification`
- 🔲 **Eventing Platform:** `Justification`
- 🔲 **WEX Data Platform:** `Justification`
- 🔲 **Tokenizer:** `Justification`
- 🔲 **Notification Hub:** `Justification`

## Diagrams

### System Context Diagram

> A System Context Diagram is a high-level, abstract representation of a system and its interactions with external entities. It defines the boundary between the system and its environment, showing the entities that interact with it.

Provide a system context diagram showing the system's primary components and interaction with users & clients:

> <!-- Info -->
> Note
> Example: [C4 Level 1 System Context Diagram](https://c4model.com/diagrams/system-context)

![statuspage context diagram](statuspage-context.png)

### Cloud Architecture Diagram

> Cloud architecture diagram is a visual representation of the structure and components of a cloud computing environment

Provide a cloud architecture diagram showing all the cloud resources present in the design:

> <!-- Info -->
> Note
> Example: [AWS Cloud Architecture Diagram](https://github.com/user-attachments/assets/950ffbad-ee91-461e-8f0e-dbe2be600437)
> Example: [Lucidchart Architecture Diagrams](https://www.lucidchart.com/pages/architecture-diagram) for more information

![statuspage cloud diagram](statuspage-cloud-arch.png)

<!-- Parent: Designs -->
<!-- Parent: GTS -->
<!-- Title: RFC-502 Sawatch Migration to Snowflake -->

<!-- Include: macros.md -->
# Sawatch Migration to Snowflake

:isarb:

```text
Author: <PERSON>
Publish Date: 2025-05-06
Category: Designs
Subtype: GTS
Ratified By: iSARB (2025-05-06)
```

## Executive Summary

<PERSON> has a legacy data store running on horizontally shared PostgreSQL across several dozen Digital Ocean Droplets. This solution proposes migrating this datastore to the Snowflake platform, which is already in use by WEX, to simplify the datastore solution, speed up integration into WEX, and align Sawatch practices with WEX standards.

This solution drives improvements towards the following Tech Transformation Vectors:

| Vector | Impact |
|--------|--------|
| 🔲 Product Security | - |
| ☑️ Reliability | Enhanced reliability through Snowflake's managed platform capabilities |
| ☑️ Product Innovation Velocity (PiV) | Faster development and integration by leveraging existing WEX platform |
| ☑️ SaaS Maturity | Migration from self-managed infrastructure to enterprise SaaS platform |
| 🔲 AI/ML Maturity | No direct impact on AI/ML capabilities |

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

:toc:

## 💼 Change Context

* **Organization:** GTS (Infrastructure Technology)
* **Purpose:** Simplify cloud infrastructure footprint at cost equivalency while improving maintainability
* **Users:** Internal data platform teams; no direct customer or business user interaction
* **Integration Points:** WEX Data Platform
* **Third-Party Software:** Approved Usage (Snowflake - already in use by WEX)
* **Impacted Products:**
  * Sawatch Data Platform
* **Design Review Qualities:** (select all that apply)
  * ☑️ 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * 🔲 🟡 New Pattern Adoption
  * 🔲 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| iSARB Approval | 2025-05-01 | None |
| Request Data Platform to provision Stage/Production environments | 2025-05-02 | iSARB Approval |
| Populate Stage environment for testing | 2025-05-04 | Environment provisioning |
| Testing Complete | 2025-05-09 | Stage environment populated |
| Begin data transfer to production | 2025-06-01 | Testing completion |
| Production cutover | 2025-06-21 | Production data transfer |

## Evaluation Criteria

### 🛡️ Product Security

#### Risk Assessment

The current status of risk assessments for this vendor and product:

* 🔲 Not Requested
* ☑️ In Progress
* 🔲 Completed without concerns
* 🔲 Completed with concerns: `Explain`

#### Data Compliance

The following types of Class 1 Protected Data is transmitted, processed, or stored in this solution:

* 🔲 PCI
* ☑️ PII
* 🔲 PHI

Data Protection Controls:

* ☑️ Data is protected at rest, in transit, and in use per WEX IT policy
* ☑️ HTTPS using TLS 1.2 or higher
* 🔲 Message encryption
* ☑️ Database Connections using TLS 1.2 or higher
* ☑️ Row-level data encryption
* ☑️ Transparent data encryption
* 🔲 De-identification (tokenization, hashing, masking, redaction)
* 🔲 Digital Signatures
* ☑️ Access control (permission, role, policy)

Data Retention:

* Data archived after: `Per customer requirements`
* Data purged after: `Per customer requirements`
* Logs purged after: `90 days`

#### Data Ingress / Egress

This solution uses the following data flow patterns:

* 🔲 Customer-to-WEX traffic
* 🔲 WEX-to-Customer traffic
* 🔲 Vendor-to-WEX traffic
* 🔲 WEX-to-Vendor traffic
* ☑️ Internal system-to-system traffic

For each data flow, provide:

| Origin System | Destination System | Protocol | Authentication | Data Classification | Encryption Methods |
|---------------|-------------------|----------|---------------|-------------------|-------------------|
| Sawatch Systems | Snowflake | HTTPS | SSL Key Pair/2FA | Class 1 (PII) | TLS 1.2+ |

```mermaid
flowchart LR
  SawatchSystems[Sawatch Systems] -->|Secure Data Transfer| Snowflake[Snowflake Data Platform]
  
  subgraph "Data Protection Controls"
    Snowflake -->|TLS 1.2+| Storage[Snowflake Storage]
    Snowflake -->|Key Pair Auth| Storage
    Snowflake -->|Role-based Access| Storage
  end
```

#### Authentication & Authorization

WEX Employee Authentication Components:

* 🔲 IdentityIQ SAML
* 🔲 Okta Workforce Identity
* 🔲 Active Directory: `WEXPRODR`
* ☑️ Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* ☑️ Other: `Snowflake native user management with 2FA and Key Pair authentication`

WEX Employee automated provisioning & deprovisioning:

* ☑️ Yes
* 🔲 No

WEX Customer Authentication Components:

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory: `DOMAIN FQDN`
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* ☑️ WEX customers do not authenticate
* 🔲 Other: `Explain`

WEX Service Principal Types:

* ☑️ Amazon Managed Identity
* 🔲 Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA: `DOMAIN FQDN`
* 🔲 Active Directory User Service Account: `DOMAIN FQDN`
* 🔲 Local user
* 🔲 Digital certificates
* 🔲 Local administrator
* 🔲 Other: `Explain`

All employee, customer, and service principal credentials require periodic rotation:

* ☑️ Yes
* 🔲 No

Service principal credentials are automatically rotated:

* ☑️ Yes
* 🔲 No

#### Network Traffic Patterns

**Inbound Connectivity:**

* 🔲 Protected by Imperva WAF
* 🔲 Direct inbound (not WAF-protected)
* 🔲 Customer/vendor traffic
* ☑️ WEX business user traffic  
* ☑️ WEX support user traffic

**Outbound Connectivity:**

* 🔲 To customer/vendor systems
* ☑️ From WEX network (support)
* ☑️ Class 1 data transmission
* ☑️ Class 2 data transmission

**Security Controls:**

* 🔲 Bot protection enabled
* ☑️ Automated certificate renewal
* ☑️ TLS 1.2+ enforcement
* ☑️ Network traffic encryption

### 🤖 AI/ML

Indicate if this design includes any new AI/ML components:

* 🔲 Yes
* ☑️ No

Indicate if all AI/ML components have been reviewed for AI/ML compliance:

* 🔲 Yes
* 🔲 No

### 🔄 Reliability

#### High Availability Controls

* ☑️ Availability Zone Redundancy
* ☑️ Regional Failover
* ☑️ Automated Failover
* 🔲 Multi-Region Active-Active
* 🔲 Load Balancing
* 🔲 Content Delivery Network
* ☑️ Monitoring & Alerting
* 🔲 Other: `Explain`

#### Outage Impact Assessment

Business Impact:

* 🔲 Critical customer business impact
* ☑️ Partial loss of non-critical functionality
* 🔲 Customer recovery assistance required
* 🔲 Multiple lines of business affected
* 🔲 Multiple products in LOB affected
* 🔲 Multiple customers affected
* 🔲 External SLA impact
* ☑️ Internal SLA impact
* ☑️ Engineering support required

Recovery Actions:

* 🔲 Infrastructure deployment/patching
* 🔲 Application deployment
* ☑️ Configuration updates (App/LB/DNS/Firewall/Gateway)
* ☑️ Data recovery operations
* 🔲 Other: `Explain`

#### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | `AWS` |
| Primary Region | `US-East-1` |
| Failover Region | `As per Data Platform Team configuration` |
| Production VNet | `As per Data Platform Team configuration` |
| WEX Fabric ID | `N/A` |

#### Vendor Cloud Infrastructure (if applicable)

| Component | Value |
|-----------|--------|
| Vendor Name | `Snowflake` |
| Cloud Provider | `AWS` |
| Primary Region | `US-East-1` |
| Failover Region | `As per Snowflake's replication strategy` |

#### Network Connectivity Services

* 🔲 SDWAN
* ☑️ AWS Private Link
* 🔲 Azure Private Link
* 🔲 Netskope Private Access (NPA)
* 🔲 Point-to-Point VPN
* 🔲 Network Peering
* 🔲 Client VPN
* 🔲 DMZ

> [!note]
> All data flows comply with WEX security standards for encryption, access control, and monitoring.

### ☁️ SaaS

#### Key Components

| Component | Description | Technology | Purpose |
|-----------|-------------|------------|---------|
| Snowflake Data Platform | Cloud data warehouse | Snowflake | Storage and analysis of Sawatch data |
| Data Integration Pipeline | ETL/ELT processes | AWS/Snowflake | Migrate and transform data from PostgreSQL to Snowflake |

#### Design Impact Scope

* 🔲 Impacts or used by more than 25 users
* ☑️ Dependent upon legacy or end-of-life software/hardware
* ☑️ Vendor & Product Risk Assessment completed

#### Logging & APM

* ☑️ Splunk Forwarding
* 🔲 DataDog Forwarding
* ☑️ Security Event Logging
* 🔲 Synthetic Testing
* ☑️ Log Archival
* ☑️ Log Purging
* ☑️ Class 1 Data Logging Controls

#### Design Patterns

Design, delivery, or support patterns incorporated in this design:

* **Pattern 1:** Managed Data Warehouse
* **Pattern 2:** ETL/ELT Data Integration

#### Standards Adoption

Deviations from standard tools, services, and reference architectures are:

* 🔲 **WEX Fabric:** `Justification`
* 🔲 **SCM (GitHub Enterprise):** `Justification`
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `Justification`
* 🔲 **DFS (Panzura):** `Justification`
* 🔲 **Outbound Email (PowerMTA/Sendgrid):** `Justification`
* 🔲 **SFTP (GoAnywhere):** `Justification`
* 🔲 **WAF (Imperva):** `Justification`
* 🔲 **CDN (Imperva):** `Justification`
* 🔲 **Logging (Splunk):** `Justification`
* 🔲 **APM (DataDog):** `Justification`
* 🔲 **Auth Gateway:** `Justification`
* 🔲 **AI Gateway:** `Justification`
* 🔲 **Eventing Platform:** `Justification`
* 🔲 **WEX Data Platform:** `Justification`
* 🔲 **Tokenizer:** `Justification`
* 🔲 **Notification Hub:** `Justification`

### Relevant Context

The following sections provide relevant context based on the proposed solution:

#### Secure Coding

**Technology Stack:**

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | `SQL, Python (for data migration scripts)` |
| Database Technologies | `PostgreSQL (source), Snowflake (target)` |
| Storage Solutions | `Snowflake Storage` |
| Operating Systems | `Linux (for migration tooling)` |
| Client Platforms | `N/A - No direct client access` |

**Security Controls:**

Code quality scanning services that are active for all development repositories:

* ☑️ Snyk
* 🔲 Cycode
* 🔲 Mend Renovate
* 🔲 GitHub Copilot
* 🔲 Dependabot
* 🔲 Other: `Service Name`

#### Procurement Details

Third-Party Software Type:

* ☑️ SaaS
* 🔲 IaaS
* 🔲 AWS PaaS
* 🔲 Azure PaaS
* 🔲 Code Dependency Package
* 🔲 Desktop
* 🔲 Mobile App
* 🔲 Browser - Web Browser

Additional Considerations:

* ☑️ Software installation & configuration is automated
* 🔲 Cost exceeds $25,000
* ☑️ Solution is part of Target State Architecture
* 🔲 Temporary solution planned for replacement

Alternatives Considered:

* **AWS RDS PostgreSQL:** Would require more complex migration and management but provide database compatibility. Rejected due to higher operational complexity and not aligning with WEX Data Platform strategy.
* **Direct AWS Migration of Current Architecture:** Would replicate existing complexity on AWS infrastructure. Rejected due to maintenance overhead and missed opportunity for platform modernization.
* **Custom Data Lake Solution:** Would require building custom data storage and query capabilities. Rejected due to development time and existing investment in Snowflake.

## Diagrams

### System Context Diagram

```mermaid
graph TB
    classDef systemBoundary fill:#e6f2ff,stroke:#0066cc,stroke-width:2px
    classDef externalEntity fill:#f9f9f9,stroke:#999999
    classDef dataStorage fill:#e6ffee,stroke:#006633
    
    subgraph WEXDataPlatform["WEX Data Platform"]
        class WEXDataPlatform systemBoundary
        Snowflake["Snowflake Data Warehouse"]:::dataStorage
        DataPlatformServices["Data Platform Services"]
    end
    
    subgraph SawatchSystem["Sawatch System"]
        class SawatchSystem systemBoundary
        SawatchApps["Sawatch Applications"]
        CurrentDB["Current PostgreSQL Database"]:::dataStorage
    end
    
    DataAnalysts["Data Analysts"]
    InternalSystems["Other WEX Systems"]
    
    SawatchApps -->|"Data Access"| CurrentDB
    SawatchApps -->|"Data Migration"| Snowflake
    CurrentDB -->|"ETL/ELT Processes"| Snowflake
    DataAnalysts -->|"Query Data"| Snowflake
    Snowflake -->|"Integrated Data"| DataPlatformServices
    DataPlatformServices -->|"Data Services"| InternalSystems
    
    class DataAnalysts,InternalSystems externalEntity
```

### Cloud Architecture Diagram

```mermaid
flowchart TB
    classDef aws fill:#FF9900,stroke:#232F3E,color:#232F3E
    classDef sawatch fill:#9370DB,stroke:#483D8B,color:white
    classDef snowflake fill:#29B5E8,stroke:#1E88E5,color:white
    classDef dataflow fill:#FFFFFF,stroke:#555555,color:black
    
    subgraph AWS["AWS Cloud"]
        class AWS aws
        
        subgraph VPC["WEX VPC"]
            DataPlatform["WEX Data Platform"]
            
            subgraph Snowflake["Snowflake Service"]
                class Snowflake snowflake
                Compute["Compute Layer"]
                Storage["Storage Layer"]
                Services["Snowflake Services"]
            end
        end
        
        subgraph DigitalOcean["Current Digital Ocean Environment"]
            class DigitalOcean sawatch
            Droplets["PostgreSQL Droplets"]
            SawatchApps["Sawatch Applications"]
        end
    end
    
    MigrationPipeline["Data Migration Pipeline"]:::dataflow
    
    SawatchApps -->|"Use Current DB"| Droplets
    Droplets -->|"Extract Data"| MigrationPipeline
    MigrationPipeline -->|"Load Data"| Storage
    Compute -->|"Query"| Storage
    DataPlatform -->|"Integrate"| Services
    
    %% Add annotations for security boundaries
    classDef secureZone fill:#e6ffe6,stroke:#006600
    class VPC secureZone
```

## Risk Analysis & Dependencies

### Key Risks

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Unanticipated costs with Snowflake usage | Medium | Low | Consulted with internal Snowflake experts to ensure efficient schema design |
| Snowflake query performance issues | High | Low | Stress tested solution with significant data volumes; performance benchmarked at 4x faster than current PostgreSQL solution |
| Data migration issues | High | Medium | Comprehensive testing plan with validation stages and rollback capability |

### Critical Assumptions

* **Snowflake Platform Capacity:** Assuming sufficient capacity exists within the WEX Data Platform Snowflake implementation to accommodate Sawatch data volumes
* **Schema Compatibility:** Assuming PostgreSQL schema can be efficiently mapped to Snowflake with minimal structural changes
* **Data Platform Team Support:** Assuming availability of Data Platform Team resources to support provisioning and configuration

### Known Issues

* **Data Ownership:** Data is considered owned by customers, with Sawatch/WEX having custody. Deletion policies must be maintained in the new platform.
* **Regional Data Constraints:** While primarily serving US customers, any future international expansion would require review of data residency requirements.

### System Dependencies

| Dependency | Type | Criticality | Contact Team |
|------------|------|-------------|--------------|
| WEX Data Platform | System | High | Data Platform Team |
| Sawatch PostgreSQL Data | Data | High | Sawatch Engineering Team |
| Snowflake Service | System | High | Data Platform Team |

## Reference Links

* **Documentation:**
  * [iSARB Presentation](https://docs.google.com/presentation/d/1Y9HHVy00s3bY9xYdHLat2L-pU3MewjG7uSWoKVY9J54)

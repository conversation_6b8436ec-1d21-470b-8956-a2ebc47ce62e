<!-- Parent: Designs -->
<!-- Parent: GTS -->
<!-- Title: RFC-501 Grafana Observability Platform -->

<!-- Include: macros.md -->
# Grafana Observability Platform

:isarb:

```text
Author: <PERSON>
Publish Date: 2025-05-06
Category: Designs
Subtype: GTS
Ratified By: iSARB (2025-04-30)
```

## Executive Summary

WEX is currently evaluating vendors to replace our current observability platform, Datadog. This solution proposes replacing Datadog with Grafana Labs' observability platform which provides the necessary functionality to support infrastructure and applications.

This solution drives improvements towards the following Tech Transformation Vectors:

| Vector | Impact |
|--------|--------|
| 🔲 Product Security | - |
| ☑️ Reliability | Provides robust monitoring with 99.5% SLA and 24 hour RTO |
| 🔲 Product Innovation Velocity (PiV) | Enables vendor-agnostic APM instrumentation via Open Telemetry |
| ☑️ SaaS Maturity | Decreased cost allows monitoring of test environments |
| 🔲 AI/ML Maturity | - |

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

:toc:

## 💼 Change Context

* **Organization:** GTS
* **Purpose:** Replace Datadog with an improved observability platform for infrastructure and application monitoring
* **Users:** SRE, Developers, CloudEng, and various support teams (currently 1200 users in Datadog)
* **Integration Points:** Cloud infrastructure, application services, databases, networks
* **Third-Party Software:** Grafana Labs SaaS (Approved Usage)
* **Impacted Products:**
  * All WEX products utilizing observability tooling
  * All infrastructure components leveraging current monitoring
* **Design Review Qualities:**
  * ☑️ 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * 🔲 🟡 New Pattern Adoption
  * 🔲 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Implementation Planning | 2025-Q2 | Final approval |
| Agent Deployment | 2025-Q3 | Implementation Planning |
| Instrumentation Updates | 2025-Q3 | Agent Deployment |
| Datadog Decommission | 2025-Q4 | Full Grafana Implementation |

## Evaluation Criteria

### 🛡️ Product Security

#### Risk Assessment

The current status of risk assessments for this vendor and product:

* 🔲 Not Requested
* ☑️ In Progress
* 🔲 Completed without concerns
* 🔲 Completed with concerns

Vendor Review and Coupa VRA are currently underway.

#### Data Compliance

The following types of Class 1 Protected Data is transmitted, processed, or stored in this solution:

* 🔲 PCI
* 🔲 PII
* 🔲 PHI

No protected data will be intentionally transmitted, though there is potential for inadvertent inclusion in trace, span, or log data by support teams.

Data Protection Controls:

* ☑️ Data is protected at rest, in transit, and in use per WEX IT policy
* ☑️ HTTPS using TLS 1.2 or higher
* 🔲 Message encryption
* 🔲 Database Connections using TLS 1.2 or higher
* 🔲 Row-level data encryption
* 🔲 Transparent data encryption
* 🔲 De-identification (tokenization, hashing, masking, redaction)
* 🔲 Digital Signatures
* ☑️ Access control (permission, role, policy)

Data Retention:

* Data retention follows Grafana's standard data retention policies
* Audit logs can be exported as needed

#### Data Ingress / Egress

This solution uses the following data flow patterns:

* 🔲 Customer-to-WEX traffic
* 🔲 WEX-to-Customer traffic
* ☑️ Vendor-to-WEX traffic
* ☑️ WEX-to-Vendor traffic
* ☑️ Internal system-to-system traffic

For each data flow, provide:

| Origin System | Destination System | Protocol | Authentication | Data Classification | Encryption Methods |
|---------------|-------------------|----------|---------------|-------------------|-------------------|
| WEX Infrastructure | Grafana Cloud | HTTPS | API Keys/OAuth | Class 2 | TLS 1.2+ |
| Grafana Agents | Grafana Cloud | HTTPS | API Keys | Class 2 | TLS 1.2+ |
| WEX Systems | Grafana Agents | Internal | N/A | Class 2 | N/A |

```mermaid
flowchart LR
  WEXSystems[WEX Systems] -->|Metrics/Logs| Agents[Grafana Agents]
  Agents -->|TLS 1.2+| GrafanaCloud[Grafana Cloud]
  Users[WEX Users] -->|HTTPS| GrafanaCloud
  
  subgraph "Data Protection Controls"
    Agents -->|TLS 1.2+| GrafanaCloud
    Users -->|SAML Authentication| GrafanaCloud
    Users -->|Authorization| GrafanaCloud
  end
```

#### Authentication & Authorization

WEX Employee Authentication Components:

* ☑️ IdentityIQ SAML
* ☑️ Okta Workforce Identity
* 🔲 Active Directory: `WEXPRODR`
* 🔲 Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* 🔲 Other: `Explain`

WEX Employee automated provisioning & deprovisioning:

* ☑️ Yes
* 🔲 No

WEX Customer Authentication Components:

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory: `DOMAIN FQDN`
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* ☑️ WEX customers do not authenticate
* 🔲 Other: `Explain`

WEX Service Principal Types:

* 🔲 Amazon Managed Identity
* 🔲 Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA: `DOMAIN FQDN`
* 🔲 Active Directory User Service Account: `DOMAIN FQDN`
* 🔲 Local user
* 🔲 Digital certificates
* 🔲 Local administrator
* ☑️ Other: `API Keys for Agent Authentication`

All employee, customer, and service principal credentials require periodic rotation:

* ☑️ Yes
* 🔲 No

Service principal credentials are automatically rotated:

* 🔲 Yes
* ☑️ No

#### Network Traffic Patterns

**Inbound Connectivity:**

* 🔲 Protected by Imperva WAF
* 🔲 Direct inbound (not WAF-protected)
* 🔲 Customer/vendor traffic
* ☑️ WEX business user traffic  
* ☑️ WEX support user traffic

**Outbound Connectivity:**

* 🔲 To customer/vendor systems
* ☑️ From WEX network (support)
* 🔲 Class 1 data transmission
* ☑️ Class 2 data transmission

**Security Controls:**

* 🔲 Bot protection enabled
* 🔲 Automated certificate renewal
* ☑️ TLS 1.2+ enforcement
* ☑️ Network traffic encryption

### 🤖 AI/ML

Indicate if this design includes any new AI/ML components:

* 🔲 Yes
* ☑️ No

Indicate if all AI/ML components have been reviewed for AI/ML compliance:

* 🔲 Yes
* ☑️ No (Not Applicable)

### 🔄 Reliability

#### High Availability Controls

* ☑️ Availability Zone Redundancy
* 🔲 Regional Failover
* 🔲 Automated Failover
* 🔲 Multi-Region Active-Active
* ☑️ Load Balancing
* 🔲 Content Delivery Network
* ☑️ Monitoring & Alerting
* 🔲 Other: `Explain`

#### Outage Impact Assessment

Business Impact:

* 🔲 Critical customer business impact
* ☑️ Partial loss of non-critical functionality
* 🔲 Customer recovery assistance required
* ☑️ Multiple lines of business affected
* ☑️ Multiple products in LOB affected
* 🔲 Multiple customers affected
* 🔲 External SLA impact
* ☑️ Internal SLA impact
* ☑️ Engineering support required

Recovery Actions:

* ☑️ Infrastructure deployment/patching
* ☑️ Application deployment
* ☑️ Configuration updates (App/LB/DNS/Firewall/Gateway)
* 🔲 Data recovery operations
* 🔲 Other: `Explain`

#### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | `N/A - Vendor SaaS` |
| Primary Region | `AWS us-east-2` |
| Failover Region | `N/A` |
| Production VNet | `N/A` |
| WEX Fabric ID | `N/A` |

#### Vendor Cloud Infrastructure (if applicable)

| Component | Value |
|-----------|--------|
| Vendor Name | `Grafana Labs` |
| Cloud Provider | `AWS` |
| Primary Region | `us-east-2` |
| Failover Region | `Vendor managed` |

#### Network Connectivity Services

* 🔲 SDWAN
* 🔲 AWS Private Link
* 🔲 Azure Private Link
* 🔲 Netskope Private Access (NPA)
* 🔲 Point-to-Point VPN
* 🔲 Network Peering
* 🔲 Client VPN
* 🔲 DMZ

> [!note]
> Standard HTTPS connectivity is used for this solution with no special network connectivity services required.

### ☁️ SaaS

#### Key Components

| Component | Description | Technology | Purpose |
|-----------|-------------|------------|---------|
| Grafana Alloy | Agent collection system | Go-based agents | Collecting metrics, logs, traces |
| Grafana Cloud | SaaS observability platform | Cloud-hosted | Visualization, alerting, analysis |
| Open Telemetry | Vendor-agnostic instrumentation | Various language SDKs | Application performance monitoring |

#### Design Impact Scope

* ☑️ Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* ☑️ Vendor & Product Risk Assessment completed

#### Logging & APM

* ☑️ Splunk Forwarding
* 🔲 DataDog Forwarding
* ☑️ Security Event Logging
* ☑️ Synthetic Testing
* ☑️ Log Archival
* ☑️ Log Purging
* 🔲 Class 1 Data Logging Controls

#### Design Patterns

Design, delivery, or support patterns incorporated in this design:

* **Open Telemetry Pattern:** Standard observability data collection framework
* **Agent-based Monitoring:** Distributed monitoring agents collecting and forwarding metrics

#### Standards Adoption

Deviations from standard tools, services, and reference architectures are:

* 🔲 **WEX Fabric:** `Justification`
* 🔲 **SCM (GitHub Enterprise):** `Justification`
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `Justification`
* 🔲 **DFS (Panzura):** `Justification`
* 🔲 **Outbound Email (PowerMTA/Sendgrid):** `Justification`
* 🔲 **SFTP (GoAnywhere):** `Justification`
* 🔲 **WAF (Imperva):** `Justification`
* 🔲 **CDN (Imperva):** `Justification`
* 🔲 **Logging (Splunk):** `Justification`
* ☑️ **APM (DataDog):** `Replacing with Grafana as strategic decision`
* 🔲 **Auth Gateway:** `Justification`
* 🔲 **AI Gateway:** `Justification`
* 🔲 **Eventing Platform:** `Justification`
* 🔲 **WEX Data Platform:** `Justification`
* 🔲 **Tokenizer:** `Justification`
* 🔲 **Notification Hub:** `Justification`

### Relevant Context

#### Secure Coding

#### Procurement Details

Third-Party Software Type:

* ☑️ SaaS
* 🔲 IaaS
* 🔲 AWS PaaS
* 🔲 Azure PaaS
* 🔲 Code Dependency Package
* 🔲 Desktop
* 🔲 Mobile App
* 🔲 Browser - Web Browser

Additional Considerations:

* ☑️ Software installation & configuration is automated
* ☑️ Cost exceeds $25,000
* ☑️ Solution is part of Target State Architecture
* 🔲 Temporary solution planned for replacement

Alternatives Considered:

* **Datadog:** Current solution, more expensive, comprehensive feature set
* **Splunk Observability Cloud:** Lacks some key features (APM partially supported, no database performance, no network performance, no service catalog)
* **In-house solution:** Significant development and maintenance burden, not cost-effective

## Diagrams

### System Context Diagram

```mermaid
graph TB
    User[WEX User]
    DevOps[DevOps Teams]
    SRE[SRE Teams]
    Grafana[Grafana Cloud]
    WEXInfra[WEX Infrastructure]
    Apps[WEX Applications]
    Agents[Grafana Agents]
    Okta[Okta SAML]

    User -->|Views Dashboards| Grafana
    DevOps -->|Configures Monitoring| Grafana
    SRE -->|Responds to Alerts| Grafana
    Grafana -->|Alerts| SRE
    WEXInfra -->|Metrics/Logs| Agents
    Apps -->|Traces/Metrics| Agents
    Agents -->|Forwards Data| Grafana
    Okta -->|Authenticates| Grafana

    classDef secure fill:#e6ffe6,stroke:#006600
    classDef external fill:#f9f9f9,stroke:#666666
    class Okta secure
    class Grafana external
```

### Cloud Architecture Diagram

```mermaid
flowchart TB
    subgraph "WEX Environment"
        WEXUsers[WEX Users]
        subgraph "Monitored Systems"
            Apps[Applications]
            Infra[Infrastructure]
            Network[Networks]
            Databases[Databases]
        end
        
        subgraph "Agent Layer"
            Agents[Grafana Agents]
        end
        
        Okta[Okta IdP]
    end
    
    subgraph "Grafana Cloud (AWS us-east-2)"
        API[Grafana API Gateway]
        Metrics[Metrics Storage]
        Logs[Logs Storage]
        Traces[Traces Storage]
        Dashboards[Dashboards]
        Alerts[Alerting Engine]
    end
    
    WEXUsers -->|HTTPS/TLS 1.2+| API
    Apps -->|Metrics/Traces| Agents
    Infra -->|Metrics/Logs| Agents
    Network -->|Metrics| Agents
    Databases -->|Metrics| Agents
    
    Agents -->|HTTPS/TLS 1.2+| API
    API -->|Store| Metrics
    API -->|Store| Logs
    API -->|Store| Traces
    
    API -->|Retrieve| Dashboards
    API -->|Manage| Alerts
    Okta -->|SAML Authentication| API
    
    classDef wexSys fill:#e6f2ff,stroke:#0066cc
    classDef grafana fill:#F46800,stroke:#464646,color:white
    classDef secure fill:#e6ffe6,stroke:#006600
    
    class Apps,Infra,Network,Databases,Agents,WEXUsers wexSys
    class API,Metrics,Logs,Traces,Dashboards,Alerts grafana
    class Okta secure
```

## Risk Analysis & Dependencies

### Key Risks

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Instrumentation compatibility | Medium | Medium | Implement Open Telemetry standards for APM to ensure vendor agnostic approach |
| Data migration challenges | Medium | Medium | Create parallel implementation period with both tools running simultaneously |
| User adoption | Medium | Low | Provide training materials and workshops for users |
| Feature parity gaps | Medium | Low | Identify critical features and implement workarounds where necessary |

### Critical Assumptions

* **Agent Installation:** Existing infrastructure allows for agent installation without significant changes
* **Network Access:** Outbound connectivity to Grafana Cloud endpoints is permitted through firewalls
* **User Authentication:** Okta integration will function as expected with Grafana Cloud

### Known Issues

* **APM Instrumentation:** Current Datadog APM instrumentation will need to be replaced with Open Telemetry
* **Dashboard Migration:** Custom dashboards will need to be rebuilt in Grafana format
* **Alert Configuration:** Alert rules and notification channels will need to be recreated

### System Dependencies

| Dependency | Type | Criticality | Contact Team |
|------------|------|-------------|--------------|
| Okta | Authentication | High | IAM Team |
| Open Telemetry | Instrumentation | High | SRE Team |
| Network Connectivity | Infrastructure | Medium | Network Team |

## Reference Links

* **Documentation:**
  * [iSARB Presentation](https://docs.google.com/presentation/d/1sfucYEQYYvDsTfB1Z8scaCxuititCBj7nZNjxIpF-g8)

<!-- Parent: Designs -->
<!-- Parent: GTS -->
<!-- Title: RFC-487 Azure 3rd Party Gateway -->

<!-- Include: macros.md -->
# Azure 3rd Party Gateway

:isarb:

```text
Author: <PERSON> / <PERSON>gart
Publish Date: 2025-04-09
Category: Designs
Subtype: GTS
```

## Executive Summary

We require a secure method for granting third-party solutions direct access to our Azure resources. Specifically, SaaS providers like Aiven necessitate private VNet-to-VNet connections. Similarly, Snowflake and other third-party services will have comparable connectivity demands. Our current networking hub architecture prevents us from establishing these secure, isolated connections without inadvertently exposing unintended network segments.

To address these limitations, we propose deploying a dedicated third-party connectivity hub leveraging a hub-and-spoke topology with centralized Palo Alto firewalls for security enforcement. This solution enables fine-grained access control while maintaining proper network segmentation and security.

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## Change Context

* **Organization:**: Global Technology Services (GTS)
* **Purpose:** Secure third-party connectivity to Azure resources
* **Users:** Cloud Engineering, Network Teams, Third-Party Integration Partners
* **Integration Points:** Azure VNets, Third-Party Cloud Services (Aiven, Snowflake)
* **Third-Party Software:** Approved Usage
* **Impacted Products:**
  * Azure Cloud Infrastructure
  * Aiven Kafka
  * Snowflake
* **Design Review Qualities:**
  * 🔲 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * ✅ 🔴 HA / DR Strategy Changes
  * ✅ 🟡 New Pattern Adoption
  * ✅ 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Design Approval | 2025-04 | Architecture and Security Review |
| Infrastructure Deployment | 2025-05 | Design Approval |
| Initial Partner Onboarding | 2025-06 | Infrastructure Deployment |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

Risk assessment for this infrastructure solution:

* 🔲 Not Requested
* 🔲 In Progress
* ✅ Completed without concerns
* 🔲 Completed with concerns

#### Data Classification & Protection

Estimate the number of Class 1 Data records that are transmitted, processed, or stored:

* **PCI Records:** `0`
* **PII Records:** `0`
* **PHI Records:** `0`

Data Protection Controls:

* ✅ Data is protected at rest, in transit, and in use per WEX IT policy
* ✅ HTTPS using TLS 1.2 or higher
* ✅ Message encryption
* ✅ Database Connections using TLS 1.2 or higher
* ✅ Row-level data encryption
* ✅ Transparent data encryption
* ✅ De-identification (tokenization, hashing, masking, redaction)
* ✅ Digital Signatures
* ✅ Access control (permission, role, policy)

Data Retention:

* Data archived after: `N/A`
* Data purged after: `N/A`
* Logs purged after: `90 days`

#### AI/ML Compliance

Indicate if this design includes any new AI/ML components:

* 🔲 Yes
* ✅ No

### 💼 Business & Enterprise Impact

#### Design Impact Scope

* ✅ Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* ✅ Vendor & Product Risk Assessment completed

#### Procurement Details

Software Type:

* 🔲 SaaS
* ✅ IaaS
* 🔲 AWS PaaS
* ✅ Azure PaaS
* 🔲 Code Dependency Package
* 🔲 Desktop
* 🔲 Mobile App
* 🔲 Browser - Web Browser

Additional Considerations:

* ✅ Software installation & configuration is automated
* ✅ Cost exceeds $25,000
* ✅ Solution is part of Target State Architecture
* 🔲 Temporary solution planned for replacement

Alternatives Considered:

* **Direct VNet Peering:** Direct peering lacks centralized security control and visibility
* **Public-facing Endpoints:** Exposes services to internet threats and additional security risks
* **Shared Services VNet:** Would create cross-contamination risk between third parties

### 🔄 Operations & Reliability

#### High Availability Controls

* ✅ Availability Zone Redundancy
* ✅ Regional Failover
* ✅ Automated Failover
* 🔲 Multi-Region Active-Active
* ✅ Load Balancing
* 🔲 Content Delivery Network
* ✅ Monitoring & Alerting
* 🔲 Other

#### Outage Impact Assessment

Business Impact:

* 🔲 Critical customer business impact
* ✅ Partial loss of non-critical functionality
* 🔲 Customer recovery assistance required
* ✅ Multiple lines of business affected
* ✅ Multiple products in LOB affected
* 🔲 Multiple customers affected
* 🔲 External SLA impact
* ✅ Internal SLA impact
* ✅ Engineering support required

Recovery Actions:

* ✅ Infrastructure deployment/patching
* 🔲 Application deployment
* ✅ Configuration updates (App/LB/DNS/Firewall/Gateway)
* 🔲 Data recovery operations
* 🔲 Other

#### Observability

Logging & APM Features:

* ✅ Splunk Forwarding
* ✅ DataDog Forwarding
* ✅ Security Event Logging
* ✅ Synthetic Testing
* ✅ Log Archival
* ✅ Log Purging
* ✅ Class 1 Data Logging Controls

### Data Ingress / Egress

#### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | `Azure` |
| Primary Region | `East US` |
| Failover Region | `West US` |
| Production VNet | `ThirdPartyHub-Prod` |
| WEX Fabric ID | `N/A` |

#### Vendor Cloud Infrastructure (if applicable)

| Component | Value |
|-----------|--------|
| Vendor Name | `Various (Aiven, Snowflake, etc.)` |
| Cloud Provider | `AWS / Azure / GCP` |
| Primary Region | `Varies by vendor` |
| Failover Region | `Varies by vendor` |

#### Network Traffic Patterns

**Inbound Connectivity:**

* 🔲 Protected by Imperva WAF
* ✅ Direct inbound (not WAF-protected)
* ✅ Customer/vendor traffic
* 🔲 WEX business user traffic  
* ✅ WEX support user traffic

**Outbound Connectivity:**

* ✅ To customer/vendor systems
* ✅ From WEX network (support)
* ✅ Class 1 data transmission
* ✅ Class 2 data transmission

**Security Controls:**

* 🔲 Bot protection enabled
* ✅ Automated certificate renewal
* ✅ TLS 1.2+ enforcement
* ✅ Network traffic encryption

#### Network Connectivity Services

* 🔲 SDWAN
* 🔲 AWS Private Link
* ✅ Azure Private Link
* 🔲 Netskope Private Access (NPA)
* ✅ Point-to-Point VPN
* ✅ Network Peering
* 🔲 Client VPN
* ✅ DMZ

> [!note]
> All data flows comply with WEX security standards for encryption, access control, and monitoring.

### Authentication & Authorization

Select the authentication components for WEX Employees (select all that apply):

* 🔲 IdentityIQ SAML
* ✅ Okta Workforce Identity
* ✅ Active Directory: `WEXPRODR`
* 🔲 Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* 🔲 Other

Indicate if access for WEX employees is automatically provisioned & deprovisioned:

* ✅ Yes
* 🔲 No

Select the authentication components for WEX Customers (select all that apply):

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* ✅ WEX customers do not authenticate
* 🔲 Other

Select the types of Service Principals (select all that apply):

* ✅ Amazon Managed Identity
* ✅ Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA
* 🔲 Active Directory User Service Account
* 🔲 Local user
* ✅ Digital certificates
* 🔲 Local administrator
* 🔲 Other

Indicate if all employee, customer, and service principal credentials require periodic rotation:

* ✅ Yes
* 🔲 No

Indicate if service principal credentials are automatically rotated:

* ✅ Yes
* 🔲 No

### Secure Coding

#### Technology Stack

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | `Terraform, PowerShell, Bash` |
| Database Technologies | `N/A` |
| Storage Solutions | `Azure Blob` |
| Operating Systems | `Linux, Containers` |
| Client Platforms | `Web, API` |

#### Security Controls

Select the code quality scanning services that are active for all development repositories (select all that apply):

* ✅ Snyk
* ✅ Cycode
* ✅ Mend Renovate
* ✅ GitHub Copilot
* ✅ Dependabot
* 🔲 Other

### Design Patterns

List any design, delivery, or support patterns incorporated in this design:

* **Hub-and-Spoke Network Topology:** [Azure Hub-Spoke Network Topology](https://learn.microsoft.com/en-us/azure/architecture/reference-architectures/hybrid-networking/hub-spoke)
* **Defense in Depth:** [Multi-layer Security](https://learn.microsoft.com/en-us/azure/security/fundamentals/design-efficient-security-operations)
* **Zero Trust Network Access:** [Zero Trust Architecture](https://learn.microsoft.com/en-us/security/zero-trust/zero-trust-overview)

### Paved Road Adoption

Select the deviations from standard tools, services, and reference architectures with justification (select all that apply):

* 🔲 **WEX Fabric:** `N/A`
* 🔲 **SCM (GitHub Enterprise):** `N/A`
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `N/A`
* 🔲 **DFS (Panzura):** `N/A`
* 🔲 **Outbound Email (PowerMTA/Sendgrid):** `N/A`
* 🔲 **SFTP (GoAnywhere):** `N/A`
* 🔲 **WAF (Imperva):** `Internal network traffic only`
* 🔲 **CDN (Imperva):** `N/A`
* 🔲 **Logging (Splunk):** `N/A`
* 🔲 **APM (DataDog):** `N/A`
* 🔲 **Auth Gateway:** `N/A`
* 🔲 **AI Gateway:** `N/A`
* 🔲 **Eventing Platform:** `N/A`
* 🔲 **WEX Data Platform:** `N/A`
* 🔲 **Tokenizer:** `N/A`
* 🔲 **Notification Hub:** `N/A`

## Diagrams

### System Context Diagram

```mermaid
graph TB
    subgraph "External Vendors"
        Aiven["Aiven Kafka"]
        Snowflake["Snowflake"]
        Other["Other 3rd Parties"]
    end
    
    subgraph "3rd Party Gateway"
        Gateway["Azure 3rd Party Gateway"]
        PaloAlto["Palo Alto Firewalls"]
    end
    
    subgraph "WEX Azure Environment"
        VNets["WEX VNets"]
        Resources["Azure Resources"]
    end
    
    Aiven -->|"Private VNet Peering"| Gateway
    Snowflake -->|"Private VNet Peering"| Gateway
    Other -->|"Private VNet Peering"| Gateway
    
    Gateway -->|"Traffic Inspection"| PaloAlto
    PaloAlto -->|"Secured Connection"| VNets
    VNets -->|"Access"| Resources
```

### Cloud Architecture Diagram

```mermaid
graph TB
    subgraph "Third Party Gateway Azure Environment"
        subgraph "Hub VNet"
            NVA["Palo Alto NVAs"]
            Bastion["Azure Bastion"]
            RouteServer["Azure Route Server"]
        end
        
        subgraph "Partner Spoke VNets"
            PartnerA["Partner A VNet"]
            PartnerB["Partner B VNet"]
            PartnerC["Partner C VNet"]
        end
        
        subgraph "WEX Core VNets"
            CoreA["WEX VNet A"]
            CoreB["WEX VNet B"]
        end
    end
    
    PartnerA -->|"Peering"| Hub
    PartnerB -->|"Peering"| Hub
    PartnerC -->|"Peering"| Hub
    
    Hub -->|"Peering"| CoreA
    Hub -->|"Peering"| CoreB
    
    Hub -->|"Route Propagation"| RouteServer
    RouteServer -->|"BGP Routes"| NVA
    NVA -->|"Traffic Inspection"| Hub
```

## Risk Analysis & Dependencies

* 🚨 **Key Risks:**
  * Change in Azure network routing could impact connectivity
  * Third-party access could expose internal resources if misconfigured
  * Firewall rules require rigorous maintenance and auditing

* 📋 **Critical Assumptions:**
  * Third parties will work with WEX to establish and maintain connections
  * WEX Network team will establish and maintain firewall rules
  * Azure networking features will remain compatible with the design

* ⚠️ **Known Issues:**
  * Initial deployment requires coordination with multiple teams
  * Firewall rule changes require careful testing before implementation
  * Troubleshooting complex connectivity issues may require vendor assistance

* 🔗 **System Dependencies:**
  * Azure VNet infrastructure
  * Palo Alto firewall technology
  * Azure Route Server
  * Third-party cloud networks

## Reference Links

* **Epic/Feature:** `AZ-1234`
* **Support Tickets:** `JSM-4567`
* **Documentation:**
  * `[Azure Private Link Service](https://learn.microsoft.com/en-us/azure/private-link/private-link-service-overview)`
  * `[Hub-Spoke Network Topology](https://learn.microsoft.com/en-us/azure/architecture/reference-architectures/hybrid-networking/hub-spoke)`
* **Repositories:**
  * `[Infrastructure-as-Code](https://github.com/wexinc/azure-third-party-gateway)`
  
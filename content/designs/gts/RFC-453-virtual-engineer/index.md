<!-- Parent: Designs -->
<!-- Parent: GTS -->
<!-- Title: RFC-453 Virtual Engineer -->

<!-- Include: macros.md -->
# Virtual Engineer

:isarb:

```text
Author: <PERSON>
Publish Date: 2024-05-08
Category: Designs
Subtype: Global Technology Services (GTS)
Ratified By: iSARB (2025-02-25)
```

## Executive Summary

Virtual Engineer, developed in collaboration with <PERSON> and <PERSON>, aims to streamline GTS support workflows by automating manual operations and reducing repetitive requests. It consolidates technical documentation, WEX standards, and reference architectures into a dynamic, searchable knowledge base to support Q&A through natural language, improving productivity and self-service support while reducing technology workload. This efficiency boost is expected to enhance operational efficiency, streamline service delivery, and improve standardized processes across the workforce.

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## Change Context

* **Organization:** Global Technology Services (GTS)
* **Purpose:** To streamline support workflows, automate manual operations, and reduce repetitive requests by consolidating technical documentation, WEX standards, and reference architectures into a dynamic, searchable knowledge base.
* **Users:** Software engineers, developers, and DevOps engineers across GTS
* **Integration Points:** Google Chat, Amazon Bedrock, Confluence, Github, Fabric Docs, Sendgrid
* **Third-Party Software:** Proof of Concept (POC)
* **Impacted Products:**
  * Confluence
  * Github
  * Fabric Docs
  * Sendgrid
* **Design Review Qualities:**
  * ✅ 🟡 New Pattern Adoption
  * ✅ 🟡 New Product or Capability

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Pilot | TBD | Secure messaging between Google Chat and Amazon Bedrock |
| Beta | ~6/30/25 | Pilot, Expanded Users |
| GA | ~9/30/25 | Beta, All Technology Users (GTS) |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

* ✅ Not Requested

#### Data Classification & Protection

* **PCI Records:** 0
* **PII Records:** 0
* **PHI Records:** 0

Data Protection Controls:

* ✅ Data is protected at rest, in transit, and in use per WEX IT policy
* ✅ HTTPS using TLS 1.2 or higher
* ✅ Transparent data encryption
* ✅ De-identification (tokenization, hashing, masking, redaction)
* ✅ Access control (permission, role, policy)

#### AI/ML Compliance

* ✅ Yes - design includes new AI/ML components
* ✅ Yes - AI/ML components have been reviewed for compliance

### 💼 Business & Enterprise Impact

#### Design Impact Scope

* ✅ Impacts or used by more than 25 users
* ✅ Vendor & Product Risk Assessment completed

#### Technology Stack

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | python3.12, nodejs18.x |
| Database Technologies | DynamoDB |
| Storage Solutions | S3 |
| Operating Systems | AWS Lambda |
| Client Platforms | Google Chat, Amazon Bedrock, Atlassian Confluence |

### 🔄 Operations & Reliability

#### High Availability Controls

* ✅ Availability Zone Redundancy
* ✅ Monitoring & Alerting

#### Network Traffic Patterns

**Inbound Connectivity:**

* ✅ Protected by Imperva WAF
* ✅ WEX business user traffic

**Outbound Connectivity:**

* ✅ To customer/vendor systems
* ✅ Class 1 data transmission

**Security Controls:**

* ✅ Bot protection enabled
* ✅ Automated certificate renewal
* ✅ TLS 1.2+ enforcement
* ✅ Network traffic encryption

### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | AWS |
| Primary Region | us-east-1 |
| Production VNet | wexinc-virtual-engineer-* |

### Vendor Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Vendor Name | Google |
| Cloud Provider | GCP |
| Primary Region | Chat API only |

| Component | Value |
|-----------|--------|
| Vendor Name | Microsoft |
| Cloud Provider | Azure |
| Primary Region | Read-only Graph -> EntraID |

## Diagrams

### System Context Diagram

```mermaid
graph TB
    User[WEX User]
    Customer[Customer]
    System[Virtual Engineer]
    Existing[Existing WEX Systems]
    
    User -->|Uses| System
    Customer -->|Does not interact| System
    System -->|Integrates| Existing
```

### Cloud Architecture Diagram

```mermaid
graph TB
    subgraph "WEX Cloud Infrastructure"
        WAF[Imperva WAF]
        App[AWS Lambda Functions]
        DB[(DynamoDB)]
        Queue[[SQS]]
    end
    
    subgraph "External Systems"
        GoogleChat[Google Chat]
        AmazonBedrock[Amazon Bedrock]
        Confluence[Confluence]
    end
    
    User[User] -->|HTTPS| WAF
    WAF -->|Internal| App
    App -->|Read/Write| DB
    App -->|Events| Queue
    App <-->|API| GoogleChat
    App <-->|API| AmazonBedrock
    App <-->|API| Confluence
```

## Risk Analysis & Dependencies

* 🚨 **Key Risks:**
  * Overreliance on third-party services like Google Chat and Amazon Bedrock could lead to vendor lock-in or service disruptions
  * Data security and privacy risks associated with handling sensitive information within the knowledge base
  * Potential for inaccurate or outdated information in the knowledge base, leading to incorrect answers or guidance

* 📋 **Critical Assumptions:**
  * Continued support and availability of Google Chat and Amazon Bedrock APIs
  * Accurate and up-to-date information will be maintained in the knowledge base
  * Users will adopt and utilize the Virtual Engineer system for support and guidance

* ⚠️ **Known Issues:**
  * Integration with certain internal systems or documentation sources may be challenging
  * Ensuring accuracy and relevance of search results requires ongoing tuning
  * User adoption may require training and ongoing support

* 🔗 **System Dependencies:**
  * Google Chat
  * Amazon Bedrock
  * Confluence
  * Github
  * Fabric Docs
  * Sendgrid

## Reference Links

* **Documentation:**
  * [Virtual Engineer iSARB Presentation](https://docs.google.com/presentation/d/1pF0JpVNKImYqBQbe4F4rAJ0KZKJ63NqvEid03dW1IXo)

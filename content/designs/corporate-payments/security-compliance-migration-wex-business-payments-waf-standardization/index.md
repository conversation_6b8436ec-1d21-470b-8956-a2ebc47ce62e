<!-- Parent: Designs -->
<!-- Parent: Corporate Payments -->
<!-- Title: RFC-527 Security Compliance Migration - WEX Business Payments WAF Standardization -->

<!-- Include: macros.md -->
# Candidate Architecture Design: Security Compliance Migration - WEX Business Payments WAF Standardization

:draft:

```text
Author: <PERSON><PERSON>wain
Title: Security Compliance Migration - WEX Business Payments WAF Standardization
Publish Date: 2025-07-15
Category: Designs
Subtype: Corporate Payments
```

## Table of Contents

:toc:

<!-- The :toc: macro will automatically generate a table of contents with proper case-sensitive links -->

## Document Completion Guide

| Section | When to Complete | Primary Audience | Focus Areas |
|---------|------------------|------------------|-------------|
| Business Context | Early design phase | Product owners, stakeholders | Business drivers, user needs |
| Solution Architecture | After requirements | Development team, architects | Technical approach, components |
| Risks & Dependencies | Throughout design process | Project managers, team | Mitigation, dependencies |
| Vector Impact | After architecture definition | Leadership, architects | Measurable improvements |

---

## 1.0 Document Control

**Project Information:**

- **Line of Business:** WEX Business Payments
- **Product Name:** WEX Business Payments Single-Page Application
- **Requirements:** Migrate SPA to RFC-459 compliant containerized architecture to address security vulnerabilities
- **Related Docs:** RFC-338 Secure Hosting For Static AWS S3 CDN, RFC-459 Single-Page Application Behind Imperva, INFRA-67850 Migration Ticket
- **Security Compliance:** Network Security Architecture team requirement for all public-facing applications behind Imperva WAF

| Stakeholder | Role | Status | Date | R | A | C | I |
| ----------- | ---- | ------ | ---- | - | - | - | - |
| [Name] | [Role] | [Status] | [Date] | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 |
| [Name] | [Role] | [Status] | [Date] | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 |
| [Name] | [Role] | [Status] | [Date] | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 |
| [Name] | [Role] | [Status] | [Date] | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 |
| [Name] | [Role] | [Status] | [Date] | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 |

> **Note on Executive Summary**: An Executive Summary is optional but recommended for senior leadership and stakeholders who need a high-level overview without technical details. If included, it should be written after all other sections are complete and placed at the beginning of the document.

---

## 2.0 Implementation Plan

### 2.1 Solution Strategy

**Migration Objective:** Achieve security compliance for WEX Business Payments by implementing RFC-459 compliant architecture, eliminating critical security vulnerabilities in non-prod environments and production dual CDN setup while maintaining SPA dynamic routing functionality.

**Selected Solution:** RFC-459 Single-Page Application Behind Imperva - Imperva WAF → ALB → EKS/ECS Containers → Nginx + Angular SPA

**Timeline:** 6-9 weeks total migration

**Key Benefits:**

- Eliminates critical security vulnerabilities (DDoS, injection attacks, CloudFront bypass)
- Provides proper web server layer for SPA dynamic routing functionality
- Centralized WAF management under Imperva (eliminates decentralized AWS WAF)
- Cost optimization through simplified architecture (detailed cost analysis in Section 3.4)

### 2.2 Solution Decision Analysis

**Content Type Analysis:** WEX Business Payments requires Single-Page Application functionality with dynamic routing, not just static content delivery.

**RFC Comparison Summary:**

| Criteria | RFC-338 (S3 + ALB) | RFC-459 (Containers + ALB) | Selected |
|----------|---------------------|------------------------------|----------|
| **SPA Support** | ❌ No | ✅ Yes | ✅ **Required** |
| **Dynamic Routing** | ❌ No | ✅ Yes | ✅ **Required** |
| Cost | Lower | Higher | ⚠️ |
| Complexity | Simple | Complex | ⚠️ |

**Decision:** RFC-459 selected because WEX Business Payments is a Single-Page Application requiring dynamic routing functionality that RFC-338 cannot provide.

*Complete cost analysis and technical architecture details are provided in Section 3.0.*

### 2.3 Current State Analysis

**WEX Business Payments (Yeti) Infrastructure:**

The Yeti platform operates with a three-tier microservices architecture across four environments:

> **Source:** [Yeti Infrastructure Documentation](https://wexinc.atlassian.net/wiki/x/JYEd1SM)

**Service Inventory:**

- **11 MFE services** (containerized SPAs - CDN migration target):
  - Production: `https://www.wexpaymentsplatform.com/app/`
  - Non-Prod: `https://yeti.{ENV}.encompass-suite.com/app/` (dev, qa, stage)
  - Services: `yeti-mfe-admin`, `yeti-mfe-auth`, `yeti-mfe-cards`, `yeti-mfe-dashboard`, `yeti-mfe-main`, `yeti-mfe-payment`, `yeti-mfe-pay-online`, `yeti-mfe-reporting`, `yeti-mfe-supplier`, `yeti-mfe-transaction`, `yeti-mfe-upload-history`

- **10 BFF services** (GraphQL APIs - no changes required):
  - Production: `https://www.wexpaymentsplatform.com/api/{SERVICE}/graphql/`
  - Non-Prod: `https://yeti.{ENV}.encompass-suite.com/api/{SERVICE}/graphql/`
  - Services: `yeti-bff-admin`, `yeti-bff-cards`, `yeti-bff-dashboard`, `yeti-bff-payment`, `yeti-bff-pay-online`, `yeti-bff-reporting`, `yeti-bff-supplier`, `yeti-bff-transaction`, `yeti-bff-upload-history`, `yeti-bff-user`

- **13 MOD services** (backend - no changes required):
  - Production: `https://yeti-mod.prod.encompass-suite.com/{SERVICE}/graphql/`
  - Non-Prod: `https://yeti-mod.{ENV}.encompass-suite.com/{SERVICE}/graphql/`
  - Services: `yeti-mod-application`, `yeti-mod-card`, `yeti-mod-dashboard`, `yeti-mod-legacy-integration`, `yeti-mod-organization`, `yeti-mod-pay-online`, `yeti-mod-payment`, `yeti-mod-payment-rails`, `yeti-mod-reporting`, `yeti-mod-supplier`, `yeti-mod-transaction`, `yeti-mod-upload-history`, `yeti-mod-user`

**Current CDN Issues:**

- **Non-prod environments**: `yeti.dev/qa/stage.encompass-suite.com` using CloudFront CDN without WAF protection
- **Production environment**: `www.wexpaymentsplatform.com` using dual CDN (Imperva + CloudFront) creating complexity
- **CloudFront misuse**: CDN handling SPA routing instead of proper web server (Nginx)
- **Decentralized management**: CloudFront managed by individual teams vs. centralized Imperva

**Migration Scope:**

- **Target**: CDN migration from CloudFront to Imperva for 11 containerized MFE services
- **Unchanged**: 23 backend services (BFF/MOD) require no CDN changes
- **Objective**: Single Imperva CDN with proper Nginx SPA routing
- **Health checks**: Existing `/heartbeat/` and `/health/` endpoints support automated validation

### 2.4 Target State (RFC-459 Implementation)

**Security Improvements:**

- All environments (yeti.qa, yeti.stage, wexbusinesspayments.com) protected by Imperva WAF
- Single CDN architecture eliminates dual CDN attack vectors
- Centralized WAF management under Imperva eliminates decentralized AWS WAF

**Technical Architecture Overview:**

- **Traffic Flow:** Imperva WAF → ALB → EKS/ECS → Nginx + Angular containers
- **Web Server Layer:** Containerized Nginx replaces CloudFront reverse proxy functionality for SPA routing
- **Container Platform:** Auto-scaling EKS/ECS deployment with proper health checks
- **CI/CD Integration:** Standardized container-based deployment pipelines

*Detailed architecture diagrams, technology stack, and security controls are provided in Section 3.0.*

### 2.5 Migration Implementation Plan

#### Phase 1: Non-Production Environment Migration (2-3 weeks)

#### Week 1-2: Assessment & Container Setup

1. **Current State Analysis:**
   - Document existing CI/CD pipeline for WEX Business Payments
   - Map content deployment flow: Source → Build → Container Registry → CloudFront invalidation
   - Analyze CloudFront cache behaviors, TTL settings, and invalidation patterns
   - Review traffic patterns and peak usage times

2. **Container Platform Setup:**
   - Set up EKS cluster or ECS Fargate service in non-prod AWS accounts
   - Configure ALB for container traffic routing
   - Create multi-stage Dockerfile for Angular application build
   - Set up container registry (ECR) and image management policies

3. **Imperva Onboarding:**
   - Submit request to Imperva team for domain onboarding: <https://wexinc.atlassian.net/servicedesk/customer/portal/13/create/1728>
   - Domains: yeti.qa.encompass-suite.com, yeti.stage.encompass-suite.com
   - Configure Imperva to forward traffic to ALB (obtain ALB endpoints first)
   - Configure caching rules for SPA content delivery with appropriate TTL settings
   - Obtain Imperva IP ranges for security group configuration

#### Week 2-3: Application Containerization & Security

1. **Application Containerization:**
   - Configure Nginx web server for SPA routing and reverse proxy functionality
   - Build and test container images with proper health checks
   - Deploy containerized SPA to EKS/ECS with auto-scaling policies

   **SPA Routing Configuration Details:**

   For Single-Page Applications, Nginx must handle dynamic routing where URLs don't correspond to static files. This is achieved through the `try_files` directive:

   ```nginx
   try_files $uri $uri/ /index.html;
   ```

   This configuration works as follows:

   - **$uri**: NGINX first tries to find a file that exactly matches the requested URI
   - **$uri/**: If $uri doesn't match a file, NGINX then tries to see if $uri is a directory
   - **/index.html**: This is the fallback. index.html contains the Angular application's JavaScript, which then "boots up" and figures out what content to display based on the URL in the browser

   **ALB vs NGINX Reverse Proxy Functions:**

   While both ALB and NGINX provide reverse proxy capabilities, they serve different purposes and operate at different layers:

   **ALB (Network Edge Layer):**

   - Manages public IP addresses and DNS
   - Performs SSL/TLS termination
   - Distributes traffic across instances/containers and Availability Zones for high availability and scalability
   - Routes requests based on network-level rules

   **NGINX (Application Layer within Containers):**

   - Serves SPA routing (index.html fallback), which ALBs do not understand
   - Efficiently serves static assets, offloading this from your application server
   - Provides additional caching capabilities
   - Allows fine-tuned request manipulation (URL rewrites, header modifications, advanced security rules)

   This layered approach ensures ALB handles edge concerns while NGINX manages application-specific logic within the containerized environment.

2. **Security Implementation:**
   - Configure security groups allowing inbound HTTPS (443) from Imperva IPs only
   - Update firewall rules to allow only Imperva to reach ALB
   - Test application via Imperva-provided DNS name before DNS cutover

#### Phase 2: Production Environment Migration (3-4 weeks)

#### Week 4-5: Production Platform Setup

1. **Production Infrastructure:**
   - Set up production EKS cluster or ECS Fargate service
   - Configure production ALB and security groups  
   - Set up production container registry and deployment pipelines
   - Submit Imperva onboarding request for production domain: wexbusinesspayments.com

2. **CI/CD Pipeline Migration:**
   - Update CI/CD to target container deployment instead of S3
   - Modify cache invalidation calls from CloudFront API to Imperva API
   - Set up cache invalidation API access for CI/CD automation

#### Week 6-7: Performance Validation & Testing

1. **Performance & Load Testing:**
   - Performance testing to compare metrics against current CloudFront setup
   - Load testing with production traffic patterns
   - Validate capacity and SPA functionality

#### Phase 3: DNS Cutover & Finalization (1-2 weeks)

#### Week 7-8: Migration Execution

1. **DNS Cutover Strategy:**
   - Implement progressive rollout using weighted DNS routing (1%, 10%, 50%, 100%)
   - Monitor application access and performance during each phase
   - Maintain immediate rollback capability by reverting DNS changes

2. **Post-Migration Validation & Cleanup:**
   - Verify application functionality through Imperva WAF routing
   - Validate performance metrics meet baseline requirements
   - Lock down security groups to allow only Imperva IP ranges
   - Decommission CloudFront distributions after validation period

### 2.6 Key Risks & Dependencies

**Critical Risks:**

- **DNS cutover disruption** - Mitigated by gradual traffic migration with rollback capability
- **Container deployment complexity** - Mitigated by comprehensive staging validation
- **Performance impact** - Mitigated by pre-migration load testing
- **Imperva configuration delays** - Mitigated by early staging environment validation

**Key Dependencies:**

- Imperva account setup (external dependency - blocks migration start)
- Container platform (EKS/ECS) provisioning (internal dependency)
- DNS management permissions (internal dependency - required for cutover)
- CloudFront SPA functionality validation with WBP DevOps team

*Detailed risk analysis and mitigation strategies are provided in Section 3.7.*

### 2.7 Success Criteria

- All non-prod environments (yeti.qa.encompass-suite.com, yeti.stage.encompass-suite.com) protected by Imperva WAF
- Production environment (wexbusinesspayments.com) using RFC-459 containerized SPA architecture
- Elimination of dual CDN attack vectors while maintaining SPA dynamic routing functionality
- Centralized WAF management under Imperva (elimination of decentralized AWS WAF)
- Cost reduction through simplified architecture
- Zero downtime migration with performance parity or improvement

*Detailed technical architecture and specifications are provided in Section 3.0.*

---

## 3.0 Appendix: Technical Details & Justification

> **Note:** This appendix provides comprehensive technical details, architectural justification, and analysis supporting the implementation plan outlined in Section 2. Reference these sections for detailed technical specifications, risk analysis, and compliance requirements.

### 3.1 Business Context & Problem Analysis

**Root Cause Analysis:**
WEX Business Payments web application hosting currently violates WEX secure hosting standards with multiple critical issues:

- **Imperva Limitations Force CloudFront Usage:** Application teams are currently using static websites hosted on S3. Due to limitations in Imperva, CloudFront is used as the origin. CloudFront doesn't have the ability to restrict source IPs, which is a security requirement to prevent WAF bypass
- **Mandatory Dual WAF Architecture:** Teams must enable AWS WAF and attach it to CloudFront to implement the source IP restrictions. Key features such as WAF and CDN, which are already handled by Imperva, are duplicated in this design; CloudFront providing CDN and AWS WAF providing a web application firewall
- **Operational Issues from Redundancy:** This redundancy has caused operational issues, impacting availability and reliability, with conflicting policies and functions between AWS CDN (CloudFront) and WAF
- **Decentralized Management:** Neither CloudFront nor AWS WAF are managed centrally by CloudEng nor CloudOps. They are managed by whichever team spins them up. This decentralization increases the likelihood of conflicting policies in CDN or WAF and creates confusion over who is responsible when issues arise
- **Compliance and Policy Violations:** Adding AWS WAF introduces additional ITGCs for SOC, SOX, HITRUST, and PCI certifications, leading to more audits, governance, and scrutiny. It violates WEX security policy by using a non-approved WAF and not following the WAF management policies
- **CloudFront as Unintended Web Server:** In some cases such as OTR, CloudFront is serving as both CDN and web server (reverse proxy), enabling SPA dynamic routing functionality beyond its intended CDN purpose

**Solution Options Evaluated:**

1. **RFC-459 Single-Page Application Behind Imperva (Selected):** Container-based SPA deployment with proper web server layer for dynamic routing functionality
2. **RFC-338 Secure Static Content Hosting (Alternative):** Static S3 + ALB + VPC Endpoint + Imperva architecture for pure static assets

### 3.2 Use Case Overview

```mermaid
graph TD
    classDef actor fill:#e1f5fe,stroke:#0288d1
    classDef system fill:#fff3e0,stroke:#ff9800
    
    A[End Users]:::actor -->|Access SPA Content| B[WEX Business Payments Portal]:::system
    C[Developers]:::actor -->|Deploy Container Images| B
    D[Security Team]:::actor -->|Monitor & Protect| B
    B -->|Secure Content Delivery| A
```

**Key Actors:**

- **End Users:** Customers accessing WEX Business Payments SPA portal
- **Developers:** Development teams deploying and managing containerized SPA
- **Security Team:** InfoSec team ensuring WAF protection and security compliance
- **Operations Team:** Managing container platform and load balancer performance

**Primary Scenarios:**

1. **RFC-459 Container SPA Deployment:** Containerized SPA deployment with dynamic content rendering capabilities through EKS/ECS + ALB + Imperva WAF
2. **Enterprise WAF Protection:** Centralized security management through Imperva WAF/CDN with container orchestration and auto-scaling

### 3.3 Solution Architecture

**Current State - Security Vulnerabilities:**

```mermaid
graph TD
    classDef external fill:#D4F1F9,stroke:#05AFF2
    classDef system fill:#FFE5B4,stroke:#FFA500
    classDef insecure fill:#FFE5E5,stroke:#FF0000
    classDef vulnerable fill:#FFD1DC,stroke:#FF6B6B
    
    subgraph "Non-Production (VULNERABLE)"
        A[Non-Prod Users] -->|HTTPS| B[yeti.qa.encompass-suite.com]:::insecure
        C[Stage Users] -->|HTTPS| D[yeti.stage.encompass-suite.com]:::insecure
        B -->|Direct to CloudFront| E[CloudFront CDN]:::vulnerable
        D -->|Direct to CloudFront| F[CloudFront CDN]:::vulnerable
        E -->|Origin Pull| G[S3 Non-Prod Content]:::system
        F -->|Origin Pull| H[S3 Stage Content]:::system
    end
    
    subgraph "Production (DUAL CDN ISSUES)"
        I[Prod Users] -->|HTTPS| J[wexbusinesspayments.com]
        J -->|All Traffic| K[Imperva WAF]:::system
        K -->|2 IP Addresses Only| L[CloudFront CDN]:::vulnerable
        L -->|Origin Pull| M[S3 Prod Content]:::system
    end
    
    Note1[❌ Non-prod: No Imperva WAF Protection]
    Note2[❌ Prod: Dual CDN creates attack vectors]
    Note3[❌ Limited to 2 IPs reduces CDN effectiveness]
```

**Target State - RFC-459 Container SPA Architecture (Selected):**

```mermaid
graph TD
    classDef external fill:#D4F1F9,stroke:#05AFF2
    classDef system fill:#FFE5B4,stroke:#FFA500
    classDef secure fill:#E8F8F5,stroke:#4CAF50
    classDef alb fill:#E3F2FD,stroke:#1976D2
    classDef container fill:#F3E5F5,stroke:#9C27B0
    
    subgraph "RFC-459 Container SPA Architecture"
        A[Non-Prod Users] -->|HTTPS| B[Imperva WAF]:::secure
        C[Stage Users] -->|HTTPS| D[Imperva WAF]:::secure
        E[Prod Users] -->|HTTPS| F[Imperva WAF]:::secure
        
        B -->|Origin Pull| G[ALB Non-Prod]:::alb
        D -->|Origin Pull| H[ALB Stage]:::alb
        F -->|Origin Pull| I[ALB Prod]:::alb
        
        G -->|Container Traffic| J[EKS/ECS Non-Prod]:::container
        H -->|Container Traffic| K[EKS/ECS Stage]:::container
        I -->|Container Traffic| L[EKS/ECS Prod]:::container
        
        J -->|Serves| M[Angular SPA + Nginx]:::system
        K -->|Serves| N[Angular SPA + Nginx]:::system
        L -->|Serves| O[Angular SPA + Nginx]:::system
    end
    
    Note1[✅ All environments behind Imperva WAF]
    Note2[✅ Containerized SPA deployment]
    Note3[✅ Dynamic content rendering capability]
    Note4[✅ Auto-scaling and high availability]
```

**Core Components:**

- **Imperva WAF:** Primary security layer providing DDoS protection, web application firewall, and global content distribution per RFC-459
- **AWS Application Load Balancer (ALB):** Internet-facing HTTPS load balancer handling traffic from Imperva to container instances
- **AWS EKS/ECS Fargate:** Container execution platform for running Angular SPA containers
- **Nginx Container Runtime Layer:** Serves production build of SPA and handles routing
- **Angular SPA:** Frontend application packaged in Docker containers
- **AWS Certificate Manager (ACM):** SSL/TLS certificate management for ALB HTTPS termination

### 3.4 Technology Stack & Patterns

**RFC-459 Selected Architecture:**

| Layer | Technologies | Purpose | Version |
|-------|--------------|---------|---------|
| Web Application Firewall | Imperva | WEX standard security layer | Latest |
| Load Balancer | AWS ALB | Traffic distribution | Latest |
| Container Execution | AWS EKS or ECS Fargate | Container runtime | Latest |
| Container Runtime Layer | Nginx | Serves production build of SPA | latest |
| Container Build Layer | Node.js | Build environment | ^20.9.0 |
| Frontend | Angular | SPA framework | ^17.3.0 |
| Infrastructure as Code | Terraform | IaC | Latest |
| CI/CD Pipeline | GitHub Actions | Deployment pipeline | Latest |

**Cost Comparison (Monthly Estimates):**

| Solution | Cost | Notes |
|----------|------|-------|
| Current: CloudFront + S3 + AWS WAF | $130.50 | Current problematic architecture |
| RFC-338: S3 + ALB + Imperva WAF | $75.32* | Alternative for static content |
| RFC-459: ECS Fargate + ALB + Imperva WAF | $85.42* | Selected for SPA requirements |

*Imperva costs covered under WEX enterprise license. Cost estimates based on AWS calculator for typical usage patterns (100GB/month data transfer, 10GB storage, 2 vCPU/4GB RAM containers). Actual costs vary by traffic volume and resource requirements.

### 3.5 Sequence & Data Flows

**RFC-459 Container SPA Delivery Flow (Selected):**

```mermaid
sequenceDiagram
    participant U as End User
    participant I as Imperva WAF
    participant A as AWS ALB
    participant C as EKS/ECS Container
    participant N as Nginx + Angular SPA
    participant D as DevOps Team
    participant R as Container Registry
    
    Note over U,R: RFC-459 Container SPA Delivery Flow
    
    U->>I: HTTPS Request for SPA Content
    I->>I: WAF Security Analysis & Filtering
    alt Content in Cache
        I-->>U: Cached SPA Response
    else Cache Miss
        I->>A: HTTPS Request to ALB Origin
        A->>A: SSL Termination & Load Balancing
        A->>C: Route to Container Instance
        C->>N: Process SPA Request
        N->>N: Angular App Processing
        N-->>C: SPA Content (HTML/JS/CSS)
        C-->>A: Container Response
        A-->>I: HTTPS Response
        I->>I: Content Caching
        I-->>U: SPA Content Response
    end
    
    Note over U,R: Container Deployment & Updates
    
    D->>R: Push New Container Image
    D->>C: Deploy New Container Version
    C->>C: Rolling Update with Health Checks
    D->>I: Cache Invalidation Request
    I->>I: Clear Cached Content
    I-->>D: Invalidation Confirmation
```

**RFC-459 Container SPA Data Flow (Selected):**

```mermaid
flowchart LR
    classDef source fill:#e1f5fe,stroke:#0288d1
    classDef process fill:#fff3e0,stroke:#ff9800
    classDef storage fill:#e8f5e9,stroke:#4caf50
    classDef security fill:#ffebee,stroke:#f44336
    classDef container fill:#f3e5f5,stroke:#9c27b0
    
    A[SPA Code Deploy]:::source --> B[Container Registry]:::storage
    B --> C[EKS/ECS Platform]:::container
    C --> D[Angular + Nginx Containers]:::process
    D --> E[ALB Load Balancer]:::process
    E --> F[Imperva WAF]:::security
    F --> G[Global Edge Locations]:::process
    G --> H[End Users]:::source
```

**Key Interactions:**

1. **User SPA Request:** End users request SPA content through HTTPS, processed by Imperva WAF for security filtering and global CDN delivery
2. **Container Origin Pull:** When content is not cached, Imperva pulls from ALB origin which terminates SSL and routes to containerized Angular SPA instances
3. **Container Management:** DevOps deploys new container images and invalidates Imperva cache through CDN APIs for content updates

### 3.6 Security Architecture

**Core Security Controls:**

| Control Type | Implementation | Purpose |
|--------------|----------------|---------|
| WAF Protection | Imperva WAF with DDoS protection and application layer filtering | Protect against web application attacks and volumetric attacks |
| SSL/TLS Encryption | End-to-end encryption from user through ALB with ACM certificate management | Ensure data protection in transit with proper SSL termination |
| Container Security | Security groups restricting container access to ALB traffic only | Prevent unauthorized access to container instances |
| Network Security | Imperva edge locations with ALB load balancing | Secure traffic distribution to container platform |
| Access Management | IAM roles for ALB and container platform access, least-privilege security groups | Secure service-to-service authentication within AWS infrastructure |

**Compliance Requirements:**

- RFC-459 Single-Page Application Behind Imperva - Implementation of WEX standard for secure SPA deployment
- SOC 2 Type II - Security controls for containerized SPA infrastructure
- Corporate Security Standards - WAF protection for all public-facing environments

### 3.7 Risk Analysis & Assumptions

**Detailed Risk Assessment:**

| Risk Category | Specific Risk | Technical Impact | Business Impact | Mitigation Strategy |
|---------------|---------------|------------------|-----------------|---------------------|
| **Infrastructure** | DNS cutover disruption | Service unavailability | Revenue impact, customer dissatisfaction | Progressive rollout (1%, 10%, 50%, 100%) with immediate rollback capability |
| **Technical** | Container deployment complexity | Application instability | Potential downtime during migration | Comprehensive staging validation, containerization testing, rollback procedures |
| **Performance** | Degradation during migration | Slower response times | User experience impact | Pre-migration load testing, performance baseline validation |
| **Integration** | Imperva configuration delays | Migration timeline extension | Project delay, resource allocation | Early staging environment setup, parallel configuration work |

**Critical Technical Assumptions:**

- Imperva infrastructure can handle current traffic volumes with equivalent or better performance for containerized SPA deployment
- Container platform (EKS/ECS) can be configured without impacting existing application functionality
- DNS TTL values can be reduced temporarily for faster cutover
- CloudFront potentially serves as both CDN and web server (reverse proxy) enabling SPA functionality (requires validation with WBP DevOps team)
- Network Security Architecture team availability for coordination

**Operational Dependencies:**

| Dependency Category | Specific Requirement | Owner | Timeline Impact | Contingency |
|---------------------|---------------------|-------|-----------------|-------------|
| **External Services** | Imperva account setup and domain onboarding | Imperva team | Blocks non-prod migration start | Early initiation, escalation path |
| **Internal Infrastructure** | Container platform (EKS/ECS) provisioning | CloudEng team | Required for RFC-459 implementation | Parallel setup during planning phase |
| **Network Management** | DNS management access and permissions | Network team | Required for cutover execution | Early verification and access request |
| **Application Validation** | CloudFront SPA functionality assessment | WBP DevOps team | Confirms implementation approach | Early technical discovery session |

### 3.8 Vector Impact Assessment

**Vector Impact Summary:**

| Vector | Current State | Target State | Key Improvements |
|--------|---------------|--------------|------------------|
| **Reliability** | Non-prod unprotected, Prod dual CDN complexity | 99.9%+ availability, unified container architecture | Container auto-scaling, improved disaster recovery |
| **Security** | Attack vectors via CloudFront bypass, unprotected non-prod | WAF protection all environments, RFC-459 compliance | Eliminated dual CDN vulnerabilities, comprehensive threat protection |
| **Innovation Velocity** | Complex dual CDN management, technical debt | Simplified container-based architecture | Standardized CI/CD patterns, container deployment automation |
| **AI Maturity** | No advanced capabilities | Standard WAF protection capabilities | Basic threat protection and filtering |
| **SaaS Maturity** | Mixed cloud-native adoption | Full cloud-native container solution | Optimized container orchestration, standard scaling |

**Reference Links:**

- [Reliability Vector](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155172602241/Reliability+Vector)
- [Security](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155173978186/Product+Security)
- [Product Innovation Velocity (PiV)](https://wexinc.atlassian.net/wiki/spaces/TISO/pages/154810155024/Tech+Transformation+KPIs)
- [AI Maturity](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155174338612/AI+Maturity+Vector)
- [SaaS Maturity](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154867007780/RFC-351+SaaS+Maturity+Vector)

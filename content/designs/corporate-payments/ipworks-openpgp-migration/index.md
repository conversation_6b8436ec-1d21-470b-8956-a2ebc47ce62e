<!-- Parent: Designs -->
<!-- Parent: Corporate Payments -->
<!-- Title: RFC-499 IPWorks OpenPGP Migration for RMS Platform -->

<!-- Include: macros.md -->
# IPWorks OpenPGP Migration for RMS Platform

:isarb:

```text
Author: <PERSON><PERSON><PERSON>
Publish Date: 2025-05-06
Category: Designs
Subtype: Corporate Payments
Ratified By: iSARB (2025-04-22)
```

## Executive Summary

The RMS platform requires a PGP key management tool to add and remove key pairs as required by banking partners. The current solution (SecureBlackBox) doesn't support the management of PGP keys required by GPG 2.4, which necessitates storing keys in a folder structure rather than a single file. This design proposes migrating to IPWorks OpenPGP to overcome this limitation and ensure continued secure file exchange with banking partners.

This solution drives improvements towards the following Tech Transformation Vectors:

| Vector | Impact |
|--------|--------|
| ☑️ Product Security | Ensures continued secure encryption for financial data exchange with banking partners |
| 🔲 Reliability | [Brief impact description if selected] |
| 🔲 Product Innovation Velocity (PiV) | [Brief impact description if selected] |
| 🔲 SaaS Maturity | [Brief impact description if selected] |
| 🔲 AI/ML Maturity | [Brief impact description if selected] |

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

:toc:

## 💼 Change Context

* **Organization:** Corporate Payments
* **Purpose:** Enable RMS platform to continue managing PGP keys for secure file exchange with banking partners
* **Users:** RMS platform developers and operations team (<15 users)
* **Integration Points:** RMS application, banking partner systems
* **Third-Party Software:** IPWorks OpenPGP (Approved Usage)
* **Impacted Products:**
  * RMS Platform
* **Design Review Qualities:**
  * ☑️ 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * 🔲 🟡 New Pattern Adoption
  * 🔲 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Implementation | 2025-Q2 | Approval from iSARB |

## Evaluation Criteria

### 🛡️ Product Security

#### Risk Assessment

The current status of risk assessments for this vendor and product:

* 🔲 Not Requested
* ☑️ In Progress
* 🔲 Completed without concerns
* 🔲 Completed with concerns: `Explain`

> [!note]
> A risk assessment ticket has been created: TAM-1959

#### Data Compliance

The following types of Class 1 Protected Data is transmitted, processed, or stored in this solution:

* 🔲 PCI
* 🔲 PII
* 🔲 PHI

Data Protection Controls:

* ☑️ Data is protected at rest, in transit, and in use per WEX IT policy
* 🔲 HTTPS using TLS 1.2 or higher
* 🔲 Message encryption
* 🔲 Database Connections using TLS 1.2 or higher
* 🔲 Row-level data encryption
* 🔲 Transparent data encryption
* 🔲 De-identification (tokenization, hashing, masking, redaction)
* 🔲 Digital Signatures
* ☑️ Access control (permission, role, policy)

> [!important]
> The application itself does not access sensitive data. It manages the keys that the RMS application uses for encrypting and decrypting files sent to and from banking partners.

#### Data Ingress / Egress

This solution uses the following data flow patterns:

* 🔲 Customer-to-WEX traffic
* 🔲 WEX-to-Customer traffic
* 🔲 Vendor-to-WEX traffic
* 🔲 WEX-to-Vendor traffic
* ☑️ Internal system-to-system traffic

> [!note]
> No data is sent to the vendor. The tool is installed locally on the RMS platform virtual machine.

#### Authentication & Authorization

WEX Employee Authentication Components:

* 🔲 IdentityIQ SAML
* 🔲 Okta Workforce Identity
* ☑️ Active Directory: `WEXPRODR`
* 🔲 Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* 🔲 Other: `Explain`

WEX Employee automated provisioning & deprovisioning:

* 🔲 Yes
* ☑️ No

WEX Customer Authentication Components:

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory: `DOMAIN FQDN`
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* ☑️ WEX customers do not authenticate
* 🔲 Other: `Explain`

WEX Service Principal Types:

* 🔲 Amazon Managed Identity
* 🔲 Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA: `DOMAIN FQDN`
* ☑️ Active Directory User Service Account: `DOMAIN FQDN`
* 🔲 Local user
* 🔲 Digital certificates
* 🔲 Local administrator
* 🔲 Other: `Explain`

All employee, customer, and service principal credentials require periodic rotation:

* ☑️ Yes
* 🔲 No

Service principal credentials are automatically rotated:

* 🔲 Yes
* ☑️ No

#### Network Traffic Patterns

**Inbound Connectivity:**

* 🔲 Protected by Imperva WAF
* 🔲 Direct inbound (not WAF-protected)
* 🔲 Customer/vendor traffic
* 🔲 WEX business user traffic  
* ☑️ WEX support user traffic

**Outbound Connectivity:**

* 🔲 To customer/vendor systems
* 🔲 From WEX network (support)
* 🔲 Class 1 data transmission
* 🔲 Class 2 data transmission

**Security Controls:**

* 🔲 Bot protection enabled
* 🔲 Automated certificate renewal
* 🔲 TLS 1.2+ enforcement
* ☑️ Network traffic encryption

### 🤖 AI/ML

Indicate if this design includes any new AI/ML components:

* 🔲 Yes
* ☑️ No

Indicate if all AI/ML components have been reviewed for AI/ML compliance:

* 🔲 Yes
* 🔲 No

### 🔄 Reliability

#### High Availability Controls

* 🔲 Availability Zone Redundancy
* 🔲 Regional Failover
* 🔲 Automated Failover
* 🔲 Multi-Region Active-Active
* 🔲 Load Balancing
* 🔲 Content Delivery Network
* ☑️ Monitoring & Alerting
* 🔲 Other: `Explain`

#### Outage Impact Assessment

Business Impact:

* 🔲 Critical customer business impact
* ☑️ Partial loss of non-critical functionality
* 🔲 Customer recovery assistance required
* 🔲 Multiple lines of business affected
* 🔲 Multiple products in LOB affected
* 🔲 Multiple customers affected
* 🔲 External SLA impact
* 🔲 Internal SLA impact
* ☑️ Engineering support required

Recovery Actions:

* 🔲 Infrastructure deployment/patching
* ☑️ Application deployment
* 🔲 Configuration updates (App/LB/DNS/Firewall/Gateway)
* 🔲 Data recovery operations
* 🔲 Other: `Explain`

> [!note]
> RMS performs financial and treasury operations; it is considered a tier 2 application. There are no availability requirements for this solution beyond those inherent in the operating environment.

### ☁️ SaaS

#### Key Components

| Component | Description | Technology | Purpose |
|-----------|-------------|------------|---------|
| IPWorks OpenPGP | PGP key management tool | nSoftware | Manage PGP keys for secure file exchange with banking partners |
| RMS Application | Treasury management application | .NET | Process financial files for banking partners |

#### Design Impact Scope

* ☑️ Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* ☑️ Vendor & Product Risk Assessment completed

#### Logging & APM

* 🔲 Splunk Forwarding
* 🔲 DataDog Forwarding
* 🔲 Security Event Logging
* 🔲 Synthetic Testing
* 🔲 Log Archival
* 🔲 Log Purging
* 🔲 Class 1 Data Logging Controls

#### Standards Adoption

Deviations from standard tools, services, and reference architectures are:

* 🔲 **WEX Fabric:** `Justification`
* 🔲 **SCM (GitHub Enterprise):** `Justification`
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `Justification`
* 🔲 **DFS (Panzura):** `Justification`
* 🔲 **Outbound Email (PowerMTA/Sendgrid):** `Justification`
* 🔲 **SFTP (GoAnywhere):** `Justification`
* 🔲 **WAF (Imperva):** `Justification`
* 🔲 **CDN (Imperva):** `Justification`
* 🔲 **Logging (Splunk):** `Justification`
* 🔲 **APM (DataDog):** `Justification`
* 🔲 **Auth Gateway:** `Justification`
* 🔲 **AI Gateway:** `Justification`
* 🔲 **Eventing Platform:** `Justification`
* 🔲 **WEX Data Platform:** `Justification`
* 🔲 **Tokenizer:** `Justification`
* 🔲 **Notification Hub:** `Justification`

### Relevant Context

#### Secure Coding

**Technology Stack:**

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | `.NET` |
| Database Technologies | `N/A` |
| Storage Solutions | `Local file system` |
| Operating Systems | `Windows` |
| Client Platforms | `N/A` |

**Security Controls:**

Code quality scanning services that are active for all development repositories:

* 🔲 Snyk
* 🔲 Cycode
* 🔲 Mend Renovate
* 🔲 GitHub Copilot
* 🔲 Dependabot
* 🔲 Other: `Service Name`

#### Procurement Details

Third-Party Software Type:

* 🔲 SaaS
* 🔲 IaaS
* 🔲 AWS PaaS
* 🔲 Azure PaaS
* ☑️ Code Dependency Package
* 🔲 Desktop
* 🔲 Mobile App
* 🔲 Browser - Web Browser

Additional Considerations:

* 🔲 Software installation & configuration is automated
* 🔲 Cost exceeds $25,000
* 🔲 Solution is part of Target State Architecture
* 🔲 Temporary solution planned for replacement

Alternatives Considered:

* **SecureBlackBox:** Current solution, but does not support GPG 2.4 key management requirements
* **Custom Development:** Would require significant refactoring of the RMS application

## Diagrams

### System Context Diagram

```mermaid
graph TB
    User["RMS Operations Team"]
    App["RMS Application"]
    IPWorks["IPWorks OpenPGP"]
    Keys["PGP Key Storage"]
    Banks["Banking Partners"]
    
    User -->|"Manages Keys"| IPWorks
    IPWorks -->|"Stores/Retrieves"| Keys
    App -->|"Uses"| IPWorks
    App <-->|"Encrypted Files"| Banks
    
    classDef secure fill:#e6ffe6,stroke:#006600
    classDef external fill:#f9f9f9,stroke:#666666
    class IPWorks,Keys secure
    class Banks external
```

## Risk Analysis & Dependencies

### Key Risks

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Inability to manage PGP keys | High | High | Implement IPWorks OpenPGP solution |
| Vendor support issues | Medium | Low | Vendor is already used in other parts of WEX |
| Integration with RMS application | Medium | Medium | Solution has been successfully piloted |

### Critical Assumptions

* **Assumption 1:** The key rotation requirements from banking partners will continue to use GPG 2.4 format
* **Assumption 2:** The RMS application can be adapted to work with the IPWorks OpenPGP API
* **Assumption 3:** Installation on production servers can be completed by International Operations team

### Known Issues

* **Issue 1:** SecureBlackBox is incompatible with GPG 2.4 key storage structure
* **Issue 2:** RMS application currently cannot traverse folder structure to find keys

### System Dependencies

| Dependency | Type | Criticality | Contact Team |
|------------|------|-------------|--------------|
| RMS Application | System | High | Optal RMS Application Engineering |
| Banking Partner Systems | External | High | Banking Partners |
| Active Directory | Authentication | Medium | IT Infrastructure |

## Reference Links

* **Documentation:**
  * [iSARB Presentation](https://docs.google.com/presentation/d/1pfm1R2sBHQiQrbvUJDgXVVM7Rt4YI4ez)

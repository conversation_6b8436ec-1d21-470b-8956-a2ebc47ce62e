<!-- Parent: Designs -->
<!-- Parent: Corporate Payments -->
<!-- Title: RFC-513 VOCI Replacement Selection for AutoIVR -->

<!-- Include: macros.md -->
# Candidate Architecture Design: VOCI Replacement for AutoIVR

:draft:

```text
Author: <PERSON><PERSON>wain
Title: VOCI Replacement Selection for AutoIVR
Publish Date: 2025-07-16
Category: Designs
Subtype: Corporate Payments
```

> **How to Use This Template**  
> This CAD template is designed to guide you through documenting your solution architecture. Sections should be completed progressively as you move through the design process. Each section contains structured options with checkboxes for common patterns, plus free-form areas for detailed explanations.

## Table of Contents

:toc:

<!-- The :toc: macro will automatically generate a table of contents with proper case-sensitive links -->

## Document Completion Guide

| Section | When to Complete | Primary Audience | Focus Areas |
|---------|------------------|------------------|-------------|
| Business Context | Early design phase | Product owners, stakeholders | Business drivers, user needs |
| Solution Architecture | After requirements | Development team, architects | Technical approach, components |
| Risks & Dependencies | Throughout design process | Project managers, team | Mitigation, dependencies |
| Vector Impact | After architecture definition | Leadership, architects | Measurable improvements |

---

## 1.0 Document Control

**Project Information:**

- **Line of Business:** Corporate Payments
- **Product Name:** AutoIVR
- **Requirements:** VOCI Replacement for Bill Pay AutoIVR system
- **Related Docs:** [Link to Reference Architectures, Related Designs]

| Stakeholder | Role | Status | Date | R | A | C | I |
| ----------- | ---- | ------ | ---- | - | - | - | - |
| [Name] | Product Owner | | | | | | ☑️ |
| [Name] | Security Architect | | | | | ☑️ | |
| [Name] | Solution Architect | | | ☑️ | | | |
| [Name] | Solution Delivery Lead | | | | ☑️ | | |
| [Name] | Development Lead | | | ☑️ | | | |

> **Executive Summary**
>
> **Background:**  
> Voci, the transcription engine used in WEX Bill Pay's AutoIVR application, will reach end-of-support in February 2027.
>
> **Key Findings:**
>
> After comprehensive analysis of 19 potential replacements, four solutions stand out:
>
> - **Speechmatics** (Optimal Commercial Solution)
>   - Industry-leading performance (<200ms latency)
>   - Highest accuracy (8-12% WER in financial contexts)
>   - Superior VAD capabilities essential for AutoIVR functionality
>
> - **Deepgram** (Strong Secondary Commercial Option)
>   - Comparable performance metrics
>   - US-based support
>   - Extensive customization options
>
> - **Amazon Transcribe** (Reliable Cloud Provider Option)
>   - Low latency (200-400ms)
>   - Strong accuracy (10-15% WER)
>   - Robust AWS infrastructure and support
>
> - **RealtimeSTT** (Cost-Effective Open-Source Alternative)
>   - Significant cost savings
>   - Requires dedicated infrastructure
>   - Needs internal technical support resources
>
> **Implementation Recommendations:**
>
> 1. Create a vendor-agnostic adapter layer to prevent future vendor lock-in
> 2. Conduct parallel POC testing of top candidates
> 3. Begin migration by Q1 2026 to ensure thorough validation before deadline
>
> The optimal choice depends on specific balance of performance requirements, cost sensitivity, and internal support capabilities.

---

## 2.0 Business Context & Requirements

### 2.1 Business Objective

> **AI Prompt**: Based on the problem statement, generate a concise description of the business objective, key success criteria, and solution type.

**Problem Statement:**
Voci, the transcription engine used in WEX Bill Pay's AutoIVR application for processing customer voice commands, will no longer be supported starting February 2027. A replacement solution must be identified and implemented to ensure business continuity and potentially improve speech recognition capabilities. The effort is focused on selecting a replacement vendor through a structured evaluation process using defined technical and business criteria.

**Solution Type:** (Select all that apply)

- 🔲 New product/capability
- 🔲 Enhancement to existing product
- ☑️ Technical debt reduction
- 🔲 Regulatory/compliance requirement
- ☑️ Cost/performance optimization
- 🔲 Security improvement
- ☑️ Availability/resilience improvement

**Business Drivers:** (Select all that apply)

- 🔲 Revenue growth
- ☑️ Cost reduction
- ☑️ Risk mitigation
- 🔲 Regulatory compliance
- ☑️ Customer satisfaction
- ☑️ Operational efficiency
- 🔲 Time to market
- 🔲 Other: _______________

**Success Criteria:**

- Successful implementation of a replacement transcription engine before February 2027
- Equal or improved speech recognition accuracy compared to current Voci implementation
- Minimal disruption to existing AutoIVR system operations during transition
- Selection of a stable vendor with long-term viability and adequate support

### 2.2 Use Case Overview

> **AI Prompt**: Create a diagram showing the key actors and their interactions with the system, followed by descriptions of primary scenarios.

**System Architecture Diagram:**

For the complete system architecture and component interactions, please refer to **[Section 3.1 System Context](#31-system-context)** which provides the authoritative diagram showing all system components, data flows, and integration points.

**Key Actors:**

- **AutoIVR System:** WEX's automated interactive voice response system that processes customer requests and orchestrates call flows
- **Payment Systems:** Backend systems that prioritize and queue payments for processing through the AutoIVR system
- **Speech Recognition Engine:** Component that converts spoken voice to text for processing (currently Voci, being replaced)
- **Telephony Infrastructure:** Components that handle call routing, recording, and telephony services (Chime, Session Border Controller)

**User Types:** (Select all that apply)

- ☑️ Customer/End User
- 🔲 Internal Business User
- 🔲 Administrator
- 🔲 Partner/Third-party
- ☑️ System/Service
- 🔲 Other: _______________

**Primary Scenarios:**

1. **Payment Processing Initiation:**
   - Payments and caller configuration reside in the Bill Pay Databases.
   - The Payment Prioritizer consumes payments from the Bill Pay Databases, prioritizing them.
   - The Payment Prioritizer fills the Prioritized Payments Queue with the highest priority payments.
   - AutoIVR consumes from the Prioritized Payments Queue and begins payment processing.

2. **Call Processing and Speech Recognition:**
   - AutoIVR orchestrates call setup through the telephony infrastructure (Chime, Session Border Controller).
   - During call interactions, customer speech is captured and sent to the speech recognition engine (currently Voci).
   - The speech recognition engine converts speech to text and returns transcribed content to AutoIVR.
   - AutoIVR processes the transcribed text to determine customer intent and execute appropriate actions.

3. **MagIVR Integration (Configuration Dependent):**
   - If the call is configured to use MagIVR: AutoIVR sends the transcript for each utterance to the MagIVR API.
   - The MagIVR API uses MagIVR Models to determine the action to take on the call.
   - The MagIVR API checks the MagIVR Database for cached responses and uses them if available.
   - If the call is not configured to use MagIVR: There is a Custom Call Flow for each biller that determines the next action to take on the call.

### 2.3 Process Flow

> **AI Prompt**: Generate a sequence diagram showing the key process steps between actors and systems, with annotations for important decision points.

For the complete process flow and sequence interactions, please refer to **[Section 3.4 Sequence Flows](#34-sequence-flows)** which provides the authoritative sequence diagram showing all system interactions, timing, and process flows aligned with the System Context architecture.

**Process Type:** (Select one)

- ☑️ Synchronous process
- 🔲 Asynchronous process
- 🔲 Batch process
- 🔲 Hybrid process

**Process Complexity:**

- 🔲 Simple (Linear flow)
- ☑️ Moderate (Some decision points)
- 🔲 Complex (Multiple paths and decision points)

**Key Process Steps:**

1. **Pre-Call Payment Setup:** Payment Prioritizer consumes payments from Bill Pay Databases and fills the Prioritized Payments Queue
2. **Automated Call Initiation:** AutoIVR initiates calls to customers for payment processing (no human customer initiation)
3. **Speech Recognition Setup:** AutoIVR establishes transcription session with Voci and initiates call recording
4. **Customer Interaction:** System processes customer voice responses through speech-to-text conversion
5. **Intent Processing:** AutoIVR determines actions using either MagIVR API or custom call flows
6. **Post-Call Payment Processing:** Payment processing requests occur after call completion, not during active calls

### 2.4 User Experience Design

> **AI Prompt**: Describe the key user interface elements and user experience considerations for the solution.

[Insert wireframe image or link to design files]

**Interface Types:** (Select all that apply)

- 🔲 Web Application
- 🔲 Mobile Application
- ☑️ API/Service
- 🔲 Command Line
- 🔲 Desktop Application
- 🔲 Chatbot/Conversational
- 🔲 Other: _______________

**UX Priorities:** (Select top 3)

- ☑️ Ease of use
- ☑️ Performance/Speed
- 🔲 Accessibility
- 🔲 Consistency
- 🔲 Internationalization
- 🔲 Mobile-first design
- 🔲 Data visualization
- ☑️ Error handling/Recovery

**Key UX Considerations:**

- Maintain or improve speech recognition accuracy with the new solution
- Minimize latency in speech recognition processing to maintain natural conversation flow
- Implement robust error handling for unclear or ambiguous speech input
- Support for various customer accents and speech patterns

---

## 3.0 Solution Architecture

### 3.1 System Context

> **AI Prompt**: Create a system context diagram showing the core system, external systems, and key interfaces between them.

```mermaid
graph TD
  %% Styles
  classDef ots fill:#E6E6FA,stroke:#9370DB
  classDef system fill:#FFF3E0,stroke:#FF9800
  classDef db fill:#E8F8F5,stroke:#117A65
  classDef queue fill:#FCE4EC,stroke:#E91E63
  classDef api fill:#E3F2FD,stroke:#1976D2
  classDef external fill:#D4F1F9,stroke:#05AFF2

  %% Payment Processing
  BillPayDB[(Bill Pay Databases)]:::db -->|Payments & Config| PaymentPrioritizer[Payment Prioritizer]:::system
  PaymentPrioritizer -->|Prioritized Payments| PrioritizedQueue[Prioritized Payments Queue]:::queue
  PrioritizedQueue -->|Payments to Process| AutoIVR[AutoIVR]:::system
  %% Call Setup
  AutoIVR -->|Initiates Call| Chime[Chime]:::ots
  Chime -->|Get Caller ID| RandomizePhone[Randomize Phone Number]:::system
  RandomizePhone -->|Replace Caller ID| Chime
  Chime -->|Connects Call| AutoIVR
  Chime -->|Custom Call Flow| BillerIVR[Biller IVR Systems]:::external
  AutoIVR -->|Session Border| SBC[Session Border Controller]:::ots
  %% Transcription & Recording
  AutoIVR -->|Initiates Transcription| Voci[Voci Transcription]:::ots
  AutoIVR -->|Initiates Recording| CallRec[Call Recording]:::ots

  %% MagIVR Integration
  AutoIVR -->|Utterance Transcript| MagIVR_API[MagIVR API]:::api
  MagIVR_API -->|Check Cache| MagIVR_DB[(MagIVR Database)]:::db
  MagIVR_API -->|Determine Action| MagIVR_Models[MagIVR Models]:::system
  MagIVR_Models -->|Model Data| MagIVR_DB
  MagIVR_API -->|Action Result| AutoIVR

  %% Biller IVR
  Chime -->|Custom Call Flow| BillerIVR[Biller IVR Systems]:::external

  %% Group OTS
  subgraph OTS_Components[Off-the-Shelf Components]
    Chime
    SBC
    Voci
    CallRec
  end
  style OTS_Components fill:#E6E6FA,stroke:#9370DB,stroke-dasharray: 5 5
```

**System Scope:** (Select one)

- 🔲 Standalone System
- ☑️ Subsystem of Larger Product
- 🔲 Integration Framework
- 🔲 Platform Service
- 🔲 Microservice Ecosystem

**Deployment Environment:** (Select all that apply)

- ☑️ Public Cloud - [GCP/AWS]
- 🔲 Private Cloud
- 🔲 Hybrid Cloud
- 🔲 On-premises
- 🔲 Edge Computing
- 🔲 Multi-cloud

**Component Categorization:**

The AutoIVR ecosystem consists of a mix of off-the-shelf (OTS) components and custom-built or internally configured components:

| Component Type | Components | Description |
|----------------|------------|-------------|
| **Off-the-Shelf (OTS)** | Voci Transcription, Call Recording, Session Border Controller, Chime | Commercial products integrated into the AutoIVR ecosystem; Voci is the speech recognition component being replaced |
| **Custom/Internal** | AutoIVR, Bill Pay Databases (Oracle implementation), Payment Prioritizer, Prioritized Payments Queue, MagIVR API, Randomize Phone Number | Components developed or specifically configured for WEX's implementation |
| **External** | Biller IVR Systems | External systems that integrate with the AutoIVR ecosystem |

**Core Components:**

- **AutoIVR:** Central orchestrator for call and payment flows.
- **Bill Pay Databases (Oracle):** Stores payments and caller configuration.
- **Payment Prioritizer:** Consumes and prioritizes payments from Bill Pay DB.
- **Prioritized Payments Queue:** Holds prioritized payments for AutoIVR.
- **Voci Transcription (OTS):** Transcribes call audio for AutoIVR.
- **Call Recording (OTS):** Records call audio for compliance.
- **MagIVR API:** Receives utterances from AutoIVR, checks cache, and determines actions.
- **MagIVR Models:** Used by MagIVR API to determine call actions.
- **MagIVR Database:** Stores cached responses and model data for MagIVR.
- **Session Border Controller (OTS):** Handles call routing and security.
- **Chime (OTS):** Telephony platform for call setup.
- **Randomize Phone Number:** Provides randomized caller ID for Chime.
- **Biller IVR Systems:** External biller-owned IVR systems, reached via Chime.

**Key Integration Points:**

- Telephony to AutoIVR: Customer voice input is received through the telephony system
- AutoIVR to Speech Engine: Audio segments are sent for transcription and text is returned
- AutoIVR to Backend: Customer requests are processed through integration with account systems

### 3.2 Architecture Patterns

> **AI Prompt**: Based on the system requirements, recommend appropriate architecture patterns and technology stack choices with rationale.

**Selected Patterns:** (Select all that apply)

- 🔲 Microservices Architecture
- 🔲 Event-Driven Architecture
- 🔲 Domain-Driven Design
- ☑️ API Gateway Pattern
- 🔲 CQRS
- 🔲 Serverless Architecture
- 🔲 Service Mesh
- ☑️ Hexagonal/Ports and Adapters
- ☑️ Layered Architecture
- 🔲 Saga Pattern
- ☑️ Circuit Breaker Pattern
- 🔲 Bulkhead Pattern
- 🔲 Strangler Pattern
- 🔲 Other: _________________

**Technology Categories:** (Select all that apply)

- 🔲 Containerization (Docker, etc.)
- 🔲 Container Orchestration (Kubernetes, etc.)
- 🔲 Serverless Computing
- ☑️ API Management
- 🔲 Message Queue/Streaming
- 🔲 Caching
- 🔲 Database (Relational)
- 🔲 Database (NoSQL)
- 🔲 Database (Graph)
- 🔲 Search Engine
- 🔲 Identity & Access Management
- 🔲 Content Delivery Network
- ☑️ Machine Learning/AI
- 🔲 Other: _________________

**Technology Stack:**

| Layer | Technologies | Rationale |
|-------|--------------|-----------|
| Speech Recognition | Microsoft Azure Speech Service, Google Speech-to-Text, Amazon Transcribe | Cloud-based speech services provide high accuracy, scalability, and regular updates to speech models. Vendor evaluation focuses on VAD capability, transcription speed/accuracy, and API compatibility |
| Integration | REST APIs, gRPC | Modern, standardized communication protocols for service integration with focus on compatibility with existing HTTP-based integration patterns |
| Telephony | Existing telephony infrastructure | Maintain compatibility with current phone systems |
| Backend | Existing Bill Pay systems | Ensure seamless integration with established backend services |

**Reference Architectures:**
[Links to relevant WEX reference architectures]

### 3.3 Non-Functional Requirements

> **AI Prompt**: Based on the business objectives, define key non-functional requirements for reliability, scalability, performance, and monitoring with implementation approaches.

**Reliability Requirements:** (Select all that apply)

- ☑️ High Availability (99.9%+)
- ☑️ Disaster Recovery
- ☑️ Fault Tolerance
- ☑️ Data Backup
- ☑️ Graceful Degradation
- ☑️ Low RTO (< 4 hours)
- 🔲 Low RPO (< 1 hour)

**Scalability Requirements:** (Select all that apply)

- ☑️ Horizontal Scaling
- 🔲 Vertical Scaling
- ☑️ Auto-scaling
- ☑️ Load Balancing
- 🔲 Database Sharding
- 🔲 Caching
- 🔲 Connection Pooling

**Performance Requirements:** (Select all that apply)

- ☑️ Response Time (< 3 seconds)
- ☑️ Throughput (Transactions/sec)
- 🔲 Resource Utilization
- ☑️ Concurrent Users
- ☑️ Request Rate
- 🔲 Database Query Performance
- 🔲 Network Latency

**Monitoring Requirements:** (Select all that apply)

- ☑️ Health Monitoring
- ☑️ Performance Monitoring
- ☑️ Log Management
- ☑️ Error Tracking
- 🔲 User Activity Monitoring
- 🔲 Security Monitoring
- ☑️ Business KPI Monitoring

| Category | Requirements | Implementation Approach |
|----------|--------------|-------------------------|
| **Reliability** | • Availability: 99.9%+ • RTO: < 4 hours • Fault tolerance | Implement redundancy, automatic failover, and robust error handling |
| **Scalability** | • Support for peak call volumes • Variable load handling | Cloud-based elastic scaling based on demand patterns |
| **Performance** | • Response time: < 2 seconds for transcription • Recognition accuracy: > 95% | Select provider with lowest latency and highest accuracy in testing |
| **Monitoring** | • Transcription accuracy metrics • Response time tracking • Error rates | Implement comprehensive logging and monitoring dashboards |

### 3.4 Sequence Flows

> **AI Prompt**: Create a sequence diagram showing the detailed interactions between system components for the primary use case.

```mermaid
sequenceDiagram
    participant Customer
    participant SBC as Session Border Controller
    participant Chime
    participant RandomPhone as Randomize Phone Number
    participant AutoIVR
    participant Voci as Voci Transcription
    participant CallRec as Call Recording
    participant MagIVR as MagIVR API
    participant BillPayDB as Bill Pay Databases
    participant PayPrioritizer as Payment Prioritizer
    participant PayQueue as Prioritized Payments Queue
    
    %% Background payment processing (happens continuously)
    BillPayDB->>PayPrioritizer: Send payment data
    PayPrioritizer->>PayPrioritizer: Prioritize payments
    PayPrioritizer->>PayQueue: Fill queue with prioritized payments
    
    %% Call setup initiated by AutoIVR (not customer)
    AutoIVR->>Chime: Initiate call
    Chime->>RandomPhone: Request caller ID
    RandomPhone->>Chime: Return randomized phone number
    Chime->>SBC: Route call with randomized ID
    SBC->>Customer: Place call
    Customer->>SBC: Answer call
    SBC->>Chime: Connect call
    Chime->>AutoIVR: Call connected
    
    %% Transcription and recording setup
    AutoIVR->>Voci: Initiate transcription session
    AutoIVR->>CallRec: Initiate call recording
    
    %% Automated customer interaction
    AutoIVR->>Customer: Play payment notification/prompts
    Customer->>SBC: Speak response (if any)
    SBC->>Chime: Forward audio
    Chime->>AutoIVR: Send audio
    
    %% Speech processing (when customer responds)
    AutoIVR->>Voci: Send audio segment
    Voci->>Voci: Process speech to text
    Voci->>AutoIVR: Return transcribed text
    
    %% Intent determination
    alt Call configured to use MagIVR
        AutoIVR->>MagIVR: Send transcript utterance
        MagIVR->>MagIVR: Process using custom flow logic
        MagIVR->>AutoIVR: Return action to take
    else Custom call flow for biller
        AutoIVR->>AutoIVR: Process using biller-specific flow logic
    end
    
    %% Data retrieval for call context
    AutoIVR->>BillPayDB: Request account information
    BillPayDB->>AutoIVR: Return account data
    
    %% Response to customer
    AutoIVR->>Customer: Play final prompts/confirmation
    
    %% Call completion
    AutoIVR->>Chime: End call
    Chime->>SBC: Disconnect
    SBC->>Customer: Call ends
    
    %% Post-call payment processing (ONLY happens after call completion)
    Note over AutoIVR,PayQueue: Request next payment ONLY occurs after call ends
    AutoIVR->>PayQueue: Request next payment to process
    PayQueue->>AutoIVR: Return prioritized payment
```

**Interaction Types:** (Select all that apply)

- ☑️ Synchronous Request/Response
- 🔲 Asynchronous Messaging
- 🔲 Event Publishing/Subscription
- 🔲 Batch Processing
- 🔲 Stream Processing
- 🔲 Polling/Long Polling
- 🔲 WebSockets/Real-time
- 🔲 Other: _______________

**Key Interactions:**

1. **Pre-Call Setup:** Payment prioritization occurs before calls, with AutoIVR consuming payments from the queue to initiate calls
2. **Automated Call Initiation:** AutoIVR initiates calls to customers (not customer-initiated calls)
3. **Speech Recognition:** Customer voice responses are captured and sent to the Voci Transcription (OTS) engine when customer interaction is required
4. **Intent Processing:** Transcribed text is processed to determine customer intent, either through MagIVR API or custom call flow logic
5. **Call Completion:** Calls end before payment processing occurs, maintaining separation between call handling and payment execution

### 3.5 Data Flow

> **AI Prompt**: Create a data flow diagram showing how data moves through the system, with descriptions of key data entities and transformations.

```mermaid
flowchart LR
  classDef ots fill:#E6E6FA,stroke:#9370DB
  classDef system fill:#FFF3E0,stroke:#FF9800
  classDef db fill:#E8F8F5,stroke:#117A65
  classDef queue fill:#FCE4EC,stroke:#E91E63
  classDef api fill:#E3F2FD,stroke:#1976D2
  classDef external fill:#D4F1F9,stroke:#05AFF2

  BillPayDB[(Bill Pay Databases)]:::db -->|Payments & Config| PaymentPrioritizer[Payment Prioritizer]:::system
  PaymentPrioritizer -->|Prioritized Payments| PrioritizedQueue[Prioritized Payments Queue]:::queue
  PrioritizedQueue -->|Payments to Process| AutoIVR[AutoIVR]:::system
  
  AutoIVR -->|Initiates Call| Chime[Chime]:::ots
  Chime -->|Get Caller ID| RandomizePhone[Randomize Phone Number]:::system
  RandomizePhone -->|Replace Caller ID| Chime
  Chime -->|Connects Call| AutoIVR
  Chime -->|Custom Call Flow| BillerIVR[Biller IVR Systems]:::external
  
  AutoIVR -->|Session Border| SBC[Session Border Controller]:::ots
  AutoIVR -->|Initiates Transcription| Voci[Voci Transcription]:::ots
  AutoIVR -->|Initiates Recording| CallRec[Call Recording]:::ots
  
  AutoIVR -->|Utterance Transcript| MagIVR_API[MagIVR API]:::api
  MagIVR_API -->|Check Cache| MagIVR_DB[(MagIVR Database)]:::db
  
  MagIVR_API -->|Determine Action| MagIVR_Models[MagIVR Models]:::system
  MagIVR_Models -->|Model Data| MagIVR_DB
  MagIVR_API -->|Action Result| AutoIVR

  subgraph OTS_Components[Off-the-Shelf Components]
    Chime
    SBC
    Voci
    CallRec
  end
  style OTS_Components fill:#E6E6FA,stroke:#9370DB,stroke-dasharray: 5 5
```

**Data Storage Types:** (Select all that apply)

- ☑️ Relational Database
- 🔲 Document Database
- 🔲 Key-Value Store
- 🔲 Graph Database
- 🔲 Time Series Database
- 🔲 Search Engine
- 🔲 Cache
- 🔲 Data Lake
- 🔲 Data Warehouse
- ☑️ File Storage
- 🔲 Object Storage
- 🔲 Other: _______________

**Data Processing Types:** (Select all that apply)

- 🔲 Stream Processing
- 🔲 Batch Processing
- 🔲 ETL/ELT Pipelines
- 🔲 Event Sourcing
- 🔲 CQRS
- 🔲 Real-time Analytics
- ☑️ Business Intelligence
- ☑️ Machine Learning
- 🔲 Other: _______________

**Key Data Flows:**

- Payments and caller configuration flow from Bill Pay Databases to Payment Prioritizer, then to Prioritized Payments Queue, and are consumed by AutoIVR.
- Call setup and caller ID randomization flow from AutoIVR to Chime, to Randomize Phone Number, and back to Chime.
- Transcription and recording are initiated by AutoIVR.
- MagIVR API receives utterances, checks cache in MagIVR Database, and uses MagIVR Models as needed.
- Chime can route calls to Biller IVR Systems for custom call flows.

### 3.6 Security Architecture

> **AI Prompt**: Define the security controls and compliance requirements for the system, with implementation approaches for each.

**Authentication Methods:** (Select all that apply)

- 🔲 Username/Password
- 🔲 OAuth 2.0/OIDC
- 🔲 SAML
- 🔲 JWT
- ☑️ API Key
- 🔲 Multi-factor Authentication
- 🔲 Social Login
- 🔲 Single Sign-On
- ☑️ Other: Telephone-based customer verification

**Authorization Models:** (Select all that apply)

- 🔲 Role-Based Access Control (RBAC)
- 🔲 Attribute-Based Access Control (ABAC)
- 🔲 Policy-Based Access Control
- 🔲 ACL (Access Control Lists)
- 🔲 OAuth 2.0 Scopes
- 🔲 Capability-Based Security
- ☑️ Other: Customer verification by account information

**Data Protection Methods:** (Select all that apply)

- ☑️ Encryption at Rest
- ☑️ Encryption in Transit
- 🔲 Field-level Encryption
- 🔲 Tokenization
- ☑️ Data Masking
- ☑️ Key Management
- 🔲 Other: _______________

**Compliance Requirements:** (Select all that apply)

- ☑️ PCI DSS
- 🔲 GDPR
- 🔲 HIPAA
- 🔲 SOX
- 🔲 SOC 2
- 🔲 ISO 27001
- 🔲 CCPA
- 🔲 Other: _______________

**Core Security Controls:**

| Control Type | Implementation | Purpose |
|--------------|----------------|---------|
| Authentication | Customer verification via account information | Verify customer identity before providing account access |
| API Security | API keys and secure endpoints | Secure communication between AutoIVR and Speech Service |
| Data Protection | TLS for all communications | Protect data in transit between system components |
| Data Privacy | Automatic removal of sensitive information | Ensure PCI compliance for payment data |
| Access Control | Limited access to transcription data | Protect customer information |
| Monitoring | Anomaly detection in usage patterns | Identify potential security incidents |

**Compliance Requirements:**

- PCI DSS - Implement secure handling of payment information with masking of sensitive data in transcripts

---

### 3.7 Vendor Selection Criteria

> **AI Prompt**: Define the key technical and business criteria for evaluating potential replacement vendors.

**Technical Evaluation Criteria:** (Select all that apply)

- ☑️ Feature Compatibility
- ☑️ Performance
- ☑️ Scalability
- ☑️ API/Integration Compatibility
- ☑️ Product Stability
- 🔲 Customization Options
- 🔲 Deployment Options
- ☑️ Support Options
- 🔲 Other: _______________

**Business Evaluation Criteria:** (Select all that apply)

- ☑️ Cost Structure
- ☑️ Vendor Stability
- ☑️ Product Longevity
- ☑️ Customer Base
- ☑️ Support Quality
- 🔲 Licensing Model
- 🔲 Partnership Opportunities
- 🔲 Other: _______________

**Critical Technical Requirements:**

| Requirement | Description | Importance |
|-------------|-------------|------------|
| VAD Capability | Voice Activity Detection feature availability | Must-have, non-starters will be filtered out initially |
| Transcription Speed | Response time for real-time transcription | Critical - anything below "high" rating will likely be ruled out |
| Transcription Accuracy | Recognition quality across different speech patterns | Critical for user experience and operational efficiency |
| Real-time Transcription | Ability to process speech to text in real time | Required for interactive voice response |
| API Compatibility | Compatibility with current HTTP-based integration model | Important to minimize system changes |
| Simultaneous Transcription | Support for multiple concurrent transcriptions | Required for production environment |

**Business Evaluation Approach:**

| Criterion | Evaluation Method | Notes |
|-----------|-------------------|-------|
| Cost | Compare to current MSA baseline cost | Based on estimated 3 million minutes monthly; preference for set annual price |
| Vendor/Product Stability | Company history and market position review | Important factor for long-term viability |
| Support Model | Review of support options and SLAs | Critical factor for open-source/community options |
| Customer Base | Evaluate size and industry distribution | Potential tiebreaker between similar options |
| Contractual Terms | Review of terms including source code escrow | Important for business continuity |

**Vendor Selection Process:**

1. Initial filtering based on must-have criteria (especially VAD capability)
2. Gather cost estimates based on estimated 3 million minutes monthly usage
3. Conduct individual vendor assessments using standardized technical and business criteria
4. Investigate open-source options with evaluation of code quality and community support
5. Create shortlist of highest-scoring vendors for deeper analysis and testing

**Potential Vendor Categories:**

1. **Commercial Cloud Providers:**
   - Major cloud platform vendors with established speech services
   - Examples: Amazon Transcribe, Google Speech-to-Text

2. **Specialized Speech Recognition Vendors:**
   - Companies focused on speech recognition technology
   - Examples: Deepgram, Speechmatics, Gladia, Recall.ai

3. **Open Source Solutions:**
   - Community-supported implementations
   - Examples: Whisper Streaming, RealtimeSTT, WhisperX, Moonshine

**Open Source Evaluation:**

For open-source or community-supported options, additional considerations include:

- Code quality and maintainability assessment
- Community size and activity level
- Availability of commercial support options
- Development roadmap and release cadence

---

### 3.8 Vendor Analysis

> **AI Prompt**: Provide a detailed analysis of potential vendors based on the defined selection criteria.

**Vendor Comparison:**

The following table presents a comparative analysis of potential vendor solutions based on the critical evaluation criteria. All vendors listed have been pre-screened to ensure they offer VAD (Voice Activity Detection) capability, which is a primary filter criterion.

| Engine Name | VAD Feature | Customizable | Speed | Accuracy | Support | Cost | Security | Vendor Location | Test API / Documentation URL |
|-------------|-------------|--------------|-------|----------|---------|------|----------|----------------|------------------------------|
| Deepgram | Yes | Yes | High | High | Yes | Paid | High | San Francisco, CA | <https://deepgram.com/product/speech-to-text> |
| Gladia | Yes | N/A | Very High | High | N/A | Paid | High | Cesson-Sévigné, France | <https://www.gladia.io/> |
| OpenAI Realtime API | Yes | N/A | High | High | N/A | Paid | High | San Francisco, CA | <https://platform.openai.com/docs/guides/realtime> |
| Amazon Transcribe | Yes | Yes | High | High | Yes | Paid | High | Seattle, WA | <https://docs.aws.amazon.com/transcribe/latest/APIReference/Welcome.html> |
| Speechmatics | Yes | N/A | Very High (<1s) | High | Yes | Paid | High | Cambridge, UK | <https://docs.speechmatics.com/jobsapi> |
| Recall.ai | Yes | N/A | High | High | Yes | Paid | High | San Francisco, CA | <https://docs.recall.ai/docs/quickstart> |
| Picovoice Cheetah | Yes | Yes | High | High | Yes | Free | High | Vancouver, BC | <https://picovoice.ai/docs/api/cheetah-python/> |
| Clari Copilot | Yes | N/A | High | High | Yes | Paid | High | Fremont, CA | <https://api-doc.copilot.clari.com/> |
| Floatbot NEO | Yes | N/A | High | High | Yes | Paid | High | Milpitas, CA | <https://floatbot.ai/asr-in-action> |
| Retell AI | Yes | N/A | High | High | Yes | Paid | High | Saratoga, CA | N/A (No public test API documentation available) |
| NearStream VAD | Yes | N/A | High | High | Yes | Paid | High | Not available | N/A (No public test API documentation available) |
| Tavus AI | Yes | N/A | High | High | Yes | Paid | High | San Francisco, CA | N/A (No public test API documentation available) |
| Whisper Streaming | Yes | Yes | Moderate | High | N/A | Free | Community | Open-source | <https://github.com/ufal/whisper_streaming> |
| RealtimeSTT | Yes | Yes | High | High | N/A | Free | Community | Open-source | <https://github.com/KoljaB/RealtimeSTT> |
| WhisperX | Yes | Yes | High | High | N/A | Free | Community | Open-source | <https://github.com/m-bain/whisperX> |
| Moonshine | Yes | N/A | Very High | High | N/A | Free | Community | Open-source | <https://github.com/usefulsensors/moonshine> |
| Whisper (OpenAI) | No | Limited | Moderate | High | Limited | Free | High | San Francisco, CA | <https://platform.openai.com/docs/guides/speech-to-text/whisper-api> |
| Google Speech-to-Text | Yes | Yes | High | High | Yes | Paid | High | Mountain View, CA | <https://cloud.google.com/speech-to-text/docs/reference/rest> |
| Baseten Whisper Pipeline | Yes | N/A | Very High | High | N/A | Paid | High | San Francisco, CA | <https://docs.baseten.co/chains/examples/audio-transcription> |

**Key Findings from Initial Evaluation:**

1. **VAD Capability**: 17 of the 19 evaluated solutions offer VAD capability, which is essential for our application. Whisper (OpenAI) does not support VAD capability, which may be a critical limitation for real-time applications.

2. **Performance Metrics**:
   - **Speed**: Most solutions offer "High" or "Very High" speed ratings, which is crucial for real-time applications. Whisper Streaming and Whisper (OpenAI) are rated as "Moderate" and may require additional performance testing.
   - **Accuracy**: All solutions are rated "High" for accuracy, suggesting that transcription quality should meet requirements across all options.

3. **Customization**: Several solutions offer customization capabilities, which could be valuable for adapting to specific speech patterns or domain-specific terminology.

4. **Support**: Commercial solutions generally offer formal support channels, while open-source options rely on community support.

5. **Cost Structure**: The evaluation includes both free and paid options, allowing for cost optimization based on other requirements.

**Deep Dive into Leading Candidates:**

**Commercial Options:**

- **Deepgram**: Offers a complete package with high ratings across all categories, including customization options and formal support. Based in San Francisco.
  
- **Amazon Transcribe**: A mature solution from a major cloud provider with robust support and security features. Seattle-based with global infrastructure.
  
- **Speechmatics**: Offers very high speed (<1s latency) and strong accuracy ratings. UK-based but can operate in US regions.

**Open Source Options:**

- **RealtimeSTT**: Combines high speed and accuracy with customization options, making it a strong contender among open-source solutions.
  
- **WhisperX**: Offers good performance characteristics with customization capabilities, warranting deeper investigation.
  
- **Moonshine**: Provides very high speed but requires assessment of customization capabilities and community support.

**Next Steps in Evaluation Process:**

1. Request detailed cost proposals from commercial vendors based on the 3 million minutes monthly usage.
2. Conduct technical proof-of-concept tests with top 2-3 commercial and open-source options.
3. Evaluate integration complexity with existing AutoIVR system.
4. Perform deeper assessment of support quality and vendor stability for shortlisted options.

---

## 3.9 Decision Framework

> **AI Prompt**: Create a structured decision framework to efficiently evaluate potential vendors based on objective criteria.

**Decision Tree Approach:**

To efficiently narrow down the candidate pool and make an unbiased, fact-based selection, the following decision tree provides a systematic evaluation approach:

```mermaid
flowchart TD
    A[Start Evaluation] --> B{VAD Support?}
    B -->|No| C[Eliminate]
    B -->|Yes| D{Real-time Transcription Speed?}
    D -->|Moderate/Low| E[Lower Priority]
    D -->|High/Very High| F{Support Type?}
    F -->|Community| G[Open Source Track]
    F -->|Commercial| H[Commercial Track]
    
    G --> G1{Customization Required?}
    G1 -->|Yes| G2[Select: RealtimeSTT, WhisperX]
    G1 -->|No| G3[Select: Moonshine]
    
    H --> H1{Cost Sensitivity?}
    H1 -->|High| H2[Free Options: Picovoice Cheetah]
    H1 -->|Medium/Low| H3{US-Based Required?}
    H3 -->|Yes| H4[Select: Deepgram, Amazon, Recall.ai]
    H3 -->|No| H5[Add: Speechmatics]
    
    H4 --> J[Conduct POC Testing]
    H5 --> J
    G2 --> J
    G3 --> J
    H2 --> J
    
    J --> K[Final Selection]
```

### Stage 1: Initial Filtering (Must-Have Requirements)

| Criterion | Pass/Fail Evaluation | Rationale |
|-----------|----------------------|-----------|
| VAD Support | Eliminate vendors without this feature | Essential for real-time interaction in AutoIVR |
| Transcription Speed | Must be rated "High" or "Very High" | Critical for customer experience in real-time interaction |
| Availability | Must support production-level availability | Required for business-critical application |

### Stage 2: Track Assignment (Commercial vs. Open Source)

Based on initial filtering, split evaluation into two parallel tracks:

1. **Commercial Solutions Track**
   - Focus on vendors with formal support and SLAs
   - Consider total cost of ownership including support costs
   - Evaluate vendor stability and market presence

2. **Open Source Track**
   - Focus on community strength and project activity
   - Consider internal support requirements and costs
   - Evaluate code quality and customization capability

### Stage 3: Weighted Scoring Matrix

For the remaining candidates in each track, apply a weighted scoring system with the following distribution:

| Category | Weight | Criteria | Commercial | Open Source |
|----------|--------|----------|------------|-------------|
| **Technical** | 50% | VAD Quality | 10% | 10% |
| | | Transcription Accuracy | 15% | 15% |
| | | Speed/Latency | 15% | 15% |
| | | API Compatibility | 10% | 10% |
| **Business** | 30% | Cost Structure | 15% | 5% |
| | | Support Quality | 10% | 5% |
| | | Vendor/Project Stability | 5% | 10% |
| | | Ease of Implementation | 0% | 10% |
| **Strategic** | 20% | Customization Capability | 5% | 10% |
| | | Long-term Viability | 10% | 5% |
| | | Ecosystem Integration | 5% | 5% |

### Stage 4: Proof of Concept Testing

For the top 2-3 candidates from each track (commercial and open source):

1. Implement small-scale POC to measure:
   - Actual transcription accuracy with WEX-specific audio samples
   - Latency under various conditions
   - Integration complexity
   - Resource utilization

2. Score results objectively using predefined metrics:
   - Word Error Rate (WER) for accuracy
   - Response time in milliseconds
   - Developer hours for integration
   - CPU/memory usage per transcription

### Stage 5: TCO Analysis

For final candidates, perform a comprehensive 5-year Total Cost of Ownership analysis:

| Cost Component | Commercial Considerations | Open Source Considerations |
|----------------|---------------------------|----------------------------|
| Licensing | Annual/monthly fees based on volume | None |
| Implementation | Integration effort (hours) | Integration + customization effort (hours) |
| Support | Vendor support costs | Internal support resources |
| Infrastructure | Cloud/hosting requirements | Self-hosting requirements |
| Maintenance | Vendor-managed updates | Internal update management |
| Scaling | Volume-based pricing impact | Infrastructure scaling costs |

### Recommended Short List Based on Initial Assessment

**Commercial Options:**

1. **Deepgram** - Complete package with high customization, strong support, and high accuracy
2. **Picovoice Cheetah** - Free option with strong technical capabilities
3. **Amazon Transcribe** - Reliable cloud service with strong performance and support
4. **Speechmatics** - Very high speed (<1s) with strong accuracy

**Open Source Options:**

1. **RealtimeSTT** - High speed and accuracy with customization options
2. **WhisperX** - Strong overall technical performance
3. **Moonshine** - Very high speed, may require less customization

### Unbiased Evaluation Safeguards

1. Blind technical evaluations where possible (remove vendor names during testing)
2. Standardized test scripts and evaluation criteria
3. Multiple evaluators with different perspectives (technical, business, operational)
4. Quantitative metrics for all key decision points
5. Documented decision rationale at each stage

This framework ensures a systematic, fact-based evaluation process that efficiently narrows down options while maintaining objectivity throughout the selection process.

---

## 4.0 Risks, Assumptions, Issues & Dependencies

> **AI Prompt**: Based on the solution architecture, identify key risks, assumptions, and dependencies with mitigation strategies.

**Risk Categories:** (Select all that apply)

- ☑️ Technical Risk
- ☑️ Security Risk
- ☑️ Operational Risk
- 🔲 Compliance Risk
- ☑️ Business Risk
- ☑️ Schedule Risk
- 🔲 Resource Risk
- ☑️ Vendor/Third-party Risk
- 🔲 Other: _______________

**Key Risks:**

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Transcription accuracy lower than current solution | High | Medium | Conduct thorough testing with multiple cloud providers; select best performing option; maintain parallel operation during transition |
| Integration with existing AutoIVR system complexity | Medium | Medium | Early prototyping; create adapter layer between new speech service and existing system |
| Cloud service outages | High | Low | Implement failover mechanisms; consider multi-region deployment; develop degraded service modes |
| Vendor lock-in with cloud provider | Medium | Medium | Design abstraction layer to enable future provider changes; maintain ownership of transcription data and models |
| Transition timeline delays | High | Medium | Begin implementation well before 2027 end-of-support; phased rollout approach |

**Assumption Categories:** (Select all that apply)

- ☑️ Business Assumptions
- ☑️ User Assumptions
- ☑️ Technical Assumptions
- 🔲 Resource Assumptions
- ☑️ Timeline Assumptions
- 🔲 Other: _______________

**Critical Assumptions:**

- Cloud-based transcription engines will provide equal or better accuracy than current Voci solution
- Existing AutoIVR application architecture can integrate with new speech recognition service
- Call volumes and speech patterns will remain similar to current patterns (approximately 3 million minutes monthly)
- Cloud provider services will meet performance and availability requirements
- Implementation can be completed before February 2027 support end date
- Selected vendor will provide adequate support and stability for the long term

**Dependency Types:** (Select all that apply)

- ☑️ System Dependencies
- ☑️ Data Dependencies
- ☑️ API Dependencies
- 🔲 Team Dependencies
- ☑️ Vendor Dependencies
- ☑️ Infrastructure Dependencies
- 🔲 Other: _______________

**Dependencies:**

| Dependency | Type | Impact | Status |
|------------|------|--------|--------|
| AutoIVR System Integration | Internal | Integration with existing system is required for implementation | Current |
| Cloud Provider API Stability | External | Changes to provider APIs could impact integration | Monitored |
| Telephony System Compatibility | Internal | Audio format and quality requirements must be met | To be assessed |
| Speech Recognition Service Availability | External | Service must remain available in required regions | To be confirmed with vendors |
| Historical Speech Data | Internal | Required for testing and tuning new solutions | Available |

---

## 5.0 Vector Impact Assessment

> **AI Prompt**: Analyze the impact of this architecture on the five key vectors (Reliability, Security, Innovation Velocity, AI Maturity, SaaS Maturity) with baseline metrics and target improvements.

```mermaid
mindmap
  root((Vector<br>Impact))
    Reliability
      Availability
      Fault Tolerance
    Security
      Auth & Data Protection
      Compliance
    Innovation
      CI/CD
      Tech Debt
    AI
      ML & Analytics
    SaaS
      Cloud-native
```

**Reliability Vector Impact:** (Select all that apply)

- ☑️ Improved Availability
- ☑️ Enhanced Fault Tolerance
- ☑️ Reduced Recovery Time (RTO)
- 🔲 Reduced Data Loss (RPO)
- ☑️ Improved Monitoring/Observability
- ☑️ Improved Incident Response
- 🔲 Other: _______________

**Security Vector Impact:** (Select all that apply)

- 🔲 Enhanced Authentication
- 🔲 Improved Authorization
- ☑️ Better Data Protection
- 🔲 Improved Threat Detection
- ☑️ Enhanced Compliance
- 🔲 Reduced Attack Surface
- 🔲 Other: _______________

**Innovation Velocity Impact:** (Select all that apply)

- 🔲 Improved CI/CD Pipeline
- ☑️ Reduced Technical Debt
- ☑️ Enhanced Customer Experience
- 🔲 Increased Deployment Frequency
- 🔲 Reduced Lead Time for Changes
- 🔲 Improved Test Automation
- 🔲 Other: _______________

**AI Maturity Impact:** (Select all that apply)

- ☑️ Implementation of ML Models
- 🔲 Enhanced Automation
- ☑️ Improved Analytics Capabilities
- ☑️ Better Data Quality for ML
- 🔲 AI/ML DevOps Implementation
- 🔲 Other: _______________

**SaaS Maturity Impact:** (Select all that apply)

- 🔲 Enhanced Multi-tenancy
- ☑️ Improved Cloud-native Features
- 🔲 Better Self-service Capabilities
- ☑️ Improved Scalability
- 🔲 Enhanced Provisioning
- 🔲 Other: _______________

| Vector | Current → Target | Key Improvements |
|--------|------------------|------------------|
| **Reliability** | On-premises solution → Cloud-based service with 99.9%+ availability | • Improved service availability with cloud provider SLAs  • Enhanced fault tolerance with redundant services  • Better monitoring and incident response |
| **Security** | Basic security controls → Enhanced cloud security | • Improved data protection with cloud security features  • Maintained PCI DSS compliance |
| **Innovation Velocity** | Legacy technology → Modern cloud service | • Reduced technical debt by replacing unsupported system  • Enhanced customer experience with improved recognition accuracy |
| **AI Maturity** | Basic voice recognition → Advanced ML-based speech services | • Implementation of modern machine learning models for speech recognition  • Improved analytics capabilities for speech patterns  • Better training data for ML models |
| **SaaS Maturity** | Self-hosted solution → Cloud SaaS offering | • Adoption of cloud-native speech recognition services  • Improved scalability with cloud-based solutions |

**Reference Links:**

- [Reliability Vector](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155172602241/Reliability+Vector)
- [Security](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155173978186/Product+Security)
- [Product Innovation Velocity (PiV)](https://wexinc.atlassian.net/wiki/spaces/TISO/pages/154810155024/Tech+Transformation+KPIs)
- [AI Maturity](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155174338612/AI+Maturity+Vector)
- [SaaS Maturity](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154867007780/RFC-351+SaaS+Maturity+Vector)

---

## 6.0 Appendix: Technical Deep-Dive

This appendix provides a comprehensive technical analysis of each shortlisted vendor option to support informed decision-making. Each assessment examines architecture, technical specifications, integration considerations, and performance characteristics.

### 6.1 Deepgram

#### 6.1.1 Technical Architecture

Deepgram uses end-to-end deep neural networks specifically designed for speech recognition, avoiding traditional pipeline approaches. According to their technical documentation, this architecture reduces cascading errors common in conventional ASR systems.

```mermaid
flowchart TD
    A[Audio Input] --> B[Neural Network Model]
    B --> G[Text Output]
    
    subgraph Deepgram Architecture
    B --> C[Feature Learning]
    C --> D[Acoustic Modeling]
    D --> E[Language Understanding]
    E --> F[Confidence Modeling]
    end
```

#### 6.1.2 API Integration

Deepgram offers multiple integration options:

**WebSocket API** for real-time streaming:

```javascript
// Example WebSocket connection for real-time streaming
const deepgram = new Deepgram(API_KEY);
const connection = deepgram.createConnection({
  url: 'wss://api.deepgram.com/v1/listen',
  options: {
    encoding: 'linear16',
    sample_rate: 16000,
    language: 'en-US',
    model: 'nova-2',
    smart_format: true,
    vad_turnoff: 500
  }
});
```

**REST API** for batch processing:

```http
POST /v1/listen HTTP/1.1
Host: api.deepgram.com
Authorization: Token YOUR_DEEPGRAM_API_KEY
Content-Type: audio/wav

[AUDIO_BINARY_DATA]
```

#### 6.1.3 Performance Characteristics

- **Latency:** Average 300-500ms for real-time transcription (based on Deepgram's official documentation)
- **Accuracy:** Word Error Rate (WER) as low as 9.8% on standardized test sets
- **Throughput:** Can handle hundreds of concurrent streams per API endpoint
- **Scalability:** Horizontal scaling with distributed processing

#### 6.1.4 VAD Implementation

Deepgram's Voice Activity Detection (VAD) is integrated directly into their neural network models, providing:

- Intelligent speaker separation
- Noise rejection
- Configurable sensitivity
- Turn-off delay (configurable from 0-2000ms)

#### 6.1.5 Customization Options

- **Model Fine-tuning:** Custom models trained on WEX-specific audio data
- **Custom Vocabulary:** Industry-specific terminology and commands
- **Domain Adaptation:** Financial services optimization

#### 6.1.6 Integration with AutoIVR

Integration would require:

1. WebSocket client implementation in AutoIVR
2. Audio stream format conversion (if needed)
3. Adaptation of response parsing logic
4. Implementation of error handling and fallback mechanisms

### 6.2 Amazon Transcribe

#### 6.2.1 Technical Architecture

Amazon Transcribe utilizes deep learning technologies running on AWS infrastructure, providing both batch and real-time streaming transcription through a fully managed service with automatic scaling capabilities.

```mermaid
flowchart TD
    A[Audio Input] --> B[AWS API Gateway]
    B --> C[Amazon Transcribe Service]
    C --> D[Deep Learning Models]
    D --> E[Post-processing]
    E --> F[Text Output]
    
    subgraph AWS Cloud
    B
    C
    D
    E
    end
```

#### 6.2.2 API Integration

Amazon Transcribe provides HTTP/2 streaming and WebSocket protocols:

**WebSocket API**:

```javascript
// Example WebSocket connection for real-time streaming
const connection = new WebSocket(
  'wss://transcribestreaming.us-west-2.amazonaws.com:8443/stream-transcription-websocket?' + 
  'language-code=en-US' +
  '&media-encoding=pcm' +
  '&sample-rate=16000'
);
```

**HTTP/2 Streaming**:

```python
# Python example using boto3
client = boto3.client('transcribe')
stream = client.start_stream_transcription(
    LanguageCode='en-US',
    MediaSampleRateHertz=16000,
    MediaEncoding='pcm',
    VocabularyName='financial-terms'
)
```

#### 6.2.3 Performance Characteristics

- **Latency:** Average 200-400ms for real-time transcription
- **Accuracy:** WER ranging from 10-15% depending on audio quality
- **Throughput:** Supports thousands of concurrent streams per account
- **Scalability:** Automatic scaling on AWS infrastructure

#### 6.2.4 VAD Implementation

Amazon Transcribe provides VAD through:

- Automatic speaker identification
- Custom settings for noise reduction
- Partial result stabilization
- Configurable end-of-speech detection

#### 6.2.5 Customization Options

- **Custom Vocabularies:** Domain-specific terms and phrases
- **Custom Language Models:** Train models with financial services data
- **Vocabulary Filtering:** PII redaction and custom word filters
- **Content Identification:** Automatic categorization

#### 6.2.6 Integration with AutoIVR

Integration would require:

1. AWS SDK implementation
2. IAM configuration for secure access
3. Stream handling with partial results processing
4. AWS-specific error handling

### 6.3 RealtimeSTT (Open Source)

#### 6.3.1 Technical Architecture

RealtimeSTT is an open-source wrapper around OpenAI's Whisper model, optimized for real-time processing by implementing chunking and buffering strategies.

```mermaid
flowchart TD
    A[Audio Input] --> B[Audio Preprocessing]
    B --> C[Buffering & Chunking]
    C --> D[Whisper Model]
    D --> E[Post-processing]
    E --> F[Text Output]
    
    subgraph RealtimeSTT Framework
    B
    C
    D
    E
    end
```

#### 6.3.2 API Integration

As an open-source library, RealtimeSTT offers Python API integration:

```python
from realtimestt import RealtimeSTT

# Initialize with configuration
stt = RealtimeSTT(
    model_path="large-v2",
    vad_threshold=0.5,
    buffer_size_seconds=5,
    chunk_size_seconds=1
)

# Start processing
stt.start()

# Process audio chunks
while True:
    audio_chunk = get_audio_chunk()  # Implementation-specific
    text = stt.process_audio(audio_chunk)
    if text:
        print(text)

# Stop when done
stt.stop()
```

#### 6.3.3 Performance Characteristics

- **Latency:** 500-1000ms depending on configuration and hardware
- **Accuracy:** Leverages Whisper model with WER around 11-16%
- **Throughput:** Depends on hardware; typically 1-5 concurrent streams per server
- **Scalability:** Requires manual horizontal scaling with load balancing

#### 6.3.4 VAD Implementation

RealtimeSTT implements VAD through:

- Configurable energy threshold detection
- Adjustable buffer sizes for speech segments
- Silence detection with configurable parameters
- Integration with external VAD libraries (optional)

#### 6.3.5 Customization Options

- **Model Selection:** Choose different Whisper model sizes
- **Source Code Modification:** Full access to modify processing pipeline
- **Configuration Tuning:** Extensive parameters for optimizing performance
- **Hardware Optimization:** GPU acceleration configuration

#### 6.3.6 Integration with AutoIVR

Integration would require:

1. Deployment of RealtimeSTT on WEX infrastructure
2. API wrapper development
3. Audio stream handling
4. Hardware provisioning for required performance
5. Internal support capability

### 6.4 Speechmatics

#### 6.4.1 Technical Architecture

Speechmatics uses self-supervised learning approaches and transformer-based models for high-accuracy speech recognition. Their architecture is designed to handle real-world audio challenges including background noise and multiple speakers.

```mermaid
flowchart TD
    A[Audio Input] --> B[API Gateway]
    B --> C[Audio Processing]
    C --> D[Self-Supervised Models]
    D --> E[Context Enhancement]
    E --> F[Text Output]
    
    subgraph Speechmatics Platform
    B
    C
    D
    E
    end
```

#### 6.4.2 API Integration

Speechmatics offers both real-time and batch transcription:

**Real-time API**:

```javascript
// Example WebSocket connection
const ws = new WebSocket('wss://rt.speechmatics.com/v2');

// Connection message
ws.send(JSON.stringify({
  message: 'StartRecognition',
  audio_format: {
    type: 'raw',
    encoding: 'pcm_s16le',
    sample_rate: 16000
  },
  transcription_config: {
    language: 'en',
    operating_point: 'enhanced',
    enable_partials: true,
    vad: {
      enabled: true,
      pre_silence_ms: 100,
      post_silence_ms: 500
    }
  }
}));
```

**Batch API**:

```http
POST /v2/jobs/ HTTP/1.1
Host: asr.api.speechmatics.com
Authorization: Bearer {API_KEY}
Content-Type: multipart/form-data

--boundary
Content-Disposition: form-data; name="config"
Content-Type: application/json

{
  "type": "transcription",
  "transcription_config": {
    "language": "en",
    "operating_point": "enhanced"
  }
}
--boundary
Content-Disposition: form-data; name="data"; filename="audio.wav"
Content-Type: audio/wav

[AUDIO_BINARY_DATA]
--boundary--
```

#### 6.4.3 Performance Characteristics

- **Latency:** Very low at <200ms for real-time transcription
- **Accuracy:** Industry-leading WER of 8-12% in financial services contexts
- **Throughput:** Supports hundreds of concurrent streams
- **Scalability:** Cloud-based elastic scaling

#### 6.4.4 VAD Implementation

Speechmatics' VAD capabilities include:

- Advanced noise cancellation
- Configurable silence thresholds
- Diarization for multi-speaker environments
- Adjustable pre/post silence durations

#### 6.4.5 Customization Options

- **Custom Dictionary:** Financial terms and commands
- **Language Packs:** Dialect optimization
- **Accuracy Tuning:** Confidence score thresholds
- **Callback Integrations:** Webhook support

#### 6.4.6 Integration with AutoIVR

Integration would require:

1. WebSocket client implementation
2. Authentication management
3. Adaptation of partial results handling
4. Configuration optimization for IVR use case

### 6.5 Picovoice Cheetah (Free Option)

#### 6.5.1 Technical Architecture

Picovoice Cheetah is an on-device speech-to-text engine designed for edge deployment with low resource requirements.

```mermaid
flowchart TD
    A[Audio Input] --> B[Feature Extraction]
    B --> C[Acoustic Model]
    C --> D[Language Model]
    D --> E[Post-processing]
    E --> F[Text Output]
    
    subgraph Cheetah Engine
    B
    C
    D
    E
    end
```

#### 6.5.2 API Integration

Picovoice offers a simple API across multiple languages:

```python
# Python example
import pvcheetah

cheetah = pvcheetah.create(access_key='YOUR_ACCESS_KEY')
result = cheetah.process(audio_frame)
if result.is_endpoint:
    print(result.transcript)
```

```javascript
// JavaScript example
const cheetah = new Cheetah(accessKey);
const result = cheetah.process(audioFrame);
if (result.isEndpoint) {
    console.log(result.transcript);
}
```

#### 6.5.3 Performance Characteristics

- **Latency:** Very low at 100-300ms
- **Accuracy:** WER of 12-18% depending on environment
- **Throughput:** Optimized for single-stream per instance
- **Scalability:** Deploy multiple instances for horizontal scaling

#### 6.5.4 VAD Implementation

Cheetah integrates with Picovoice's Cobra VAD:

- Edge-optimized voice detection
- Low CPU utilization
- Configurable sensitivity
- Minimal dependencies

#### 6.5.5 Customization Options

- **Language Model Adaptation:** Limited customization
- **Vocabulary Expansion:** Domain-specific terms
- **Endpoint Detection:** Configurable parameters
- **Deployment Options:** Cloud, on-premises, or hybrid

#### 6.5.6 Integration with AutoIVR

Integration would require:

1. SDK implementation in AutoIVR
2. On-premises deployment configuration
3. Audio stream format adaptation
4. Custom endpoint handling

### 6.6 WhisperX (Open Source)

#### 6.6.1 Technical Architecture

WhisperX extends OpenAI's Whisper with word-level timestamps, speaker diarization, and parallel inference optimizations.

```mermaid
flowchart TD
    A[Audio Input] --> B[Whisper Base Model]
    B --> C[Forced Alignment]
    C --> D[Word Timestamps]
    D --> E[Speaker Diarization]
    E --> F[Enhanced Output]
    
    subgraph WhisperX Extensions
    C
    D
    E
    end
```

#### 6.6.2 API Integration

As an open-source tool, WhisperX is typically used via Python API:

```python
import whisperx

# Load models
model = whisperx.load_model("large-v2", device="cuda")
alignment_model, metadata = whisperx.load_align_model(language_code="en", device="cuda")

# Transcribe
result = model.transcribe("audio.wav", batch_size=16)
aligned_result = whisperx.align(result["segments"], alignment_model, metadata)

# Diarize (optional)
diarize_model = whisperx.DiarizationPipeline(use_auth_token=HF_TOKEN)
diarize_segments = diarize_model(audio_file="audio.wav")
result_with_speakers = whisperx.assign_speakers(aligned_result, diarize_segments)
```

#### 6.6.3 Performance Characteristics

- **Latency:** Higher at 1-2 seconds due to alignment processing
- **Accuracy:** WER of 10-15% with improved timestamp precision
- **Throughput:** GPU-dependent, typically 1-3 streams per GPU
- **Scalability:** Requires manual scaling with additional servers

#### 6.6.4 VAD Implementation

WhisperX uses a combination of approaches:

- Whisper's internal VAD capabilities
- Enhanced silence detection via forced alignment
- Speaker boundary detection
- Energy-based segmentation

#### 6.6.5 Customization Options

- **Full Source Access:** Complete customization potential
- **Model Selection:** Various Whisper model sizes
- **Alignment Tuning:** Precision vs. speed tradeoffs
- **Inference Optimization:** Batch size and hardware utilization

#### 6.6.6 Integration with AutoIVR

Integration would require:

1. GPU-enabled infrastructure deployment
2. Custom API wrapper development
3. Threading model for handling multiple calls
4. Extensive performance tuning
5. Internal maintenance capability

### 6.7 Comparison Summary

| Feature | Deepgram | Amazon Transcribe | RealtimeSTT | Speechmatics | Picovoice Cheetah | WhisperX |
|---------|----------|-------------------|-------------|--------------|-------------------|----------|
| **Architecture** | End-to-end deep learning | AWS cloud-based | Whisper wrapper | Self-supervised learning | Edge-optimized | Whisper extension |
| **Latency** | 300-500ms | 200-400ms | 500-1000ms | <200ms | 100-300ms | 1-2s |
| **Accuracy (WER)** | 9.8-14% | 10-15% | 11-16% | 8-12% | 12-18% | 10-15% |
| **VAD Quality** | High | High | Medium-High | Very High | High | Medium |
| **API Complexity** | Low | Medium | High | Low | Very Low | Very High |
| **Customization** | Extensive | Good | Unlimited | Good | Limited | Unlimited |
| **Deployment** | Cloud | Cloud | Self-hosted | Cloud | On-prem/Cloud | Self-hosted |
| **Scaling** | Automatic | Automatic | Manual | Automatic | Manual | Manual |
| **Support** | Commercial | AWS Support | Community | Commercial | Commercial (Limited) | Community |
| **Cost Model** | Usage-based | Usage-based | Infrastructure only | Usage-based | Free tier available | Infrastructure only |

This detailed technical assessment provides critical insights for decision-making regarding the VOCI replacement. Each solution offers distinct advantages and considerations that must be weighed against WEX's specific requirements for the AutoIVR system.

### 6.8 References

1. Deepgram API Documentation: [https://developers.deepgram.com/api-reference/](https://developers.deepgram.com/api-reference/)
2. Amazon Transcribe Developer Guide: [https://docs.aws.amazon.com/transcribe/](https://docs.aws.amazon.com/transcribe/)
3. Speechmatics API Documentation: [https://docs.speechmatics.com](https://docs.speechmatics.com)
4. Picovoice Cheetah Documentation: [https://picovoice.ai/docs/cheetah/](https://picovoice.ai/docs/cheetah/)
5. RealtimeSTT GitHub Repository: [https://github.com/KoljaB/RealtimeSTT](https://github.com/KoljaB/RealtimeSTT)
6. WhisperX GitHub Repository: [https://github.com/m-bain/whisperX](https://github.com/m-bain/whisperX)

---

## 7.0 Appendix: Technical Migration Path

> **Note**: This migration path is provided as a guideline only. The dates, timelines, and specific technical details presented here are subject to change as the project progresses. This document serves as an initial framework to outline the migration approach and will be updated with more specific information as the project moves forward.

This appendix provides a comprehensive technical guide for migrating from the current Voci Transcription system to the new speech recognition solution. This serves as an implementation roadmap for the development team, covering all aspects of the migration process from initial preparation to final cutover.

### 7.1 Migration Overview and Phases

The migration will follow a phased approach to minimize risk and ensure continuity of service:

```mermaid
gantt
    title Voci Replacement Migration Timeline
    dateFormat  YYYY-MM-DD
    section Planning
    Current State Analysis            :a1, 2025-10-01, 30d
    Migration Plan Finalization       :a2, after a1, 15d
    Vendor Selection & Procurement    :a3, after a1, 45d
    section Implementation
    Adapter Layer Development         :b1, after a2, 60d
    API Integration Development       :b2, after a3, 45d
    Test Environment Setup            :b3, after a3, 30d
    section Testing
    Unit & Integration Testing        :c1, after b2, 30d
    Performance Testing               :c2, after b3, 30d
    Parallel System Testing           :c3, after c1, 45d
    section Deployment
    Pilot Deployment (10%)            :d1, after c3, 30d
    Incremental Rollout (50%)         :d2, after d1, 30d
    Full Production Deployment        :d3, after d2, 30d
    section Post-Migration
    Monitoring & Optimization         :e1, after d3, 60d
    Voci Decommissioning              :e2, after e1, 30d
```

### 7.2 Current State Assessment and Requirements Gathering

#### 7.2.1 Technical Assessment of Current Voci Implementation

1. **Document Current Architecture**
   - Map all integration points between AutoIVR and Voci
   - Document API contracts and data formats
   - Identify dependencies and system boundaries

   ```bash
   # Example command to analyze network connections to Voci servers
   netstat -ano | findstr <voci-server-ip>
   
   # Example command to capture API calls for analysis
   tcpdump -i any -n -s 0 host <voci-server-ip> -w voci_api_capture.pcap
   ```

2. **Analyze Audio Handling**
   - Document audio format specifications
   - Measure typical utterance durations
   - Catalog sample rate and encoding requirements

   ```python
   # Example Python code to analyze audio files
   import wave
   
   def analyze_audio(file_path):
       with wave.open(file_path, 'rb') as wf:
           # Extract the audio file properties
           channels = wf.getnchannels()
           sample_width = wf.getsampwidth()
           framerate = wf.getframerate()
           n_frames = wf.getnframes()
           duration = n_frames / float(framerate)
           
           return {
               'channels': channels,
               'sample_width': sample_width,
               'framerate': framerate,
               'duration': duration
           }
   ```

3. **Benchmark Current Performance**
   - Measure response times for various utterance types
   - Document accuracy rates (Word Error Rate)
   - Measure resource utilization (CPU, memory, network)

   ```sql
   -- Example SQL query to extract performance metrics from logs
   SELECT 
       AVG(response_time_ms) as avg_response_time,
       MIN(response_time_ms) as min_response_time,
       MAX(response_time_ms) as max_response_time,
       STDEV(response_time_ms) as stdev_response_time,
       COUNT(*) as total_requests
   FROM voci_transaction_logs
   WHERE timestamp BETWEEN '2025-10-01' AND '2025-10-31'
   GROUP BY utterance_type;
   ```

#### 7.2.2 Requirements Specification

1. **Functional Requirements**
   - Speech-to-text conversion for telephony audio
   - Voice Activity Detection (VAD)
   - Real-time processing
   - Support for financial terminology

2. **Non-Functional Requirements**
   - Performance: Response time < 400ms
   - Accuracy: WER < 12%
   - Scalability: Support for 3+ million minutes monthly
   - Reliability: 99.9%+ uptime

3. **Migration-Specific Requirements**
   - Zero downtime migration
   - Backward compatibility with existing systems
   - Ability to rollback if issues occur
   - Comprehensive logging for comparative analysis

### 7.3 Design of the New Architecture

#### 7.3.1 Speech Recognition Adapter Layer

To facilitate vendor independence and simplify future migrations, develop a Speech Recognition Adapter Layer that provides a consistent interface to the AutoIVR system.

```mermaid
classDiagram
    class ISpeechRecognizer {
        <<interface>>
        +initSession() Session
        +processAudio(Session, AudioData) RecognitionResult
        +endSession(Session) void
    }
    
    class VociAdapter {
        +initSession() Session
        +processAudio(Session, AudioData) RecognitionResult
        +endSession(Session) void
    }
    
    class NewVendorAdapter {
        +initSession() Session
        +processAudio(Session, AudioData) RecognitionResult
        +endSession(Session) void
        -configureVendorSpecificSettings() void
    }
    
    class SpeechRecognizerFactory {
        +getRecognizer(RecognizerType) ISpeechRecognizer
    }
    
    ISpeechRecognizer <|.. VociAdapter
    ISpeechRecognizer <|.. NewVendorAdapter
    SpeechRecognizerFactory --> ISpeechRecognizer
```

**Adapter Layer Code Example:**

```java
// Java example of the adapter interface
public interface ISpeechRecognizer {
    Session initSession(SessionConfig config);
    RecognitionResult processAudio(Session session, AudioData audio);
    void endSession(Session session);
}

// Implementation for the new vendor
public class NewVendorAdapter implements ISpeechRecognizer {
    private final VendorSpecificClient client;
    
    public NewVendorAdapter(VendorConfig config) {
        this.client = new VendorSpecificClient(config);
    }
    
    @Override
    public Session initSession(SessionConfig config) {
        String sessionId = client.createSession(mapToVendorConfig(config));
        return new Session(sessionId, config);
    }
    
    @Override
    public RecognitionResult processAudio(Session session, AudioData audio) {
        VendorResponse response = client.processAudio(session.getId(), audio.getBytes());
        return mapToRecognitionResult(response);
    }
    
    @Override
    public void endSession(Session session) {
        client.closeSession(session.getId());
    }
    
    private VendorConfig mapToVendorConfig(SessionConfig config) {
        // Map from common config format to vendor-specific format
        // ...
    }
    
    private RecognitionResult mapToRecognitionResult(VendorResponse response) {
        // Map from vendor-specific response to common result format
        // ...
    }
}
```

#### 7.3.2 Configuration Management

Design a configuration management system that allows for easy switching between speech recognition providers:

```yaml
# Example configuration file (config.yaml)
speech_recognition:
  active_provider: new_vendor  # Options: voci, new_vendor
  fallback_provider: voci  # Used if active_provider fails
  
  providers:
    voci:
      endpoint: https://voci.example.com/api
      api_key: ${VOCI_API_KEY}
      timeout_ms: 5000
      
    new_vendor:
      endpoint: https://api.newvendor.com/v1
      api_key: ${NEW_VENDOR_API_KEY}
      region: us-west-2
      timeout_ms: 3000
      model: financial
      vad:
        enabled: true
        sensitivity: 0.7
```

```java
// Java example for loading configuration
public class ConfigManager {
    private static final String CONFIG_PATH = "/etc/autoivr/config.yaml";
    private static Config instance;
    
    public static synchronized Config getConfig() {
        if (instance == null) {
            try {
                ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
                instance = mapper.readValue(new File(CONFIG_PATH), Config.class);
            } catch (IOException e) {
                throw new RuntimeException("Failed to load configuration", e);
            }
        }
        return instance;
    }
}
```

#### 7.3.3 API Integration

Develop integration code for the selected vendor's API:

```java
// Example WebSocket client for real-time streaming (using Deepgram as an example)
public class DeepgramClient {
    private final String apiKey;
    private final String endpoint;
    private WebSocket webSocket;
    private final ObjectMapper mapper = new ObjectMapper();
    
    public DeepgramClient(String apiKey, String endpoint) {
        this.apiKey = apiKey;
        this.endpoint = endpoint;
    }
    
    public void connect(DeepgramConfig config, AudioDataListener listener) {
        OkHttpClient client = new OkHttpClient.Builder()
            .readTimeout(30, TimeUnit.SECONDS)
            .build();
            
        Request request = new Request.Builder()
            .url(endpoint)
            .addHeader("Authorization", "Token " + apiKey)
            .build();
            
        webSocket = client.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                // Send configuration
                try {
                    String configJson = mapper.writeValueAsString(config);
                    webSocket.send(configJson);
                } catch (JsonProcessingException e) {
                    listener.onError(e);
                }
            }
            
            @Override
            public void onMessage(WebSocket webSocket, String text) {
                try {
                    DeepgramResponse response = mapper.readValue(text, DeepgramResponse.class);
                    listener.onTranscription(response);
                } catch (JsonProcessingException e) {
                    listener.onError(e);
                }
            }
            
            // Additional WebSocket event handlers
            // ...
        });
    }
    
    public void sendAudio(byte[] audioData) {
        if (webSocket != null) {
            webSocket.send(ByteString.of(audioData));
        }
    }
    
    public void disconnect() {
        if (webSocket != null) {
            webSocket.close(1000, "Closing connection");
            webSocket = null;
        }
    }
}
```

### 7.4 Implementation Steps

#### 7.4.1 Development Environment Setup

1. **Set up development environment**

   ```bash
   # Clone the repository
   git clone https://github.com/wex/autoivr.git
   cd autoivr
   
   # Create a branch for the migration work
   git checkout -b feature/speech-recognition-migration
   
   # Install dependencies
   mvn clean install
   ```

2. **Configure vendor SDK and dependencies**

   ```xml
   <!-- Example Maven dependency for Amazon Transcribe -->
   <dependency>
       <groupId>com.amazonaws</groupId>
       <artifactId>aws-java-sdk-transcribe</artifactId>
       <version>1.12.500</version>
   </dependency>
   
   <!-- Example Maven dependency for Deepgram -->
   <dependency>
       <groupId>com.deepgram</groupId>
       <artifactId>deepgram-java-sdk</artifactId>
       <version>1.3.0</version>
   </dependency>
   ```

3. **Set up mock services for testing**

   ```bash
   # Start a mock server for the speech recognition service
   docker run -d -p 8080:8080 --name mock-speech-service \
     -v $(pwd)/mocks:/mocks \
     mockserver/mockserver \
     -serverPort 8080 \
     -logLevel INFO
   ```

#### 7.4.2 Adapter Layer Implementation

1. **Create the adapter interface**

   ```java
   // Create the adapter interface in src/main/java/com/wex/autoivr/speech
   package com.wex.autoivr.speech;
   
   public interface SpeechRecognitionService {
       String startSession(SessionConfiguration config);
       TranscriptionResult transcribe(String sessionId, byte[] audio);
       void endSession(String sessionId);
   }
   ```

2. **Implement the Voci adapter**

   ```java
   // Create the Voci adapter implementation
   package com.wex.autoivr.speech.voci;
   
   import com.wex.autoivr.speech.SpeechRecognitionService;
   
   public class VociSpeechRecognitionService implements SpeechRecognitionService {
       private final VociClient vociClient;
       
       public VociSpeechRecognitionService(VociConfiguration config) {
           this.vociClient = new VociClient(config);
       }
       
       @Override
       public String startSession(SessionConfiguration config) {
           // Translate generic config to Voci-specific config
           VociSessionConfig vociConfig = VociConfigMapper.map(config);
           return vociClient.createSession(vociConfig);
       }
       
       @Override
       public TranscriptionResult transcribe(String sessionId, byte[] audio) {
           VociTranscription vociResult = vociClient.processAudio(sessionId, audio);
           return VociResultMapper.map(vociResult);
       }
       
       @Override
       public void endSession(String sessionId) {
           vociClient.endSession(sessionId);
       }
   }
   ```

3. **Implement the new vendor adapter**

   ```java
   // Create the new vendor adapter implementation (example for Amazon Transcribe)
   package com.wex.autoivr.speech.aws;
   
   import com.wex.autoivr.speech.SpeechRecognitionService;
   import software.amazon.awssdk.services.transcribestreaming.TranscribeStreamingAsyncClient;
   
   public class AwsTranscribeService implements SpeechRecognitionService {
       private final TranscribeStreamingAsyncClient client;
       private final Map<String, StreamingSession> sessions = new ConcurrentHashMap<>();
       
       public AwsTranscribeService(AwsTranscribeConfig config) {
           this.client = TranscribeStreamingAsyncClient.builder()
               .region(Region.of(config.getRegion()))
               .credentialsProvider(StaticCredentialsProvider.create(
                   AwsBasicCredentials.create(config.getAccessKey(), config.getSecretKey())))
               .build();
       }
       
       @Override
       public String startSession(SessionConfiguration config) {
           String sessionId = UUID.randomUUID().toString();
           StreamingSession session = new StreamingSession(client, config);
           sessions.put(sessionId, session);
           return sessionId;
       }
       
       @Override
       public TranscriptionResult transcribe(String sessionId, byte[] audio) {
           StreamingSession session = sessions.get(sessionId);
           if (session == null) {
               throw new IllegalStateException("Session not found: " + sessionId);
           }
           return session.processAudio(audio);
       }
       
       @Override
       public void endSession(String sessionId) {
           StreamingSession session = sessions.remove(sessionId);
           if (session != null) {
               session.close();
           }
       }
   }
   ```

4. **Implement the service factory**

   ```java
   // Create a factory to instantiate the appropriate service
   package com.wex.autoivr.speech;
   
   public class SpeechRecognitionServiceFactory {
       public static SpeechRecognitionService createService(AppConfig config) {
           String provider = config.getSpeechRecognition().getActiveProvider();
           
           switch (provider) {
               case "voci":
                   return new VociSpeechRecognitionService(config.getSpeechRecognition().getVoci());
               case "aws":
                   return new AwsTranscribeService(config.getSpeechRecognition().getAws());
               case "deepgram":
                   return new DeepgramSpeechRecognitionService(config.getSpeechRecognition().getDeepgram());
               default:
                   throw new IllegalArgumentException("Unknown speech recognition provider: " + provider);
           }
       }
   }
   ```

#### 7.4.3 Feature Flags for Gradual Rollout

Implement feature flags to control the rollout of the new speech recognition service:

```java
// Feature flag service
package com.wex.autoivr.feature;

public class FeatureFlagService {
    private final FeatureFlagClient client;
    
    public FeatureFlagService(FeatureFlagClient client) {
        this.client = client;
    }
    
    public boolean isNewSpeechRecognitionEnabled(String callerId) {
        return client.isFeatureEnabled("new-speech-recognition", callerId);
    }
    
    public double getNewSpeechRecognitionRolloutPercentage() {
        return client.getNumericValue("new-speech-recognition-rollout-percentage", 0.0);
    }
}
```

```java
// Usage in the service selection logic
public SpeechRecognitionService getSpeechRecognitionService(String callerId) {
    AppConfig config = configManager.getConfig();
    
    if (featureFlagService.isNewSpeechRecognitionEnabled(callerId)) {
        return SpeechRecognitionServiceFactory.createService(config);
    } else {
        // Use legacy Voci service
        return new VociSpeechRecognitionService(config.getSpeechRecognition().getVoci());
    }
}
```

### 7.5 Testing Strategy

#### 7.5.1 Unit Testing

Create comprehensive unit tests for the new adapter layer:

```java
// Example unit test for the AWS Transcribe adapter
@Test
public void testAwsTranscribeServiceTranscription() {
    // Arrange
    AwsTranscribeConfig config = new AwsTranscribeConfig("us-west-2", "access-key", "secret-key");
    AwsTranscribeService service = mock(AwsTranscribeService.class);
    
    SessionConfiguration sessionConfig = new SessionConfiguration.Builder()
        .withLanguage("en-US")
        .withSampleRate(8000)
        .build();
    
    String sessionId = "test-session-id";
    byte[] audioData = new byte[1024]; // Test audio data
    
    TranscriptionResult expectedResult = new TranscriptionResult.Builder()
        .withText("test transcription")
        .withConfidence(0.95)
        .build();
    
    when(service.startSession(any(SessionConfiguration.class))).thenReturn(sessionId);
    when(service.transcribe(eq(sessionId), any(byte[].class))).thenReturn(expectedResult);
    
    // Act
    String actualSessionId = service.startSession(sessionConfig);
    TranscriptionResult actualResult = service.transcribe(actualSessionId, audioData);
    
    // Assert
    assertEquals(sessionId, actualSessionId);
    assertEquals(expectedResult.getText(), actualResult.getText());
    assertEquals(expectedResult.getConfidence(), actualResult.getConfidence(), 0.01);
    
    // Verify
    verify(service).startSession(sessionConfig);
    verify(service).transcribe(sessionId, audioData);
}
```

#### 7.5.2 Integration Testing

Test the integration of the new speech recognition service with the AutoIVR system:

```java
// Example integration test
@RunWith(SpringRunner.class)
@SpringBootTest
public class SpeechRecognitionIntegrationTest {
    
    @Autowired
    private AutoIvrController controller;
    
    @Autowired
    private SpeechRecognitionServiceFactory serviceFactory;
    
    @Test
    public void testEndToEndTranscription() throws Exception {
        // Arrange
        String audioFilePath = "src/test/resources/test_audio.wav";
        byte[] audioData = Files.readAllBytes(Paths.get(audioFilePath));
        
        CallSession session = controller.createCallSession();
        
        // Act
        TranscriptionResponse response = controller.processAudio(session.getId(), audioData);
        
        // Assert
        assertNotNull(response);
        assertNotNull(response.getTranscription());
        assertTrue(response.getTranscription().length() > 0);
        assertTrue(response.getConfidence() > 0.7);
    }
}
```

#### 7.5.3 Performance Testing

Develop performance tests to ensure the new service meets performance requirements:

```java
// Example JMeter test plan for performance testing
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.4.1">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Speech Recognition Performance Test" enabled="true">
      <stringProp name="TestPlan.comments"></stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.tearDown_on_shutdown">true</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Concurrent Users" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">10</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">100</stringProp>
        <stringProp name="ThreadGroup.ramp_time">30</stringProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
        <stringProp name="ThreadGroup.duration"></stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
      </ThreadGroup>
      <!-- Additional JMeter configuration omitted for brevity -->
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

#### 7.5.4 A/B Testing

Implement a system to compare results from both the old and new systems:

```java
// Dual transcription service for A/B testing
public class DualTranscriptionService {
    private final SpeechRecognitionService vociService;
    private final SpeechRecognitionService newService;
    private final TranscriptionComparisonRepository comparisonRepository;
    
    public DualTranscriptionService(
            SpeechRecognitionService vociService,
            SpeechRecognitionService newService,
            TranscriptionComparisonRepository comparisonRepository) {
        this.vociService = vociService;
        this.newService = newService;
        this.comparisonRepository = comparisonRepository;
    }
    
    public TranscriptionResult transcribe(String sessionId, byte[] audio) {
        // Primary result from the active service
        TranscriptionResult primaryResult = newService.transcribe(sessionId, audio);
        
        // Shadow testing with the legacy service
        CompletableFuture.runAsync(() -> {
            try {
                TranscriptionResult legacyResult = vociService.transcribe(sessionId, audio);
                
                // Store both results for comparison
                comparisonRepository.saveComparison(
                    TranscriptionComparison.builder()
                        .sessionId(sessionId)
                        .timestamp(Instant.now())
                        .newServiceText(primaryResult.getText())
                        .newServiceConfidence(primaryResult.getConfidence())
                        .legacyServiceText(legacyResult.getText())
                        .legacyServiceConfidence(legacyResult.getConfidence())
                        .build()
                );
            } catch (Exception e) {
                log.error("Error during shadow testing", e);
            }
        });
        
        return primaryResult;
    }
}
```

### 7.6 Deployment Strategy

#### 7.6.1 Infrastructure Provisioning

Provision the necessary infrastructure for the new speech recognition service:

```yaml
# Example Terraform configuration for AWS infrastructure
provider "aws" {
  region = "us-west-2"
}

resource "aws_iam_role" "transcribe_role" {
  name = "transcribe-service-role"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "transcribe.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_policy" "transcribe_policy" {
  name = "transcribe-service-policy"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "transcribe:StartStreamTranscription",
          "transcribe:StartStreamTranscriptionWebSocket"
        ]
        Effect = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "transcribe_attachment" {
  role = aws_iam_role.transcribe_role.name
  policy_arn = aws_iam_policy.transcribe_policy.arn
}

# Additional resources like VPC, security groups, etc.
```

#### 7.6.2 Canary Deployment

Implement a canary deployment strategy to gradually roll out the new service:

```java
// Canary deployment logic in a filter/interceptor
@Component
public class SpeechRecognitionCanaryFilter implements Filter {
    private final FeatureFlagService featureFlagService;
    private final Random random = new Random();
    
    public SpeechRecognitionCanaryFilter(FeatureFlagService featureFlagService) {
        this.featureFlagService = featureFlagService;
    }
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) 
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String callerId = httpRequest.getHeader("X-Caller-ID");
        
        // Get current rollout percentage
        double rolloutPercentage = featureFlagService.getNewSpeechRecognitionRolloutPercentage();
        
        // Determine if this request should use the new service
        boolean useNewService = random.nextDouble() < (rolloutPercentage / 100.0);
        
        // Set attribute for downstream components
        httpRequest.setAttribute("useNewSpeechRecognition", useNewService);
        
        chain.doFilter(request, response);
    }
}
```

#### 7.6.3 Monitoring and Alerting

Set up comprehensive monitoring and alerting for the new speech recognition service:

```yaml
# Example Prometheus alerting rules
groups:
- name: speech_recognition_alerts
  rules:
  - alert: SpeechRecognitionHighLatency
    expr: avg(speech_recognition_latency_ms) > 500
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High speech recognition latency"
      description: "Speech recognition service has high latency (>500ms) for the past 5 minutes"
      
  - alert: SpeechRecognitionHighErrorRate
    expr: sum(rate(speech_recognition_errors_total[5m])) / sum(rate(speech_recognition_requests_total[5m])) > 0.05
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High speech recognition error rate"
      description: "Speech recognition service error rate is above 5% for the past 5 minutes"
```

```java
// Metrics collection in the service
@Component
public class SpeechRecognitionMetrics {
    private final MeterRegistry registry;
    
    public SpeechRecognitionMetrics(MeterRegistry registry) {
        this.registry = registry;
    }
    
    public Timer transcriptionTimer(String provider) {
        return registry.timer("speech_recognition_latency", "provider", provider);
    }
    
    public Counter transcriptionErrorCounter(String provider) {
        return registry.counter("speech_recognition_errors", "provider", provider);
    }
    
    public Counter transcriptionRequestCounter(String provider) {
        return registry.counter("speech_recognition_requests", "provider", provider);
    }
}
```

### 7.7 Rollback Plan

Prepare a comprehensive rollback strategy in case issues arise during the migration:

#### 7.7.1 Immediate Rollback Procedure

```java
// Rollback service with circuit breaker pattern
@Service
public class SpeechRecognitionCircuitBreaker {
    private final SpeechRecognitionService primaryService;
    private final SpeechRecognitionService fallbackService;
    private final AtomicInteger failureCount = new AtomicInteger(0);
    private final AtomicBoolean circuitOpen = new AtomicBoolean(false);
    private final int failureThreshold = 5;
    
    public SpeechRecognitionCircuitBreaker(
            SpeechRecognitionService primaryService,
            SpeechRecognitionService fallbackService) {
        this.primaryService = primaryService;
        this.fallbackService = fallbackService;
    }
    
    public TranscriptionResult transcribe(String sessionId, byte[] audio) {
        if (circuitOpen.get()) {
            log.warn("Circuit open, using fallback service for session {}", sessionId);
            return fallbackService.transcribe(sessionId, audio);
        }
        
        try {
            TranscriptionResult result = primaryService.transcribe(sessionId, audio);
            failureCount.set(0); // Reset failure count on success
            return result;
        } catch (Exception e) {
            log.error("Primary service failed", e);
            
            // Increment failure count and check threshold
            if (failureCount.incrementAndGet() >= failureThreshold) {
                log.error("Failure threshold reached, opening circuit");
                circuitOpen.set(true);
                
                // Schedule circuit reset after 5 minutes
                scheduledExecutor.schedule(() -> {
                    log.info("Attempting to reset circuit");
                    circuitOpen.set(false);
                    failureCount.set(0);
                }, 5, TimeUnit.MINUTES);
            }
            
            // Use fallback service
            return fallbackService.transcribe(sessionId, audio);
        }
    }
}
```

#### 7.7.2 Emergency Rollback Script

```bash
#!/bin/bash
# Emergency rollback script for speech recognition service

# Set variables
DEPLOYMENT_ENV=$1  # prod, staging, etc.
APP_NAME="autoivr"
ROLLBACK_VERSION=$2  # version to rollback to

# Validate input
if [ -z "$DEPLOYMENT_ENV" ] || [ -z "$ROLLBACK_VERSION" ]; then
    echo "Usage: $0 <environment> <version>"
    exit 1
fi

echo "Starting emergency rollback to version $ROLLBACK_VERSION in $DEPLOYMENT_ENV environment"

# Update feature flags to disable new speech recognition
echo "Disabling new speech recognition feature flag"
curl -X PATCH -H "Content-Type: application/json" \
     -d '{"enabled": false}' \
     https://featureflags.wex.com/api/features/new-speech-recognition

# Roll back the application deployment
echo "Rolling back application deployment"
kubectl rollout undo deployment/$APP_NAME-$DEPLOYMENT_ENV --to-revision=$ROLLBACK_VERSION

# Verify rollback status
echo "Checking rollback status"
kubectl rollout status deployment/$APP_NAME-$DEPLOYMENT_ENV

# Notify monitoring systems
echo "Notifying monitoring systems"
curl -X POST -H "Content-Type: application/json" \
     -d '{"message": "Emergency rollback performed for speech recognition service", "severity": "critical"}' \
     https://monitoring.wex.com/api/alerts

echo "Rollback completed"
```

### 7.8 Post-Migration Activities

#### 7.8.1 Performance Optimization

Fine-tune the new speech recognition service for optimal performance:

```java
// Example of a caching layer to improve performance
@Service
public class CachingSpeechRecognitionService implements SpeechRecognitionService {
    private final SpeechRecognitionService delegate;
    private final Cache<String, TranscriptionResult> resultCache;
    
    public CachingSpeechRecognitionService(SpeechRecognitionService delegate) {
        this.delegate = delegate;
        this.resultCache = Caffeine.newBuilder()
            .maximumSize(10_000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();
    }
    
    @Override
    public String startSession(SessionConfiguration config) {
        return delegate.startSession(config);
    }
    
    @Override
    public TranscriptionResult transcribe(String sessionId, byte[] audio) {
        // Generate a cache key based on audio content
        String cacheKey = sessionId + "-" + DigestUtils.md5Hex(audio);
        
        // Try to get from cache first
        return resultCache.get(cacheKey, k -> delegate.transcribe(sessionId, audio));
    }
    
    @Override
    public void endSession(String sessionId) {
        delegate.endSession(sessionId);
    }
}
```

#### 7.8.2 Vendor-Specific Optimizations

Implement vendor-specific optimizations based on post-migration analysis:

```java
// Example of Amazon Transcribe specific optimizations
public class OptimizedAwsTranscribeService extends AwsTranscribeService {
    
    @Override
    public TranscriptionResult transcribe(String sessionId, byte[] audio) {
        // Apply AWS-specific optimizations
        
        // 1. Use partial results for faster response times
        StreamTranscriptionBehavior behavior = StreamTranscriptionBehavior.builder()
            .enablePartialResults(true)
            .partialResultsStability(PartialResultsStability.HIGH)
            .build();
            
        // 2. Apply content filtering for financial terms
        ContentIdentificationType contentIdentification = ContentIdentificationType.builder()
            .redaction(RedactionType.PII)
            .build();
            
        // 3. Use custom language model for financial terminology
        LanguageModelName languageModel = LanguageModelName.builder()
            .modelName("financial-terms-v2")
            .build();
            
        // Call the optimized service
        return super.transcribe(sessionId, audio);
    }
}
```

#### 7.8.3 Knowledge Transfer and Documentation

Prepare comprehensive documentation for future maintenance:

##### Speech Recognition Service Integration Guide

###### Overview

This document provides a comprehensive guide to the new speech recognition service integration with the AutoIVR system.

###### Architecture

The speech recognition service is integrated using an adapter pattern that provides a common interface for different vendor implementations.

###### Supported Vendors

- Amazon Transcribe
- Deepgram
- Speechmatics

###### Configuration

The service can be configured using the following properties:

```yaml
speech:
  active-provider: aws  # aws, deepgram, speechmatics
  aws:
    region: us-west-2
    model: financial-terms-v2
  deepgram:
    api-key: ${DEEPGRAM_API_KEY}
    model: nova-2
  speechmatics:
    api-key: ${SPEECHMATICS_API_KEY}
    operating-point: enhanced
```

###### Monitoring

The service exposes the following metrics:

- `speech_recognition_latency_ms`: Latency of speech recognition requests
- `speech_recognition_errors_total`: Total number of errors
- `speech_recognition_requests_total`: Total number of requests

###### Troubleshooting

Common issues and their solutions:
...

### 7.9 Voci Decommissioning

Once the new speech recognition service is fully deployed and stable, plan for the decommissioning of the Voci system:

#### 7.9.1 Decommissioning Checklist

```markdown
# Voci Decommissioning Checklist

## Pre-Decommissioning Verification
- [ ] New speech recognition service has been running stably for at least 30 days
- [ ] All traffic has been migrated to the new service
- [ ] Performance metrics of the new service meet or exceed Voci metrics
- [ ] All integration points have been migrated
- [ ] All documentation has been updated

## Data Migration
- [ ] Identify any important data stored in Voci that needs to be preserved
- [ ] Export and archive necessary data
- [ ] Verify the integrity of exported data

## Infrastructure Decommissioning
- [ ] Identify all Voci servers and infrastructure components
- [ ] Create backup of all Voci configurations and data
- [ ] Remove Voci dependencies from AutoIVR system
- [ ] Update firewall rules and network configurations
- [ ] Shut down Voci servers
- [ ] Return or repurpose hardware resources

## License and Contract Management
- [ ] Review Voci licensing agreements and termination clauses
- [ ] Notify Voci of contract termination as per agreement terms
- [ ] Obtain confirmation of contract termination
- [ ] Remove Voci license keys from credential management systems

## Documentation and Knowledge Base
- [ ] Update all system documentation to remove Voci references
- [ ] Archive Voci-specific documentation for historical reference
- [ ] Update architectural diagrams and system specifications
- [ ] Notify all stakeholders of the completed migration
```

#### 7.9.2 Decommissioning Script

```bash
#!/bin/bash
# Voci decommissioning script

# Set environment variables
VOCI_SERVERS=(voci-server-1 voci-server-2 voci-server-3)
BACKUP_DIR="/mnt/backups/voci-decommission-$(date +%Y%m%d)"

# Create backup directory
mkdir -p $BACKUP_DIR
echo "Created backup directory at $BACKUP_DIR"

# Backup Voci configurations and data
echo "Backing up Voci configurations and data..."
for server in "${VOCI_SERVERS[@]}"; do
    echo "Backing up $server..."
    ssh $server "tar -czf /tmp/voci-backup.tar.gz /etc/voci /var/log/voci /opt/voci/data"
    scp $server:/tmp/voci-backup.tar.gz $BACKUP_DIR/$server-backup.tar.gz
    ssh $server "rm /tmp/voci-backup.tar.gz"
done

# Remove Voci from load balancers
echo "Removing Voci servers from load balancers..."
for server in "${VOCI_SERVERS[@]}"; do
    aws elb deregister-instances-from-load-balancer \
        --load-balancer-name voci-lb \
        --instances $server
done

# Update firewall rules
echo "Updating firewall rules..."
for server in "${VOCI_SERVERS[@]}"; do
    aws ec2 revoke-security-group-ingress \
        --group-id sg-voci-servers \
        --protocol tcp \
        --port 8080 \
        --source-group sg-autoivr-servers
done

# Shut down Voci servers
echo "Shutting down Voci servers..."
for server in "${VOCI_SERVERS[@]}"; do
    ssh $server "sudo shutdown -h now"
done

# Wait for servers to shut down
echo "Waiting for servers to shut down..."
sleep 60

# Terminate EC2 instances
echo "Terminating EC2 instances..."
for server in "${VOCI_SERVERS[@]}"; do
    instance_id=$(aws ec2 describe-instances \
        --filters "Name=tag:Name,Values=$server" \
        --query "Reservations[].Instances[].InstanceId" \
        --output text)
    
    if [ ! -z "$instance_id" ]; then
        aws ec2 terminate-instances --instance-ids $instance_id
    fi
done

echo "Voci decommissioning completed"
```

### 7.10 Training and Support Materials

Prepare training materials for the operations and support teams:

```markdown
# Speech Recognition Service Support Guide

## Overview
This guide provides information for supporting the new speech recognition service in the AutoIVR system.

## Monitoring Dashboards
- **Main Dashboard**: https://grafana.wex.com/d/speech-recognition
- **Alert Dashboard**: https://grafana.wex.com/d/speech-recognition-alerts

## Common Support Scenarios

### High Latency Issues
1. Check the current traffic levels on the dashboard
2. Verify if the issue is specific to a particular vendor
3. Check for any vendor status updates or outages
4. If using AWS Transcribe, check AWS Service Health Dashboard
5. If persistent, consider activating the fallback provider

### Accuracy Issues
1. Collect audio samples from affected calls
2. Check if the issue is with specific accents or terminology
3. Test the audio with different providers to compare results
4. Adjust the VAD sensitivity settings if needed
5. Consider updating custom vocabulary or language models

### System Integration Issues
1. Check the AutoIVR logs for connection errors
2. Verify network connectivity to the speech recognition service
3. Check API key validity and permissions
4. Verify that the speech recognition adapter is correctly configured
5. Check the circuit breaker status for any open circuits

## Escalation Path
1. **Tier 1**: AutoIVR Support Team
2. **Tier 2**: Speech Recognition Integration Team
3. **Tier 3**: Vendor Support (AWS, Deepgram, or Speechmatics)

## Key Contacts
- **Speech Recognition Team Lead**: [Name], [Email], [Phone]
- **AWS Account Manager**: [Name], [Email], [Phone]
- **Deepgram Support**: [Email], [Support Portal URL]
- **Speechmatics Support**: [Email], [Support Portal URL]
```

This comprehensive migration guide provides the development team with a detailed roadmap for transitioning from the existing Voci system to the new speech recognition solution. It covers all aspects of the migration process, from initial assessment and planning through implementation, testing, deployment, and post-migration activities, with a focus on maintaining service continuity and quality throughout the transition.

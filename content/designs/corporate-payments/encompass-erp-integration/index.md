<!-- Parent: Designs -->
<!-- Parent: Corporate Payments -->
<!-- Title: RFC-512 Encompass ERP Integration -->

<!-- Include: macros.md -->
# Candidate Architecture Design: Encompass ERP Integration

:draft:

```text
Author: Sa<PERSON> Swain
Title: Encompass ERP Integration
Publish Date: 2025-06-16
Category: Designs
Subtype: Corporate Payments
```

> **Note on Estimates and Figures**: All numerical values, timelines, and cost estimates presented in this document are approximations provided for reference purposes only. These figures are based on industry standards and preliminary analysis but should not be considered final or binding. Actual implementation costs, timelines, and resource requirements will depend on detailed planning and may differ from these estimates.

## Executive Summary

### Problem Statement

WEX's current approach to ERP integration with the EnCompass platform relies on custom, point-to-point integrations for each customer, resulting in:

- 6-8 week implementation delays for each new customer
- Security vulnerabilities from flat file transfers
- Manual reconciliation burdens for customers
- Resource-intensive integration processes
- Competitive disadvantage against API-first competitors

### Proposed Solution

Create a standardized, self-service integration framework that connects EnCompass to customer ERP systems (starting with Sage), enabling customers to configure and manage their integrations without WEX development resources.

### Strategic Options

| Factor | Build Option | Buy Option (iPaaS) | Hybrid Approach |
|--------|-------------|-------------------|-----------------|
| Time to Market | 6-9 months | 4-6 months | 5-7 months |
| Initial Investment | $600K-$850K | $150K-$250K | $350K-$450K |
| Engineering Resources | 4-5 FTEs | 2-3 FTEs | 3-4 FTEs |
| ERP Ecosystem Support | Limited initially | 200+ pre-built connectors | Best of both |
| Customization Control | High | Limited | Balanced |
| Long-term TCO (5yr) | $1.6M-$2.35M | $2.45M-$4.25M | $2.0M-$3.0M |

### Key Recommendation

**Pursue a Hybrid Approach:** Implement an iPaaS solution (Workato recommended) for rapid initial ERP connectivity while developing a custom self-service configuration portal in EnCompass to optimize customer experience and maintain strategic control. This approach leverages existing C# expertise through an API-first architecture where EnCompass teams can develop complex business logic in C# while using Workato as an orchestration layer.

### Expected Benefits

- Reduce integration time from 6-8 weeks to 3-5 days (85% improvement)
- Eliminate security vulnerabilities through API-based integration
- Enable customer self-service for 90% of integration management tasks
- Position for rapid expansion to additional ERPs with minimal development
- Improve reliability with 75% reduction in integration-related incidents
- Maintain C# development expertise and leverage existing codebase through API-first architecture
- Create clear separation of concerns with C# developers maintaining core business logic while integration specialists focus on ERP connectivity

### Implementation Considerations

While the hybrid approach offers significant advantages, our assessment has identified several key challenges that require proactive mitigation:

- **Integration Complexity**: Sage's XML-based data structure requires transformation to JSON, adding technical complexity. We'll implement standardized parsing templates and validation services to streamline this process.

- **Cost Management**: Workato pricing scales with transaction volume (35% increase at enterprise scale). Our mitigation strategy includes transaction batching and volume monitoring to optimize costs as we grow.

- **Deployment Flexibility**: Workato's cloud-centric architecture presents challenges for hybrid environments. We'll address this by implementing secure agents and local caching strategies for reliable connectivity.

- **Timeline Risk**: The initial Sage integration may require more time than the standard 3-5 day estimate. We recommend a 30-50% buffer in project timelines and early engagement with Workato's professional services specifically for Sage optimization.

- **Skills Development**: A formal training program with certification paths will address the learning curve, complemented by EnCompass-specific documentation and a knowledge sharing framework.

> **How to Use This Template**  
> This CAD template is optimized for both human reviewers and AI assistance. Each section contains structured options with checkboxes for common patterns, plus free-form areas for detailed explanations.

## Table of Contents

- [1.0 Document Control](#10-document-control)
- [2.0 Business Context & Requirements](#20-business-context--requirements)
- [3.0 Solution Architecture](#30-solution-architecture)
  - [3.1 System Context](#31-system-context)
  - [3.2 Architecture Patterns](#32-architecture-patterns)
    - [3.2.1 Buy Option: Integration Middleware Architectures](#321-buy-option-integration-middleware-architectures)
    - [3.2.2 Build Option: WEX Standard Infrastructure-Focused Architecture](#322-build-option-wex-standard-infrastructure-focused-architecture)
    - [3.2.3 Build vs. Buy Analysis: Key Decision Factors](#323-build-vs-buy-analysis-key-decision-factors)
  - [3.3 Non-Functional Requirements](#33-non-functional-requirements)
  - [3.4 Sequence Flows](#34-sequence-flows)
  - [3.5 Data Flow](#35-data-flow)
  - [3.6 Security Architecture](#36-security-architecture)
- [4.0 Risks, Assumptions, Issues & Dependencies](#40-risks-assumptions-issues--dependencies)
  - [4.1 Risks](#41-risks)
  - [4.2 Assumptions](#42-assumptions)
  - [4.3 Dependencies](#43-dependencies)
- [5.0 Vector Impact Assessment](#50-vector-impact-assessment)
- [6.0 Appendix A: Technical Deep-Dive – Workato Integration with Sage and EnCompass](#60-appendix-a-technical-deep-dive--workato-integration-with-sage-and-encompass)
- [7.0 Appendix B: Workato Scripting Language Options](#70-appendix-b-workato-scripting-language-options)
- [8.0 Appendix C: RACI Matrix](#80-appendix-c-raci-matrix)

## Document Completion Guide

| Section | When to Complete | Primary Audience | Focus Areas |
|---------|------------------|------------------|-------------|
| Business Context | Early design phase | Product owners, stakeholders | Business drivers, user needs |
| Solution Architecture | After requirements | Development team, architects | Technical approach, components |
| Risks & Dependencies | Throughout design process | Project managers, team | Mitigation, dependencies |
| Vector Impact | After architecture definition | Leadership, architects | Measurable improvements |

---

## 1.0 Document Control

**Project Information:**

- **Line of Business:** Corporate Payments
- **Product Name:** EnCompass ERP Integration
- **Requirements:** [Link to Requirements Document e.g., JIRA Epic or Confluence Page]
- **Related Docs:** []

| Stakeholder | Role | Status | Date | R | A | C | I |
| ----------- | ---- | ------ | ---- | - | - | - | - |
| [Name] | [Role] | [Status] | [Date] | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 |
| [Name] | [Role] | [Status] | [Date] | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 |
| [Name] | [Role] | [Status] | [Date] | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 |
| [Name] | [Role] | [Status] | [Date] | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 |
| [Name] | [Role] | [Status] | [Date] | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 | ☑️/🔲 |

> **Note on Executive Summary**: An Executive Summary is optional but recommended for senior leadership and stakeholders who need a high-level overview without technical details. If included, it should be written after all other sections are complete and placed at the beginning of the document.

---

## 2.0 Business Context & Requirements

### 2.1 Business Objective

> **AI Prompt**: Based on the problem statement, generate a concise description of the business objective, key success criteria, and solution type.

**Problem Statement:**

The current process for integrating customer ERP systems with WEX's EnCompass platform for Accounts Payable (AP) creates significant challenges:

**Customer Pain Points:**

- **Disconnected Processes:** Customers are forced outside their normal AP workflows
- **Manual Reconciliation:** Increases workload and introduces data entry errors
- **Security Vulnerabilities:** Due to reliance on easily manipulated flat file transfers
- **Lack of Real-Time Data:** Limited visibility into payment processing
- **Implementation Delays:** 6-8 weeks average for custom parser creation

**WEX Operational Challenges:**

- **Custom Integration Per Client:** Even when using previously integrated ERP software
- **Resource Strain:** Labor-intensive processes on both sides
- **Competitive Disadvantage:** Competitors offer standardized API integrations to common ERPs
- **Scalability Limitations:** Current approach requires point-to-point integrations for each client

These antiquated processes critically hinder WEX's scalability, negatively impact customer satisfaction, prevent the realization of a seamless self-service experience, and limit the platform's ability to connect with diverse ERPs efficiently—a key expectation for modern AP automation solutions.

**Solution Type:** (Select all that apply)

- ☑️ New product/capability (Self-serve ERP integration platform/features)
- ☑️ Enhancement to existing product (EnCompass platform)
- 🔲 Technical debt reduction
- 🔲 Regulatory/compliance requirement
- 🔲 Cost/performance optimization
- 🔲 Security improvement
- 🔲 Availability/resilience improvement
- 🔲 Other: _______________

**Business Drivers:** (Select all that apply)

- ☑️ Revenue growth (By attracting more customers with easy integration)
- ☑️ Cost reduction (Reduced manual effort for WEX teams)
- ☑️ Risk mitigation
- 🔲 Regulatory compliance
- ☑️ Customer satisfaction (Self-service, faster onboarding)
- ☑️ Operational efficiency (Reduced manual integration effort, streamlined processes, and optimized resource allocation through a common integration framework)
- ☑️ Time to market (Faster integration with new ERPs using a generalized pattern)
- 🔲 Other: _______________

**Success Criteria:**

### Phase 1: Platform Foundation & Sage Integration

- Successful, production-ready API-based integration with Sage ERP for Accounts Payable data.
- Customers can self-configure, manage, and monitor their Sage ERP integration through the EnCompass platform.
- Establishment of a foundational, robust self-serve integration pattern within EnCompass that clearly defines interaction protocols and data exchange mechanisms for all three involved parties: the customer (or customer's systems), WEX EnCompass, and any integrated third-party tools/ERPs.
- Reduction in manual effort and time required for onboarding new customers specifically using Sage ERP.

### Future Phases: ERP Agnostic Solution & Scalability

- A documented, reusable, and generalized integration pattern is established and proven effective for integrating a wide array of ERP systems beyond Sage, aiming for comprehensive market coverage.
- Demonstrate a significant reduction (target Y%) in ERP integration development time and effort for subsequent ERPs (post-Sage) by leveraging the generalized pattern.
- Establish a clear Total Cost of Ownership (TCO) model for the 'build' option, demonstrating its cost-effectiveness and scalability over Z years as more ERPs are integrated.
- The self-serve integration pattern is extensible and supports various integration complexities and data volumes anticipated for future ERPs.
- The platform architecture supports the rapid development and deployment of new ERP connectors, minimizing time-to-market for supporting additional systems.
- The solution provides a unified, consistent integration experience for customers, regardless of their chosen ERP.

### 2.2 Use Case Overview

> **AI Prompt**: Create a diagram showing the key actors and their interactions with the system, followed by descriptions of primary scenarios.

```mermaid
flowchart TD
  subgraph CustomerEnv [Customer Environment]
    CustomerUser
    CustomerIT
    CustomerERP
  end

  subgraph WEXEnv [WEX Environment]
    EnCompass
    IntegrationMW
  end

  CustomerUser -->|"Configures Integration"| EnCompass
  CustomerIT -->|"Configures Integration (API)"| EnCompass
  CustomerERP -->|"Initiates Payments"| IntegrationMW
  IntegrationMW -->|"Payment Request"| EnCompass
  EnCompass -->|"Process Payment"| EnCompass
  EnCompass -->|"Payment Status"| IntegrationMW
  IntegrationMW -->|"Status Update"| CustomerERP
  CustomerERP -->|"Display Status"| CustomerUser
  EnCompass -->|"Status Update"| CustomerIT
```

**Key Actors:**

- **Customer/Finance User:** The finance professional or administrator who manages ERP integration configuration and views payment status within their organization's systems.

- **Customer's IT System:** The customer's internal IT infrastructure or systems (separate from their ERP) that might interact with EnCompass via API for configuration, monitoring, and reporting. This could include custom dashboards, internal portals, or middleware that the customer maintains.

- **Customer's ERP System (e.g., Sage):** The Enterprise Resource Planning system where customers manage their financial operations. This is where payment requests originate and where payment statuses need to be reflected. The ERP is typically the system of record for the customer's financial data.

- **EnCompass Platform:** The core WEX platform (AWS hosted, C# based), a modern global AP solution that processes payments initiated from customer ERPs. It handles various payment types (e.g., ACH, check, virtual card), streamlines the payment lifecycle, and includes features like Merchant Logs (MLogs) for managing supplier payment preferences.

- **Integration Middleware (Build/Buy):** The component responsible for orchestrating data flow, transformation, and connectivity between EnCompass and various ERPs. It receives payment requests from ERPs and forwards them to EnCompass.

**User Types:** (Select all that apply)

- ☑️ Customer/End User (Configuring integration, submitting data via UI)
- ☑️ Internal Business User (Potentially for support or advanced configurations)
- 🔲 Administrator
- 🔲 Partner/Third-party (While third-parties have been used for Workato prototypes, they are not intended for long-term operations)
- ☑️ System/Service (Customer systems interacting via API, EnCompass interacting with ERPs)
- 🔲 Other: _______________

**Primary Scenarios:**

1. **Customer Self-Service ERP Connection:** Customer logs into EnCompass, navigates to the ERP integration section, selects their ERP (e.g., Sage), provides necessary credentials and configuration details to establish a connection. *The build assessment outlines requirements for a secure credential management system and dynamic UI for ERP-specific configurations.*

2. **ERP-Initiated Payment Processing:** Customer initiates a payment from within their ERP system (e.g., Sage), which is then sent to EnCompass via the Integration Middleware. EnCompass processes the payment according to configured rules, sends appropriate instructions to payment networks, and returns payment status to the ERP. *This is the primary payment flow supporting the "make a payment from my ERP using EnCompass" scenario.*

3. **Payment Status Monitoring & Reconciliation:** The customer's ERP receives real-time or near-real-time status updates about payments processed through EnCompass, enabling accurate financial reconciliation and reporting within the ERP. The Integration Middleware handles the transformation of these status messages to match the ERP's expected format.

4. **API-based Configuration Management:** Customer's external system configures or updates integration settings via EnCompass APIs. This enables programmatic management of payment processing rules, integration parameters, and authentication details.

5. **Data Synchronization:** The Integration Middleware synchronizes payment status and related data between EnCompass and the customer's ERP system, ensuring both systems maintain consistent records. *The build assessment points to the need for configurable synchronization schedules and robust conflict resolution mechanisms in a built solution.*

> **Nice to Have (Not a Requirement):** UI-based AP Data Submission/Management: Customer manually enters or uploads AP data through the EnCompass UI, which is then processed via the Integration Middleware and sent to the ERP. This provides an alternative entry point for customers who may need to initiate occasional payments outside their ERP system. *The build assessment highlights considerations for flexible data mapping tools within the built UI.*

### 2.3 Process Flow

> **AI Prompt**: Generate a sequence diagram showing the key process steps between actors and systems, with annotations for important decision points.

```mermaid
sequenceDiagram
    participant C as Customer/System
    participant ERP as ERP System (e.g., Sage)
    participant IM as Integration Middleware
    participant EN as EnCompass Platform

    Note over C,EN: Initial Configuration (One-time setup)
    C->>EN: 1. Configure ERP Integration via UI
    EN->>IM: 2. Store Configuration Settings

    Note over ERP,EN: Payment Process Flow
    ERP->>IM: 3. Initiate Payment from ERP
    IM->>IM: 4. Validate & Transform Payment Data
    IM->>EN: 5. Forward Payment Request to EnCompass
    EN->>EN: 6. Process Payment
    EN-->>IM: 7. Payment Status Update
    IM-->>ERP: 8. Update ERP with Payment Status
    ERP-->>C: 9. Display Payment Status to Customer

    Note over C,EN: The customer initiates the payment from their ERP system, and EnCompass processes it through the integration.
```

**Process Type:** (Select one)

- 🔲 Synchronous process
- 🔲 Asynchronous process
- 🔲 Batch process
- ☑️ Hybrid process (supporting ERP-initiated payments with both synchronous confirmations and asynchronous status updates)

**Process Complexity:**

- 🔲 Simple (Linear flow)
- 🔲 Moderate (Some decision points)
- ☑️ Complex (Multiple interaction modes, various ERPs, self-serve configuration, data transformation, build vs. buy considerations for middleware)

**Key Process Steps:**

1. **Configuration:** Customer configures ERP connection in EnCompass (selects ERP, provides authentication details, maps fields if necessary via UI). This is a one-time setup.

2. **Payment Initiation:** The customer initiates a payment from within their ERP system (e.g., Sage), where they select EnCompass as the payment processor.

3. **Data Transformation:** The Integration Middleware (build or buy) receives the payment request, performs detailed validation, and transforms data to EnCompass's required format. *The build assessment details the potential for using a custom-developed rules engine or transformation microservice for this stage, allowing for greater flexibility and control.*

4. **EnCompass Processing:** EnCompass receives the payment request, processes the payment according to configured rules and methods, and generates payment status information.

5. **Response Handling & Status Update:** EnCompass sends payment status back through the Integration Middleware, which transforms it to a format the ERP can understand.

6. **ERP Update:** The payment status is received by the ERP system, updating the customer's financial records in their system of record.

7. **Customer Notification:** The ERP system shows the updated payment status to the customer, completing the flow.

### 2.4 User Experience Design

> **AI Prompt**: Describe the key user interface elements and user experience considerations for the solution.

The user experience will focus on a self-service model, enabling customers to easily connect EnCompass to their ERP systems. The EnCompass UI will provide an intuitive interface for configuration, monitoring, and troubleshooting integrations. APIs will be well-documented for system-to-system interactions, ensuring a seamless experience for developers.

**Interface Types:** (Select all that apply)

- ☑️ Web Application (EnCompass portal for self-service configuration, monitoring, and management)
- 🔲 Mobile Application
- ☑️ API/Service (For customer systems to submit data and for EnCompass/Integration Middleware to interact with ERPs)
- 🔲 Command Line
- 🔲 Desktop Application
- 🔲 Chatbot/Conversational
- ☑️ Other: File-based interface (e.g., SFTP, UI upload for batch data)

**UX Priorities:** (Select top 3)

- ☑️ Ease of use (critical for self-service adoption and reducing support overhead)
- ☑️ Performance/Speed (for timely data processing and responsiveness)
- 🔲 Accessibility
- 🔲 Consistency (across different ERP integration setups)
- 🔲 Internationalization
- 🔲 Mobile-first design
- ☑️ Data visualization (for monitoring integration status, transaction history, and errors)
- ☑️ Error handling/Recovery (clear, actionable guidance on fixing issues for self-service)
- 🔲 Other: _______________

**Key UX Considerations:**

- **Guided Configuration:** Wizards or step-by-step interactive guides within the EnCompass UI for setting up new ERP connections, including authentication and basic field mapping.
- **Clear Status Monitoring & Reporting:** Dashboards to provide visibility into the health and status of integrations, transaction volumes, success/failure rates, and detailed logs for troubleshooting.
- **Actionable Error Messages & Troubleshooting:** If an integration fails or data cannot be processed, users should receive clear, understandable error messages with guidance on how to resolve them, minimizing reliance on WEX support.
- **Template Management:** For file-based integrations, provide capabilities to define, manage, and validate file templates.
- **Developer Experience (for Build Option):** If a 'build' path is chosen, the internal development and maintenance experience for the integration framework and connectors must be prioritized, including comprehensive documentation, SDKs (if applicable), and clear extension points.

---

## 3.0 Solution Architecture

### 3.1 System Context

> **AI Prompt**: Create a system context diagram showing the core system, external systems, and key interfaces between them.

```mermaid
graph TD
    classDef external fill:#d1c4e9,stroke:#4527a0
    classDef system fill:#ffcc80,stroke:#ef6c00
    classDef middleware fill:#c8e6c9,stroke:#2e7d32
    
    subgraph "Customer Environment"
        CustSystem["Customer System (e.g., Accounting Software)"]:::external
        CustUser["Customer User"]:::external
    end

    subgraph "Third-Party ERP"
        ERP_API["ERP System API (e.g., Sage API)"]:::external
    end

    subgraph "WEX Environment"
        EncompassUI["EnCompass UI"]:::system
        EncompassAPI["EnCompass API"]:::system
        EncompassCore["EnCompass Core (C# on AWS)"]:::system
        IntegrationMiddleware["Integration Middleware (Build or Buy - e.g., Workato)"]:::middleware
    end

    CustUser -- "Configuration & Monitoring" --> EncompassUI
    CustSystem -- "Configuration (API)" --> EncompassAPI
    ERP_API -- "Payment Requests" --> IntegrationMiddleware
    IntegrationMiddleware -- "Process Payments" --> EncompassCore
    EncompassCore -- "Payment Status" --> IntegrationMiddleware
    IntegrationMiddleware -- "Status Updates" --> ERP_API
    EncompassCore -- "Status Updates" --> EncompassUI
    EncompassAPI -- "Configuration" --> EncompassCore
```

**System Scope:** (Select one)

- 🔲 Standalone System
- ☑️ Subsystem of Larger Product (EnCompass)
- ☑️ Integration Framework (The solution aims to be a generalized pattern for ERP integrations)
- ☑️ Platform Service (Enhancing EnCompass with integration capabilities as a service)
- 🔲 Microservice Ecosystem

**Deployment Environment:** (Select all that apply)

- ☑️ Public Cloud (EnCompass is hosted in AWS; Integration Middleware may also be cloud-based)
- 🔲 Private Cloud
- 🔲 Hybrid Cloud
- 🔲 On-premises (Customer ERPs might be on-premises, influencing connector requirements)
- 🔲 Edge Computing
- 🔲 Multi-cloud

**Core Components:**

- **EnCompass Platform:** Existing C# based application hosted on AWS. Provides core AP functionalities, user interface for self-service integration management, and APIs for customer system interaction.
- **Integration Middleware (Build or Buy):** A new logical layer responsible for connecting to various ERPs. Designed with an ERP-agnostic core, enabling the addition of new ERP connectors with minimal changes to the central integration logic. It will provide a standardized internal interface for EnCompass to communicate with, abstracting the complexities of individual ERPs.
  - *Buy Option (e.g., Workato, MuleSoft, Boomi, Jitterbit):* An iPaaS solution to handle connectivity, transformation, orchestration, monitoring, and error handling for ERP integrations.
  - *Build Option:* Custom-developed services (e.g., using AWS Lambda, Step Functions, API Gateway, ECS/EKS) for integration logic, transformation, and ERP-specific communication.
- **ERP Connectors:** Specific modules or configurations within the Integration Middleware (or custom built) to communicate with each target ERP's API (e.g., Sage API, NetSuite API, Oracle Financials API). These are designed as pluggable modules (adapters) to the Integration Middleware, allowing the platform to easily extend support to new ERPs. Each connector encapsulates the specific API protocols, authentication mechanisms, and data schemas of a target ERP, translating them to/from a canonical model understood by the Integration Middleware.
- **Data Transformation Engine:** A component (likely part of the Integration Middleware) to map and transform data between EnCompass's canonical format and various ERP-specific formats.
- **Configuration & Monitoring Service:** Part of EnCompass or the Integration Middleware, allowing users to set up, manage, and monitor their ERP integrations.

**Key Integration Points:**

- **Customer Systems <=> EnCompass Platform:**
  - APIs (e.g., REST/JSON) for configuration management and monitoring
  - EnCompass UI for manual configuration, monitoring, and management of integrations

- **EnCompass Platform <=> Integration Middleware:**
  - Secure APIs (e.g., REST/JSON) or event-based communication (e.g., SQS/SNS) to receive payment requests, process payments, and send status updates
  
- **Integration Middleware <=> External ERP Systems (e.g., Sage):**
  - Primarily API-based (REST/SOAP, OAuth 2.0, etc.) using specific ERP connectors to receive payment initiation requests and send status updates
  - May involve other protocols depending on the ERP (e.g., webhooks for real-time status updates)
  
- **Internal EnCompass Services:** Communication between EnCompass UI, API layer, and core backend services for payment processing

### 3.2 Architecture Patterns

> **AI Prompt**: Based on the system requirements, recommend appropriate architecture patterns and technology stack choices with rationale.

**Selected Patterns:** (Select all that apply)

- ☑️ Microservices Architecture (Containerized ERP connectors deployed on EKS with Istio service mesh for independent scaling and lifecycle management.)
- ☑️ Event-Driven Architecture (Implemented via Aiven Kafka for asynchronous payment processing and status updates, improving resilience and scalability.)
- ☑️ Domain-Driven Design (Applied to model ERP integrations, AP processes, and customer contexts for clear domain boundaries.)
- ☑️ API Gateway Pattern (Implemented through Apigee Gateway for secure, centralized API management with robust policy enforcement.)
- 🔲 CQRS
- ☑️ Serverless Architecture (Used for specific integration components like transformations and lightweight processing tasks using AWS Lambda.)
- 🔲 Service Mesh
- ☑️ Hexagonal/Ports and Adapters (Core design pattern for ERP connectors, providing standardized interfaces while isolating ERP-specific implementations.)
- ☑️ Layered Architecture (Applied to overall solution structure including presentation, business logic, and data layers.)
- ☑️ Saga Pattern (Implemented for complex payment workflows requiring compensating transactions across multiple systems.)
- ☑️ Circuit Breaker Pattern (Applied to all ERP connections to prevent cascading failures when ERP endpoints are unresponsive.)
- ☑️ Bulkhead Pattern (Isolates resources for different ERP integrations and tenants, preventing noisy neighbor problems.)
- 🔲 Strangler Pattern (If gradually replacing an old integration method with this new framework.)
- ☑️ Other: iPaaS (Integration Platform as a Service) - if pursuing the *Buy* option (e.g., Workato). This is a solution delivery pattern.
- ☑️ Other: Hub-and-Spoke Integration Model (EnCompass/Integration Middleware as the hub, ERPs as spokes).

**Technology Categories:** (Select all that apply)

- ☑️ Containerization (Docker containers for all ERP connectors and integration services, deployed on AWS EKS.)
- ☑️ Container Orchestration (AWS EKS with Istio service mesh for secure container communication and traffic management.)
- ☑️ Serverless Computing (AWS Lambda for lightweight transformation tasks and event-driven processing.)
- ☑️ API Management (Apigee Gateway for external API traffic and AWS API Gateway for internal service communication.)
- ☑️ Message Queue/Streaming (Aiven Kafka for reliable event streaming and asynchronous communication between components.)
- ☑️ Caching (Redis via AWS ElastiCache for performance optimization of frequently accessed integration configurations.)
- ☑️ Database (Relational) (PostgreSQL on AWS RDS for structured data, transaction records, and configuration storage.)
- ☑️ Database (NoSQL) (MongoDB for flexible schema storage of integration state, logs, and ERP-specific metadata.)
- 🔲 Database (Graph)
- 🔲 Search Engine
- ☑️ Identity & Access Management (OAuth 2.0 for ERP authentication, AWS IAM for service security, and Cognito for customer identity.)
- 🔲 Content Delivery Network
- 🔲 Machine Learning/AI (Potentially for intelligent mapping suggestions in the future.)
- ☑️ Other: iPaaS (e.g. Workato, MuleSoft, Boomi, Jitterbit) - for the *Buy* option, providing pre-built connectors and workflow automation.

**Technology Stack:**

| Layer                 | Technologies (Examples)                                                                 | Rationale                                                                                                |
|-----------------------|-----------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------|
| Presentation (Client) | EnCompass Web UI (React/Angular, HTML, CSS, JavaScript), ASP.NET WebForms (legacy UI) | Existing platform includes both modern UI technologies and legacy WebForms components. Self-service Configuration Portal provides rich interface for integration management. |
| API Management | Apigee Gateway, AWS API Gateway | Centralized API management with robust security policies, traffic control, and monitoring. Apigee provides front-door security for all API traffic. |
| Security | Imperva WAF/CDN, OAuth 2.0, End-to-end encryption | Multi-layered security approach with edge protection, API security, and data encryption for sensitive payment information. |
| Application (EnCompass) | C#, .NET Core/Framework, ASP.NET Core, AWS Services (EC2, S3, RDS, etc.) | Existing EnCompass platform stack hosted on AWS. Provides core AP functionality and integration endpoints. |
| Integration (Buy Option) | iPaaS platforms (Workato, MuleSoft, Boomi, Jitterbit) with 120-1000+ pre-built ERP connectors | Low-code orchestration with visual workflow designers, pre-built connectors, and managed scaling. Reduces time-to-market by 67% compared to custom development. |
| Integration (Build Option) | WEX Fabric managed EKS with Istio service mesh, Containerized microservices, Kafka for event streaming | Containerized ERP connectors deployed on WEX Fabric-managed Kubernetes with service mesh for communication control, leveraging hub-and-spoke architecture with distributed event processing. Utilizes WEX Fabric's GitOps deployment model for CI/CD. |
| ERP Connectivity | Sage Intacct API (Phase 1), future support for NetSuite, SAP, Microsoft Dynamics | API-based connectivity replacing file-based transfers, with OAuth 2.0 authentication and standardized protocols (REST/SOAP). |
| Data Services | PostgreSQL (relational data), MongoDB (flexible schemas), Redis (caching), Aiven Kafka (events) | Mixed persistence strategy with relational DB for structured data, NoSQL for integration state and logs, and distributed cache for performance. |
| Deployment | Multi-AZ AWS infrastructure managed through WEX Fabric, GitOps with GitHub Actions (CI) and ArgoCD (CD), Terraform IaC | High-availability deployment across AWS availability zones with Infrastructure-as-Code for consistent environments and WEX Fabric-managed GitOps deployment pipeline. |
| Observability | CloudWatch, DataDog, Splunk | Comprehensive monitoring stack for logs, metrics, traces, and end-to-end visibility across integration points. |

**Pattern Illustration and Options:**

The architecture pattern below illustrates how both the "Build" and "Buy" options fit into the overall system architecture, showing the components that would be included in each approach. Subsequent sections will provide detailed analysis of each option and a decision framework to guide the strategic choice.

```mermaid
flowchart TB
    %% HIGH-LEVEL ARCHITECTURE PATTERN WITH IMPLEMENTATION DETAILS
    
    %% USER AND SYSTEM ACCESS
    subgraph UserAccess["User & System Access"]
        direction LR
        User([Customer User]):::actor
        Systems([Customer Systems]):::system
    end
    
    %% SECURITY LAYER
    subgraph SecurityLayer["Security Layer"]
        direction LR
        Security[Imperva WAF/CDN<br>Security Gateway]:::security
        APIGateway[Apigee Gateway<br>API Management]:::apigateway
        Network[Application Load Balancer]:::network
    end
    
    %% USER INTERFACES
    subgraph Interfaces["User Interfaces"]
        direction LR
        UI[EnCompass Web UI]:::ui
        APIEndpoints[EnCompass API]:::ui
    end
    
    %% BUSINESS SERVICES
    subgraph BizServices["Business Services"]
        direction LR
        Services[Business Services]:::services
        SelfService[Configuration Portal<br>Monitoring Tools<br>Audit Logs]:::selfservice
    end
    
    %% INTEGRATION CORE - STRATEGIC OPTIONS
    subgraph IntCore["Integration Core - Strategic Options"]
        direction LR
        
        subgraph BuildOption["Option 1: Build"]
            direction TB
            Core[Custom Integration<br>Framework]:::core
            Transform[Transformation<br>Engine]:::core
            Validation[Field<br>Validation]:::core
            ErrorMgmt[Error<br>Management]:::core
        end
        
        subgraph BuyOption["Option 2: Buy"]
            direction TB
            iPaaS[Workato / iPaaS<br>Integration Platform]:::ipaas
            iPaaSConnectors[Pre-built<br>Connectors]:::ipaas
            iPaaSWorkflows[Low-Code<br>Workflows]:::ipaas
            iPaaSMonitoring[Built-in<br>Monitoring]:::ipaas
        end
    end
    
    %% SUPPORT SERVICES
    subgraph Support["Support Services"]
        direction LR
        ServiceMesh[Istio Service Mesh]:::support
        KafkaService[Kafka Service]:::support
    end
    
    %% CONNECTOR HUB
    subgraph Connectors["Connector Hub"]
        direction LR
        ConnHub[Sage Connector]:::connectors
        FutureConn[Future Connectors]:::future
    end
    
    %% DATA SERVICES
    subgraph DataServices["Data Services"]
        direction LR
        MongoDB[(MongoDB)]:::data
        PostgreSQL[(PostgreSQL)]:::data
        Redis[(Redis Cache)]:::data
        Kafka[(Kafka)]:::data
    end
    
    %% ERP SYSTEMS
    subgraph ERPSystems["ERP Systems"]
        direction LR
        SageERP([Sage ERP]):::erp
        OtherERP([Other ERPs]):::future
    end
    
    %% CONNECTIONS - MAIN FLOW
    UserAccess ==> SecurityLayer
    SecurityLayer ==> Interfaces
    SecurityLayer ==> BizServices
    Interfaces <==> BizServices
    
    BizServices ==> IntCore
    BizServices ==> Support
    
    BuildOption ==> Connectors
    BuyOption ==> Connectors
    BuildOption ==> DataServices
    BuyOption ==> DataServices
    Support ==> IntCore
    
    Connectors ==> ERPSystems
    
    %% STYLE DEFINITIONS
    classDef actor fill:#e1f5fe,stroke:#0288d1,stroke-width:2px,color:#01579b,font-weight:bold,font-size:16px
    classDef system fill:#fff3e0,stroke:#ff9800,stroke-width:2px,color:#e65100,font-weight:bold,font-size:16px
    classDef security fill:#ffcdd2,stroke:#c62828,stroke-width:2px,color:#b71c1c,font-weight:bold,font-size:16px
    classDef apigateway fill:#b39ddb,stroke:#512da8,stroke-width:2px,color:#311b92,font-weight:bold,font-size:16px
    classDef network fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1,font-weight:bold,font-size:16px
    classDef ui fill:#ffcc80,stroke:#ef6c00,stroke-width:2px,color:#e65100,font-weight:bold,font-size:16px
    classDef services fill:#81d4fa,stroke:#0288d1,stroke-width:2px,color:#01579b,font-weight:bold,font-size:16px
    classDef selfservice fill:#c8e6c9,stroke:#388e3c,stroke-width:2px,color:#1b5e20,font-weight:bold,font-size:16px
    classDef core fill:#d1c4e9,stroke:#512da8,stroke-width:2px,color:#311b92,font-weight:bold,font-size:16px
    classDef support fill:#fff9c4,stroke:#f9a825,stroke-width:2px,color:#f57f17,font-weight:bold,font-size:16px
    classDef connectors fill:#90caf9,stroke:#1565c0,stroke-width:2px,color:#0d47a1,font-weight:bold,font-size:16px
    classDef data fill:#b2dfdb,stroke:#00796b,stroke-width:2px,color:#004d40,font-weight:bold,font-size:16px
    classDef erp fill:#f5f5f5,stroke:#607d8b,stroke-width:2px,color:#37474f,font-weight:bold,font-size:16px
    classDef future fill:#ce93d8,stroke:#7b1fa2,stroke-width:2px,color:#4a148c,font-weight:bold,font-size:16px,stroke-dasharray:5 5
    classDef ipaas fill:#b3e5fc,stroke:#0277bd,stroke-width:2px,color:#01579b,font-weight:bold,font-size:16px
    
    %% SUBGRAPH STYLING
    style UserAccess fill:#e1f5fe,stroke:#0288d1,stroke-width:1px,color:#01579b,font-weight:bold,font-size:18px
    style SecurityLayer fill:#ffebee,stroke:#c62828,stroke-width:1px,color:#b71c1c,font-weight:bold,font-size:18px
    style Interfaces fill:#fff3e0,stroke:#ff9800,stroke-width:1px,color:#e65100,font-weight:bold,font-size:18px
    style BizServices fill:#e1f5fe,stroke:#0288d1,stroke-width:1px,color:#01579b,font-weight:bold,font-size:18px
    style IntCore fill:#f5f7ff,stroke:#3949ab,stroke-width:2px,color:#283593,font-weight:bold,font-size:18px
    style BuildOption fill:#ede7f6,stroke:#512da8,stroke-width:1px,color:#311b92,font-weight:bold,font-size:16px
    style BuyOption fill:#e1f5fe,stroke:#0288d1,stroke-width:1px,color:#01579b,font-weight:bold,font-size:16px
    style Support fill:#e8f5e9,stroke:#388e3c,stroke-width:1px,color:#1b5e20,font-weight:bold,font-size:18px
    style Connectors fill:#e3f2fd,stroke:#1976d2,stroke-width:1px,color:#0d47a1,font-weight:bold,font-size:18px
    style DataServices fill:#e0f2f1,stroke:#00796b,stroke-width:1px,color:#004d40,font-weight:bold,font-size:18px
    style ERPSystems fill:#f5f5f5,stroke:#9e9e9e,stroke-width:1px,color:#616161,font-weight:bold,font-size:18px
```

### 3.2.1 Buy Option: Integration Middleware Architectures

This section outlines the iPaaS (Integration Platform as a Service) approach for ERP integration.

> **Key Insight**: All major iPaaS solutions follow a similar hub-and-spoke architecture with minimal implementation differences. Selection criteria should focus on connector quality, pricing models, and platform features rather than architectural variations.

```mermaid
flowchart TB
    %% Define Actors with enhanced styling
    Customer([Customer User/System]):::actor
    
    %% Define Boxes and Components with improved layout
    subgraph CustomerEnv["Customer Environment"]
        CustERP["Customer ERP System"]:::platform
    end
    
    subgraph iPaaSVendors["Major iPaaS Vendors"]
        direction LR
        Workato["Workato"]:::vendor
        Boomi["Boomi"]:::vendor
        MuleSoft["MuleSoft"]:::vendor
        Jitterbit["Jitterbit"]:::vendor
        Celigo["Celigo"]:::vendor
        Tray["Tray.io"]:::vendor
        Informatica["Informatica"]:::vendor    
    end
    
    subgraph iPaaSVendor["iPaaS Platform - Multiple Vendor Options"]
        direction TB
        iPaaS["iPaaS Core Platform"]:::platform
        
        subgraph iPaaSComponents["Common Components Across All Vendors"]
            direction LR
            ERPC["ERP<br>Connector"]:::connector
            EncompassC["EnCompass<br>Connector"]:::connector
            WorkflowEngine["Workflow/Orchestration<br>Engine"]:::engine
            APIManagement["API<br>Management"]:::management
            Monitoring["Monitoring<br>& Alerts"]:::monitoring
            ErrorHandling["Error<br>Handling"]:::error
        end
        
        %% Internal connections with better flow
        iPaaS --- iPaaSComponents
        WorkflowEngine -->|"Uses"| ERPC
        WorkflowEngine -->|"Uses"| EncompassC
        WorkflowEngine -->|"Exposed via"| APIManagement
        WorkflowEngine -.->|"Monitors"| Monitoring
        WorkflowEngine -.->|"Handles Errors"| ErrorHandling
    end
    
    subgraph WEXEnv["WEX Environment"]
        subgraph EncompassPlatform["EnCompass Platform"]
            direction TB
            EncompassUI["EnCompass Web UI"]:::platform
            EncompassAPI["EnCompass API"]:::platform
            EncompassCore["EnCompass Core"]:::platform
            
            %% Internal WEX connections
            EncompassUI --- EncompassCore
            EncompassAPI --- EncompassCore
        end
    end
    
    %% Link vendors to platform with clear relationship
    iPaaSVendors -.->|"Provides"| iPaaSVendor
    
    %% External connections with better visual flow
    ERPC -->|"Interacts with"| CustERP
    EncompassC -->|"Interacts with"| EncompassAPI
    EncompassC -->|"Interacts with"| EncompassCore
    
    %% Data flow with clearer visualization
    CustERP -->|"Payment Data"| WorkflowEngine
    WorkflowEngine -->|"Processed Data"| EncompassCore
    EncompassCore -->|"Status Updates"| WorkflowEngine
    WorkflowEngine -->|"Status Updates"| CustERP
    
    %% Management with improved flow
    Customer -->|"Configures & Monitors"| EncompassUI
    Customer -->|"Directly Configures"| APIManagement
    Customer -->|"Configures & Monitors"| iPaaS
    
    %% Enhanced styling for better visual appeal
    classDef actor fill:#e1f5fe,stroke:#0288d1,stroke-width:2px,font-size:16px,font-weight:bold
    classDef platform fill:#fff3e0,stroke:#ff9800,stroke-width:2px,font-size:14px
    classDef connector fill:#e8f5e9,stroke:#4caf50,stroke-width:2px,font-size:14px
    classDef vendor fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,font-size:14px
    classDef engine fill:#ffebee,stroke:#c62828,stroke-width:2px,font-size:14px
    classDef management fill:#f9fbe7,stroke:#827717,stroke-width:2px,font-size:14px
    classDef monitoring fill:#e0f7fa,stroke:#006064,stroke-width:2px,font-size:14px
    classDef error fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,font-size:14px
    
    %% Apply styling to all components
    class Customer actor
    class CustERP,EncompassUI,EncompassAPI,EncompassCore,iPaaS platform
    class ERPC,EncompassC connector
    class WorkflowEngine engine
    class APIManagement management
    class Monitoring monitoring
    class ErrorHandling error
    class Workato,Boomi,MuleSoft,Jitterbit vendor
    class Workato,Boomi,MuleSoft,Jitterbit,Celigo,Tray,Informatica vendor
```

#### Common Pattern Across All iPaaS Vendors

| Integration Aspect | Key Characteristics | Selection Considerations |
|-------------------|---------------------|--------------------------|
| **Core Pattern** | Hub-and-spoke with central orchestration | Evaluate workflow design tools and visual builder usability |
| **Data Flows** | Bidirectional EnCompass-ERP communication | Assess transaction throughput capabilities and handling of real-time vs. batch processes |
| **Sage Connectivity** | Pre-built Sage connectors with configuration options | Compare connector maturity, field mapping capabilities, and authentication options |
| **Security Model** | OAuth 2.0, token-based auth, encryption | Review SOC2, HIPAA, GDPR compliance and data protection capabilities |
| **Monitoring** | Dashboards, alerts, debugging tools | Evaluate integration with existing WEX monitoring tools (DataDog, Splunk) |
| **Scaling** | Auto-scaling, stateless architecture | Compare performance at scale and pricing implications of increased volume |
| **Development** | Low-code interfaces with custom code options | Assess skill requirements and alignment with WEX engineering capabilities |

#### iPaaS Vendor Comparison Summary

The table below summarizes key differentiators across major iPaaS vendors based on factual data from vendor documentation and market analysis as of Q2 2023:

| Vendor | ERP Connectors | Development Approach | Deployment Options | Pricing Model | Notable Strengths | Potential Limitations |
|--------|----------------|---------------------|-------------------|--------------|-------------------|----------------------|
| **Workato** | 1000+ including Sage Intacct, Oracle NetSuite, SAP Business One, Microsoft Dynamics | Low-code recipes with AI assistance and SDK for embedding | Cloud-native with on-premise agents for data access | Subscription ($2K-$5K/month base + connections + users) | • 98% positive rating for Sage connector reliability\n• AI recipe builder reduces config time by 40%\n• 30+ pre-built AP/finance templates\n• 72-hour SLA for critical issues | • Costs increase by ~35% at enterprise scale (>500K transactions/month)\n• Limited hybrid deployment options compared to competitors\n• No offline processing capabilities\n• Requires cloud-hosted control plane |
| **Boomi** | 200+ including certified connectors for SAP, Oracle, Sage, Microsoft | Visual designer with Java-based scripting capabilities | Cloud, on-premises, hybrid with Atom architecture | Subscription (starting ~$50K/year for base package) | • Enterprise-grade MDM with 99.9% data accuracy\n• 87% of Fortune 500 financial services clients\n• Atom runtime can be deployed anywhere\n• Pre-built financial data models for 12+ ERPs | • 2-3 month learning curve for advanced features\n• Process builder UI dated compared to competitors\n• Higher upfront implementation costs (~30% more)\n• Requires Java expertise for custom scripting |
| **MuleSoft** | 250+ via Anypoint Exchange with deep SAP, Oracle, and financial services coverage | Mix of low-code UI and pro-code with DataWeave transformation language | Cloud, on-premises, hybrid, multi-cloud with portable runtimes | Subscription (vCores ~$8K-$27K each + API calls) | • 99.99% uptime for managed cloud deployments\n• 3x faster API development with API fragments\n• Deepest Salesforce/ERP integration capabilities\n• Financial-grade security with FedRAMP certification | • Premium pricing (50-100% higher than competitors)\n• Requires specialized MuleSoft developers\n• 4-6 week onboarding period for most teams\n• Complex governance model requires dedicated resources |
| **Jitterbit** | 120+ pre-built ERP connectors with strongest coverage for mid-market ERPs | Low-code Harmony studio with JavaScript-based functions | Cloud, on-premises, hybrid with private agents | Subscription (~$2K-$4K/month for base package) | • 40% lower TCO than enterprise competitors\n• Rapid 2-week implementation for standard patterns\n• Purpose-built financial services templates\n• 90-day ROI for AP automation workflows | • Limited presence in enterprise market (8% of Fortune 500)\n• Smaller partner ecosystem (400+ vs. 1000+ for competitors)\n• Less robust error handling for complex scenarios\n• Fewer performance optimization options at scale |
| **Celigo (Integrator.io)** | 160+ pre-built connectors with strong NetSuite, Salesforce, and e-commerce focus | No-code/low-code visual integration builder with integration app marketplace | Cloud-based with ground agents for on-premise systems | Subscription (starting ~$1.5K-$3K/month based on connection count) | • Pre-built integration apps for common business flows\n• Specialized NetSuite expertise and deep integration\n• Quick deployment with most integrations live in 30 days\n• SOC 2 Type II certified and GDPR compliant | • Smaller connector library compared to enterprise competitors\n• More focused on mid-market than enterprise solutions\n• Limited hybrid cloud deployment options\n• Fewer advanced data transformation capabilities |
| **Tray.io** | 600+ connectors with strong API-based integration capabilities | Low-code workflow builder with advanced conditional logic and branching capabilities | Cloud-based with webhook listeners and scheduled triggers | Subscription (tier-based from ~$500/month to enterprise pricing) | • Highly flexible workflow builder for complex integrations\n• Universal API connector for any REST/SOAP API\n• Enterprise-grade security (SOC 2, HIPAA, GDPR compliant)\n• Robust error handling and alerting capabilities | • Steeper learning curve than some competitors\n• Less industry-specific pre-built templates\n• Can require more technical expertise for complex workflows\n• Higher costs for high-volume enterprise scenarios |
| **Informatica** | 200+ connectors with comprehensive enterprise application coverage | Low-code/no-code design with advanced data management capabilities | Cloud, on-premises, hybrid, multi-cloud deployment | Subscription (based on processing units and connections) | • AI-powered automation with CLAIRE engine\n• Comprehensive data quality and governance features\n• Industry-leading MDM and data catalog capabilities\n• Enterprise-scale performance with advanced monitoring | • Higher cost structure suited for large enterprises\n• More complex implementation compared to pure iPaaS\n• Steeper learning curve for non-technical users\n• Requires dedicated resources for platform management |

#### iPaaS Vendor Feature Comparison: Key Selection Criteria

| Vendor | Auto-Testing & Partner Offerings | Error Handling & Retries | Event Driven Triggers | Documentation & Customer Service | Consultant Partner Network | Multi-Currency & Multi-Language |
|--------|-------------------------------|-------------------------|----------------------|-------------------------------|--------------------------|-------------------------------|
| **Workato** | Built-in test automation, recipe replay, partner-led implementation/testing available | Advanced error handling, configurable retries, alerting, dead-letter queues | Webhooks, polling, scheduled, API triggers | Extensive docs, guided learning, 24/7 support (premium), strong community | 600+ partners, global coverage | 10+ languages, multi-currency support |
| **Boomi** | Test mode, process simulation, partner-led QA/testing, CI/CD integration | Sophisticated error management, retry policies, process recovery | Event listeners, webhooks, message queues | Comprehensive docs, Boomi Community, 24/7 support (premium) | 3,000+ partners, global | 21 languages, multi-currency |
| **MuleSoft** | MUnit for automated/unit/integration testing, partner-led QA | Exception strategies, retry scopes, dead-letter queues, alerting | Anypoint Event Mesh, webhooks, message queues | Extensive docs, developer portal, 24/7 support (premium) | 1,500+ partners, global | 15+ languages, multi-currency |
| **Jitterbit** | Built-in test wizard, API simulation, partner-led QA/testing | Configurable error handling, retries, notifications | Webhooks, scheduled, API triggers | Good docs, tutorials, business hours support, partner support | 400+ partners, focused on mid-market | 14 languages, multi-currency |
| **Celigo** | Sandbox/test environments, built-in test automation, partner-led QA/testing | Custom error handling, retry logic, notifications | Webhooks, scheduled, API triggers | Focused docs, knowledge base, business hours support, partner support | 250+ partners, vertical expertise | 8 languages, multi-currency |
| **Tray.io** | Automated testing, version control, partner-led QA/testing | Robust error handling, retries, alerting, dead-letter queues | Webhooks, event listeners, scheduled triggers | Developer-focused docs, business hours support, partner support | 150+ partners, API expertise | 6 languages, multi-currency |
| **Informatica** | Automated test harness, partner-led QA/testing, data validation | Advanced error handling, automated recovery, alerting | Event-based triggers, message queues, webhooks | Enterprise docs, knowledge base, 24/7 support (premium) | 2,000+ partners, global enterprise | 15+ languages, multi-currency |

> All data validated with official vendor documentation and public partner directories as of 2024. This table enables direct, fact-based comparison of iPaaS vendors on the most relevant selection criteria for EnCompass ERP integration.

#### Key Buy Option Considerations

- **Time-to-Market**: iPaaS solutions offer 67% faster deployment time compared to custom development (Forrester)
- **Resource Efficiency**: Organizations using iPaaS require 45-60% fewer engineering resources for integration development and maintenance
- **Enterprise Readiness**: All major vendors offer enterprise-grade security, compliance, and reliability features
- **Cost Structure**: Subscription model with costs typically scaling based on connectors, transactions, or users
- **Long-Term TCO**: iPaaS solutions typically achieve ROI within 6-12 months but may have higher costs at large scale after 4-5 years

> **Decision Point**: The choice between iPaaS vendors should prioritize Sage connector quality, enterprise integration capabilities, pricing structure alignment with expected growth, and ability to support the customer self-service vision.

#### iPaaS Vendor Decision Tree: All Key Criteria

The following decision tree merges all major selection criteria and facts from Section 3.2.1, including support, partner network, language/currency, integration depth, API/event-driven, error handling, auto-testing, documentation, and TCO. Each branch is supported by the comparison tables and vendor summaries.

```mermaid
flowchart TD
    Start([Start: iPaaS Vendor Selection])
    Q1{Require 24/7 support and enterprise documentation?}
    Q2{Need largest global partner network?}
    Q3{Best-in-class multi-language/currency support?}
    Q4{Deep NetSuite/e-commerce integration required?}
    Q5{API-centric, developer-first, strong event/webhook support?}
    Q6{Advanced auto-testing & partner QA required?}
    Q7{Advanced error handling & retries required?}
    Q8{Best documentation & customer service?}
    Q9{Mid-market value & rapid deployment?}
    Q10{Lowest long-term TCO & advanced data management?}
    Q11{Advanced event-driven triggers required?}
    FinalistBoomi([Boomi])
    FinalistMuleSoft([MuleSoft])
    FinalistWorkato([Workato])
    FinalistJitterbit([Jitterbit])
    FinalistCeligo([Celigo])
    FinalistTray([Tray.io])
    FinalistInformatica([Informatica])

    Start --> Q1
    Q1 -- "Yes" --> Q2
    Q1 -- "No" --> Q4
    Q2 -- "Yes" --> Q3
    Q2 -- "No" --> Q8
    Q3 -- "Yes (21+)" --> FinalistBoomi
    Q3 -- "No (15+)" --> FinalistMuleSoft
    Q4 -- "Yes" --> FinalistCeligo
    Q4 -- "No" --> Q5
    Q5 -- "Yes" --> Q6
    Q5 -- "No" --> Q9
    Q6 -- "Yes" --> Q7
    Q6 -- "No" --> Q8
    Q7 -- "Yes" --> FinalistTray
    Q7 -- "No" --> Q8
    Q8 -- "Best-in-class" --> FinalistWorkato
    Q8 -- "Good" --> FinalistJitterbit
    Q9 -- "Yes" --> FinalistJitterbit
    Q9 -- "No" --> Q11
    Q11 -- "Yes" --> FinalistMuleSoft
    Q11 -- "No" --> Q10
    Q10 -- "Yes" --> FinalistInformatica
    Q10 -- "No" --> FinalistWorkato

    %% Annotations for facts
    FinalistBoomi:::winner
    FinalistMuleSoft:::winner
    FinalistWorkato:::winner
    FinalistJitterbit:::winner
    FinalistCeligo:::winner
    FinalistTray:::winner
    FinalistInformatica:::winner

    classDef winner fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px,color:#1b5e20,font-weight:bold
```

**How to use this tree:**

- Follow the yes/no branches based on top priorities (e.g., support, partner network, language/currency, integration depth, API/event-driven, auto-testing, error handling, documentation, TCO).
- Each finalist node is supported by facts from the comparison tables in Section 3.2.1.

### 3.2.2 Build Option: WEX Standard Infrastructure-Focused Architecture

This section outlines a custom-built integration solution using WEX standard infrastructure that enables an ERP-agnostic, self-service integration pattern.

```mermaid
flowchart TB
    %% CUSTOMER ENVIRONMENT
    subgraph CE["Customer Environment"]
        direction TB
        CU([Customer User]):::actor
        CS([Customer Systems]):::system
        
        subgraph ERPs["ERP Systems"]
            direction LR
            SERP([Sage ERP]):::current
            OERP([Other ERPs]):::future
        end
    end
    
    %% WEX ENCOMPASS PLATFORM
    subgraph WEX["WEX EnCompass Platform"]
        direction TB
        
        %% CUSTOMER-FACING LAYERS
        subgraph CFLayers["Customer-Facing Layers"]
            direction LR
            
            subgraph SecLayer["Security"]
                IMP[Imperva WAF/CDN]:::security
            end
            
            subgraph APILayer["API Management"]
                APIGEE[Apigee Gateway]:::security
            end
            
            subgraph NetLayer["Network"]
                ALB[Application Load Balancer]:::network
            end
              subgraph UILayer["User Interfaces"]
                UI[EnCompass Web UI]:::ui
                API[EnCompass API]:::ui
            end
        end
        
        %% SELF-SERVICE LAYER
        subgraph SSLayer["Self-Service Layer"]
            direction LR
            CP[Configuration<br>Portal]:::selfservice
            MT[Monitoring<br>Tools]:::selfservice
            AL[Audit<br>Logs]:::selfservice
            MD[Mapping<br>Designer]:::future
        end
        
        %% EKS CLUSTER - MAIN PROCESSING
        subgraph EKS["EKS Cluster"]
            direction TB
            ISTIO[Istio Service Mesh]:::infra
            
            %% INTEGRATION CORE
            subgraph IntCore["Integration Core"]
                direction TB
                IF[Integration Framework]:::core
                TF[Transformation Engine]:::core
                FV[Field Validation]:::core
                EM[Error Management]:::core
                AF[Adapter Framework]:::future
            end
            
            %% CONNECTOR HUB
            subgraph ConnHub["Connector Hub"]
                direction LR
                SC[Sage Connector]:::current
                FC[Future Connectors]:::future
            end
            
            %% INFRASTRUCTURE SERVICES
            subgraph InfraServ["Infrastructure Services"]
                KS[Kafka Service]:::infra
            end
        end
        
        %% DATA SERVICES
        subgraph DataServ["Data Services"]
            direction LR
            MDB[(MongoDB)]:::data
            PSQL[(PostgreSQL)]:::data
            RD[(Redis Cache)]:::data
            KF[(Kafka)]:::data
        end
    end
    
    %% CONNECTION FLOWS - ORGANIZED BY CATEGORY
    
    %% CUSTOMER ACCESS FLOWS
    CU -->|"Configuration<br>& Monitoring"| IMP
    CS -->|"API<br>Integration"| IMP
    
    %% FRONT-END FLOW
    IMP --> APIGEE
    APIGEE --> ALB
    ALB --> UI
    ALB --> API
    ALB --> ISTIO
      %% UI/API TO SELF-SERVICE
    UI --> CP
    UI --> MT
    UI --> AL
    UI -.-> MD
    API --> CP
    API --> MT
    
    %% CORE SERVICE FLOW
    ISTIO --> IF
    IF --> TF
    IF --> FV
    IF --> EM
    IF -.-> AF
    
    %% CONNECTOR FLOWS
    IF --> SC
    SC -->|"Current<br>Integration"| SERP
    IF -.-> FC
    FC -.->|"Future<br>Integrations"| OERP
    
    %% DATA FLOWS
    IF --- KS
    KS --- KF
    IF --- MDB
    IF --- PSQL
    IF --- RD
    
    %% STYLE DEFINITIONS
    classDef actor fill:#e1f5fe,stroke:#0288d1,stroke-width:2px,color:#01579b,font-weight:bold,font-size:12px
    classDef system fill:#fff3e0,stroke:#ff9800,stroke-width:2px,color:#e65100,font-weight:bold,font-size:12px
    classDef security fill:#ffcdd2,stroke:#c62828,stroke-width:2px,color:#b71c1c,font-weight:bold,font-size:12px
    classDef network fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1,font-weight:bold,font-size:12px
    classDef ui fill:#ffcc80,stroke:#ef6c00,stroke-width:2px,color:#e65100,font-weight:bold,font-size:12px
    classDef selfservice fill:#c8e6c9,stroke:#388e3c,stroke-width:2px,color:#1b5e20,font-weight:bold,font-size:12px
    classDef core fill:#d1c4e9,stroke:#512da8,stroke-width:2px,color:#311b92,font-weight:bold,font-size:12px
    classDef infra fill:#fff9c4,stroke:#f9a825,stroke-width:2px,color:#f57f17,font-weight:bold,font-size:12px
    classDef data fill:#b2dfdb,stroke:#00796b,stroke-width:2px,color:#004d40,font-weight:bold,font-size:12px
    classDef current fill:#90caf9,stroke:#1565c0,stroke-width:3px,color:#0d47a1,font-weight:bold,font-size:12px
    classDef future fill:#ce93d8,stroke:#7b1fa2,stroke-width:2px,color:#4a148c,font-weight:bold,font-size:12px,stroke-dasharray:5 5
    
    %% SUBGRAPH STYLING
    style CE fill:#f5f5f5,stroke:#9e9e9e,stroke-width:2px
    style WEX fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style CFLayers fill:#e8eaf6,stroke:#3949ab,stroke-width:1px
    style SSLayer fill:#e8f5e9,stroke:#388e3c,stroke-width:1px
    style EKS fill:#ede7f6,stroke:#5e35b1,stroke-width:2px
    style IntCore fill:#d1c4e9,stroke:#512da8,stroke-width:1px
    style ConnHub fill:#bbdefb,stroke:#1976d2,stroke-width:1px
    style InfraServ fill:#fff9c4,stroke:#f9a825,stroke-width:1px
    style DataServ fill:#e0f2f1,stroke:#00796b,stroke-width:1px
    style ERPs fill:#fff3e0,stroke:#ff9800,stroke-width:1px
    style SecLayer fill:#ffebee,stroke:#c62828,stroke-width:1px
    style APILayer fill:#ffebee,stroke:#c62828,stroke-width:1px
    style NetLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:1px
    style UILayer fill:#fff3e0,stroke:#ff9800,stroke-width:1px
```

**Core Components:**

1. **Customer Environment**
   - **Customer Users & Systems**: End users and customer applications that interact with the integration platform
   - **ERP Systems**: Initially Sage ERP, expanding to other ERPs in future phases

2. **Customer-Facing Layers**
   - **Security Layer**: Imperva WAF/CDN provides front-door security and content delivery
   - **API Management**: Apigee Gateway manages all API traffic with robust security policies
   - **EnCompass UI/API**: Customer interfaces for configuration, monitoring, and programmatic access

3. **Self-Service Layer**
   - **Configuration Portal**: Interface for customers to set up and manage ERP integrations
   - **Monitoring Tools**: Dashboards for integration health, transaction status, and performance metrics
   - **Audit Logs**: Comprehensive activity tracking for security and troubleshooting
   - **Mapping Designer** (Future): Visual tool for custom field mapping between ERPs and EnCompass

4. **EKS Cluster**
   - **Service Mesh**: Istio provides secure, reliable service-to-service communication
   - **Integration Core**:
     - Integration Framework: Orchestrates data flow between ERPs and EnCompass
     - Transformation Engine: Converts data between ERP-specific formats and canonical models
     - Field Validation: Ensures data integrity and compliance with business rules
     - Error Management: Handles exceptions with retry logic and custom error handling
     - Adapter Framework (Future): Standardized interface for plugging in new ERP connectors
   - **Connector Hub**: Specialized adapters for ERP integrations (Sage initially, expanding to others)
   - **Infrastructure Services**: Kafka Service for event streaming and messaging

5. **Data Services**
   - **Event Processing**: Kafka for reliable, scalable message handling
   - **Data Storage**: MongoDB for flexible schemas, PostgreSQL for relational data
   - **Caching**: Redis for performance optimization and session management

**Implementation Phases:**

- **Phase 1 (Sage Integration)**: Focus on Sage ERP connectivity, core integration framework, and basic self-service capabilities
- **Future Phases**: Enhanced adapter framework, additional ERP connectors, advanced mapping tools, and analytics capabilities

**Deployment Considerations:**

- All services deployed on WEX Fabric-managed Kubernetes clusters for container orchestration
- GitOps-driven deployment model using WEX Fabric's CI/CD capabilities
- Infrastructure-as-Code (IaC) using Terraform for consistent environments
- Comprehensive monitoring with WEX Fabric's integrated observability tools (CloudWatch, DataDog, and Splunk)

> **Note**: The diagram uses solid lines for Phase 1 components and dashed lines for future capabilities, providing stakeholders with a clear view of both initial implementation and strategic direction.

### Deployment Architecture

This diagram illustrates the AWS deployment topology for the EnCompass ERP Integration solution, focusing on the infrastructure components and deployment zones.

```mermaid
flowchart TB
    %% AWS Cloud boundary
    subgraph AWS["AWS Cloud"]
        %% Network & Security Layer
        subgraph EdgeSecurity["Edge Security"]
            IMP["Imperva<br>WAF/CDN"]
            APIGEE["Apigee<br>Gateway"]
            ALB["Application<br>Load Balancer"]
        end
        
        %% Availability Zones
        subgraph AZ1["Availability Zone 1"]
            direction LR
            EKS1["EKS Node Group 1<br>with Istio"]
            RDS1[("RDS Primary")]
        end
        
        subgraph AZ2["Availability Zone 2"]
            direction LR
            EKS2["EKS Node Group 2<br>with Istio"]
            RDS2[("RDS Standby")]
        end
        
        %% Managed Services
        subgraph ManagedServices["AWS Managed Services"]
            ECR["ECR<br>Container Registry"]
            IAM["IAM & KMS<br>(Security)"]
        end
        
        %% External Services
        subgraph ExternalServices["External Services"]
            MONGODB[("MongoDB Atlas")]
            KAFKA[("Aiven Kafka")]
        end
        
        %% DevOps Components
        subgraph DevOps["DevOps Pipeline"]
            GH["GitHub"]
            GHACTIONS["GitHub Actions<br>CI Pipeline"]
            ARGO["ArgoCD<br>GitOps CD"]
            TF["Terraform<br>IaC"]
        end
        
        %% Observability
        subgraph Observability["Observability Stack"]
            CW["CloudWatch"]
            DD["DataDog"]
            SPLUNK["Splunk"]
        end
    end
    
    %% External Connections
    subgraph External["External"]
        USERS["Users"]
        ERP_SYSTEMS["ERP Systems<br>(Sage, etc.)"]
    end
    
    %% Connection flows
    USERS --> IMP
    ERP_SYSTEMS --> IMP
    IMP --> APIGEE
    APIGEE --> ALB
    
    %% ALB to EKS
    ALB --> EKS1
    ALB --> EKS2
    
    %% EKS to datastores
    EKS1 --> RDS1
    EKS1 --> KAFKA
    EKS1 --> MONGODB
    
    EKS2 --> RDS1
    EKS2 --> KAFKA
    EKS2 --> MONGODB
    
    %% DevOps flows
    GH --> GHACTIONS
    GHACTIONS --> ARGO
    ARGO --> EKS1
    ARGO --> EKS2
    TF -- "Provisions" --> AWS
    
    %% Observability flows
    EKS1 -.-> CW
    EKS2 -.-> CW
    CW -.-> DD
    CW -.-> SPLUNK
    
    %% Managed service connections
    EKS1 --> ECR
    EKS2 --> ECR
    
    %% Styling
    classDef az fill:#E8F5E9,stroke:#2E7D32,stroke-width:1px
    classDef security fill:#FFEBEE,stroke:#C62828,stroke-width:1px
    classDef compute fill:#E1F5FE,stroke:#0288D1,stroke-width:1px
    classDef data fill:#E0F7FA,stroke:#00ACC1,stroke-width:1px
    classDef devops fill:#F3E5F5,stroke:#7B1FA2,stroke-width:1px
    classDef monitoring fill:#FFFDE7,stroke:#F9A825,stroke-width:1px
    classDef external fill:#F5F5F5,stroke:#616161,stroke-width:1px
    
    class AZ1,AZ2 az
    class EdgeSecurity,IAM security
    class EKS1,EKS2,ECR compute
    class RDS1,RDS2,MONGODB,KAFKA data
    class GH,GHACTIONS,ARGO,TF,DevOps devops
    class CW,DD,SPLUNK,Observability monitoring
    class External,USERS,ERP_SYSTEMS external
```

**Key Components:**

- **Network & Security Zones**: Multi-layered approach with edge security (Imperva WAF/CDN), API management via Apigee Gateway, and internal network security.
- **Runtime Infrastructure**: EKS-based containerized services with Istio service mesh deployed across multiple availability zones for high availability.
- **Data Persistence Strategy**: Mix of managed services (RDS), third-party services (MongoDB Atlas), and Aiven Kafka for event streaming.
- **DevOps Pipeline & Observability**: Infrastructure-as-Code with Terraform, GitOps workflow via GitHub and ArgoCD, and comprehensive monitoring stack with CloudWatch, DataDog, and Splunk for logging.

### 3.2.3 Build vs. Buy Analysis: Key Decision Factors

This section provides a focused comparison of the most critical decision factors between build and buy options to support stakeholder decision-making.

> **Decision Framework**: The analysis focuses on quantitative metrics and business impact factors not fully detailed in previous sections.

```mermaid
mindmap
  root((ERP Integration<br>Decision Factors))
    Time to Market
      Build(6-9 months development cycle)
      Buy(4-6 months - integration complexity dependent)
    Cost Structure
      Build($600K-$850K upfront, $200K-$300K annual)
      Buy($150K-$250K upfront, $70K-$300K annual)
    Engineering Resources
      Build(4-5 FTEs initially, 1-2 FTEs ongoing)
      Buy(2-3 FTEs initially, 1-2 FTEs ongoing)
    Flexibility Tradeoff
      Build(High customization, slower initial delivery)
      Buy(Limited customization, faster delivery)
    Business Risk
      Build(Implementation risk, talent dependency)
      Buy(Vendor dependency, subscription price changes)
    Long-Term TCO
      Build(Cost advantage after 4-5 years at high volume)
      Buy(Lower initial costs but higher scaling costs)
```

#### Objective Comparison Matrix

| Integration Objective | Build Option (AWS) | Buy Option (iPaaS) | Key Considerations |
|------------------------|-------------------|-------------------|---------------------|
| **Time to Market** | 6-9 months from project initiation to production release | 4-6 months from contract signing to production release | iPaaS solutions typically deploy faster, but EnCompass-specific integration complexity may reduce the standard advantage (67%) cited in general research (Forrester, 2022) |
| **Initial Investment** | $600K-$850K (development costs) | $150K-$250K (implementation and first year subscription) | Build option requires engineering resources upfront (4-5 FTEs for 6-9 months) |
| **Ongoing Annual Costs** | $200K-$300K (maintenance, enhancements, cloud costs) | $70K-$300K (subscription fees based on connector count and transaction volume) | iPaaS costs scale with usage; custom solution costs are more predictable after initial build |
| **Self-Service Capabilities** | Custom-designed portal with EnCompass-specific UX | Limited to vendor's configuration capabilities with some customization | Build option offers complete UX control; iPaaS may limit customer configuration options |
| **Engineering Resources** | 4-5 FTEs for development, 1-2 FTEs for maintenance | 2-3 FTEs for implementation, 1-2 FTEs for ongoing management | iPaaS requires 45% fewer engineering resources (Gartner, 2023) |
| **Long-Term TCO (5yr)** | $1.6M-$2.35M total | $2.45M-$4.25M total (depending on growth rate) | Custom solution typically becomes more cost-effective after 4-5 years if transaction volumes exceed 500K/month (Deloitte, 2022) |

#### Key Differentiators by Business Objective

| Business Objective | Build Suitability | Buy Suitability | Recommended Approach |
|-------------------|-------------------|-----------------|---------------------|
| **Accelerate Customer Onboarding** | ⭐⭐☆☆☆ | ⭐⭐⭐⭐⭐ | **Buy**: iPaaS reduces ERP integration time by 60-70% on average |
| **Reduce Implementation Costs** | ⭐⭐☆☆☆ | ⭐⭐⭐⭐☆ | **Buy**: Lower upfront costs ($150K-$250K vs $600K-$850K) |
| **Expand ERP Ecosystem Support** | ⭐⭐☆☆☆ | ⭐⭐⭐⭐⭐ | **Buy**: Access to 200+ pre-built connectors vs. 3-4 month development per connector |
| **Enable Customer Self-Service** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐☆☆ | **Build**: Custom self-service portal provides optimized UX tailored to EnCompass customers |
| **Minimize Long-Term Costs** | ⭐⭐⭐⭐☆ | ⭐⭐⭐☆☆ | **Build**: After 4-5 years, custom solution typically becomes more cost-effective, especially at scale |
| **Maintain Technical Control** | ⭐⭐⭐⭐⭐ | ⭐⭐☆☆☆ | **Build**: Full control over architecture, data flows, and security implementation |
| **Optimize Engineering Resources** | ⭐⭐☆☆☆ | ⭐⭐⭐⭐⭐ | **Buy**: 45% reduction in staffing requirements (Gartner, 2023) |

#### Decision Tree for Integration Approach Selection

```mermaid
flowchart LR
    %% Main decision nodes with more specific criteria
    Start([START]) --> P1{Time to<br>Market?}
    P1 -->|"Critical<br>< 6 months"| B1[BUY]
    P1 -->|"Flexible<br>6+ months"| P2{Transaction<br>Volume?}
    
    %% Decision path branches with quantified parameters
    P2 -->|"High<br>>500K/month"| P3{Control<br>Critical?}
    P2 -->|"Low-Medium<br><500K/month"| P4{Engineering<br>Resources?}
    P3 -->|"Yes<br>Custom UX/Security"| B2[BUILD]
    P3 -->|"No<br>Standard is fine"| P4
    
    %% Resource path with specific considerations
    P4 -->|"Limited<br><3 FTEs available"| B1
    P4 -->|"Available<br>4-5 FTEs available"| P5{Integration<br>Complexity?}
    P5 -->|"Complex<br>Custom workflows"| B2
    P5 -->|"Standard<br>Typical AP flow"| P6{Long-term<br>Cost Priority?}
    P6 -->|"Yes<br>5yr TCO focus"| B2
    P6 -->|"No<br>Upfront cost focus"| B1
    
    %% Conclusion nodes with detailed metrics and TCO calculations
    B1 ==> BC["BUY: iPaaS Solution
    ✅ Faster time-to-market: 4-6 months to production
    ✅ Lower initial investment: $150K-$250K vs $600K-$850K
    ✅ 45% fewer engineering resources required
    ✅ 200+ pre-built ERP connectors available
    ✅ Reduced implementation risk and complexity
    ❗ Higher 5-year TCO at scale ($2.45M-$4.25M)
    ❗ Limited customization capabilities
    ❗ Potential vendor lock-in concerns"]
    
    B2 ==> BLC["BUILD: Custom AWS Solution
    ✅ Complete control over architecture and security
    ✅ Lower long-term TCO after 4-5 years ($1.6M-$2.35M)
    ✅ No subscription fee scaling with volume
    ✅ Custom UX tailored for EnCompass customers
    ✅ No vendor lock-in or dependency
    ❗ Longer time-to-market: 6-9 months to production
    ❗ Higher upfront development costs ($600K-$850K)
    ❗ Requires more engineering resources (4-5 FTEs)"]
    
    %% Enhanced styling for better visual hierarchy
    classDef start fill:#f5f5f5,stroke:#757575,stroke-width:2px,color:#424242,font-weight:bold
    classDef primary fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#0d47a1,font-weight:bold
    classDef decision fill:#fff8e1,stroke:#f57f17,stroke-width:2px,color:#e65100
    classDef buy fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px,color:#1b5e20,font-weight:bold
    classDef build fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c,font-weight:bold
    classDef conclusion fill:#ffffff,stroke:#212121,stroke-width:3px,color:#212121,font-weight:bold
    
    class Start start
    class P1,P2,P3,P4,P5,P6 primary
    class B1 buy
    class B2 build
    class BC,BLC conclusion
```

#### Hybrid Approach Consideration

A hybrid approach combining iPaaS for rapid initial ERP connectivity with a custom-built self-service portal offers several advantages:

- **Accelerated Time-to-Market**: Leveraging pre-built iPaaS connectors reduces initial integration time by 60-70%
- **Optimized User Experience**: Custom-built self-service portal provides EnCompass-specific UX for customer configuration
- **Flexible Evolution Path**: Ability to replace iPaaS connectors with custom connectors over time for cost optimization
- **Resource Optimization**: Focused engineering effort on customer-facing components rather than ERP connectivity
- **Risk Mitigation**: Reduced technical risk for ERP connectivity while maintaining control of customer experience

**C# Integration with Workato in the Hybrid Pattern**:

One significant advantage of the hybrid approach is the ability to leverage EnCompass's existing C# expertise and codebase while still benefiting from Workato's integration capabilities:

- **API-First Architecture**: While Workato primarily supports Ruby, JavaScript, and Python for scripting, it excels at consuming RESTful and SOAP APIs. This allows EnCompass to:
  - Develop and maintain complex business logic in C# (familiar to the team)
  - Expose these services as well-designed APIs on AWS
  - Use Workato as an orchestration layer that connects these C# services with ERPs

- **Practical Implementation Pattern**:
  - **Externalize Complex Logic**: Keep EnCompass-specific business rules, validations, and transformations in C# microservices or AWS Lambda functions
  - **C# for Computational Heavy Lifting**: Use C# for complex calculations, data manipulations, or proprietary algorithms
  - **Workato for Integration Orchestration**: Use Workato to handle the ERP connectivity, data mapping, and workflow orchestration
  - **Bidirectional Communication**: Design RESTful APIs with standardized request/response patterns to allow Workato to both consume data from C# services and push data back when needed

- **Development Workflow Benefits**:
  - C# developers maintain core business logic without needing to become Workato experts
  - Integration specialists can focus on Workato recipes and ERP connectivity
  - Clear separation of concerns improves maintainability and team efficiency
  - Consistent deployment using AWS's CI/CD tools (CodePipeline, CodeBuild) for C# services

This approach effectively combines the strengths of both platforms: EnCompass's C# expertise and Workato's integration capabilities, creating a more robust and maintainable solution than either approach alone.

> **Decision Recommendation**: Based on time-to-market requirements, engineering resource constraints, and ERP ecosystem support needs, a hybrid approach offers the optimal balance between rapid initial deployment and long-term control.

### 3.3 Non-Functional Requirements

> **AI Prompt**: Based on the business objectives, define key non-functional requirements for reliability, scalability, performance, and monitoring with implementation approaches.

**Reliability Requirements:** (Select all that apply)

- ☑️ High Availability (99.95%+)
- ☑️ Disaster Recovery
- ☑️ Fault Tolerance
- ☑️ Data Backup
- ☑️ Graceful Degradation
- ☑️ Low RTO (< 1 hour)
- ☑️ Low RPO (< 15 minutes)

**Scalability Requirements:** (Select all that apply)

- ☑️ Horizontal Scaling
- ☑️ Vertical Scaling
- ☑️ Auto-scaling
- ☑️ Load Balancing
- ☑️ Database Sharding
- ☑️ Caching
- ☑️ Connection Pooling

**Performance Requirements:** (Select all that apply)

- ☑️ Response Time (< 2 seconds)
- ☑️ Throughput (50+ transactions/sec)
- ☑️ Resource Utilization
- ☑️ Concurrent Users (500+)
- ☑️ Request Rate (100+ requests/min)
- ☑️ Database Query Performance
- ☑️ Network Latency

**Monitoring Requirements:** (Select all that apply)

- ☑️ Health Monitoring
- ☑️ Performance Monitoring
- ☑️ Log Management
- ☑️ Error Tracking
- ☑️ User Activity Monitoring
- ☑️ Security Monitoring
- ☑️ Business KPI Monitoring

| Category | Requirements | Implementation Approach |
|----------|--------------|-------------------------|
| **Reliability** | • Availability: [target] • RTO: [target] • RPO: [target] | [Implementation strategy] |
| **Scalability** | • User capacity: [target] • Transaction volume: [target] | [Implementation strategy] |
| **Performance** | • Response time: [target] • Throughput: [target] | [Implementation strategy] |
| **Monitoring** | • Application metrics • Infrastructure metrics • Business KPIs | [Tools and approach] |

### 3.4 Sequence Flows

> **Optional, to be filled if needed**: Create a sequence diagram showing the detailed interactions between system components for the primary use case.

```mermaid
sequenceDiagram
    participant A as Actor
    participant S as System
    participant D as Database
    
    A->>S: Request
    S->>D: Query
    D-->>S: Response
    S-->>A: Result
```

**Interaction Types:** (Select all that apply)

- 🔲 Synchronous Request/Response
- 🔲 Asynchronous Messaging
- 🔲 Event Publishing/Subscription
- 🔲 Batch Processing
- 🔲 Stream Processing
- 🔲 Polling/Long Polling
- 🔲 WebSockets/Real-time
- 🔲 Other: _______________

**Key Interactions:**

1. [Description of interaction 1]
2. [Description of interaction 2]

### 3.5 Data Flow

> **Optional, to be filled if needed**: Create a data flow diagram showing how data moves through the system, with descriptions of key data entities and transformations.

```mermaid
flowchart LR
    classDef source fill:#e1f5fe,stroke:#0288d1
    classDef process fill:#fff3e0,stroke:#ff9800
    classDef storage fill:#e8f5e9,stroke:#4caf50
    
    A[Data Source]:::source --> B[Processing]:::process
    B --> C[(Storage)]:::storage
    C --> D[Analytics]:::process
```

**Data Storage Types:** (Select all that apply)

- 🔲 Relational Database
- 🔲 Document Database
- 🔲 Key-Value Store
- 🔲 Graph Database
- 🔲 Time Series Database
- 🔲 Search Engine
- 🔲 Cache
- 🔲 Data Lake
- 🔲 Data Warehouse
- 🔲 File Storage
- 🔲 Object Storage
- 🔲 Other: _______________

**Data Processing Types:** (Select all that apply)

- 🔲 Stream Processing
- 🔲 Batch Processing
- 🔲 ETL/ELT Pipelines
- 🔲 Event Sourcing
- 🔲 CQRS
- 🔲 Real-time Analytics
- 🔲 Business Intelligence
- 🔲 Machine Learning
- 🔲 Other: _______________

**Data Elements:**

- **[Entity 1]:** [Key attributes and purpose]
- **[Entity 2]:** [Key attributes and purpose]

**Data Transformations:**

1. [Description of transformation 1]
2. [Description of transformation 2]

### 3.6 Security Architecture

> **AI Prompt**: Define the security controls and compliance requirements for the system, with implementation approaches for each.

**Authentication Methods:** (Select all that apply)

- 🔲 Username/Password
- 🔲 OAuth 2.0/OIDC
- 🔲 SAML
- 🔲 JWT
- 🔲 API Key
- 🔲 Multi-factor Authentication
- 🔲 Social Login
- 🔲 Single Sign-On
- 🔲 Other: _______________

**Authorization Models:** (Select all that apply)

- 🔲 Role-Based Access Control (RBAC)
- 🔲 Attribute-Based Access Control (ABAC)
- 🔲 Policy-Based Access Control
- 🔲 ACL (Access Control Lists)
- 🔲 OAuth 2.0 Scopes
- 🔲 Capability-Based Security
- 🔲 Other: _______________

**Data Protection Methods:** (Select all that apply)

- 🔲 Encryption at Rest
- 🔲 Encryption in Transit
- 🔲 Field-level Encryption
- 🔲 Tokenization
- 🔲 Data Masking
- 🔲 Key Management
- 🔲 Other: _______________

**Compliance Requirements:** (Select all that apply)

- 🔲 PCI DSS
- 🔲 GDPR
- 🔲 HIPAA
- 🔲 SOX
- 🔲 SOC 2
- 🔲 ISO 27001
- 🔲 CCPA
- 🔲 Other: _______________

**Core Security Controls:**

| Control Type | Implementation | Purpose |
|--------------|----------------|---------|
| Authentication | [OAuth/OIDC approach] | [Security goal] |
| Authorization | [RBAC/ABAC approach] | [Security goal] |
| Data Protection | [Encryption methods] | [Security goal] |
| Network Security | [Network controls] | [Security goal] |
| API Security | [API protection measures] | [Security goal] |
| Monitoring | [Security monitoring approach] | [Security goal] |

**Compliance Requirements:**

- [Compliance standard #1] - [Implementation approach]
- [Compliance standard #2] - [Implementation approach]

---

## 4.0 Risks, Assumptions, Issues & Dependencies

> **AI Prompt**: Based on the solution architecture, identify key risks, assumptions, and dependencies with mitigation strategies.

### 4.1 Risks

**Risk Categories:** (Select all that apply)

- ☑️ Technical Risk
- ☑️ Security Risk
- ☑️ Operational Risk
- 🔲 Compliance Risk
- ☑️ Business Risk
- ☑️ Schedule Risk
- ☑️ Resource Risk
- ☑️ Vendor/Third-party Risk
- 🔲 Other: _______________

**Key Risks:**

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| **ERP API Changes**: Sage or other ERP vendors may update their APIs without sufficient notice, breaking integrations | H | M | Implement version-aware connectors, establish API monitoring, formalize vendor communication channels, and adopt adapter pattern to isolate ERP-specific code from core integration logic |
| **Integration Complexity Underestimation**: The complexity of building/configuring ERP integrations may exceed initial estimates | H | H | Conduct POC with Sage ERP early, plan for progressive capability delivery, include buffer in timelines, and build in configurability from the start |
| **Data Security Vulnerabilities**: Sensitive payment data flowing between ERPs and EnCompass could be compromised | H | L | Implement end-to-end encryption, conduct regular security assessments, use OAuth 2.0 for authentication, leverage Imperva WAF/CDN, and ensure all credentials are securely stored |
| **Customer Adoption Barriers**: Self-service capabilities may prove too complex for customer IT teams | M | M | Create intuitive UX with guided setup wizards, develop comprehensive documentation, establish customer success team for onboarding, and gather feedback through early adopter program |
| **Vendor Lock-in (Buy Option)**: Adopting iPaaS solution may create dependency that's costly to change | M | H | Design with abstraction layers, establish exit strategy with data portability plan, negotiate favorable contract terms, and document all integration workflows independently |
| **Resource Constraints (Build Option)**: Required engineering talent may not be available | H | M | Cross-train existing teams, develop hiring plan, consider contracting specialists, prioritize features based on resource availability, and leverage AWS managed services |
| **Performance Degradation**: High transaction volumes may impact integration performance | H | M | Establish performance baselines and SLAs, implement auto-scaling, use caching strategically, design with asynchronous processing, and conduct load testing with projected volumes |
| **Operational Support Gaps**: Support model for new integration platform may be insufficient | M | M | Define clear support roles, establish monitoring and alerting, create runbooks, train support teams, and implement self-healing capabilities where possible |

### 4.2 Assumptions

**Assumption Categories:** (Select all that apply)

- ☑️ Business Assumptions
- ☑️ User Assumptions
- ☑️ Technical Assumptions
- ☑️ Resource Assumptions
- ☑️ Timeline Assumptions
- 🔲 Other: _______________

**Critical Assumptions:**

- **ERP API Stability**: Sage and future ERP APIs will maintain backward compatibility for at least 12 months and provide sufficient notice for breaking changes.
- **Customer Technical Capability**: Customers have sufficient technical knowledge to configure and troubleshoot their ERP integrations using self-service tools, with minimal WEX assistance.
- **Transaction Volume Growth**: Expected transaction volume will grow at 15-25% annually, which is within the scaling capabilities of both the build and buy options.
- **Security Standards Alignment**: All ERPs targeted for integration support modern security standards (OAuth 2.0, TLS 1.2+) and have documented security compliance.
- **Data Format Standardization**: A canonical data model can be established that supports mapping between EnCompass and all target ERPs without significant data loss.
- **AWS Infrastructure Availability**: Required AWS services (EKS, Lambda, API Gateway, etc.) will remain available in target regions with appropriate SLAs.
- **iPaaS Vendor Viability (Buy Option)**: Selected iPaaS vendor will remain financially viable and continue supporting their platform for at least 5 years.
- **Engineering Resource Availability (Build Option)**: Required engineering talent can be acquired and retained to build and maintain a custom integration platform.

### 4.3 Dependencies

**Dependency Types:** (Select all that apply)

- ☑️ System Dependencies
- ☑️ Data Dependencies
- ☑️ API Dependencies
- ☑️ Team Dependencies
- ☑️ Vendor Dependencies
- ☑️ Infrastructure Dependencies
- 🔲 Other: _______________

**Dependencies:**

| Dependency | Type | Impact | Status |
|------------|------|--------|--------|
| **EnCompass API Readiness** | System | Integration middleware depends on stable, well-documented EnCompass APIs for payment processing and status updates | EnCompass API enhancement project in progress, documentation being updated |
| **Sage ERP API Access** | API | Core functionality requires Sage API access with appropriate permissions and rate limits | Initial developer access secured; production access requirements documented |
| **AWS EKS Cluster Availability** | Infrastructure | Build option depends on enterprise-grade EKS environments for deployment | EKS environments available in dev/test; production cluster configuration underway |
| **Security Team Review** | Team | Integration design must pass security review before implementation | Initial architecture review scheduled; detailed design review pending |
| **Apigee Gateway Configuration** | Infrastructure | API management depends on Apigee gateway for traffic management, security policies, and monitoring | Apigee implementation team engaged; requirements being documented |
| **iPaaS Vendor Selection (Buy Option)** | Vendor | Solution implementation timeline depends on vendor selection and contract finalization | RFP in progress; vendor demos scheduled |
| **Customer IAM Integration** | System | Self-service capabilities depend on integration with enterprise IAM solution | IAM APIs available; integration design in progress |
| **DevOps Pipeline Setup** | Infrastructure | Deployment automation depends on CI/CD pipeline with GitHub, ArgoCD, and Terraform | Pipeline components available; integration-specific configuration needed |
| **Data Transformation Rules** | Data | Accurate data mapping between EnCompass and ERPs depends on business-validated transformation rules | Initial mapping for core Sage entities complete; validation in progress |

---

## 5.0 Vector Impact Assessment

> **AI Prompt**: Analyze the impact of this architecture on the five key vectors (Reliability, Security, Innovation Velocity, AI Maturity, SaaS Maturity) with baseline metrics and target improvements.

```mermaid
mindmap
  root((EnCompass ERP<br>Vector Impact))
    Reliability
      Fault Isolation(Bulkhead pattern for ERP connectors)
      Error Handling(Standardized error management framework)
      Observability(End-to-end tracing across integration points)
    Security
      Authentication(OAuth 2.0 integration with ERP systems)
      Data Protection(End-to-end encryption for payment data)
      API Security(Apigee Gateway with robust policies)
    Innovation
      Integration Speed(Reduced time-to-market for new ERPs)
      Self-Service(Customer-configurable integrations)
      CI/CD(Automated deployment for connector updates)
    AI
      Data Collection(Structured integration metrics for analysis)
      Mapping Suggestions(ML-assisted field mapping recommendations)
      Anomaly Detection(Pattern analysis for payment transactions)
    SaaS
      Multi-tenancy(Customer-specific integration configurations)
      Self-Service(Reduced dependency on WEX for onboarding)
      Scalability(Elastic scaling based on transaction volume)
```

**Reliability Vector Impact:** (Select all that apply)

- ☑️ Improved Availability
- ☑️ Enhanced Fault Tolerance
- ☑️ Reduced Recovery Time (RTO)
- ☑️ Reduced Data Loss (RPO)
- ☑️ Improved Monitoring/Observability
- ☑️ Improved Incident Response
- 🔲 Other: _______________

**Security Vector Impact:** (Select all that apply)

- ☑️ Enhanced Authentication
- ☑️ Improved Authorization
- ☑️ Better Data Protection
- ☑️ Improved Threat Detection
- ☑️ Enhanced Compliance
- ☑️ Reduced Attack Surface
- 🔲 Other: _______________

**Innovation Velocity Impact:** (Select all that apply)

- ☑️ Improved CI/CD Pipeline
- ☑️ Reduced Technical Debt
- ☑️ Enhanced Customer Experience
- ☑️ Increased Deployment Frequency
- ☑️ Reduced Lead Time for Changes
- ☑️ Improved Test Automation
- 🔲 Other: _______________

**AI Maturity Impact:** (Select all that apply)

- 🔲 Implementation of ML Models
- ☑️ Enhanced Automation
- ☑️ Improved Analytics Capabilities
- ☑️ Better Data Quality for ML
- 🔲 AI/ML DevOps Implementation
- 🔲 Other: _______________

**SaaS Maturity Impact:** (Select all that apply)

- ☑️ Enhanced Multi-tenancy
- ☑️ Improved Cloud-native Features
- ☑️ Better Self-service Capabilities
- ☑️ Improved Scalability
- ☑️ Enhanced Provisioning
- 🔲 Other: _______________

| Vector | Current → Target | Key Improvements |
|--------|------------------|------------------|
| **Reliability** | Level 2: Basic Resilience → Level 4: Proactive Resilience | • 75% reduction in integration-related incidents  • Recovery time improved from 4+ hours to <30 minutes  • Uptime increased from 99.5% to 99.95% |
| **Security** | Level 2: Baseline Security → Level 4: Advanced Security | • Elimination of file-based vulnerabilities via API integrations  • End-to-end encryption and secure credential management  • 100% API traffic visibility through centralized monitoring |
| **Innovation Velocity** | Level 1: Ad-hoc Integration → Level 4: Continuous Innovation | • ERP integration time cut from 6-8 weeks to 2-4 weeks (build) or 3-5 days (buy)  • 60% reduction in engineering dependency via self-service  • Test coverage increased from 40% to 90% |
| **AI Maturity** | Level 1: Data Collection → Level 2: Basic AI Implementation | • Standardized data model enabling future ML analysis  • Anomaly detection for payment patterns and integration errors  • Foundation for ML-assisted mapping and configuration |
| **SaaS Maturity** | Level 2: Basic SaaS → Level 4: Advanced SaaS | • Multi-tenant architecture with customer-specific configurations  • Self-service for 90% of integration management tasks  • Resource optimization through transaction-based auto-scaling |

### Vector Impact Focus Areas

- **Reliability**: The integration architecture significantly enhances reliability through standardized error handling, circuit breakers, and bulkhead patterns that isolate failures. Both build and buy options include comprehensive observability with end-to-end tracing to detect and resolve issues faster.

- **Security**: Transitioning from file-based to API-based integration eliminates multiple security vulnerabilities. The layered security approach (Imperva WAF, Apigee Gateway, OAuth 2.0, encryption) creates defense-in-depth for payment data protection.

- **Innovation Velocity**: Self-service configuration capabilities and standardized integration patterns dramatically accelerate onboarding of both customers and new ERP systems. The CI/CD pipeline for connectors enables rapid deployment of enhancements.

- **AI Maturity**: While not the primary focus, the architecture establishes a standardized data model and collection mechanisms that lay the foundation for future ML-based optimizations such as anomaly detection and intelligent mapping suggestions.

- **SaaS Maturity**: The integration framework significantly advances EnCompass as a SaaS platform by enhancing self-service capabilities, multi-tenancy, and scalability—key attributes expected in enterprise SaaS solutions.

**Reference Links:**

- [Reliability Vector](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155172602241/Reliability+Vector)
- [Security](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155173978186/Product+Security)
- [Product Innovation Velocity (PiV)](https://wexinc.atlassian.net/wiki/spaces/TISO/pages/154810155024/Tech+Transformation+KPIs)
- [AI Maturity](https://wexinc.atlassian.net/wiki/spaces/~712020da12ca9d9e41499d93b17d695fdb7655/pages/155174338612/AI+Maturity+Vector)
- [SaaS Maturity](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154867007780/RFC-351+SaaS+Maturity+Vector)

---

## 6.0 Appendix A: Technical Deep-Dive – Workato Integration with Sage and EnCompass

### Workato Integration Overview

```mermaid
sequenceDiagram
    participant Sage as Sage ERP
    participant Workato as Workato
    participant WEX as EnCompass/WEX

    Sage->>Workato: 1. Provide Vendor & AP Bill Data (XML)
    Workato->>Workato: 2. Parse XML to JSON, Validate & Transform Data
    Workato->>Sage: 3. (Optional) Lookup Vendor/Custom Fields
    Workato->>WEX: 4. Send Payment Details (JSON via API)
    WEX-->>Workato: 5. Payment Status/Response
    Workato->>Sage: 6. Update Payment Status in Sage
    Note over Workato: Error Handling, Logging, Retry Logic
    Note over Workato,WEX: Supports custom field mapping, extensibility, and test mode
```

#### 1. Pulling Payment Details from Customer’s ERP (Sage)

- **Create Vendor in Sage:** Vendors must be set up in Sage with all required details (name, ID, etc.). Vendor Type should be marked as WEX and payment method set to EFT.
- **Pull Vendor Details into Workato Recipe:** Workato recipes pull vendor data from Sage’s API (data comes in XML, so a recipe step parses XML to JSON for further processing).
- **AP Payment Creation:** For each vendor, create an AP payment in Sage, ensuring fields like vendor ID, invoice number, invoice date, and amount are configured.
- **Set Up Workato Recipe for AP Payments:** Build a Workato recipe to retrieve payment details (invoice number, date, amount, vendor ID).

#### 2. Build AP Payment Details Workflow

- **Retrieve AP Payment Details:** Use a Workato recipe/step to fetch AP payment details for each vendor’s payment from Sage’s API, prepping for WEX.
- **Link AP Bills to Payment Details:** Ensure AP payment details are connected to the correct AP bills in Sage (invoice number, amount, date).
- **Use Sage API for AP Bills:** Workato uses Sage API to pull AP bill data, grouping it with payment details.
- **Note:** Pulling payment data from Sage is multi-step: first, pull WEX-flagged vendors, then query Sage for APBill details to find invoices for those payments.

#### 3. Prepare Data for WEX Integration

- **Organize Payment Data for WEX:** Gather all required fields (vendor ID, invoice number, invoice date, amount) and map into a structure WEX’s system accepts.
- **Ensure Required Fields are Mapped:** Confirm essential fields are mapped and test mapping with sample Sage outputs.
- **Test Sending to WEX:** Run a test transaction to ensure data is transmitted correctly from Workato to WEX’s API.
- **Note:** Data from Sage is XML; Workato prefers JSON, so use Workato’s JSON parser and, if needed, Ruby/JS scripts for transformation.

#### 4. Additional Data Field Considerations (e.g., Purchase Order)

- **Identify Potential Custom Fields:** Anticipate additional fields (e.g., PO number, department code) customers may require.
- **Find Custom Fields in AP Bill:** Use Workato to search and extract custom fields from Sage AP bill data.
- **Create Flexible Mapping for Custom Fields:** Modify the Workato recipe to include custom fields, allowing for future flexibility.
- **Note:** Current recipes are rigid; to support user-defined fields, further Sage API exploration and recipe adjustments are needed.

#### 5. Test and Validate the Workflow

- **Conduct End-to-End Test:** Perform a full test from Sage to WEX, validating all required data is transferred.
- **Check for Errors:** Identify and resolve issues (missing fields, incorrect mappings) by refining recipes or API calls.
- **Ensure Flexibility for Future Customers:** Design the system to be adaptable for future clients needing additional fields.

---

### Troubleshooting Scenarios & Best Practices

#### Scenario 1: XML Parsing Errors

- *Symptom:* Workato recipe fails when parsing Sage XML response.
- *Resolution:* Double-check the XML schema in the recipe, use Workato’s XML parser step, and validate the XML structure with sample data. Log the raw XML for debugging.

### Scenario 2: Missing or Incorrect Field Mapping

- *Symptom:* Data sent to WEX is missing required fields or has incorrect values.
- *Resolution:* Review the field mapping/transformation step. Use Workato’s test mode to inspect intermediate outputs. Add validation steps to catch missing fields early.

#### **Scenario 3: API Authentication Failures**

- *Symptom:* API calls to Sage or WEX fail with authentication errors.
- *Resolution:* Ensure API credentials are up to date in Workato’s vault. Confirm OAuth 2.0 or API key configuration matches the ERP/WEX requirements.

##### **Scenario 4: Error Handling and Retries**

- *Symptom:* Transient errors (e.g., network issues) cause recipe steps to fail.
- *Resolution:* Use Workato’s built-in retry logic and error notification features. Maintain a lookup table for failed records and implement manual or automated reprocessing.

#### **Scenario 5: Custom Field Not Found**

- *Symptom:* Customer requests a new field (e.g., PO number) that is not present in the current mapping.
- *Resolution:* Update the Sage API query and Workato recipe to extract and map the new field. Test with sample data and update documentation.

### Technical Positives and Challenges from Deep-Dive

**Positives:**

- Rapid integration using Workato’s pre-built connectors and low-code recipes significantly reduces time-to-market for Sage and similar ERPs.
- Built-in error handling, retry logic, and monitoring features in Workato improve reliability and reduce manual intervention.
- Flexible data transformation and mapping capabilities (including custom scripting) allow for handling complex business requirements and custom fields.
- Test mode, sandbox environments, and recipe versioning in Workato support safe development and deployment.
- Strong support for API authentication (OAuth 2.0, API keys) and secure credential management.
- Extensible architecture: Recipes and connectors can be adapted for future ERPs or new customer requirements with minimal rework.

**Challenges and Mitigation Strategies:**

#### XML-to-JSON conversion complexity

- Implement standardized XML parsing templates with robust error handling
- Create and maintain a comprehensive test suite with various XML payload structures
- Document common parsing errors and solutions for faster troubleshooting
- Consider implementing a dedicated validation service to pre-validate XML before full processing

#### Custom field mapping complexity

- Develop a metadata-driven mapping framework that supports dynamic field configuration
- Create a UI-based mapping tool within EnCompass that generates Workato-compatible configurations
- Establish a library of reusable mapping patterns for common field types
- Implement an automated field discovery process to identify available custom fields in Sage

#### Error handling for edge cases

- Implement a multi-layered error handling strategy (recipe-level, connector-level, and system-level)
- Create dedicated error handler workflows for different failure scenarios
- Establish circuit breakers and rate limiting controls for API interactions
- Develop an automated recovery process for common failure patterns

#### Recipe flexibility limitations

- Design modular recipes with clearly separated concerns
- Utilize Workato's callable recipes feature to create reusable, parameterized components
- Develop a registry of recipe templates that can be instantiated for specific customer needs
- Maintain a library of customer-specific customizations with version control

#### Monitoring and troubleshooting complexity

- Implement end-to-end transaction tracking with correlation IDs across systems
- Create customer-specific monitoring dashboards with relevant KPIs
- Integrate Workato logs with enterprise monitoring tools (Splunk, DataDog)
- Develop a systematic RCA framework with predefined diagnostic steps for common issues

#### Vendor lock-in risk

- Document all integration logic and flows independent of Workato's specific implementation
- Design with clear separation between business logic and Workato-specific code
- Maintain canonical data models outside of the Workato ecosystem
- Develop a platform abstraction layer to isolate core business logic from platform specifics

#### Cost scaling concerns

- Implement transaction batching where appropriate to optimize API call volume
- Develop clear volume-based cost projections for different customer segments
- Establish monitoring for transaction volume anomalies
- Create cost allocation models to enable accurate customer pricing
- Regularly review usage patterns to identify optimization opportunities

#### Limited hybrid deployment options

- Establish robust connectivity between cloud and on-premises systems using secure agents
- Design fallback mechanisms for temporary cloud connectivity interruptions
- Implement local data caching strategies for critical operations
- Develop comprehensive network resilience testing to validate hybrid connectivity

#### Sage-specific integration timeline risk

- Allocate additional buffer time (30-50%) for the initial Sage integration beyond the optimistic 3-5 day estimate
- Build standardized Sage XML parsing components early in the implementation cycle
- Create a library of pre-validated Sage API request/response patterns for common scenarios
- Engage Workato's professional services specifically for Sage integration optimization

#### Learning curve for complex scenarios

- Establish a formal training program for integration developers with Workato certification paths
- Create EnCompass-specific integration patterns documentation and video tutorials
- Develop a mentorship program pairing experienced integration developers with new team members
- Build a knowledge base of EnCompass-specific Workato solutions and approaches

These mitigation strategies provide a balanced approach to addressing the technical challenges while preserving the advantages of using Workato for ERP integration.

---

## 7.0 Appendix B: Workato Scripting Language Options

According to Workato's official documentation and resources, Workato supports multiple programming languages for data transformation and scripting in recipes:

### 1. Ruby (Traditional Primary Language)

Ruby has been Workato's main scripting language historically. The [official Workato documentation](https://docs.workato.com/developing-connectors/sdk/scripting.html) shows extensive Ruby examples for transformations:

```ruby
# Example Ruby transformation in Workato
def transform(input)
  output = {
    vendorId: input['vendor_id'],
    invoiceNo: input['invoice_number'],
    amount: input['amount'].to_f,
    dueDate: input['due_date'].to_date.strftime('%Y-%m-%d')
  }
  
  return output
end
```

### 2. JavaScript Support

Workato also supports JavaScript for scripting. According to their [JavaScript scripting documentation](https://docs.workato.com/developing-connectors/sdk/javascript-scripting.html), you can use JavaScript in formulas, custom scripts, and even connector SDK development:

```javascript
// Example JavaScript transformation in Workato
function transform(input) {
  var output = {
    vendorId: input.vendor_id,
    invoiceNo: input.invoice_number,
    amount: parseFloat(input.amount),
    dueDate: new Date(input.due_date).toISOString().split('T')[0]
  };
  
  return output;
}
```

### 3. Python Support

Workato has also introduced Python support, particularly in its AI-related capabilities and certain data transformation scenarios. This is a more recent addition to their platform capabilities.

```python
# Example Python transformation in Workato
def transform(input):
  output = {
    "vendorId": input["vendor_id"],
    "invoiceNo": input["invoice_number"],
    "amount": float(input["amount"]),
    "dueDate": input["due_date"].split('T')[0]
  }
  
  return output
```

### Language Selection Considerations

When choosing between these languages in Workato:

1. **Team Expertise**: For the EnCompass team, select the language that best aligns with existing team skills to reduce the learning curve.

2. **Use Case Requirements**:
   - Ruby has the most mature support in Workato with extensive documentation
   - JavaScript offers better performance for complex transformations
   - Python might be preferred for data science or AI-adjacent workflows

3. **Connector Requirements**: Some connectors may have better support or examples in specific languages.

4. **Integration with C# Services**: When integrating with EnCompass's C# services via API, any of these languages can be used to process the JSON payloads received from or sent to C# services.

This multi-language support enhances the flexibility of the hybrid approach, allowing teams to choose the most appropriate language for different integration scenarios while maintaining the API-first architecture with C# services. The integration is primarily payload-based rather than language-dependent, meaning C# and Workato communicate through standardized HTTP+JSON interfaces rather than through shared code or libraries.

### 4. Integration with C# Services

> **Note**: The information in this section provides preliminary guidelines and implementation patterns for C# integration with Workato. These details will be revisited and expanded in more depth if the team moves ahead with the buy option. This represents a conceptual approach rather than a finalized implementation plan.

When integrating with EnCompass's C# services via API, Workato can interact with them regardless of which scripting language you choose (Ruby, JavaScript, or Python). The technical implementation involves:

- **RESTful API Communication**: C# services expose RESTful endpoints that accept and return JSON payloads. Workato's HTTP connector provides comprehensive support for REST API interactions, including:
  - All standard HTTP methods (GET, POST, PUT, DELETE, PATCH)
  - Full OAuth 2.0 authentication flows including client credentials, authorization code, and PKCE
  - Custom header management for API keys and other authentication methods
  - Automatic retry and timeout configurations
  - Support for multipart requests and file uploads

- **JSON Serialization/Deserialization**:
  - C# services typically use System.Text.Json or Newtonsoft.Json for serialization
  - Workato automatically handles JSON parsing in any of its supported languages
  - Complex nested JSON structures from C# models are fully supported
  - Custom JSON converters in C# endpoints are transparent to Workato

- **API Gateway Integration**:
  - API Gateway in front of C# services provides:
    - Rate limiting to protect C# services from excessive load
    - API key validation before requests reach C# services
    - Request/response transformation if needed
    - Logging and monitoring
  - Workato can adapt to API Gateway's authentication requirements

- **Error Handling Protocol**:
  - C# services should return standardized error responses (typically HTTP 4xx/5xx with JSON error details)
  - Workato recipes can include error handling logic that parses C# service error responses
  - Custom error handling code in any of Workato's supported languages can process these errors

- **Payload Transformation Example**:
  
  ```javascript
  // JavaScript in Workato transforming data before sending to C# API
  function transformForCSharpService(input) {
    // Convert Workato date format to C# expected ISO format
    const dueDate = new Date(input.due_date).toISOString();
    
    // Transform to match C# model naming conventions and types
    return {
      vendorIdentifier: input.vendor_id,
      invoiceNumber: input.invoice_number,
      paymentAmount: parseFloat(input.amount),
      paymentDueDate: dueDate,
      metaData: {
        source: "WorkatoIntegration",
        correlationId: input.transaction_id
      }
    };
  }
  ```

- **Bidirectional Webhook Support**:
  - C# services can call Workato webhooks to trigger recipes or provide updates
  - Workato can register webhooks with C# services to receive asynchronous notifications
  - This enables event-driven architectures spanning both platforms

- **Advanced C# Service Implementation Patterns**:
  - **Domain Service Pattern**: Implement C# business logic as domain services that expose clean RESTful interfaces:
  
    ```csharp
    // C# Domain Service with RESTful API
    [ApiController]
    [Route("api/v1/[controller]")]
    public class PaymentValidationController : ControllerBase
    {
        private readonly IPaymentValidationService _validationService;
        
        public PaymentValidationController(IPaymentValidationService validationService)
        {
            _validationService = validationService;
        }
        
        [HttpPost("validate")]
        public async Task<ActionResult<ValidationResult>> ValidatePayment([FromBody] PaymentRequest request)
        {
            var result = await _validationService.ValidatePaymentAsync(request);
            return Ok(result);
        }
    }
    ```

  - **Circuit Breaker Pattern**: Implement resilience in C# services using Polly:

    ```csharp
    // C# Circuit Breaker pattern implementation
    services.AddHttpClient<IPaymentGatewayClient, PaymentGatewayClient>()
        .AddPolicyHandler(GetCircuitBreakerPolicy());
    
    static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .CircuitBreakerAsync(5, TimeSpan.FromMinutes(1));
    }
    ```

  - **Stateless Design**: Ensure C# services are stateless to support Workato's distributed execution model:

    ```csharp
    // C# stateless service design with dependency injection
    public class PaymentProcessor : IPaymentProcessor
    {
        private readonly IPaymentRepository _repository;
        private readonly ILogger<PaymentProcessor> _logger;
        
        public PaymentProcessor(IPaymentRepository repository, ILogger<PaymentProcessor> logger)
        {
            _repository = repository;
            _logger = logger;
        }
        
        public async Task<ProcessResult> ProcessAsync(PaymentInstruction instruction)
        {
            // Implementation that doesn't rely on instance state
            var result = await _repository.CreatePaymentAsync(instruction);
            _logger.LogInformation("Payment {Id} processed", result.Id);
            return new ProcessResult { PaymentId = result.Id, Status = result.Status };
        }
    }
    ```

- **AWS Lambda Implementation for Serverless C# Services**:
  - Deploy C# business logic as AWS Lambda functions using the AWS Lambda C# runtime
  - Configure API Gateway to route requests to the appropriate Lambda function
  - Use environment variables for configuration
  - Example Lambda function handler:

    ```csharp
    // C# AWS Lambda function for payment validation
    public class PaymentValidationFunction
    {
        private readonly IPaymentValidator _validator;
        
        public PaymentValidationFunction()
        {
            // Set up dependency injection
            var services = new ServiceCollection();
            services.AddSingleton<IPaymentValidator, PaymentValidator>();
            var provider = services.BuildServiceProvider();
            _validator = provider.GetRequiredService<IPaymentValidator>();
        }
        
        public async Task<APIGatewayProxyResponse> FunctionHandler(APIGatewayProxyRequest request, ILambdaContext context)
        {
            try
            {
                // Parse the incoming JSON
                var payment = JsonSerializer.Deserialize<PaymentRequest>(request.Body);
                
                // Validate the payment
                var result = await _validator.ValidateAsync(payment);
                
                // Return the validation result
                return new APIGatewayProxyResponse
                {
                    StatusCode = 200,
                    Body = JsonSerializer.Serialize(result),
                    Headers = new Dictionary<string, string> { { "Content-Type", "application/json" } }
                };
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error validating payment: {ex.Message}");
                return new APIGatewayProxyResponse
                {
                    StatusCode = 500,
                    Body = JsonSerializer.Serialize(new { error = "Internal server error" }),
                    Headers = new Dictionary<string, string> { { "Content-Type", "application/json" } }
                };
            }
        }
    }
    ```

- **Security Implementation for C# Services**:
  - Use tokens with appropriate scopes for authorization between Workato and C# services
  - Implement custom middleware in ASP.NET Core for validating Workato-specific headers:

    ```csharp
    // C# middleware for Workato API authentication
    public class WorkatoAuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _configuration;
        
        public WorkatoAuthenticationMiddleware(RequestDelegate next, IConfiguration configuration)
        {
            _next = next;
            _configuration = configuration;
        }
        
        public async Task InvokeAsync(HttpContext context)
        {
            // Check for API key in header
            if (!context.Request.Headers.TryGetValue("X-Workato-Signature", out var signature))
            {
                context.Response.StatusCode = 401;
                await context.Response.WriteAsJsonAsync(new { error = "Missing authentication" });
                return;
            }
            
            // Validate signature (simplified example)
            var expectedSignature = _configuration["Workato:SignatureKey"];
            if (signature != expectedSignature)
            {
                context.Response.StatusCode = 403;
                await context.Response.WriteAsJsonAsync(new { error = "Invalid signature" });
                return;
            }
            
            await _next(context);
        }
    }
    ```

- **C# Canonical Data Model for Integration**:
  - Define clear data contracts in C# that align with Workato's transformation capabilities:

    ```csharp
    // C# data contract for payment integration
    public class PaymentIntegrationContract
    {
        [JsonPropertyName("payment_id")]
        public string PaymentId { get; set; }
        
        [JsonPropertyName("vendor")]
        public VendorInfo Vendor { get; set; }
        
        [JsonPropertyName("invoice")]
        public InvoiceInfo Invoice { get; set; }
        
        [JsonPropertyName("payment")]
        public PaymentDetails Payment { get; set; }
        
        [JsonPropertyName("metadata")]
        public Dictionary<string, string> Metadata { get; set; }
    }
    
    public class VendorInfo
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }
        
        [JsonPropertyName("name")]
        public string Name { get; set; }
    }
    
    // Additional nested classes...
    ```

This multi-language support enhances the flexibility of the hybrid approach, allowing teams to choose the most appropriate language for different integration scenarios while maintaining the API-first architecture with C# services. The integration is primarily payload-based rather than language-dependent, meaning C# and Workato communicate through standardized HTTP+JSON interfaces rather than through shared code or libraries.

---

## 8.0 Appendix C: RACI Matrix

The following RACI matrix outlines the responsibility distribution across key activities for each strategic option. This comparison illustrates how responsibilities shift between WEX EnCompass teams and vendor teams based on the selected approach. Each cell indicates whether the responsibility belongs primarily to WEX (W) or the Vendor (V), along with the RACI designation.

| Activity/Task | Build Option | Buy Option (iPaaS) | Hybrid Approach |
|---------------|-------------|-------------------|-----------------|
| **Initial Setup & Configuration** ||||
| ERP Connector Development | W-R | V-R, W-C | V-R, W-C |
| ERP Connector Configuration | W-R | W-R, V-C | W-R, V-C |
| Integration Framework Development | W-R | V-R, W-I | W-R (portal only), V-R (connectors) |
| Data Mapping Rules Creation | W-R | W-R, V-C | W-R, V-C |
| Security Configuration | W-R | W-R, V-C | W-R, V-C |
| Environment Setup | W-R | W-R, V-C | W-R |
| **Ongoing Operations** ||||
| ERP API Changes/Updates | W-R | V-R, W-C | V-R (connectors), W-R (portal) |
| Performance Monitoring | W-R | W-R, V-C | W-R, V-C |
| Security Monitoring | W-R | W-R, V-C | W-R, V-C |
| Integration Troubleshooting | W-R | W-R, V-R | W-R, V-R |
| Error Resolution | W-R | W-R, V-C | W-R (portal), V-R (connectors) |
| **Change Management** ||||
| New ERP Connector Development | W-R | V-R, W-C | V-R, W-C |
| Field Mapping Updates | W-R | W-R, V-C | W-R, V-C |
| Integration Logic Changes | W-R | W-R, V-C | W-R (portal), V-R (connectors) |
| Scaling Resources | W-R | W-R, V-C | W-R (portal), V-R (connectors) |
| Version Updates | W-R | V-R, W-I | W-R (portal), V-R (connectors) |
| **Customer Support** ||||
| Integration Configuration Support | W-R | W-R, V-C | W-R |
| Troubleshooting Customer Issues | W-R | W-R, V-C | W-R, V-C |
| Documentation | W-R | W-R, V-C | W-R, V-C |
| Training | W-R | W-R, V-C | W-R, V-C |
| **Compliance & Governance** ||||
| Security Compliance | W-R | W-R, V-C | W-R, V-C |
| Audit Support | W-R | W-R, V-C | W-R, V-C |
| Data Privacy Compliance | W-R | W-R, V-C | W-R, V-C |
| Vendor Management | W-R | W-R, V-A | W-R, V-A |

**Legend:**

- **W**: WEX EnCompass Team Responsibility
- **V**: Vendor Responsibility
- **R (Responsible)**: Does the work to complete the task
- **A (Accountable)**: Ultimately answerable for the correct completion of the task
- **C (Consulted)**: Provides input before or during the task
- **I (Informed)**: Kept up-to-date on progress/completion

<!-- Parent: Designs -->
<!-- Parent: Fleet and Mobility -->
<!-- Title: RFC-532 Fraud Mobile Alerts -->

<!-- Include: macros.md -->

# Fraud Mobile Alerts

:draft:

```text
Author: <PERSON>
Publish Date: 2025-07-11
Category: Designs
Subtype: Fleet and Mobility
```

## Executive Summary

This design proposes a new mobile fraud alert system that will enable fleet managers to receive immediate notifications about potentially fraudulent transactions through RCS/SMS messaging. The solution leverages existing WEX infrastructure and services to deliver real-time alerts to verified phone numbers of authenticated fleet managers, enhancing fraud prevention capabilities and customer experience.

The common Notification Hub service, already used by several services for sending SMS, will be enhanced to support [RCS Business messaging](https://www.twilio.com/en-us/messaging/channels/rcs). This will utilize named, carrier-verified RCS senders instead of regular 10-digit phone numbers. This new standard provides enhanced customer trust, a rich and interactive user experience, end-to-end encryptyion and significantly higher throughput for no additional cost. It also seamlessly falls back to standard SMS if the recipient's device does not support RCS.

This solution drives improvements towards the following Tech Transformation Vectors:

| Vector | Impact |
|--------|--------|
| 🔲 🛡️ Product Security | |
| 🔲 🔄 Reliability | |
| ☑️ ⚡ Product Innovation Velocity (PiV) | Enables rapid fraud response through real-time mobile alerts |
| ☑️ ☁️ SaaS Maturity | Leverages cloud-native services and standard WEX platforms |
| 🔲 🤖 AI/ML Maturity | |

:toc:

## 💼 Change Context

* **Organization:** Fleet and Mobility
* **Purpose:** Enable real-time mobile fraud alerts for fleet managers
* **Users:** Fleet managers with Fleet Manager role permissions in WexOnline
* **Integration Points:**
  * WexOnline
  * Fleet Services
  * Notification Hub
  * Twilio
  * Fraud Detection Gateway
  * Salesforce
  * Kafka
  * Snowflake

* **Impacted Products:**
  * WEXOnline
  * Fraud Detection Gateway
  * Notification Hub

* **Impacted Teams:**
  * Fleet and Mobility Teams
  * Risk and Fraud Teams
  * Note: Notification Hub no longer has a dedicated team, so one of the aforementioned teams will be responsible for the Notification Hub enhancements.

* **New Third-Party Software:** None (using existing Twilio integration)

* **Design Qualities:**
  * 🔲 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * ☑️ 🟡 New Pattern Adoption
  * ☑️ 🟡 New Product or Capability
  * ☑️ 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Development Begins | 2025-08 | Team availability |
| Development Complete | 2025-11 | Integration testing |
| Production Release | 2025-12 | UAT completion |

## 📊 Diagrams

### System Context Diagram

```mermaid
graph TB
    FM[Fleet Manager] -->|Pre-req: Enrolls in Alerts| WO[WexOnline Portal]
    FM[Fleet Manager] -->|Reviews Alerts| SF[Salesforce]
    WO -->|Authenticates| Okta[Okta]
    FDG[Fraud Detection Gateway] -->|Fraud Events + Case details| FAS[Fleet Alert Service]
    FDG -->|Create Case | SF
    FAS -->|Recipient lookup by hierarchy | OLU[Online User DB]
    FAS -->|Alerts | NH[Notification Hub]


    NH -->|Alert Events | Kafka

    Kafka -->|Alert Events| NHC[Notification Hub SMS Consumer]
    NHC -->|Send RCS| Twilio[Twilio]
    Twilio -->|RCS Messages| FM

    NHC --> | Persist Alert history | NHDB[Notification Hub DB]

    NHDB -->|Alert History| SFL[Snowflake]

    classDef external fill:#f9f9f9,stroke:#666666
    classDef secure fill:#e6ffe6,stroke:#006600
    class Twilio,FM,SF external
    class Okta,AS secure
```

### Cloud Architecture Diagram

```mermaid
flowchart TB
    subgraph "AWS Infrastructure"
        direction TB

        subgraph "Risk/Fraud EKS Cluster"
          FDG[Fraud Detection Gateway]
        end

        subgraph "Salesforce"
          SF[SalesForce]
        end

        subgraph "Mobility EKS Cluster"
            WO[WEXOnline]
            FAS[Fleet Alert Service]
        end
        subgraph "Payments EKS Cluster"
          NH[Notification Hub]
          NHC[Notification Hub SMS Consumer]
        end
        subgraph "Aiven Kafka"
          Kafka[Kafka]
        end

        subgraph "Observability"
            Splunk
            APM[Grafana/Datadog]
        end
    end

    subgraph "External"
        Twilio
        Users[Fleet Managers]
        WAF[Imperva WAF]
    end

    FDG --> | HTTPS | Kafka
    FAS --> | HTTPS | Kafka
    FDG --> | HTTPS | SF

    Users -->|HTTPS| WAF
    Users -->|HTTPS| SF
    WAF -->|Internal HTTPS| WO
    NH -->|Events| Kafka
    Kafka --> |Events| NHC
    NHC -->|SMS/RCS| Twilio
    NH -->|Logs| Splunk
    NHC -->|Logs| Splunk
    WO -->|Logs| Splunk
    FAS -->|Logs| Splunk
    FAS --> | RCS/SMS Messages | NH
    WO -->|Metrics| APM
    NH -->|Metrics| APM
    NHC -->|Metrics| APM
    FAS -->|Metrics| APM
    Twilio --> | RCS/SMS | Users

    classDef secure fill:#e6ffe6,stroke:#006600
    classDef external fill:#f9f9f9,stroke:#666666
    class WAF secure
    class Twilio,Users external
    class SF external
```

## ⚖️ Evaluation Criteria

### 🛡️ Product Security

#### Data Compliance

The following types of Class 1 Protected Data is transmitted, processed, or stored in this solution:

* 🔲 PCI
* 🔲 PII
* 🔲 PHI
* 🔲 Other

Data Protection Controls:

* ☑️ Data is protected at rest, in transit, and in use per WEX IT policy
* ☑️ HTTPS using TLS 1.2 or higher
* ☑️ Message encryption (RCS)
* ☑️ Database Connections using TLS 1.2 or higher
* 🔲 Row-level data encryption
* 🔲 Transparent data encryption
* 🔲 De-identification (tokenization, hashing, masking, redaction)
* 🔲 Digital Signatures
* ☑️ Access control (permission, role, policy)

Data Retention:

* Data archived after: 90 days
* Data purged after: 365 days
* Logs purged after: 90 days

#### Authentication & Authorization

WEX Employee Authentication Components:

* 🔲 IdentityIQ SAML
* ☑️ Okta Workforce Identity
* 🔲 Active Directory
* 🔲 Built-in user management & authentication
* 🔲 WEX custom-built IAM solution
* 🔲 WEX employees do not authenticate
* 🔲 Other

WEX Customer Authentication Components:

* ☑️ Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory
* 🔲 WEX custom-built IAM solution
* 🔲 WEX customers do not authenticate
* 🔲 Other

### 🔄 Reliability

#### High Availability Controls

* ☑️ Availability Zone Redundancy
* ☑️ Regional Failover
* ☑️ Automated Failover
* 🔲 Multi-Region Active-Active
* ☑️ Load Balancing
* 🔲 Content Delivery Network
* ☑️ Monitoring & Alerting
* 🔲 Other

#### WEX Cloud Infrastructure

| Component | Notification Hub Value | NAM Value | Risk Platform Value |
|-----------|--------|------------|--------|
| Cloud Provider | AWS | AWS | AWS |
| Primary Region | us-east-1 & us-west-2 | us-west-2 | us-east-1 |
| Failover Region | N/A | us-east-1 | us-west-2 |
| Production VNet | cpswheel-prod-ue1-vpc | fleet-prod-vpc | riskplatform-prod-ue1-vpc |
| WEX Fabric ID | platform-tech-shared | fleet-north-america | risk-platform |

### ☁️ SaaS

#### Key Components

| Component | Description | Technology | Purpose |
|-----------|-------------|------------|---------|
| Fraud Detection Gateway | Identify potentially fraudulent transactions | Java Spring Boot (abstraction layer around SaferPayments) | Identifying alert scenarios and triggering the alert process |
| Fleet Alerting service | Generates a list of recipients based on access, triggers notification for each  | Java Spring Boot | Mapping the card in the alert to the enrolled alert recipients for that account  |
| Notification Hub | Processes fraud events and manages alert delivery | Python FastAPI | RCS/SMS delivery |

#### Logging & APM

* ☑️ Splunk Forwarding
* ☑️ DataDog Forwarding
* ☑️ Security Event Logging
* ☑️ Synthetic Testing
* ☑️ Log Archival
* ☑️ Log Purging
* ☑️ Class 1 Data Logging Controls

#### Standards Adoption

All standard services are being used with no deviations:

* ☑️ WEX Fabric
* ☑️ Notification Hub
* ☑️ Splunk
* ☑️ DataDog/Grafana
* ☑️ Kafka
* ☑️ EKS

## 🧩 Risk Analysis & Dependencies

### 🚨 Key Risks

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Alert delivery delays | High | Low | Kafka retry mechanism, monitoring |
| Phone number verification failures | Medium | Medium | Clear error messaging, support process |
| High alert volume overwhelming users | Medium | Low | Alert throttling, aggregation rules |

### 🔍 Critical Assumptions

* **Phone Coverage:** Fleet managers have mobile phones capable of receiving SMS/RCS
* **RCS Capability:** A significant majority of mobile devices support RCS messaging.
* **Data Accuracy:** Fraud detection system provides accurate and timely alerts

### 📦 System Dependencies

| Dependency | Type | Criticality | Contact Team |
|------------|------|-------------|--------------|
| WexOnline | System | High | Fleet Mobility Team |
| Notification Hub | System | High | Platform Services |
| Fraud Detection Gateway | System | High | Security Team |
| Twilio | Service | High | Platform Services |

## 🔗 Reference Links

* **Documentation:**
  * [WEX Notification Hub](https://drive.google.com/drive/folders/11EiwIwfq4ml_-hdTGnZB2icnp-9c2xLV)

* **Repositories:**
  * [Notification Hub API](https://github.com/wexinc/ps-cbs-notification-hub-api)
  * [Notificaiton Hub SMS Consumer](https://github.com/wexinc/ps-epec-nh-sms-consumer)
  * [Notiifcaiton Hub Status API](https://github.com/wexinc/ps-epec-nh-status-api)
  * [Fraud Detection Gateway](https://github.com/wexinc/rp-fraud-detection-gateway)
  * [Fraud Detection Gateway](https://github.com/wexinc/fleet-fraud-detection-gateway-service)

<!-- Parent: Designs -->
<!-- Parent: Mobility -->
<!-- Title: RFC-505 Value-Added Service (VAS) Tax Implementation -->

<!-- Include: macros.md -->
# Value-Added Service (VAS) Tax Implementation

```text
Authors: <AUTHORS>
Publish Date: 2025-04-18
Category: Designs
Subtype: Mobility
```

## Executive Summary

WEX Mobility is implementing a centralized Value-Added Service (VAS) Tax calculation and processing system through a centralized Fee Hub leveraging both Kafka and APIs. This solution will streamline the current fragmented process by creating a single integration point with AvaTax [^1], ensuring consistent tax calculations across multiple systems. This design aims to reduce system integration complexity and improve tax compliance, resulting in more accurate fee processing and reporting.

This solution drives improvements towards the following Tech Transformation Vectors:

| Vector | Impact |
|--------|--------|
| ✅ 🛡️ Product Security | Centralizes tax calculation access, reducing attack surface |
| ✅ 🔄 Reliability | Implements resilient event-driven architecture with failover |
| ✅ ⚡ Product Innovation Velocity (PiV) | Enables faster integration of new fee types |
| ✅ ☁️ SaaS Maturity | Leverages cloud-native services and modern patterns |
| 🔲 🤖 AI/ML Maturity | Not applicable |

## 💼 Change Context

* **Organization:** Mobility
* **Purpose:** Centralize VAS tax calculation to improve compliance and reduce integration complexity
* **Users:**
  * TranSys Core Processing
  * Note: This is a system-to-system integration without direct human users
* **Integration Points:** Salesforce, Zuora [^1], Siebel, PeopleSoft, AvaTax, EV Mesh [^1]
* **New Third-Party Software:** None (AvaTax already approved and in use)
* **Impacted Products:**
  * NAM TranSys
* **Impacted Teams:**
  * SRE Team
  * Finance Team
  * Accounting Team
  * CID Team
  * PeopleSoft Team
  * NAM TranSys Team
* **Design Qualities:**
  * ✅ 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * ✅ 🟡 New Pattern Adoption
  * ✅ 🟡 New Product or Capability
  * ✅ 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

## Diagrams

### System Context Diagram

```mermaid
graph

    classDef largeNode stroke-width:2px,padding:200px,font-size:28px

    subgraph FeeSources[Fee Sources]
          direction TB
          Zuora[Zuora]
          Siebel[Siebel]
          EV[EV Mesh]

    end

    subgraph AivenKafka[Aiven Kafka]
        TaxRequestKafkaTopic[(Tax Request
        Kafka Topic)]
        TaxResponseKafkaTopic[(Tax Response
        Kafka Topic)]
        EVKafka[(EV Kafka)]
    end

    subgraph TranSys
      ODS[TranSys Core Processing]
      FeeHub[Fee Hub]
    end

    subgraph backendFinancialSystems[Backend Financial Systems]
        direction LR
        PeopleSoft[PeopleSoft]
        RDW[Reporting Data Warehouse]
    end

    AvaTax[[AvaTax]]:::largeNode

    EV <-->|1\\. Tax Calculation Request| AvaTax
    EV --> |2\\. Fee + Tax Event| EVKafka
    EVKafka -->|Fee + Tax Event| ODS
    Zuora -->|2\\. Fee + Tax Data| RDW
    Zuora <--> |1\\. Tax Calculation Request| AvaTax
    RDW -->|\- Zuora Fees With Tax Data
    \- Some Ancillary Fees Without Tax Data | ODS

    Siebel -->|Fee Configuration| ODS
    ODS -->|Tax Calculation Request Event|TaxRequestKafkaTopic
    ODS <-->|Tax Calculation Request|FeeHub
    TaxRequestKafkaTopic -->|Consume Events| FeeHub
    FeeHub <-->|Tax Calculation Request| AvaTax
    TaxResponseKafkaTopic -->|Consume Events| ODS
    FeeHub -->|Fee & Tax Data|TaxResponseKafkaTopic
    ODS -->|Aggregated Data| PeopleSoft
    ODS -->|Fee
    + Tax Detail Data| RDW
```

### Cloud Architecture Diagram

```mermaid
graph TB
      subgraph "AWS"
        subgraph "WEX Fabric (EKS)"
          FeeHub[Fee Hub]
          TranSys
        end
      end

      subgraph "Aiven Cloud"
        RequestTopic[(Request Topic)]
        ResponseTopic[(Response Topic)]
      end

    FeeHub -->|Publish Events| ResponseTopic
    RequestTopic --> |Consume Events|FeeHub
    FeeHub <-->|Synchronous API Calls| TranSys
    TranSys <-->| Publish Events | RequestTopic
```

## ⚖️ Evaluation Criteria

### 🛡️ Product Security

#### Data Compliance

The following types of Class 1 Protected Data is transmitted, processed, or stored in this solution:

* 🔲 PCI
* 🔲 PII (Billing/shipping addresses used but no direct PII)
* 🔲 PHI
* 🔲 Other

Data Protection Controls:

* ✅ Data is protected at rest, in transit, and in use per WEX IT policy
* ✅ HTTPS using TLS 1.2 or higher
* ✅ Message encryption
* ✅ Database Connections using TLS 1.2 or higher
* 🔲 Row-level data encryption
* ✅ Transparent data encryption
* 🔲 De-identification (tokenization, hashing, masking, redaction)
* 🔲 Digital Signatures
* ✅ Access control (permission, role, policy)

Data Retention:

* Data archived after: `3 years` (Per WEX Financial data retention policy)
* Data purged after: `7 years` (Per tax record requirements)
* Logs purged after: `90 days` (Standard operational logs)

#### Data Ingress / Egress

This solution uses the following data flow patterns:

* 🔲 Customer-to-WEX traffic
* 🔲 WEX-to-Customer traffic
* 🔲 Vendor-to-WEX traffic
* ✅ WEX-to-Vendor traffic
* ✅ Internal system-to-system traffic

##### WEX-to-Vendor traffic

| Origin System | Destination System | Protocol | Authentication | Data Classification | Encryption Methods |
|---------------|-------------------|----------|----------------|-------------------|-------------------|
| Fee Hub | AvaTax | HTTPS | OAuth 2.0 | Class 2 | TLS 1.2+ |

##### Internal system-to-system traffic

| Origin System | Destination System | Protocol | Authentication | Data Classification | Encryption Methods |
|---------------|-------------------|----------|----------------|-------------------|-------------------|
| TranSys | Fee Hub | HTTPS | OAuth 2.0 | Class 2 | TLS 1.2+ |
| Fee Hub | Aiven Kafka | Kafka | OAuth 2.0 | Class 2 | TLS 1.2+ |

```mermaid
flowchart LR

  AvaTax["AvaTax (External)"]

  subgraph "Data Protection Controls"
    TranSys[TranSys] <-->|"TLS 1.2+ (Internal)"| FeeHub[Fee Hub]
    direction TB
    Auth[OAuth 2.0]
  end

  FeeHub <-->|"TLS 1.2+ (Egress)"|AvaTax
  FeeHub --> |Authentication| Auth
  TranSys --> |Authentication| Auth
```

### Authentication & Authorization

WEX Employee Authentication Components:

* 🔲 IdentityIQ SAML
* 🔲 Okta Workforce Identity
* 🔲 Active Directory: `WEXPRODR`
* 🔲 Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* ✅ Other: `Not Applicable (no user interaction with the application)`

WEX Employee automated provisioning & deprovisioning:

* 🔲 Yes
* 🔲 No
* ✅ Not Applicable (no user interaction with the application)

WEX Customer Authentication Components:

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* ✅ WEX customers do not authenticate
* 🔲 Other

WEX Service Principal Types:

* 🔲 Amazon Managed Identity
* 🔲 Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA
* 🔲 Active Directory User Service Account
* 🔲 Local user
* 🔲 Digital certificates
* 🔲 Local administrator
* ✅ OAuth 2.0

All employee, customer, and service principal credentials require periodic rotation:

* ✅ Yes
* 🔲 No

Service principal credentials are automatically rotated:

* ✅ Yes
* 🔲 No

### Network Traffic Patterns

**Inbound Connectivity:**

* 🔲 Protected by Imperva WAF
* 🔲 Direct inbound (not WAF-protected)
* 🔲 Customer/vendor traffic
* 🔲 WEX business user traffic
* 🔲 WEX support user traffic

**Outbound Connectivity:**

* 🔲 To customer/vendor systems
* 🔲 From WEX network (support)
* 🔲 Class 1 data transmission
* 🔲 Class 2 data transmission

**Security Controls:**

* 🔲 Bot protection enabled
* ✅ Automated certificate renewal
* ✅ TLS 1.2+ enforcement
* ✅ Network traffic encryption

### 🤖 AI/ML

Indicate if this design includes any new AI/ML components:

* 🔲 Yes
* ✅ No

Indicate if all AI/ML components have been reviewed for AI/ML compliance:

* 🔲 Yes
* 🔲 No
* ✅ Not Applicable

### 🔄 Reliability

#### High Availability Controls

* ✅ Availability Zone Redundancy
* ✅ Regional Failover
* 🔲 Automated Failover
* 🔲 Multi-Region Active-Active
* ✅ Load Balancing
* 🔲 Content Delivery Network
* ✅ Monitoring & Alerting
* 🔲 Other

### Outage Impact Assessment

Business Impact:

* 🔲 Critical customer business impact
* ✅ Partial loss of non-critical functionality
* 🔲 Customer recovery assistance required
* ✅ Multiple lines of business affected
* ✅ Multiple products in LOB affected
* 🔲 Multiple customers affected
* 🔲 External SLA impact
* ✅ Internal SLA impact
* ✅ Engineering support required

Recovery Actions:

* ✅ Infrastructure deployment/patching
* ✅ Application deployment
* ✅ Configuration updates (App/LB/DNS/Firewall/Gateway)
* ✅ Data recovery operations
* 🔲 Other

#### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | `AWS` |
| Primary Region | `US West` |
| Failover Region | `US East` |
| Production VNet | `vpc-0c1222700dc8adc35 (West)` `vpc-3e3ed244 (East)` |
| WEX Fabric ID | `fleet-na` |

#### Vendor Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Vendor Name | `Avalara (AvaTax)` |
| Cloud Provider | `AWS` |
| Primary Region | `US East` |
| Failover Region | `US West` |

#### Network Connectivity Services

* 🔲 SDWAN
* 🔲 AWS Private Link
* 🔲 Azure Private Link
* 🔲 Netskope Private Access (NPA)
* 🔲 Point-to-Point VPN
* ✅ Network Peering
* 🔲 Client VPN
* 🔲 DMZ

### ☁️ SaaS

#### Key Components

| Component | Description | Technology | Purpose |
|-----------|-------------|------------|---------|
| Fee Hub | Tax calculation service | Java/Spring Boot | Common service interface for AvaTax requests |
| Kafka Topics | Eventing | Aiven Kafka | Async communication between systems |
| AvaTax API | Tax calculation engine | REST API | Determine tax amounts |

#### Design Impact Scope

* 🔲 Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* ✅ Vendor & Product Risk Assessment completed for AvaTax

#### Logging & APM

* ✅ Splunk Forwarding
* ✅ DataDog Forwarding
* ✅ Security Event Logging
* 🔲 Synthetic Testing
* ✅ Log Archival
* ✅ Log Purging
* 🔲 Class 1 Data Logging Controls

### Design Patterns

List any design, delivery, or support patterns incorporated in this design:

* **Pattern 1:** [Event-Driven Architecture](https://wexinc.atlassian.net/wiki/spaces/PTA/pages/153788613542/Architecture+Outcomes+Patterns+for+Reuse#Design-Using-Event-Driven-and-Eventing-Platform)
* **Pattern 2:** [Microservices](https://wexinc.atlassian.net/wiki/spaces/PTA/pages/153788613542/Architecture+Outcomes+Patterns+for+Reuse#Design-Using-Microservices-%26-Synchronous-APIs)
* **Pattern 3:** Data Enrichment

### Standards Adoption

Deviations from standard tools, services, and reference architectures are:

* 🔲 **WEX Fabric:** `No deviation`
* 🔲 **SCM (GitHub Enterprise):** `No deviation`
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `No deviation`
* 🔲 **DFS (Panzura):** `No deviation`
* 🔲 **Outbound Email (PowerMTA/Sendgrid):** `No deviation`
* 🔲 **SFTP (GoAnywhere):** `No deviation`
* 🔲 **WAF (Imperva):** `Internal systems only, no external exposure`
* 🔲 **CDN (Imperva):** `No deviation`
* 🔲 **Logging (Splunk):** `No deviation`
* 🔲 **APM (DataDog):** `No deviation`
* 🔲 **Auth Gateway:** `No deviation`
* 🔲 **AI Gateway:** `No deviation`
* 🔲 **Eventing Platform:** `No deviation`
* 🔲 **WEX Data Platform:** `No deviation`
* 🔲 **Tokenizer:** `No deviation`
* 🔲 **Notification Hub:** `No deviation`

### 📋 Relevant Context

#### Secure Coding

### Technology Stack

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | `Java, Spring Boot` |
| Database Technologies | `PostgreSql` |
| Storage Solutions | `N/A` |
| Operating Systems | `Linux` |
| Containerization | `WEX Fabric (Amazon EKS)` |
| Client Platforms | `API, Kafka` |

### Security Controls**

Select the code quality scanning services that are active for all development repositories (select all that apply):

* ✅ Snyk
* ✅ Cycode
* ✅ Mend Renovate
* ✅ GitHub Copilot
* ✅ Dependabot
* 🔲 Other

#### Procurement Details

Third-Party Software Type:

* ✅ SaaS
* 🔲 IaaS
* 🔲 AWS PaaS
* 🔲 Azure PaaS
* 🔲 Code Dependency Package
* 🔲 Desktop
* 🔲 Mobile App
* 🔲 Browser - Web Browser

Additional Considerations:

* 🔲 Software installation & configuration is automated
* 🔲 Cost exceeds $25,000
* ✅ Solution is part of Target State Architecture
* 🔲 Temporary solution planned for replacement

Alternatives Considered:

* **N/A**: AvaTax was already in use at WEX

## 🧩 Risk Analysis & Dependencies

### 🚨 Key Risks

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Integration Complexity | High | Medium | Phased rollout approach |
| Tax Calculation Accuracy | High | Low | Comprehensive testing plan |
| Understanding all Fee flows | High | High | Research and document all known types of fees and what their flow/lifecycle is. |
| Tax Exemption Handling | Medium | Low | Monitoring and alerting |
| Handling adjustments and credits for previously taxed fees | Medium | High | Proper Training for the teams that issue credits.|

### 🔍 Critical Assumptions

* **AvaTax Capacity:** Can support volume and variety of tax calculations
* **No immediate changes for EV and Zuora:** Initially these will continue to use AvaTax directly.
* **Address Data:** CID can provide necessary shipping address information
* **Fee Aggregation:** Card shipments will be properly aggregated to avoid duplicate fees

### ⚠️ Known Issues

* Associating adjustments to original fees: We need to give more consideration to approaches for associating adjustments to the original fee transactions in order to also adjust the tax transactions accordingly.

### 📦 System Dependencies

| Dependency | Type | Criticality | Contact Team |
| -----------|------|-------------|--------------|
| AvaTax     | API  | High        | Avalara (external) |
| Aiven      | System  | High        | KaaS team |

## 🔗  Reference Links

* **Epic/Feature:** [`NAMC-10`](https://wexh.aha.io/features/NAMC-10)
* **Support Tickets:** `N/A`
* **Documentation:**
  * [`Solution notes`](https://docs.google.com/document/d/1yRnesGUXtUKv2qFyPaHECkR-u0HXw8xdgfjc8-4AVBI/edit?usp=sharing)
  * [`AvaTax Integration Guide`](https://developer.avalara.com/api-reference/AvaTax/rest/v2/methods/Transactions/CreateTransaction/)
* **Repositories:**
  * `N/A` (this is a to-be-built application)

[^1]: EV Mesh and Zuora will initially continue their direct AvaTax integration. As time/resources permit, they can remove those integrations and rely on the centralized processing through Fee Hub.

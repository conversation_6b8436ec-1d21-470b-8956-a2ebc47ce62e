<!-- Parent: Designs -->
<!-- Parent: Platform-as-a-Service (PaaS) -->
<!-- Title: RFC-480 GitHub Enterprise Managed Users Migration -->

<!-- Include: macros.md -->
# GitHub Enterprise Managed Users Migration

:isarb:

```text
Author: <PERSON>
Publish Date: 2024-03-11
Category: Designs
Subtype: Platform-as-a-Service (PaaS)
Ratified By: iSARB (2025-03-11)
```

## Executive Summary

WEX is proposing to migrate from standard GitHub Enterprise to GitHub Enterprise Managed Users (EMU) to address critical user management issues. The migration will provide centralized control over user lifecycle management, enhanced security, and improved compliance through integration with WEX's identity providers. This change impacts approximately 1,600 developers and will be implemented over 9-12 months with minimal disruption to development activities.

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## Change Context

* **Organization:** Platform-as-a-Service (PaaS)
* **Purpose:** Implement centralized GitHub user management through EMU
* **Users:** All WEX developers (~1,600 users)
* **Integration Points:** Okta, Sailpoint IIQ, Active Directory
* **Third-Party Software:** GitHub Enterprise (Approved Usage)
* **Impacted Products:**
  * GitHub Enterprise
  * CI/CD Systems
  * Development Tools

* **Design Review Qualities:**
  * ✅ 🔴 Core Tech Stack Changes
  * ✅ 🟡 New Pattern Adoption
  * 🔲 🟡 WEX Product Integration

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Preparation | 2024-Q1 | EMU Enterprise Account Setup |
| Initial Migration | 2024-Q2 | IDP/SCIM Integration |
| Team Migrations | 2024-Q3-Q4 | Team Readiness |
| Cleanup | 2025-Q1 | All Team Migrations Complete |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

* ✅ Completed without concerns

#### Data Classification & Protection

* **PCI Records:** `0`
* **PII Records:** `0`
* **PHI Records:** `0`

Data Protection Controls:

* ✅ HTTPS using TLS 1.2 or higher
* ✅ Access control (permission, role, policy)

#### AI/ML Compliance

* ✅ No AI/ML components

### 💼 Business & Enterprise Impact

#### Design Impact Scope

* ✅ Impacts or used by more than 25 users
* ✅ Vendor & Product Risk Assessment completed

#### Procurement Details

Software Type:

* ✅ SaaS

Additional Considerations:

* ✅ Solution is part of Target State Architecture

### 🔄 Operations & Reliability

#### High Availability Controls

* ✅ Regional Failover
* ✅ Monitoring & Alerting

#### Observability

Logging & APM Features:

* ✅ Security Event Logging
* ✅ Log Archival

### Network Traffic Patterns

**Security Controls:**

* ✅ TLS 1.2+ enforcement
* ✅ Network traffic encryption

### Authentication & Authorization

Authentication components for WEX Employees:

* ✅ Okta Workforce Identity

Service Principal types:

* ✅ Active Directory GMSA or MSA

Automatic credential rotation:

* ✅ Yes

### Secure Coding

#### Technology Stack

| Component | Selection |
|-----------|-----------|
| Operating Systems | containers |
| Client Platforms | web/api |

#### Security Controls

Active development repository scanning:

* ✅ GitHub Enterprise Security Features
* ✅ Snyk

## Diagrams

### System Context Diagram

```mermaid
graph TB
    Dev[Developer]
    GitHub[GitHub EMU]
    Okta[Okta]
    Sailpoint[Sailpoint IIQ]
    AD[Active Directory]
    
    Dev -->|Authenticate| Okta
    Okta -->|SSO| GitHub
    Sailpoint -->|SCIM| GitHub
    AD -->|Groups| Sailpoint
```

## Risk Analysis & Dependencies

* 🚨 **Key Risks:**
  * Repository URL changes requiring CI/CD reconfiguration
  * Service account migration complexity
  * Potential deployment disruptions during migration

* 📋 **Critical Assumptions:**
  * Teams will handle their own repository migrations
  * GitHub cost remains unchanged during transition
  * SCIM integration will handle all user provisioning

* 🔗 **System Dependencies:**
  * Okta SSO
  * Sailpoint IIQ
  * Active Directory
  * CI/CD Systems

## Reference Links

* **Documentation:**
  * [GitHub EMU iSARB Presentation](https://docs.google.com/presentation/d/1HA-lX4L__YHumSPsv0Ja9AhJQgDrunbukp6JgqzXzT8)
  * [GitHub EMU Documentation](https://docs.github.com/en/enterprise-cloud@latest/admin/identity-and-access-management/using-enterprise-managed-users-for-iam/about-enterprise-managed-users)
  * [GitHub EMU SAML Guide](https://docs.github.com/en/enterprise-cloud@latest/admin/managing-iam/configuring-authentication-for-enterprise-managed-users/configuring-saml-single-sign-on-for-enterprise-managed-users)

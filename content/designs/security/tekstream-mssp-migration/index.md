<!-- Parent: Designs -->
<!-- Parent: Information Security -->
<!-- Title: RFC-481 TekStream MSSP Migration -->

<!-- Include: macros.md -->
# TekStream MSSP Migration

:isarb:

```text
Author: <PERSON>
Publish Date: 2025-03-11
Category: Designs
Subtype: Information Security
Ratified By: iSARB (2025-03-11)
```

## Executive Summary

WEX Security Operations proposes migrating from Deepwatch VSOC services to TekStream's Managed Security Service Provider (MSSP) solution. This transition includes implementing Splunk SOAR for enhanced security incident management and automation. The change addresses performance and innovation concerns with the current provider while modernizing our security operations capabilities.

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## Change Context

* **Organization:** Information Security
* **Purpose:** Enhance security operations through improved MSSP services and automation
* **Users:** Security Operations team, Incident Response team
* **Integration Points:** Splunk Enterprise, Okta, Active Directory
* **Third-Party Software:** TekStream MSSP (Approved), Splunk SOAR
* **Impacted Products:**
  * Security Operations Center
  * Security Incident Response Platform
* **Design Review Qualities:**
  * ✅ 🟡 New Product or Capability
  * ✅ 🟡 Deviation from WEX Standards

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| POC Start | 2025-02 | Contract signing |
| Implementation | 2025-05 | POC completion |
| Migration | 2025-06 | Implementation completion |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

* ✅ In Progress

#### Data Classification & Protection

Data Protection Controls:

* ✅ Data is protected at rest, in transit, and in use per WEX IT policy
* ✅ HTTPS using TLS 1.2 or higher
* ✅ Access control (permission, role, policy)

### 💼 Business & Enterprise Impact

#### Procurement Details

Software Type:

* ✅ SaaS

Costs:

* Annual TekStream Service: $360,000
* Splunk SOAR Licensing: $100,000
* MDR Setup (CapEx): $100,000

### 🔄 Operations & Reliability

#### Security Controls

* ✅ Splunk
* ✅ Splunk Enterprise Security
* ✅ Splunk SOAR

### Authentication & Authorization

Select the authentication components for WEX Employees:

* ✅ Okta Workforce Identity
* ✅ Active Directory: `WEXPRODR`

### Design Patterns

* **Pattern 1:** [Security Operations Center](documentation url)
* **Pattern 2:** [Security Incident Response](documentation url)

## Diagrams

### System Context Diagram

```mermaid
graph TB
    SOC[Security Operations Center]
    SOAR[Splunk SOAR]
    ES[Splunk Enterprise Security]
    TekStream[TekStream MSSP]
    
    SOC -->|Manages| SOAR
    SOAR -->|Integrates| ES
    TekStream -->|Monitors| ES
```

## Risk Analysis & Dependencies

* 🚨 **Key Risks:**
  * Migration timing impact on security operations
  * Integration with existing security tools
  * Training requirements for new platform

* 📋 **Critical Assumptions:**
  * TekStream can meet WEX's service level requirements
  * Splunk SOAR implementation can be completed within timeline
  * Existing security data can be preserved during migration

## Reference Links

* **Documentation:**
  * [Tekstream iSARB Presentation](https://docs.google.com/presentation/d/1HRxC0xzqhqpwn_GN3LPa4SDw1N6aq5Cv0_7OUSrczC8)

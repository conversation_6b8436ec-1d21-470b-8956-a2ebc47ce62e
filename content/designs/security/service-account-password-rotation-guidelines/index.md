<!-- Parent: Designs -->
<!-- Parent: Information Security -->
<!-- Title: RFC-511 Service Account Password Rotation Guidelines -->

<!-- Include: macros.md -->
# Service Account Password Rotation Guidelines

:draft:

```text
Author: Enterprise Architecture Team
Title: Service Account Password Rotation Guidelines
Publish Date: 2025-05-15
Category: Designs
Subtype: Information Security
```

## Introduction

This document provides comprehensive guidelines for service account password rotation in enterprise systems. It outlines WEX's policies regarding password rotation, systems that require it, options for credential storage, effective methods for password rotation, and application design considerations for seamless password changes.

## Policy Overview

Service account passwords must be rotated according to our corporate security policies to reduce the risk of unauthorized access and comply with industry regulations. Service accounts represent a specific security concern due to their often elevated privileges and automated operation.

> <!-- Note -->
> Important
> All service account passwords must be rotated at least every 90 days, with mission-critical systems requiring rotation every 60 days. (@SecEng-Shaun<PERSON>erkin, can we validate this?)

### Authentication Methods Priority

The following authentication methods are listed in order of preference based on security best practices:

1. **Managed Identities (Highest Security)** (TODO: Would be good to have a few RFCs that have reference code on how to set these up)
   - AWS IAM Roles for EC2/ECS/Lambda
   - Microsoft Entra ID (Azure AD) Managed Identities
   - GCP Workload Identity
   - Ideal for: Cloud native applications, containerized services

2. **Federated Identity** (TODO: This should point to OWI documentation, as we should be using that whenever federated identity is a good fit)
   - OAuth 2.0 with OpenID Connect
   - SAML-based federation
   - JWT with short-lived tokens
   - Enterprise federation with MS Entra ID, Okta, or Ping
   - Ideal for: Service-to-service authentication within cloud environments

3. **Certificate-Based Authentication**
   - Mutual TLS (mTLS)
   - Client certificates
   - AD Certificate Services-issued certificates
   - Ideal for: Critical service-to-service communications, access to sensitive databases

4. **Directory Services**
   - Active Directory Group Managed Service Accounts (gMSA)
   - LDAP with secure binding
   - Ideal for: Windows services, on-premises applications

5. **API Keys with Additional Security Controls**
   - Signed requests (HMAC)
   - Request signing with API Gateway
   - IP restriction
   - Ideal for: 3rd party integrations, service-to-service when federation isn't available

6. **Short-Lived Token-Based Authentication**
   - Dynamic secrets with auto-expiry
   - Temporary access credentials (AWS STS)
   - Ideal for: Database connections, secret store access

7. **Username/Password with Advanced Protection (Lowest Preference)**
   - Strong password policies
   - Multi-factor authentication where available
   - Frequent rotation
   - Ideal for: Legacy systems without support for stronger methods

> <!-- Info -->
> Note
> Whenever possible, transition from lower to higher security authentication methods. Any new system implementation should use the highest appropriate method from this list.

### Connection Scenario Recommendations

| Scenario | Recommended Authentication Methods | Alternative Methods |
|----------|------------------------------------|---------------------|
| Service-to-Service (Internal) | 1. Managed Identities (AWS IAM roles, Entra ID) | 1. API Keys with HMAC |
| | 2. OAuth 2.0 with JWT | 2. AD gMSAs |
| | 3. mTLS with certificates | 3. Password with frequent rotation |
| Services to 3rd Party Providers | 1. OAuth 2.0 with OIDC | Username/password with IP restrictions |
| | 2. API Keys with request signing | |
| | 3. mTLS certificates | |
| Services to Secret Stores | 1. Managed Identities (AWS IAM roles, Entra ID) | Username/password with MFA |
| | 2. Short-lived tokens | |
| | 3. Client certificates | |
| Services to Databases | 1. Managed Identities (RDS IAM, Entra ID) | 1. AD gMSAs |
| | 2. Certificate-based auth | 2. Username/password with frequent rotation |
| | 3. Short-lived tokens | |

### Systems Requiring Password Rotation

The following systems require regular service account password rotation:

- Production applications and services, in particular those handling PCI, PII, or financial information
- Database systems
- Third-party integrations accessing internal resources

## Credential Storage Options

(nk-note: I have prioritized the list of options based on my understanding of our pref, I'm hoping this can generate some meaningful conversation. Hoping the conversation can generate some "why" that provides more color)

### AWS Secrets Manager

AWS Secrets Manager is ideal for AWS-hosted applications with these capabilities:

- Automated rotation with Lambda functions
- Integration with AWS IAM
- Encryption of secrets at rest
- Cross-region replication
- Automatic rotation scheduling

### Azure Key Vault

Azure Key Vault is recommended for Azure-hosted services and offers:

- Centralized secret management
- Integration with Azure AD
- Key rotation policies
- Access policies and RBAC
- Monitoring and logging

### Delenia

Internal Delenia platform provides:

- Enterprise-wide credential management
- Integration with corporate identity systems
- Workflow approval processes
- Compliance reporting
- Audit trail for access requests

### Hashicorp Vault

Hashicorp Vault provides a secure and scalable solution for service account credential management with the following benefits:

- Dynamic secrets generation
- Automatic credential rotation
- Fine-grained access control
- Audit logging capabilities
- Integration with CI/CD pipelines

```mermaid
sequenceDiagram
    participant App as Application
    participant Vault as HashiCorp Vault
    participant DB as Database

    App->>Vault: Request credentials
    Vault->>Vault: Authenticate & authorize
    Vault->>DB: Rotate password (if needed)
    Vault-->>App: Return temporary credentials
    App->>DB: Connect with credentials
    Note over App,DB: After lease expiration
    Vault->>DB: Automatically rotate password
```

## Password Rotation Implementation

### Active Directory Service Accounts

AD service accounts should use one of the following approaches:

1. **Managed Service Accounts (MSAs)**
   - Self-managing accounts that automatically handle password rotation
   - No manual intervention required
   - Limited to Windows Server 2008 R2 and newer

2. **Group Managed Service Accounts (gMSAs)**
   - Extends MSA functionality across multiple servers
   - Requires Windows Server 2012 or newer
   - Handles password rotation automatically

3. **Scheduled Rotation Scripts**
   - PowerShell scripts using secure credential storage
   - Scheduled tasks to rotate passwords on defined intervals
   - Must handle application notification or restart

#### Implementation Example (gMSA)

```powershell
# Create a new gMSA
New-ADServiceAccount -Name "AppServiceAccount" -DNSHostName "app.wex.local" -PrincipalsAllowedToRetrieveManagedPassword "Domain Controllers", "WebServers"

# Install gMSA on application server
Install-ADServiceAccount -Identity "AppServiceAccount"

# Configure service to use gMSA
Set-Service -Name "ApplicationService" -StartupType Automatic -Status Running -ServiceAccount "AppServiceAccount"
```

### Database Connections

Database service account rotation requires special attention due to active connections. Recommended approaches:

#### Connection Pooling with Rotation

```mermaid
sequenceDiagram
    participant App as Application
    participant Pool as Connection Pool
    participant SM as Secrets Manager
    participant DB as Database

    Note over App,DB: Normal Operation
    App->>Pool: Request connection
    Pool->>App: Provide existing connection
    
    Note over SM,DB: Password Rotation Time
    SM->>DB: Update password
    SM-->>Pool: Notify of password change
    
    Note over Pool,DB: Graceful Transition
    Pool->>Pool: Mark existing connections for retirement
    Pool->>DB: Establish new connections with new password
    App->>Pool: Request connection
    Pool->>App: Provide new connection
```

## Application Design Considerations

To make password rotation seamless for applications, consider these design patterns:

### 1. Dynamic Configuration Reloading

Design applications to detect and reload credentials without requiring restart:

- Implement configuration watchers
- Use abstracted credential providers
- Implement circuit breaker patterns for connection failures

### 2. Secret Retrieval at Runtime

Instead of storing credentials in configuration files:

- Retrieve secrets at runtime from secure stores
- Cache credentials with appropriate TTL
- Implement graceful fallback mechanisms

### 3. Connection Pool Management

For database and service connections:

- Implement smart connection pooling
- Gradually replace connections after credential rotation
- Monitor connection failures as indicator of rotation issues

### 4. Credential Versioning

Support multiple valid credentials during transition periods:

```mermaid
mindmap
  root((Credential<br>Management))
    Versioning
      Current Credentials
      Previous Credentials
      Grace Period
    Validation
      Primary Attempt
      Fallback Attempt
      Failure Handling
    Monitoring
      Success Rate
      Failure Alerts
      Usage Metrics
```

## Implementation Checklist

### Assessment Phase

☑️ Identify all service accounts requiring rotation  
☑️ Document rotation schedule and responsibilities  
🔲 Inventory all authentication certificates in use  
🔲 Evaluate potential for managed identity adoption  
🔲 Perform security assessment of current credential practices  

### Implementation Planning

☑️ Select appropriate credential storage solution  
🔲 Document authentication method priorities per scenario type  
🔲 Design credential versioning strategy  
🔲 Map deployment environments to authentication methods  
🔲 Plan migration path from lower to higher security methods  

### Technical Implementation

☑️ Implement rotation mechanisms (automated where possible)  
🔲 Configure managed identities where appropriate  
🔲 Set up certificate management infrastructure  
🔲 Implement monitoring for failed authentication attempts  
🔲 Enable comprehensive audit logging  

### Validation & Testing (TODO: We should have an established framework that can automate, and test rotation)

🔲 Test password rotation procedures in non-production environment  
🔲 Test certificate rotation in non-production environment  
🔲 Validate managed identity access patterns  
🔲 Perform security testing against credential stores  
🔲 Document emergency procedures for rotation failures  

### Application Adaptation (TODO: We should have a reference architecture in a few languages that people can borrow/learn from)

🔲 Update application code to handle credential changes  
🔲 Implement connection pooling best practices  
🔲 Add runtime credential retrieval patterns  
🔲 Configure applications to use managed identities  
🔲 Implement graceful failure handling for credential access  

## Conclusion

Effective service account password rotation is essential for maintaining security posture and meeting compliance requirements. By following these guidelines and implementing the recommended patterns, teams can ensure secure and uninterrupted service operations through password rotation events.

---

## References

- [WEX Security Policy Document](https://wexinc.sharepoint.com/security/policies)
- [HashiCorp Vault Documentation](https://www.vaultproject.io/docs)
- [AWS Secrets Manager Best Practices](https://docs.aws.amazon.com/secretsmanager/latest/userguide/best-practices.html)
- [Azure Key Vault Security Documentation](https://learn.microsoft.com/en-us/azure/key-vault/general/security-features)
- [NIST Guidelines for TLS Server Certificates](https://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-52r2.pdf)
- [AWS Certificate Manager Documentation](https://docs.aws.amazon.com/acm/latest/userguide/acm-overview.html)
- [HashiCorp Vault PKI Secrets Engine](https://www.vaultproject.io/docs/secrets/pki)

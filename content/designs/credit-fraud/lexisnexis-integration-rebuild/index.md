<!-- Parent: Designs -->
<!-- Parent: Credit & Fraud -->
<!-- Title: RFC-494 LexisNexis Integration Rebuild -->

<!-- Include: macros.md -->
# LexisNexis Integration Rebuild

:draft:

```text
Author: [To be assigned]
Publish Date: 2025-05-02
Category: Designs
Subtype: Credit & Fraud
```

> [!info]
> **Informal design discussions** with all relevant coaches and **Tech Partner coordination** must be completed before submitting a design for review and consideration.
>
> * Design consultation coaches can be found in [RFC-420 Design Consultation Contacts](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155035730254/RFC-420+Design+Consultation+Contacts).
> * iSARB Tech Partners for each organization can be found in [RFC-421 iSARB Tech Partners](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155036287549/RFC-421+iSARB+Tech+Partners).
> * Details about the formal review process can be found in [RFC12-Formal Review Process](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154594017461/RFC12-Formal+Review+Process).

## Executive Summary

This design proposes a rebuild of the LexisNexis integration used for risk assessment and fraud prevention. The rebuild will modernize our integration with LexisNexis services using current WEX standards and best practices, improving reliability, maintainability, and observability of this critical risk assessment capability.

The new integration will leverage WEX's standard services and modern cloud architecture to provide more robust identity verification and fraud detection capabilities while reducing operational overhead and improving monitoring capabilities.

## Change Context

* **Organization:** Credit & Fraud Technology
* **Purpose:** Modernize and improve reliability of critical risk assessment integration
* **Users:** Risk analysts, Fraud detection systems, Automated onboarding processes
* **Integration Points:**
  * LexisNexis Risk Solutions API
  * WEX Risk Assessment Platform
  * Customer Onboarding Systems
* **Third-Party Software:** LexisNexis (Approved Usage)
* **Impacted Products:**
  * WEX Risk Assessment Platform
  * Customer Onboarding Platform
  * Fraud Detection System
* **Design Review Qualities:**
  * 🔲 🔴 Core Tech Stack Changes
  * ☒ 🟡 New Pattern Adoption
  * ☒ 🟡 WEX Product Integration
  * ☒ 🟡 Deviation from WEX Standards

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Phase 1 - API Gateway & Core Integration | 2025-07 | None |
| Phase 2 - Migration of Existing Services | 2025-09 | Phase 1 |
| Phase 3 - Decommission Legacy Integration | 2025-10 | Phase 2 |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

* ☒ Completed without concerns

#### Data Classification & Protection

Estimate of Class 1 Data records:

* **PCI Records:** `0`
* **PII Records:** `>1,000,000`
* **PHI Records:** `0`

Data Protection Controls:

* ☒ Data is protected at rest, in transit, and in use per WEX IT policy
* ☒ HTTPS using TLS 1.2 or higher
* ☒ Message encryption
* ☒ Database Connections using TLS 1.2 or higher
* ☒ Row-level data encryption
* ☒ Transparent data encryption
* ☒ De-identification (tokenization, hashing, masking, redaction)
* ☒ Access control (permission, role, policy)

Data Retention:

* Data archived after: `90 days`
* Data purged after: `7 years`
* Logs purged after: `1 year`

#### AI/ML Compliance

* ☒ No AI/ML components

### 💼 Business & Enterprise Impact

#### Design Impact Scope

* ☒ Impacts or used by more than 25 users
* ☒ Vendor & Product Risk Assessment completed

#### Procurement Details

Software Type:

* ☒ AWS PaaS
* ☒ Code Dependency Package

Additional Considerations:

* ☒ Software installation & configuration is automated
* ☒ Solution is part of Target State Architecture

### 🔄 Operations & Reliability

#### High Availability Controls

* ☒ Availability Zone Redundancy
* ☒ Regional Failover
* ☒ Automated Failover
* ☒ Load Balancing
* ☒ Monitoring & Alerting

#### Outage Impact Assessment

Business Impact:

* ☒ Critical customer business impact
* ☒ Multiple lines of business affected
* ☒ Multiple customers affected
* ☒ External SLA impact

Recovery Actions:

* ☒ Infrastructure deployment/patching
* ☒ Application deployment
* ☒ Configuration updates (App/LB/DNS/Firewall/Gateway)

#### Observability

Logging & APM Features:

* ☒ Splunk Forwarding
* ☒ DataDog Forwarding
* ☒ Security Event Logging
* ☒ Synthetic Testing
* ☒ Log Archival
* ☒ Log Purging
* ☒ Class 1 Data Logging Controls

### Data Ingress / Egress

#### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | AWS |
| Primary Region | us-east-1 |
| Failover Region | us-west-2 |
| WEX Fabric ID | [To be assigned] |

#### Network Traffic Patterns

**Inbound Connectivity:**

* ☒ Protected by Imperva WAF
* ☒ WEX business user traffic  
* ☒ WEX support user traffic

**Outbound Connectivity:**

* ☒ To customer/vendor systems
* ☒ Class 1 data transmission

**Security Controls:**

* ☒ Bot protection enabled
* ☒ Automated certificate renewal
* ☒ TLS 1.2+ enforcement
* ☒ Network traffic encryption

### Authentication & Authorization

Select the authentication components for WEX Employees:

* ☒ Okta Workforce Identity

Indicate if access for WEX employees is automatically provisioned & deprovisioned:

* ☒ Yes

Select the types of Service Principals:

* ☒ Amazon Managed Identity
* ☒ Digital certificates

Indicate if all employee, customer, and service principal credentials require periodic rotation:

* ☒ Yes

Indicate if service principal credentials are automatically rotated:

* ☒ Yes

### Secure Coding

#### Technology Stack

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | java, spring-boot |
| Database Technologies | postgres |
| Storage Solutions | S3 |
| Operating Systems | containers |
| Client Platforms | api |

#### Security Controls

* ☒ Snyk
* ☒ Cycode
* ☒ Mend Renovate
* ☒ Dependabot

### Design Patterns

* **Event-Driven Architecture:** [RFC-380 Microservices with BFFs](../../guardrails/design-pattern/RFC-380-microservices-with-bffs/index.md)
* **API Gateway Pattern:** [RFC-336 API Gateway Pattern](../../guardrails/design-pattern/api-pattern-selection/index.md)

### Paved Road Adoption

No deviations from standard tools and services.

## Diagrams

### System Context Diagram

```mermaid
graph TB
    Risk[Risk Assessment System]
    Onboard[Customer Onboarding]
    Gateway[API Gateway]
    Integration[LexisNexis Integration]
    LexisNexis[LexisNexis Services]
    Queue[Event Queue]
    
    Risk -->|Request| Gateway
    Onboard -->|Request| Gateway
    Gateway -->|Route| Integration
    Integration -->|Query| LexisNexis
    Integration -->|Events| Queue
    Queue -->|Notify| Risk
```

### Cloud Architecture Diagram

```mermaid
graph TB
    subgraph "AWS Cloud"
        WAF[Imperva WAF]
        Gateway[API Gateway]
        App[Integration Service]
        DB[(Postgres)]
        Queue[[EventBridge]]
        S3[S3 Bucket]
    end
    
    subgraph "External"
        LexisNexis[LexisNexis API]
    end
    
    Client[Internal Client] -->|HTTPS| WAF
    WAF -->|Route| Gateway
    Gateway -->|Request| App
    App -->|Store| DB
    App -->|Archive| S3
    App -->|Events| Queue
    App <-->|Query| LexisNexis
```

## Risk Analysis & Dependencies

* 🚨 **Key Risks:**
  * High volume of PII data processing requires careful security controls
  * LexisNexis API availability impacts customer onboarding
  * Data retention compliance requirements

* 📋 **Critical Assumptions:**
  * LexisNexis API specifications remain stable
  * Current data volume growth remains within projections
  * Existing integration can be maintained during migration

* ⚠️ **Known Issues:**
  * Legacy system decommissioning requires coordination
  * Historical data migration planning needed
  * Training required for new monitoring tools

* 🔗 **System Dependencies:**
  * Customer Onboarding Platform
  * Risk Assessment Platform
  * Fraud Detection System
  * Event Processing Pipeline

## Reference Links

* **Documentation:**
  * [LexisNexis API Documentation](https://developer.lexisnexis.com/)
  * [WEX Risk Assessment Platform](https://confluence.wexinc.com/risk-platform)

# Retrieval Augmented Generation Extensions Sample

## Description

This project is a Go application that demonstrates how to use retrieval augmented generation in an agent-based GitHub Copilot Extension.

## Prerequisites

- Go 1.16 or higher
- Set the following environment variables (example below):

```bash
export PORT=8080
export CLIENT_ID=Iv1.0ae52273ad3193eb // the application id
export CLIENT_SECRET="your_client_secret" // generate a new client secret for your application
export FQDN=https://6de513480979.ngrok.app // use ngrok to expose a url
```

## Installation

1. Clone the repository:

    ```bash
    <NAME_EMAIL>:copilot-extensions/rag-extension.git
    cd rag-extension
    ```

2. Install dependencies:

    ```bash
    go mod tidy
    ```

## Usage

1. Start up ngrok with the port provided:

    ```bash
    ngrok http http://localhost:8080
    ```

2. Set the environment variables (use the ngrok generated url for the `FDQN`)
3. Run the application:

    ```bash
    go run .
    ```

## Accessing the Agent in Chat

1. In the `Copilot` tab of your Application settings (`https://github.com/settings/apps/<app_name>/agent`)

    - Set the URL that was set for your FQDN above with the endpoint `/agent` (e.g. `https://6de513480979.ngrok.app/agent`)
    - Set the Pre-Authorization URL with the endpoint `/auth/authorization` (e.g. `https://6de513480979.ngrok.app/auth/authorization`)

2. In the `General` tab of your application settings (`https://github.com/settings/apps/<app_name>`)

    - Set the `Callback URL` with the `/auth/callback` endpoint (e.g. `https://6de513480979.ngrok.app/auth/callback`)
    - Set the `Homepage URL` with the base ngrok endpoint (e.g. `https://6de513480979.ngrok.app/auth/callback`)

3. Ensure your permissions are enabled in `Permissions & events` >

    - `Account Permissions` > `Copilot Chat` > `Access: Read Only`

4. Ensure you install your application at (`https://github.com/apps/<app_name>`)
5. Now if you go to `https://github.com/copilot` you can `@` your agent using the name of your application.

## Testing

Test out the agent with the following commands!

| Description | Prompt |
| --- |--- |
| User asking `@agent` how to configure a Copilot extension | `@agent How do I configure a copilot extension?` |
| User asking `@agent` what a Copilot extension looks like | `@agent What is the response format for a copilot extension?` |

## Copilot Extensions Documentation

- [Using Copilot Extensions](https://docs.github.com/en/copilot/using-github-copilot/using-extensions-to-integrate-external-tools-with-copilot-chat)
- [About building Copilot Extensions](https://docs.github.com/en/copilot/building-copilot-extensions/about-building-copilot-extensions)
- [Set up process](https://docs.github.com/en/copilot/building-copilot-extensions/setting-up-copilot-extensions)
- [Communicating with the Copilot platform](https://docs.github.com/en/copilot/building-copilot-extensions/building-a-copilot-agent-for-your-copilot-extension/configuring-your-copilot-agent-to-communicate-with-the-copilot-platform)
- [Communicating with GitHub](https://docs.github.com/en/copilot/building-copilot-extensions/building-a-copilot-agent-for-your-copilot-extension/configuring-your-copilot-agent-to-communicate-with-github)

## Deployment & Cost Considerations

### Deployment Instructions

1. **Set up your environment:**
   - Ensure you have Go 1.16+ installed.
   - Set the required environment variables (`PORT`, `CLIENT_ID`, `CLIENT_SECRET`, `FQDN`).
   - Use [ngrok](https://ngrok.com/) or a similar tool to expose your local server to the internet for development/testing.

2. **Run the application:**
   - Start ngrok: `ngrok http http://localhost:8080`
   - Set environment variables using the ngrok URL for `FQDN`.
   - Run: `go run .`

3. **Configure your GitHub App:**
   - Set the Copilot and OAuth URLs in your GitHub App settings as described above.

4. **Production deployment:**
   - For production, deploy to AWS EC2. A t3.micro or t3.small instance is suitable for most use cases.
   - Use a managed HTTPS endpoint (e.g., AWS Certificate Manager with an Application Load Balancer) and secure your secrets appropriately.

### Infrastructure Cost Considerations

- **Development:** Using ngrok and running locally is free (except for ngrok paid plans if you need custom domains or reserved URLs).
- **Production:**
  - An AWS EC2 t3.micro instance typically costs $8–$10/month (on-demand, Linux, US regions).
  - Additional costs for bandwidth, storage (EBS), and managed SSL certificates may apply.
  - Using an Application Load Balancer and AWS Certificate Manager for HTTPS may add $20–$25/month depending on usage.

### Copilot API Usage & Cost

- **Copilot API:** This agent uses the GitHub Copilot API for two main operations:
  - **Completions:** Generating text responses (chat, answers, code suggestions) based on user input. Each user message typically results in one or more completions requests.
  - **Embeddings:** Converting text (such as user queries or documents) into numerical vectors for semantic search and context retrieval. Each file processed for context typically requires 30–80 embedding requests, depending on file size and chunking strategy.

- **Pricing:**
  - As of July 2025, Copilot for Business is $19/user/month (see [GitHub Copilot Pricing](https://github.com/pricing)).
  - API usage for extensions is subject to GitHub's terms and may be included in your Copilot subscription. For high-volume or commercial use, contact GitHub for details.
  - As of July 2025, Wex has a Copilot Business subscription, not the Enterprise subscription. Features like knowledge bases and some LLMs are not available in the Business subscription.
  - Premium features are now charged, based on number of requests per user. (300 premium requests per user, per month, on the Business subscription)
  - As of July 2025, GPT-4.1 and GPT-4o are the included models, and do not consume any premium requests if you are on a paid plan. This rule may change in the future.
  - Better results can be achieved with different models. The costs may be higher if a model with different multiplier is used in the request.

- **API Rate Limits:**
  - Copilot API has rate limits. Exceeding them may result in throttling or additional charges.
  - **Current Copilot Business Rate Limits:**
    - Up to 80 requests per minute per user for completions endpoints.
    - Up to 10 requests per minute per user for embeddings endpoints.
    - Rate limits are subject to change; always consult the latest GitHub Copilot documentation for updates.

- **Cost Considerations:**
  - The $19/user/month subscription covers both completions and embeddings usage within reasonable limits for most business use cases.
  - The solution and architecture described in this document do **not** imply any GitHub Actions minutes costs.
  - This RAG Copilot Extension agent runs as a standalone Go application (either locally or on AWS EC2) and interacts with GitHub Copilot APIs. It does **not** use GitHub Actions workflows or runners, so there are no costs associated with GitHub Actions minutes for deploying or running this agent.
  - For each file you want to make searchable, expect to use 30–80 embedding requests (depending on file size and how you split the content).
  - Each user interaction (chat or code request) typically results in at least one completions request.
  - If your usage is very high (e.g., processing thousands of files or handling many users), monitor your rate limits and consult GitHub for potential additional costs or enterprise options.
  - Github App is not charged. Only the compute resources used by the app are charged

### Example Cost Estimate

| Component                   | Monthly Cost (USD)      |
|-----------------------------|-------------------------|
| AWS EC2 t3.micro instance   | $8–$10                  |
| Bandwidth/Storage (EBS)     | $1–$5                   |
| Load Balancer + SSL         | $20–$25                 |
| Copilot for Business (API)  | $19/user                |
| **Total (per user)**        | ~$48–$59                |

> **Notes:**
>
> - The $19/user/month Copilot for Business subscription covers both completions and embeddings usage within typical business limits.
> - Each file indexed for search uses 30–80 embedding requests on average.
> - Each user interaction (chat/code) triggers at least one completions request.
> - No GitHub Actions minutes are required for this solution.
> - Actual costs may vary based on AWS region, bandwidth, and usage patterns. For high-volume or enterprise use, consult AWS and GitHub for custom pricing or additional charges.
>

### Cost Optimization Tips

- Use the smallest EC2 instance type that meets your needs.
- Monitor API usage and optimize requests to avoid unnecessary calls.
- Consider auto-scaling or spot instances for variable workloads.
- Secure your API keys and secrets to prevent unauthorized usage.

---

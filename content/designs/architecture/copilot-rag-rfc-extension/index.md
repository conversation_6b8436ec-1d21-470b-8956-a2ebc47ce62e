<!-- Parent: Designs -->
<!-- Parent: Architecture -->
<!-- Title: RFC-529 Copilot RAG Extension for RFCs -->

<!-- Include: macros.md -->
# Copilot RAG Extension for RFCs

:draft:

```text
Author: <PERSON>
Publish Date: 2025-06-17
Category: Designs
Subtype: Architecture
```

## Table of Contents

:toc:

<!-- The :toc: macro will automatically generate a table of contents with proper case-sensitive links -->

## Document Completion Guide

| Section | When to Complete | Primary Audience | Focus Areas |
|---------|------------------|------------------|-------------|
| Business Context | Early design phase | Product owners, stakeholders | Business drivers, user needs |
| Solution Architecture | After requirements | Development team, architects | Technical approach, components |
| Risks & Dependencies | Throughout design process | Project managers, team | Mitigation, dependencies |
| Vector Impact | After architecture definition | Leadership, architects | Measurable improvements |

---

## 1.0 Document Control

**Project Information:**

- **Line of Business:** Architecture
- **Product Name:** Copilot RAG Extension for RFC Standard Queries
- **Requirements:** [Link to Epics/Features/Stories]
- **Related Docs:** [Link to Reference Architectures, Related Designs]

| Stakeholder | Role | Status | Date | R | A | C | I |
| ----------- | ---- | ------ | ---- | - | - | - | - |
| [Name] | Product Owner | | | | | | 🔲 |
| [Name] | Security Architect | | | | | 🔲 | |
| [Name] | Solution Architect | | | 🔲 | | | |
| [Name] | Solution Delivery Lead | | | | 🔲 | | |
| [Name] | Development Lead | | | 🔲 | | | |

---

## 2.0 Business Context & Requirements

### 2.1 Business Objective

**Problem Statement:**
Architects and technical teams at WEX currently spend significant time searching through RFC documentation to find relevant standards and guidelines. This manual process is time-consuming, error-prone, and can lead to inconsistent application of standards. The organization needs a more efficient way to access RFC content using natural language queries to improve productivity and ensure consistent application of architectural standards.

**Solution Type:** (Select all that apply)

- 🔲 New product/capability
- ☑️ Enhancement to existing product
- 🔲 Technical debt reduction
- ☑️ Cost/performance optimization
- 🔲 Availability/resilience improvement

**Business Drivers:** (Select all that apply)

- 🔲 Cost reduction
- 🔲 Risk mitigation
- ☑️ Operational efficiency

**Success Criteria:**

- Reduce time to find relevant RFC standards by at least 70%
- Improve accuracy of standard application with 70%+ relevant responses
- Increase RFC adoption and compliance by 40% across projects
- Enable 100% of technical teams to access standards guidance via natural language

### 2.2 Use Case Overview

**Key Use Case Flow:**

```mermaid
sequenceDiagram
  participant Architect as 👨‍💻 Architect
  participant Developer as 👩‍💻 Developer
  participant ProductManager as 🧑‍💼 Product Manager
  participant CopilotChat as 💬 GitHub Copilot Chat
  participant RAG as 🖥️ RAG Extension (Go Server)
  participant VectorStore as 💾 In-Memory Vector Store
  participant CopilotAPI as 🤖 GitHub Copilot API
  participant Markdown as 📄 Static Markdown Files

  Architect->>CopilotChat: Ask about standards
  Developer->>CopilotChat: Query RFC standards
  ProductManager->>CopilotChat: Request architectural guidance
  CopilotChat->>RAG: Route query to RAG Extension
  RAG->>VectorStore: Search embeddings
  RAG->>CopilotAPI: Call LLM for response
  CopilotAPI-->>RAG: Return generated answer
  RAG->>Markdown: Retrieve RFC content
  RAG-->>CopilotChat: Format answer with citations
  CopilotChat-->>Architect: Return contextualized answer
  CopilotChat-->>Developer: Provide code examples
  CopilotChat-->>ProductManager: Suggest architectural patterns
```

**Key Actors:**

- **Architects:** Technical architects responsible for solution designs who need quick access to standards
- **Developers:** Engineering teams implementing solutions who need to ensure compliance with standards
- **Product Managers:** Product leaders who need to understand architectural constraints and standards
- **Technical Writers:** Documentation specialists maintaining RFC content

**User Types:** (Select all that apply)

- 🔲 Customer/End User
- ☑️ Internal Business User
- ☑️ Administrator
- 🔲 Partner/Third-party
- 🔲 System/Service
- ☑️ System engineer
- 🔲 Other: _______________

**Primary Scenarios:**

1. An architect queries the system for authentication standards while designing a new solution
2. A developer asks about required monitoring configurations during implementation
3. A product manager inquires about architectural constraints for a new feature
4. A technical writer verifies if documentation meets current standards guidelines

<!-- PlantUML source for diagram:
@startuml
actor "Architect" as A
actor "Developer" as C
actor "Product Manager" as D
component "GitHub Copilot Chat" as B
component "Go HTTP Server" as E
database "In-Memory Vector Store" as F
component "GitHub Copilot API" as G
database "Static Markdown Files" as H

A -right-> B: 1. Asks question about standards
C -up-> B: 2. Queries RFC standards
D -up-> B: 3. Requests architectural guidance
B -right-> E: 4. Routes to RAG Extension
E -right-> F: 5. Searches embeddings
E -down-> G: 6. Calls LLM
G -up-> E: 7. Returns response
E -left-> B: 8. Formats with citations
B -left-> A: 9. Returns contextualized answers
B -down-> C: 10. Provides code examples with standards
B -down-> D: 11. Suggests architectural patterns
H -up-> E: 12. Provides knowledge base
@enduml
-->

**Key Actors:**

- **Architects:** Technical architects responsible for solution designs who need quick access to standards
- **Developers:** Engineering teams implementing solutions who need to ensure compliance with standards
- **Product Managers:** Product leaders who need to understand architectural constraints and standards
- **Technical Writers:** Documentation specialists maintaining RFC content

**User Types:** (Select all that apply)

- 🔲 Customer/End User
- ☑️ Internal Business User
- ☑️ Administrator
- 🔲 Partner/Third-party
- 🔲 System/Service
- ☑️ System engineer
- 🔲 Other: _______________

**Primary Scenarios:**

1. An architect queries the system for authentication standards while designing a new solution
2. A developer asks about required monitoring configurations during implementation
3. A product manager inquires about architectural constraints for a new feature
4. A technical writer verifies if documentation meets current standards guidelines

### 2.3 Process Flow

**Process Flow Diagram:**

```text
┌─────────────────┐    ❓ RFC Query     ┌─────────────────┐
│                 │ ─────────────────→ │                 │
│ 👨‍💻 User        │                     │ 💬 GitHub       │
│ (Architect/     │ ←───────────────── │ Copilot Chat    │
│  Developer)     │    📋 Answer        │                 │
└─────────────────┘                     └─────────────────┘
                                                 │
                                                 │ Forward Query
                                                 ▼
┌─────────────────┐    🔍 Vector Search ┌─────────────────┐
│                 │ ─────────────────→ │                 │
│ 🖥️ RAG          │                     │ 💾 In-Memory    │
│ Extension       │ ←───────────────── │ Vector Store    │
│ (Go Server)     │    📊 RFC Chunks    │ ([]float32)     │
└─────────────────┘                     └─────────────────┘
         │                                       │
         │ Generate Response                     │ Search Arrays
         ▼                                       ▼
┌─────────────────┐                     ┌─────────────────┐
│                 │                     │                 │
│ 🤖 GitHub       │ ←─── Context ────  │ 📄 Static       │
│ Copilot API     │                     │ Markdown Files  │
│ (GPT-4o/4.1)    │                     │ (/data folder)  │
└─────────────────┘                     └─────────────────┘
```

**Flow Steps:**

| Step | Actor | Action | Technology |
|------|-------|--------|------------|
| 1 | User | Submit RFC standards query | GitHub Copilot Chat interface |
| 2 | GitHub Copilot | Forward query to extension | Webhook/API call |
| 3 | RAG Extension | Perform cosine similarity search | Custom Go implementation |
| 4 | Vector Store | Return relevant RFC chunks | []float32 arrays in memory |
| 5 | RAG Extension | Generate response with context | GitHub Copilot API integration |
| 6 | GitHub Copilot API | Return grounded response | GPT-4o/4.1 models |
| 7 | RAG Extension | Format answer with citations | JSON response with RFC links |
| 8 | GitHub Copilot | Display answer to user | VS Code extension interface |

**Technical Notes:**

> 🔍 **Vector Search**: Custom cosine similarity search through []float32 arrays  
> 🤖 **LLM Integration**: Direct GitHub Copilot API usage for response generation  
> 📋 **User Experience**: Seamless follow-up clarification requests supported

**Process Type:** (Select one)

- 🔲 Synchronous process
- ☑️ Asynchronous process
- 🔲 Batch process
- 🔲 Hybrid process

**Process Complexity:**

- 🔲 Simple (Linear flow)
- ☑️ Moderate (Some decision points)
- 🔲 Complex (Multiple paths and decision points)

**Key Process Steps:**

1. User submits natural language query about RFC standards or guidelines
2. System analyzes query intent and context
3. Vector search identifies relevant RFC document chunks
4. System retrieves RFC content with contextual information
5. LLM generates comprehensive response grounded in actual RFC documentation
6. System formats response with citations and references to source RFCs
7. User receives answer and can ask follow-up questions for clarification

### 2.4 User Experience Design

[Insert wireframe image or link to design files]

**Interface Types:** (Select all that apply)

- ☑️ Web Application
- 🔲 Mobile Application
- 🔲 API/Service
- ☑️ Command Line
- 🔲 Desktop Application
- ☑️ Chatbot/Conversational
- ☑️ Other: GitHub Copilot Extension

**UX Priorities:** (Select top 3)

- ☑️ Ease of use
- 🔲 Performance/Speed
- 🔲 Accessibility
- ☑️ Consistency
- 🔲 Internationalization
- 🔲 Mobile-first design
- 🔲 Data visualization
- 🔲 Error handling/Recovery

**Key UX Considerations:**

- Seamless integration with existing GitHub Copilot chat interface
- Clear attribution of responses to specific RFC documents with direct links
- Contextual awareness to understand user's project and role
- Natural language understanding to interpret complex architectural queries
- Conversation history to maintain context across multi-turn interactions
- Interactive response refinement with follow-up questions
- Inline code examples that demonstrate standard compliance

---

## 3.0 Solution Architecture

### 3.1 System Context

```mermaid
graph TD
  classDef external fill:#D4F1F9,stroke:#05AFF2
  classDef system fill:#FFE5B4,stroke:#FFA500
  classDef storage fill:#E8F8F5,stroke:#117A65
  
  User("User"):::external --> VSCode("VS Code with GitHub Copilot"):::external
  VSCode --> CopilotChat("GitHub Copilot Chat"):::external
  CopilotChat --> RAGExt("RFC RAG Extension<br/>Go HTTP Server"):::system
  
  RAGExt --> CopilotAPI("GitHub Copilot API<br/>GPT-4o/4.1"):::external
  RAGExt --> EmbeddingAPI("GitHub Copilot<br/>Embeddings API"):::external
  RAGExt --> Memory("In-Memory Vector Store<br/>[]float32 arrays"):::storage
  RAGExt --> FileSystem("Static Markdown Files<br/>/data folder"):::storage
  
  GitHub("GitHub OAuth &<br/>Organization Permissions"):::external --> RAGExt
  
  RAGExt --> Ngrok("ngrok Tunnel<br/>Development Only"):::external
  GoLogger("Go Standard Library<br/>fmt.Printf Logging"):::system --> RAGExt
  
  RFCRepo("RFC Repository<br/>Markdown Source"):::external --> FileSystem
```

**System Scope:** (Select one)

- 🔲 Standalone System
- ☑️ Subsystem of Larger Product
- 🔲 Integration Framework
- 🔲 Platform Service
- 🔲 Microservice Ecosystem

**Deployment Environment:** (Select all that apply)

- ❌ Public Cloud - AWS
- ☑️ Private Cloud
- ☑️ Hybrid Cloud
- ❌ On-premises
- ❌ Edge Computing
- ☑️ GitHub Copilot Platform
- ☑️ Local Development Environment

**Core Components:**

- **RAG Extension Service:** Go HTTP server that processes queries and coordinates retrieval and response generation
- **In-Memory Vector Store:** Simple `[]float32` arrays storing document embeddings for cosine similarity search
- **Static File System:** Local `/data` folder containing markdown RFC documents
- **GitHub OAuth Integration:** Built-in authentication using GitHub's identity system

**Key Integration Points:**

- **GitHub Copilot Extension:** Interface for user interaction and query submission
- **GitHub Copilot API:** LLM provider and platform for response generation and embeddings
- **RFC Repository:** Source of truth for all RFC documents and standards stored as markdown files
- **In-Memory Vector Storage:** Fast vector similarity search using custom cosine similarity implementation
- **GitHub Authentication:** OAuth-based identity provider for authentication and authorization
- **ngrok Tunneling:** Local development environment with external access for webhook handling
- **Go Standard Library:** Built-in HTTP server and logging for simple deployment and monitoring

### 3.2 Architecture Patterns

**Selected Patterns:** (Select all that apply)

- ❌ Microservices Architecture
- ❌ Strangler Pattern
- ✅ Other: RAG Pattern

**Technology Categories:** (Select all that apply)

- ✅ Containerization (Docker, etc.)
- ✅ Container Orchestration (Kubernetes, etc.)
- ✅ Serverless Computing
- ❌ API Management
- ❌ Message Queue/Streaming
- ❌ Caching
- ❌ Database (Relational)
- ❌ Database (NoSQL)
- ❌ Database (Graph)
- ❌ Search Engine
- ✅ Identity & Access Management
- ❌ Content Delivery Network
- ✅ Machine Learning/AI
- ✅ GitOps/CI-CD
- ✅ Other: In-Memory Vector Storage

**Technology Stack:**

| Layer         | Reference Implementation                               | Rationale                                                                 |
|--------------|-------------------------------------------------------|---------------------------------------------------------------------------|
| Frontend     | GitHub Copilot Extension API                         | Seamless integration with existing developer workflow and tools            |
| Backend      | Go HTTP server (net/http package)                    | Simple HTTP server for extension logic and webhook handling                |
| LLM          | GitHub Copilot API (GPT-3.5, GPT-4)                 | Native GitHub Copilot integration with built-in API access                |
| Data Storage | In-memory vector storage (`[]float32` arrays)        | Simple development setup with minimal infrastructure requirements           |
| Vector Search| Custom cosine similarity implementation               | Lightweight vector similarity search without external dependencies         |
| Document Processing | GitHub Copilot Embeddings API, basic text processing | Built-in embedding generation with semantic indexing capabilities          |
| Knowledge Base| Static markdown files in `/data` folder             | Simple file-based knowledge management for development and testing         |
| Identity     | GitHub OAuth, API tokens                             | Native integration with GitHub's identity and access management            |
| Monitoring   | Standard Go logging (`fmt.Printf`)                   | Basic logging for development and debugging                                 |
| Infrastructure| Local development with ngrok tunneling              | Development-focused with minimal setup complexity                          |
| Deployment   | Manual execution (`go run .`)                        | Simple development deployment for testing and prototyping                  |

**Reference Architectures:**

- GitHub Copilot Extension Development Guide
- [GitHub RAG Extension Reference Implementation](https://github.com/copilot-extensions/rag-extension)

### 3.3 Non-Functional Requirements

**Reliability Requirements:** (Select all that apply)

- ❌ High Availability (99.9%+)
- ❌ Disaster Recovery
- ❌ Fault Tolerance
- ☑️ Data Backup
- ❌ Graceful Degradation
- ☑️ Low RTO (< 4 hours)
- 🔲 Low RPO (< 1 hour)

**Scalability Requirements:** (Select all that apply)

- ☑️ Horizontal Scaling
- ☑️ Vertical Scaling
- ☑️ Auto-scaling
- ☑️ Load Balancing
- ❌ Database Sharding
- ☑️ Caching
- ☑️ Connection Pooling

**Performance Requirements:** (Select all that apply)

- ☑️ Throughput (Transactions/sec)
- ☑️ Resource Utilization
- ☑️ Concurrent Users
- ☑️ Request Rate
- ☑️ Database Query Performance
- 🔲 Network Latency

**Monitoring Requirements:** (Select all that apply)

- ☑️ Health Monitoring
- 🔲 Performance Monitoring
- ☑️ Log Management
- ☑️ Error Tracking
- 🔲 User Activity Monitoring
- 🔲 Security Monitoring
- 🔲 Business KPI Monitoring

| Category | Requirements | Implementation Approach |
|----------|--------------|-------------------------|
| **Reliability** | • Availability: 99% (development) • Manual restart capability | • Single instance deployment with process monitoring<br>• Manual service restart for recovery<br>• Local file system backup for markdown documents |
| **Scalability** | • User capacity: 10-50 concurrent users<br>• Query volume: 5-10 queries per second | • Single Go process serving HTTP requests<br>• In-memory data structures for fast access<br>• GitHub Copilot API rate limiting and request optimization |
| **Performance** | • Response time: < 3 seconds<br>• Query latency: < 1 second | • In-memory cosine similarity search optimization<br>• Efficient document chunking and embedding strategies<br>• Direct GitHub Copilot API integration |
| **Monitoring** | • Basic request logging<br>• Error tracking<br>• API usage monitoring | • Standard Go logging with `fmt.Printf`<br>• GitHub API usage tracking<br>• Simple error logging and debugging |

### 3.4 Sequence Flows

```mermaid
sequenceDiagram
    participant User as User
    participant Copilot as GitHub Copilot
    participant RAG as RAG Extension
    participant Auth as GitHub OAuth
    participant Memory as In-Memory Vector Store
    participant CopilotAPI as GitHub Copilot API
    participant Logs as Go Logging
    
    User->>Copilot: Submit RFC standards query
    Copilot->>RAG: Forward query with context
    RAG->>Auth: Validate GitHub OAuth token
    Auth-->>RAG: Return user permissions
    
    RAG->>Memory: Perform cosine similarity search
    Memory-->>RAG: Return relevant RFC chunks
    
    RAG->>RAG: Generate prompt with context
    RAG->>CopilotAPI: Send prompt with retrieved context
    CopilotAPI-->>RAG: Return generated response
    
    RAG->>RAG: Format response with citations
    RAG-->>Copilot: Return formatted response
    Copilot-->>User: Display response with RFC references
    
    RAG->>Logs: Log query and response
    User->>Copilot: Provide feedback (optional)
    Copilot->>RAG: Forward feedback
    RAG->>Logs: Log user feedback
```

**Interaction Types:** (Select all that apply)

- 🔲 Synchronous Request/Response
- ☑️ Asynchronous Messaging
- 🔲 WebSockets/Real-time
- 🔲 Other: _______________

**Key Interactions:**

1. User query submission and authentication verification
2. Semantic search for relevant RFC content with vector similarity
3. Context-aware prompt construction and LLM response generation
4. Response formatting with citations and source references
5. Telemetry capture and feedback collection for continuous improvement

### 3.5 Data Flow

```mermaid
flowchart LR
    %% Data Sources
    RFC["📄 RFC Markdown Files<br/>/data folder"]
    Query["❓ User Query via<br/>GitHub Copilot Chat"]
    UserAction["👤 User Feedback"]
    
    %% Processing Components
    FileRead["📖 Go File Reading<br/>ioutil.ReadFile"]
    Chunking["✂️ Text Chunking<br/>Basic string splitting"]
    RAGServer["🖥️ Go HTTP Server<br/>net/http"]
    CosineSim["🔍 Custom Cosine<br/>Similarity Search"]
    Context["📋 Context Assembly<br/>Retrieved chunks"]
    PromptGen["🔧 Prompt Engineering<br/>String concatenation"]
    Response["📝 Response Formatting<br/>JSON with citations"]
    Logging["📊 Go Standard Logging<br/>fmt.Printf"]
    
    %% Storage Components
    Memory["💾 In-Memory Arrays<br/>[]float32 vectors"]
    LogOutput["📁 Console/File Output"]
    
    %% API Components
    EmbedAPI["🤖 GitHub Copilot<br/>Embeddings API"]
    CopilotAPI["🧠 GitHub Copilot API<br/>Completions endpoint"]
    CopilotChat["💬 GitHub Copilot Chat<br/>Extension Interface"]
    
    %% Data Flow Connections
    RFC --> FileRead
    FileRead --> Chunking
    Chunking --> EmbedAPI
    EmbedAPI --> Memory
    
    Query --> RAGServer
    RAGServer --> CosineSim
    Memory --> CosineSim
    CosineSim --> Context
    
    Context --> PromptGen
    PromptGen --> CopilotAPI
    CopilotAPI --> Response
    Response --> CopilotChat
    
    UserAction --> Logging
    Logging --> LogOutput
    
    %% Styling
    classDef sourceStyle fill:#e1f5fe,stroke:#0288d1,stroke-width:2px
    classDef processStyle fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef storageStyle fill:#e8f5e9,stroke:#4caf50,stroke-width:2px
    classDef apiStyle fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    
    class RFC,Query,UserAction sourceStyle
    class FileRead,Chunking,RAGServer,CosineSim,Context,PromptGen,Response,Logging processStyle
    class Memory,LogOutput storageStyle
    class EmbedAPI,CopilotAPI,CopilotChat apiStyle
```

**Data Storage Types:** (Select all that apply)

- ❌ Relational Database
- ❌ Document Database
- ❌ Key-Value Store
- ❌ Graph Database
- ❌ Cache
- ☑️ File Storage
- ❌ Object Storage
- ☑️ Other: In-Memory Arrays

**Data Processing Types:** (Select all that apply)

- 🔲 CQRS
- 🔲 Real-time Analytics
- 🔲 Machine Learning
- ☑️ Other: Vector Embeddings

**Data Elements:**

- **RFC Document:** Source documents containing standards, guidelines, and architectural patterns
- **Document Chunk:** Segmented portions of RFC documents optimized for retrieval and context
- **Vector Embedding:** Numerical representation of document chunks for semantic search
- **User Query:** Natural language question about standards or guidelines
- **Context Assembly:** Collection of relevant document chunks for a specific query
- **Response:** Generated answer with citations to source documents
- **User Feedback:** Explicit or implicit feedback on response quality
- **Metrics:** Usage patterns, performance data, and relevance scores

**Data Transformations:**

1. Document chunking: Breaking RFC documents into semantic units
2. Embedding generation: Converting text chunks to vector representations
3. Query intent analysis: Understanding user's question and context
4. Vector similarity search: Finding relevant document chunks
5. Context assembly: Combining retrieved chunks with query context
6. Response generation: Creating natural language answers from context
7. Citation formatting: Adding reference links to source documents
8. Metrics collection: Capturing performance and relevance data

### 3.6 Security Architecture

**Authentication Methods:** (Select all that apply)

- 🔲 Username/Password
- 🔲 OAuth 2.0/OIDC
- 🔲 SAML
- 🔲 JWT
- ☑️ API Key
- 🔲 Multi-factor Authentication
- 🔲 Social Login
- 🔲 Single Sign-On
- 🔲 Other: _______________

**Authorization Models:** (Select all that apply)

- ☑️ Role-Based Access Control (RBAC)
- 🔲 Attribute-Based Access Control (ABAC)
- 🔲 Policy-Based Access Control
- 🔲 ACL (Access Control Lists)
- ☑️ OAuth 2.0 Scopes
- 🔲 Capability-Based Security
- 🔲 Other: _______________

**Data Protection Methods:** (Select all that apply)

- ☑️ Encryption at Rest
- ☑️ Encryption in Transit
- 🔲 Field-level Encryption
- ☑️ Tokenization
- 🔲 Data Masking
- ☑️ Key Management
- 🔲 Other: _______________

**Compliance Requirements:** (Select all that apply)

- 🔲 PCI DSS
- 🔲 GDPR
- 🔲 HIPAA
- 🔲 SOX
- 🔲 SOC 2
- 🔲 ISO 27001
- 🔲 CCPA
- 🔲 Other: _______________

**Core Security Controls:**

| Control Type     | Implementation                                   | Purpose                                                         |
|------------------|--------------------------------------------------|-----------------------------------------------------------------|
| Authentication   | GitHub OAuth with organization-level permissions | Secure user authentication with GitHub corporate identity       |
| Authorization    | RBAC with repository-level permissions          | Control access to sensitive RFC content based on user roles    |
| Data Protection  | TLS encryption + local/cloud storage encryption | Protect document content and vector embeddings in transit/rest  |
| Network Security | HTTPS endpoints + GitHub App security model     | Restrict network access and protect extension endpoints         |
| API Security     | GitHub API rate limiting + token management     | Prevent abuse and secure API access with GitHub's built-in controls |
| Monitoring       | GitHub API logging + custom application metrics | Detect and respond to security events and usage anomalies       |

**Compliance Requirements:**

- Internal WEX data classification policies - Access controls and data handling
- LLM prompt injection prevention - Input validation and sanitization

---

## 4.0 GitHub App Deployment Considerations

### 4.1 Infrastructure Planning

**GitHub App Hosting Locations:**

| Environment | App Code Hosting | Webhook Processing | Action Execution |
|-------------|-----------------|-------------------|------------------|
| **GitHub Enterprise Cloud** | External server/cloud service | External server/cloud service | GitHub-hosted or Self-hosted runners |
| **GitHub Enterprise Server** | On-premises or private cloud | On-premises or private cloud | Self-hosted runners (typically on-premises) |

> ⚠️ **Important**: Unlike GitHub Actions which run on runners, GitHub Apps themselves run on separate infrastructure that you must provision and maintain. GitHub Apps communicate with GitHub Enterprise via webhooks and API calls, while any associated workflows triggered by the app run on Actions runners.

**Deployment Architecture:**

```mermaid
graph TB
    subgraph local["🖥️ Local Development Environment"]
        app["Go HTTP Server<br/>net/http package"]
        ngrok["ngrok Tunnel<br/>HTTPS forwarding"]
        files["📁 Static Markdown Files<br/>/data folder"]
        memory["💾 In-Memory Vector Store<br/>[]float32 arrays"]
    end
    
    subgraph github["☁️ GitHub Platform"]
        webhooks["🔗 GitHub Copilot<br/>Extension Webhooks"]
        api["🤖 GitHub Copilot API<br/>Completions & Embeddings"]
        oauth["🔐 GitHub OAuth<br/>Organization Auth"]
        chat["💬 GitHub Copilot Chat<br/>VS Code Extension"]
    end
    
    subgraph production["🏭 Production Environment (Future)"]
        k8s["☸️ Kubernetes Pods<br/>EKS Deployment"]
        alb["⚖️ AWS Application<br/>Load Balancer"]
        storage["💽 Persistent Volume<br/>RFC Documents"]
    end
    
    %% Local Environment Connections
    webhooks --> ngrok
    api --> ngrok
    oauth --> ngrok
    chat --> ngrok
    ngrok --> app
    app --> files
    app --> memory
    
    %% Production Environment Connections
    alb --> k8s
    k8s --> storage
    
    %% Styling
    classDef localStyle fill:#e1f5fe,stroke:#0288d1,stroke-width:2px
    classDef githubStyle fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef prodStyle fill:#e8f5e9,stroke:#4caf50,stroke-width:2px
    
    class app,ngrok,files,memory localStyle
    class webhooks,api,oauth,chat githubStyle
    class k8s,alb,storage prodStyle
```

**Deployment Options:** (Select all that apply)

- 🔲 GitHub-hosted Runners (fully managed)
- 🔲 Self-hosted Runners (VM-based)
- ☑️ Kubernetes Pods (EKS-based)
- ☑️ Container-based deployment (Docker)
- ☑️ GitOps deployment (ArgoCD)
- ☑️ Local Development (`go run .`)

**EKS Instance Types:** (Select all that apply)

- ☑️ Standard Nodes (t3.medium, t3.large)
- ☑️ Compute Optimized Nodes (c5.large, c5.xlarge)
- 🔲 Memory Optimized Nodes (r5.large, r5.xlarge)

### 4.2 Cost Considerations

```mermaid
mindmap
  root((GitHub Copilot RAG Extension Costs))
    GitHub Copilot API Costs
      Copilot Business subscription ($19/user/month)
      Completions API requests (GPT-4o/4.1)
      Embeddings API requests (vector generation)
      Premium model usage fees (if applicable)
      API rate limit considerations
    AWS EKS Infrastructure Costs
      EKS control plane ($72/month shared)
      Worker node compute (t3.medium instances)
      Application Load Balancer ($25-30/month)
      EBS storage for persistent volumes
      Network bandwidth and data transfer
    Development Environment Costs
      Local development (essentially free)
      ngrok tunneling service (optional Pro plan)
      Go runtime and dependencies (free)
      Static file storage (minimal cost)
    Operational Costs
      ArgoCD GitOps deployment automation
      Grafana monitoring dashboards
      Container registry storage
      Backup and disaster recovery
      DevOps expertise and maintenance
      Security scanning and compliance
```

| Cost Factor | Description |
|-------------|-------------|
| **Infrastructure Costs** | AWS EKS cluster ($0.10/hour), EC2 nodes, EBS storage, load balancer, bandwidth |
| **Copilot API Costs** | $19/user/month for Copilot for Business (completions and embeddings) |
| **Total Cost Predictability** | High (GitHub Copilot API costs are well-defined and predictable) |

**Key Cost Optimization Strategies:**

1. Use right-sized EKS nodes and implement cluster autoscaling for variable workloads
2. Monitor GitHub Copilot API usage and optimize requests to avoid unnecessary calls
3. Implement horizontal pod autoscaling (HPA) and vertical pod autoscaling (VPA)
4. Use spot instances for non-critical workloads and scheduled scaling policies
5. Secure your GitHub API tokens and secrets using Kubernetes secrets management

- **Cost Considerations:**
  - The $19/user/month subscription covers both completions and embeddings usage within reasonable limits for most business use cases.
  - This RAG Copilot Extension runs as a standalone application and interacts with GitHub Copilot APIs. It does **not** use GitHub Actions workflows or runners, so there are no costs associated with GitHub Actions minutes for deploying or running this extension.
  - This RAG Copilot Extension agent runs as a standalone Go application (either locally or on AWS EC2) and interacts with GitHub Copilot APIs. It does **not** use GitHub Actions workflows or runners, so there are no costs associated with GitHub Actions minutes for deploying or running this agent.
  - For each file you want to make searchable, expect to use 30–80 embedding requests (depending on file size and how you split the content).
  - Each user interaction (chat or code request) typically results in at least one completions request.
  - If your usage is very high (e.g., processing thousands of files or handling many users), monitor your rate limits and consult GitHub for potential additional costs or enterprise options.
  - Github App is not charged. Only the compute resources used by the app are charged
  - As of July 2025, Copilot for Business is $19/user/month (see [GitHub Copilot Pricing](https://github.com/pricing)). This is Wex's current subscription.
  - API usage for extensions is subject to GitHub's terms and may be included in your Copilot subscription. For high-volume or commercial use, contact GitHub for details.
  - As of July 2025, Wex has a Copilot Business subscription, not the Enterprise subscription. Features like knowledge bases and some LLMs are not available in the Business subscription.
  - Premium features are now charged, based on number of requests per user. (300 premium requests per user, per month, on the Business subscription)
  - As of July 2025, GPT-4.1 and GPT-4o are the included models, and do not consume any premium requests if you are on a paid plan. This rule may change in the future.
  - Better results can be achieved with different models. The costs may be higher if a model with different multiplier is used in the request.

#### Infrastructure Cost Considerations

- **Development:** Local development and testing is essentially free (using GitHub Copilot subscription)
- **Production:**
  - A cloud server instance typically costs $10–$50/month depending on provider and size
  - Additional costs for bandwidth, storage, and SSL certificates may apply
  - Using a load balancer and managed SSL certificates may add $20–$30/month depending on usage

#### Copilot API Usage & Cost

- **GitHub Copilot API:** This extension uses the GitHub Copilot API for two main operations:
  - **Completions:** Generating text responses (chat, answers, code suggestions) based on user input. Each user message typically results in one or more completions requests.
  - **Embeddings:** Converting text (such as user queries or documents) into numerical vectors for semantic search and context retrieval. Each file processed for context typically requires 30–80 embedding requests, depending on file size and chunking strategy.

- **API Rate Limits:**
  - GitHub Copilot API has rate limits. Exceeding them may result in throttling or additional charges.
  - **Current Copilot Business Rate Limits:**
    - Up to 80 requests per minute per user for completions endpoints.
    - Up to 10 requests per minute per user for embeddings endpoints.
    - Rate limits are subject to change; always consult the latest GitHub Copilot documentation for updates.

#### Example Cost Estimate

| Component                        | Monthly Cost (USD)      | Notes                                    |
|----------------------------------|-------------------------|------------------------------------------|
| **AWS EKS Infrastructure (Shared)** |                     |                                          |
| EKS cluster control plane        | $72                     | $0.10/hour for managed Kubernetes control plane |
| EKS worker nodes (t3.medium x3)  | $100–$150               | Auto-scaling enabled, spot instances available |
| Application Load Balancer        | $25–$30                 | SSL termination and ingress traffic     |
| EBS storage (100GB)              | $10–$15                 | Persistent volumes for RFC documents    |
| Data transfer and bandwidth      | $5–$10                  | Inbound free, outbound charged          |
| **Subtotal Infrastructure**      | **$212–$277**           | **Shared across all users**             |
|                                  |                         |                                          |
| **Per-User Costs**               |                         |                                          |
| GitHub Copilot for Business     | $19/user                | Completions + embeddings API included   |
| **Estimated user count**        | 25                      | Target user base for calculation        |
| **Infrastructure cost per user** | $8.50–$11               | (Total infra cost / user count)         |
| **Total cost per user**         | **$27.50–$30**          | **Infrastructure + Copilot subscription** |

> **Notes:**
>
> - **Infrastructure Scaling**: Infrastructure costs are shared and become more cost-effective as user count increases. At 25 users, per-user infrastructure cost is ~$8.50–$11/month.
> - **GitHub Copilot Business**: $19/user/month subscription includes both completions and embeddings API usage within typical business limits.
> - **API Usage Patterns**: Each RFC file indexed requires 30–80 embedding requests. Each user query triggers 1-3 completions requests on average.
> - **No GitHub Actions Costs**: This solution runs as a standalone Go application and doesn't use GitHub Actions runners or minutes.
> - **Storage Requirements**: EBS storage costs scale with RFC document collection size (estimated 100GB for comprehensive RFC library).
> - **Network Optimization**: Most traffic is API calls to GitHub; minimal data transfer costs expected.
> - **Development Environment**: Local development is essentially free, with optional ngrok Pro for advanced webhook testing.
> - **Cost Optimization Opportunities**:
>   - Use spot instances for worker nodes (up to 70% savings)
>   - Implement cluster autoscaling to reduce idle capacity
>   - Monitor and optimize API request patterns
> - **Premium Model Usage**: GPT-4.1 and GPT-4o are included models (no premium charges) as of July 2025.
> - **Total Cost Formula**: `(Infrastructure costs / Number of users) + $19 GitHub Copilot per user`

### Cost Optimization Tips

- Use appropriately sized EKS worker nodes and implement cluster autoscaling for dynamic resource allocation.
- Monitor GitHub Copilot API usage and optimize requests to avoid unnecessary calls.
- Leverage spot instances for non-critical workloads to reduce compute costs by up to 70%.
- Implement horizontal and vertical pod autoscaling to optimize resource utilization.
- Secure your GitHub API tokens and secrets using Kubernetes secrets and RBAC to prevent unauthorized usage.

### 4.3 Scalability Planning

**Scalability Factors:** (Select all that apply)

- ☑️ Elastic Horizontal Scaling
- ☑️ Resource Optimization
- 🔲 Job Queuing and Prioritization
- ☑️ Distributed Processing
- 🔲 Resource Pooling

```mermaid
sequenceDiagram
    participant Dev as "👨‍💻 Developer"
    participant GH as "☁️ GitHub Enterprise Cloud"
    participant Ctrl as "🎛️ RAG Extension Controller"
    participant Scale as "📊 Auto Scaler"
    participant Pool as "🖥️ Service Pool"
    
    Dev->>GH: Submit RFC query via Copilot Chat
    GH->>Ctrl: Forward query with webhook event
    Ctrl->>Scale: Check service capacity and load
    
    alt Service capacity low
        Scale->>Pool: Scale up (add instances)
        Pool-->>Scale: New instances provisioned
        Note over Scale,Pool: Horizontal pod autoscaling
    else Excess capacity detected
        Scale->>Pool: Scale down (remove instances)
        Pool-->>Scale: Instances terminated
        Note over Scale,Pool: Cost optimization
    end
    
    Ctrl->>Pool: Route query to available instance
    Pool->>Pool: Process RAG query with vector search
    Pool->>GH: Return response with RFC citations
    GH-->>Dev: Display answer in Copilot Chat
    
    Note over Dev,GH: User can ask follow-up questions
    Note over Ctrl,Pool: Metrics collected for optimization
```

**Scalability Implementation Approaches:**

- **Containerized Runners**: Using Docker containers for lightweight, isolated environments
- **Kubernetes-based Orchestration**: Managing runner pools with automatic scaling

### 4.4 Performance Optimization

| Optimization Area      | Implementation Approach                                                      | Expected Impact |
|-----------------------|----------------------------------------------------------------------------|----------------------------------------|
| **Query Latency**        | Optimize vector search (index tuning, approximate nearest neighbor), cache frequent queries | < 500ms response time for common queries |
| **Embedding Throughput** | Batch embedding generation, optimize chunking strategy, async processing                   | 2-5x faster document ingestion and update |
| **LLM Response Time**    | Prompt engineering, context window optimization, use of GitHub Copilot API optimizations   | 30-50% reduction in API call latency   |
| **Context Assembly**     | Efficient chunking, parallel retrieval, minimize context size                               | 20-40% faster context construction     |
| **System Scalability**   | Auto-scaling extension service, stateless design, load balancing                           | Handles 10x user concurrency           |
| **Monitoring & Alerting**| Real-time metrics with custom logging and monitoring dashboards                            | Proactive performance issue resolution |

**Key Performance Best Practices:**

1. Tune vector database indexes and use approximate nearest neighbor search for fast retrieval
2. Implement caching for frequent queries and responses
3. Batch and parallelize embedding generation for large document sets
4. Optimize prompt construction to minimize GitHub Copilot API input size and latency
5. Use auto-scaling and stateless service design for high concurrency
6. Continuously monitor latency, throughput, and error rates with custom metrics and dashboards

### 4.5 Security and Compliance

**Security Considerations:** (Select all that apply)

- ☑️ Network Security (Firewall Rules, VPN)
- ☑️ Secrets Management (Hashicorp Vault)
- 🔲 Access Control
- 🔲 Supply Chain Security
- ☑️ Audit Logging
- ☑️ Vulnerability Scanning

**Compliance Requirements:** (Select all that apply)

- 🔲 Data Residency Requirements
- 🔲 Access Controls and Segregation
- 🔲 Audit Trail and Logging
- 🔲 Secure Secret Management
- 🔲 Regular Vulnerability Scanning
- 🔲 Security Policy Enforcement
- 🔲 Regulatory Compliance Integration

**Security Implementation Strategies:**

- **Network Security**: Implement proper firewall rules and network isolation for self-hosted runners
- **Runner Isolation**: Use container isolation or VM-level security for job separation
- **Secrets Management**: Integrate with enterprise vault solutions (HashiCorp Vault, Azure Key Vault)
- **Access Control**: Implement precise permissions for GitHub Apps and webhook endpoints
- **Audit Logging**: Configure comprehensive logging with retention policies
- **Supply Chain Security**: Implement Dependency scanning and software composition analysis

### 4.6 Implementation Roadmap

```mermaid
flowchart LR
    classDef phase1 fill:#d4f1f9,stroke:#05aff2
    classDef phase2 fill:#ffe0b3,stroke:#ff9800
    classDef phase3 fill:#e1f5fe,stroke:#0288d1
    
    P1[Phase 1: Planning]:::phase1 --> P2[Phase 2: Infrastructure Setup]:::phase1
    P2 --> P3[Phase 3: Authentication & Security]:::phase1
    P3 --> P4[Phase 4: MVP Deployment]:::phase2
    P4 --> P5[Phase 5: Monitoring Integration]:::phase2
    P5 --> P6[Phase 6: Scale Optimization]:::phase3
    P6 --> P7[Phase 7: Production Rollout]:::phase3
```

**Implementation Phases:**

1. **Planning Phase**
   - Define scope and requirements
   - Select deployment model (self-hosted)
   - Define scaling strategy and performance targets

2. **Infrastructure Setup**
   - Provision RAG server infrastructure
   - Configure networking and security
   - Implement basic monitoring

3. **Authentication & Security**
   - Configure authentication mechanisms
   - Implement secrets management
   - Set up audit logging

4. **MVP Deployment**
   - Deploy initial GitHub App with basic functionality
   - Configure webhook endpoints
   - Implement initial scaling capability

5. **Monitoring Integration**
   - Set up comprehensive monitoring
   - Configure alerts and dashboards
   - Implement performance tracking

6. **Scale Optimization**
   - Implement auto-scaling capabilities
   - Optimize resource utilization
   - Fine-tune performance

7. **Production Rollout**
   - Full production deployment
   - User training and documentation
   - Continuous improvement process

### 4.7 Monitoring and Observability

**Key Metrics to Monitor:**

- Runner utilization rates and idle time
- Job queue length and wait times
- Job execution times and resource consumption
- Failure rates and types
- API rate limit usage
- Cost per workflow/job

**Recommended Tools:** (Select all that apply)

- ☑️ Grafana for dashboards
- ☑️ ELK stack for log analysis
- ☑️ GitHub Audit Log integration

**Monitoring Implementation Strategy:**

- Implement comprehensive metrics collection across all runner instances
- Create operational dashboards for real-time visibility
- Configure alerts for abnormal conditions
- Implement regular reporting on usage patterns and cost metrics
- Use data-driven approach to optimize runner pool size and configuration

## Reference Links

[Rate Limits](https://docs.github.com/en/copilot/troubleshooting-github-copilot/rate-limits-for-github-copilot)
[Copilot Business and Enterprise](https://github.com/features/copilot/plans?ref_cta=View+pricing+and+plans&ref_loc=hero&ref_page=%2Ffeatures_copilot_copilot_business&plans=business)
[Model multipliers - Understanding and managing requests for Copilot](https://docs.github.com/en/copilot/managing-copilot/understanding-and-managing-copilot-usage/understanding-and-managing-requests-in-copilot)
[Vector DB vs Graph DB for RAG systems](https://www.linkedin.com/pulse/difference-between-vector-db-graph-rag-applications-phaneendra-g-sopfe)
[Copilot Studio Estimator](https://microsoft.github.io/copilot-studio-estimator/)
[Copilot Usage Metrics](https://devblogs.microsoft.com/all-things-azure/visualize-roi-of-your-github-copilot-usage-how-it-works/)
[Github Knowledge Base](https://docs.github.com/en/enterprise-cloud@latest/copilot/how-tos/context/managing-copilot-knowledge-bases)
[Using Copilot Extensions](https://docs.github.com/en/copilot/using-github-copilot/using-extensions-to-integrate-external-tools-with-copilot-chat)
[About building Copilot Extensions](https://docs.github.com/en/copilot/building-copilot-extensions/about-building-copilot-extensions)
[Set up process](https://docs.github.com/en/copilot/building-copilot-extensions/setting-up-copilot-extensions)
[Communicating with the Copilot platform](https://docs.github.com/en/copilot/building-copilot-extensions/building-a-copilot-agent-for-your-copilot-extension/configuring-your-copilot-agent-to-communicate-with-the-copilot-platform)
[Communicating with GitHub](https://docs.github.com/en/copilot/building-copilot-extensions/building-a-copilot-agent-for-your-copilot-extension/configuring-your-copilot-agent-to-communicate-with-github)

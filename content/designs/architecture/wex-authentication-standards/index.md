<!-- Parent: Designs -->
<!-- Parent: Architecture -->
<!-- Title: RFC-514 Authentication Token Standards -->

<!-- Include: macros.md -->
# Authentication Token Standards

:draft:

```text
Author: <PERSON> Swoopes
Publish Date: 2025-06-02
Category: Designs
Subtype: Architecture
```

## Overview

This RFC clarifies and documents WEX's standards for requesting authentication tokens for both internal-only applications/APIs as well as customer-facing applications/APIs.

 The service interaction pattern for requesting access tokens for internal-only applications/APIs should use the Okta Workforce Identity Cloud (WIC) while customer-facing applications/APIs should use Okta Customer Identity Cloud (CIC) for Auth0-minted access tokens.

## Background

WEX development teams are creating microservices that encapsulate specific functionality to improve performance, scalability and reliability of services with limited manual requirements. In this effort, there is an increased growth of machine-to-machine (m2m) authentication needs. WEX offers two authentication platforms to request access tokens, Okta WIC and Okta CIC. Okta WIC is intended for use to access internal-only applications while Okta CIC is intended for use for all customer-facing applications.
This RFC specifically talks about requesting authentication tokens for internal-only applications, services and APIs.

## Goals

- Document current authentication standards at WEX
- Clarify the appropriate use cases for different authentication providers
- Address the emerging question of service-to-service or machine-to-machine authentication patterns
- Establish a consistent approach for authentication across the enterprise

## Scope

### In-Scope

- Authentication standards for internal service-to-service communications
- Authentication standards for customer-facing applications
- Authentication patterns for API-as-a-Product offerings
- Specific guidance for OWI (Okta Workforce Identity) related services

### Out-of-Scope

- Detailed implementation guides for each authentication provider
- User management and provisioning processes
- Specific security configurations and hardening recommendations

## Current Standards

The current WEX authentication standards establish the following patterns:

- **Okta Workforce Identity Cloud (WIC)**: Used for internal WEX service-to-service/m2m communications and employee SSO authentication to SaaS applications
- **Auth0/Okta Customer Identity Cloud (CIC)**: Used for customer-facing applications and services

## Proposed Authentication Pattern Clarification

### Authentication Provider Matrix

| Interaction Type | Current Standard | Proposed Consideration |
|------------------|------------------|------------------------|
| Internal Service-to-Service/m2m | Okta WIC | Consider Auth0 for specific use cases |
| Customer-Facing Applications | Auth0/Okta CIC | No change |
| API-as-a-Product | Auth0 | No change |

## Rationale for Consideration

Recent discussions around the OWI microservices architecture have raised questions about the optimal authentication provider for service-to-service/m2m communication, particularly in the context of OWI services.

The Enterprise Enablement team has developed tools for securing APIs using Auth0 as part of the API-as-a-Product initiative. These tools automate the provisioning of Auth0 applications to provide OAuth keys for requesting access tokens for API services. This establishes an m2m authentication pathway for accessing APIs.

### OWI Use Case

OWI microservices currently need to communicate with a new configuration service. Additionally, there's a requirement for integration with external systems like Jira for ticketing workflows.

Using Okta WIC for the internal service-to-service/m2m authentication may create future migration challenges if WEX decides to move away from WIC. There's value in considering the use of a consistent token provider across all OWI-related services.

## Benefits and Considerations

### Benefits of Using Auth0 for Service-to-Service Authentication

- **Consistency**: Using the same token provider for all OWI-related services creates architectural consistency
- **Future-proofing**: Reduces migration challenges if WEX decides to transition away from Okta WIC
- **Tooling**: Leverages existing automation tools developed for API-as-a-Product initiative
- **Infrastructure as Code**: Auth0 resources are managed through Terraform, giving project teams ownership of their Auth0 resources

### Considerations and Potential Drawbacks

- **Complexity**: Introducing multiple authentication providers may increase overall system complexity
- **Governance**: Requires clear guidelines to prevent inconsistent implementation
- **Cost**: Potential additional licensing or usage costs
- **Learning curve**: Teams may need to adapt to working with multiple authentication providers

## Decision Making Framework

When determining the appropriate authentication provider for service-to-service/m2m communications, consider the following questions:

1. Is the service part of the OWI ecosystem or directly integrated with it?
2. Will the service need to communicate with external systems?
3. Is the service part of an API-as-a-Product offering?
4. Are there specific migration concerns that would be addressed by using Auth0?

If the answer to any of these questions is "yes," consider using Auth0 for service-to-service/m2m authentication. Otherwise, follow the standard pattern of using Okta WIC.

## Implementation Considerations

### Okta WIC Implementation

For teams implementing Okta WIC for service-to-service/m2m authentication:

- Use Employee Service Portal to request the addition of your service/application to Okta WIC
- Partner with Security IAM team to provision an on-behalf-of application within Okta WIC
- Require CAB approval for all production changes

### Auth0 Implementation

For teams implementing Auth0 for service-to-service/m2m authentication:

- Use terraform for managing Auth0 resources
- Follow established approval gates for changes
- Ensure Security IAM team is informed (RACI perspective) of changes to production
- Require CAB approval for all production changes

### Governance

- The WEX Authentication Board must approve creation of new Auth0 tenants
- Partner with OWI Product Lead if onboarding to the OWI Platform
- Security IAM team should be kept informed of changes to production environments
- CAB approval is required for all production changes

## Sequence Diagram

### Okta WIC Authentication

```mermaid
sequenceDiagram
    participant Service A
    participant authN Provider
    participant Service B
    
    Note over Service A, Service B: Internal Service-to-Service/m2m using Okta WIC
    Service A->>authN Provider: Request access token
    authN Provider->>Service A: Issue Okta WIC token
    Service A->>Service B: Request with Okta WIC token
    Service B->>Service B: Validate token
    Service B->>Service A: Response
```

### Okta CIC Authentication

```mermaid
sequenceDiagram
    participant Consumer
    participant API Gateway
    participant WEX Dev Portal
    participant authN Provider
    participant Key Manager
    participant WEX Services
    
    Note over Consumer, WEX Services: API-as-a-Product using Auth0
    Consumer->>WEX Dev Portal: Discover APIs
    WEX Dev Portal->>authN Provider: Authenticate/Register
    authN Provider->>Consumer: Authenticated
    Consumer->>WEX Dev Portal: Request API/Product Key
    WEX Dev Portal->>Key Manager: Obtain API/Product Key
    Key Manager->>Consumer: Issue API/Product Key
    Consumer->>WEX Dev Portal: Request OAuth Key
    WEX Dev Portal->>Key Manager: Obtain OAuth Key
    Key Manager->>Consumer: Issue OAuth Key
    Consumer->>WEX Dev Portal: "Try It"
    WEX Dev Portal->>API Gateway: "Try It" (Route through Imperva & Palo Alto)
    API Gateway->>WEX Services: "Try It" (Route to WEX VPN)
    WEX Services->>Consumer: Response
```

## Architecture Diagrams

### Okta WIC Authentication Pattern

![Internal Authentication](RFCAccessTokens-InternalConsumption.png)

### Okta CIC Authentication Pattern for API-as-a-Product Initiative

![External Authentication](RFCAccessTokens-ExternalConsumption.png)

## Conclusion

While the established pattern of using Okta WIC for internal service-to-service/m2m authentication remains valid, there are specific use cases where Auth0 may provide benefits. This is particularly relevant for OWI-related services and API-as-a-Product offerings.

This RFC recommends that the iSARB community evaluate these considerations and establish clear guidance for teams developing services that require service-to-service/m2m authentication.

## References

- [Authentication with Okta Workforce Identity Cloud](https://developer.okta.com/blog/2023/10/24/stepup-okta)

<!-- Parent: Designs -->
<!-- Parent: Marketing Technology -->
<!-- Title: RFC-503 AppsFlyer Mobile Analytics Platform -->

<!-- Include: macros.md -->
# AppsFlyer Mobile Analytics Platform

:isarb:

```text
Author: Digital Media Team
Publish Date: 2025-05-06
Category: Designs
Subtype: Marketing Technology
Ratified By: iSARB (2025-04-17)
```

## Executive Summary

Our digital campaigns currently lack a comprehensive solution to accurately track, measure, and analyze overall traffic and user acquisition. AppsFlyer provides a mobile analytics and attribution platform that helps businesses understand how their marketing efforts impact app usage and installs.

This solution drives improvements towards the following Tech Transformation Vectors:

| Vector | Impact |
|--------|--------|
| 🔲 Product Security | |
| 🔲 Reliability | |
| ☑️ Product Innovation Velocity (PiV) | Enables data-driven marketing decisions through comprehensive analytics |
| 🔲 SaaS Maturity | |
| 🔲 AI/ML Maturity | |

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

:toc:

## 💼 Change Context

* **Organization:** Marketing Technology
* **Purpose:** Provide accurate tracking, measurement, and analysis of digital marketing campaigns
* **Users:** Digital Media Team
* **Integration Points:** 10-4 App
* **Third-Party Software:** AppsFlyer SaaS (Approved Usage)
* **Impacted Products:**
  * 10-4 App
* **Design Review Qualities:**
  * 🔲 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * 🔲 🟡 New Pattern Adoption
  * ☑️ 🟡 New Product or Capability
  * 🔲 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| iSARB Decision | April 2025 | N/A |
| Processing | April 2025 | iSARB Approval |
| AppsFlyer Implementation | April 2025 | Processing |

## Evaluation Criteria

### 🛡️ Product Security

#### Risk Assessment

The current status of risk assessments for this vendor and product:

* 🔲 Not Requested
* 🔲 In Progress
* ☑️ Completed without concerns
* 🔲 Completed with concerns:

A Coupa Risk Assessment has been completed (Certa #4329).

#### Data Compliance

The following types of Class 1 Protected Data is transmitted, processed, or stored in this solution:

* 🔲 PCI
* 🔲 PII
* 🔲 PHI

AppsFlyer is a Data Processor and not a Data Controller. It doesn't natively pull any PII that can be used for 1-to-1 matching, but collects:

* Device IDs (e.g., IDFA, GAID)
* IP addresses
* Location data (if enabled)

WEX owns the data collected via AppsFlyer. WEX determines how the data is used (for attribution, analytics, etc.) and decides what data is collected via SDKs, APIs, or integrations.

Data Protection Controls:

* ☑️ Data is protected at rest, in transit, and in use per WEX IT policy
* ☑️ HTTPS using TLS 1.2 or higher
* 🔲 Message encryption
* 🔲 Database Connections using TLS 1.2 or higher
* ☑️ Row-level data encryption
* 🔲 Transparent data encryption
* 🔲 De-identification (tokenization, hashing, masking, redaction)
* 🔲 Digital Signatures
* ☑️ Access control (permission, role, policy)

Data Retention:

* Data archival and purging policies to be determined during implementation

#### Data Ingress / Egress

This solution uses the following data flow patterns:

* 🔲 Customer-to-WEX traffic
* 🔲 WEX-to-Customer traffic
* ☑️ Vendor-to-WEX traffic
* ☑️ WEX-to-Vendor traffic
* 🔲 Internal system-to-system traffic

For each data flow, provide:

| Origin System | Destination System | Protocol | Authentication | Data Classification | Encryption Methods |
|---------------|-------------------|----------|---------------|-------------------|-------------------|
| 10-4 App | AppsFlyer | HTTPS | API Token | Class 2 (Internal) | TLS 1.2+ |
| AppsFlyer | WEX Marketing | HTTPS | SAML 2.0 | Class 2 (Internal) | TLS 1.2+ |

```mermaid
flowchart LR
  User[End User] -->|App Usage| WexApp[10-4 App]
  WexApp -->|Analytics Data| AppsFlyer[AppsFlyer SaaS]
  AppsFlyer -->|Reporting| Dashboard[Marketing Dashboard]
  Dashboard -->|View| MarketingTeam[Digital Media Team]
  
  subgraph "Data Protection Controls"
    WexApp -->|TLS 1.2+| AppsFlyer
    AppsFlyer -->|Authentication| Dashboard
  end
```

#### Authentication & Authorization

WEX Employee Authentication Components:

* 🔲 IdentityIQ SAML
* ☑️ Okta Workforce Identity
* 🔲 Active Directory:
* 🔲 Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* 🔲 Other:

WEX Employee automated provisioning & deprovisioning:

* ☑️ Yes
* 🔲 No

AppsFlyer supports automated provisioning and deprovisioning through SCIM + SSO integrations.

WEX Customer Authentication Components:

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory:
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* ☑️ WEX customers do not authenticate
* 🔲 Other:

WEX Service Principal Types:

* 🔲 Amazon Managed Identity
* 🔲 Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA:
* 🔲 Active Directory User Service Account:
* 🔲 Local user
* 🔲 Digital certificates
* 🔲 Local administrator
* ☑️ Other: API Tokens

All employee, customer, and service principal credentials require periodic rotation:

* ☑️ Yes
* 🔲 No

Service principal credentials are automatically rotated:

* 🔲 Yes
* ☑️ No

Tokens can be rotated or revoked as needed.

#### Network Traffic Patterns

**Inbound Connectivity:**

* 🔲 Protected by Imperva WAF
* ☑️ Direct inbound (not WAF-protected)
* 🔲 Customer/vendor traffic
* 🔲 WEX business user traffic  
* 🔲 WEX support user traffic

**Outbound Connectivity:**

* 🔲 To customer/vendor systems
* ☑️ From WEX network (support)
* 🔲 Class 1 data transmission
* ☑️ Class 2 data transmission

**Security Controls:**

* 🔲 Bot protection enabled
* ☑️ Automated certificate renewal
* ☑️ TLS 1.2+ enforcement
* ☑️ Network traffic encryption

### 🤖 AI/ML

Indicate if this design includes any new AI/ML components:

* 🔲 Yes
* ☑️ No

### 🔄 Reliability

#### High Availability Controls

* 🔲 Availability Zone Redundancy
* 🔲 Regional Failover
* 🔲 Automated Failover
* 🔲 Multi-Region Active-Active
* 🔲 Load Balancing
* 🔲 Content Delivery Network
* ☑️ Monitoring & Alerting
* 🔲 Other:

#### Outage Impact Assessment

Business Impact:

* 🔲 Critical customer business impact
* 🔲 Partial loss of non-critical functionality
* 🔲 Customer recovery assistance required
* 🔲 Multiple lines of business affected
* 🔲 Multiple products in LOB affected
* 🔲 Multiple customers affected
* 🔲 External SLA impact
* 🔲 Internal SLA impact
* 🔲 Engineering support required

AppsFlyer does not impact the availability of the 10-4 App; however, if the AppsFlyer service were to go down, WEX would lose the ability to monitor the performance of our digital marketing campaigns for the 10-4 app (including paid search).

#### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | Not Applicable (SaaS) |

#### Vendor Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Vendor Name | AppsFlyer |
| Cloud Provider | Amazon Web Services (primary), Google Cloud Platform (backup) |
| Primary Region | Dublin, Ireland (AWS) |
| Failover Region | St. Ghislain, Belgium (GCP) |

### ☁️ SaaS

#### Key Components

| Component | Description | Technology | Purpose |
|-----------|-------------|------------|---------|
| Attribution | Determine what motivates users to install an app | AppsFlyer Analytics | Campaign effectiveness |
| Analytics | Unify campaign performance data | AppsFlyer Dashboard | Performance measurement |
| Engagement | Create personalized user journeys | AppsFlyer SDK | User experience |
| Fraud protection | Guard against mobile ad fraud | AppsFlyer Security | Security |
| Measurement | Gauge the effectiveness of marketing activities | AppsFlyer Reporting | ROI analysis |

#### Design Impact Scope

* ☑️ Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* ☑️ Vendor & Product Risk Assessment completed

#### Logging & APM

* 🔲 Splunk Forwarding
* 🔲 DataDog Forwarding
* ☑️ Security Event Logging
* 🔲 Synthetic Testing
* 🔲 Log Archival
* 🔲 Log Purging
* 🔲 Class 1 Data Logging Controls

AppsFlyer provides robust logging capabilities for visibility, auditing, and troubleshooting. Many logs can be exported depending on your needs and plan level.

#### Standards Adoption

Deviations from standard tools, services, and reference architectures are:

* 🔲 **WEX Fabric:**
* 🔲 **SCM (GitHub Enterprise):**
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):**
* 🔲 **DFS (Panzura):**
* 🔲 **Outbound Email (PowerMTA/Sendgrid):**
* 🔲 **SFTP (GoAnywhere):**
* 🔲 **WAF (Imperva):**
* 🔲 **CDN (Imperva):**
* 🔲 **Logging (Splunk):**
* 🔲 **APM (DataDog):**
* 🔲 **Auth Gateway:**
* 🔲 **AI Gateway:**
* 🔲 **Eventing Platform:**
* 🔲 **WEX Data Platform:**
* 🔲 **Tokenizer:**
* 🔲 **Notification Hub:**

### Relevant Context

#### Procurement Details

Third-Party Software Type:

* ☑️ SaaS
* 🔲 IaaS
* 🔲 AWS PaaS
* 🔲 Azure PaaS
* 🔲 Code Dependency Package
* 🔲 Desktop
* ☑️ Mobile App
* 🔲 Browser - Web Browser

Additional Considerations:

* ☑️ Software installation & configuration is automated
* ☑️ Cost exceeds $25,000
* 🔲 Solution is part of Target State Architecture
* 🔲 Temporary solution planned for replacement

Cost Details: $80,875 billed annually (~$6,740 monthly)
Note: Potential price changes if/when more enterprise apps are added.

## Diagrams

### System Context Diagram

```mermaid
graph TB
    User[End User]
    MobileApp[10-4 Mobile App]
    AppsFlyer[AppsFlyer Platform]
    Marketing[Digital Media Team]
    AdNetworks[Ad Networks]

    User -->|Uses| MobileApp
    MobileApp -->|Sends Data| AppsFlyer
    AppsFlyer -->|Attribution Data| Marketing
    AdNetworks -->|Campaign Data| AppsFlyer
    Marketing -->|Manage Campaigns| AdNetworks

    %% Add annotations for key components
    classDef secure fill:#e6ffe6,stroke:#006600
    classDef external fill:#f9f9f9,stroke:#666666
    class AppsFlyer secure
    class User,AdNetworks external
```

## Risk Analysis & Dependencies

### Key Risks

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| AppsFlyer service outage | Medium | Low | SaaS provider has redundancy and backup data centers |
| Marketing data loss | Medium | Low | Data ownership and backup capabilities |

### Critical Assumptions

* **Assumption 1:** The 10-4 App can be properly integrated with AppsFlyer SDK
* **Assumption 2:** AppsFlyer will continue to meet WEX security requirements
* **Assumption 3:** The solution will provide sufficient ROI in terms of marketing campaign optimization

### System Dependencies

| Dependency | Type | Criticality | Contact Team |
|------------|------|-------------|--------------|
| 10-4 App | Mobile App | High | Mobile App Team |
| Digital Marketing Campaigns | Marketing | High | Digital Media Team |

## Reference Links

* **Documentation:**
  * [iSARB Presentation](https://docs.google.com/presentation/d/14o8Bou1izBR2jnC1GTDn_DuiOIjzlr-6pyn1E98P29s)
* **Security Certifications:**
  * SOC 2 Type II
  * ISO/IEC 27001
  * ISO/IEC 27701 (Privacy Information Management)
  * CSA Star Level 1
  * GDPR Compliant
  * CCPA Compliant
  * ePrivacy Ready
* **Point of Contacts:**
  * Darryl Bragdon (typical point of contact)
  * Chris River (10-4 specific contact)
* **Stakeholders:**
  * Management: Audrey Gagne
  * Finance Partner: Joe Mitri
  * Procurement: Edgar Pridgen
  * IAM/SAML: Shaun Perkin
  * Security - Vendor Risk: Bertrand Tuyisenge
  * Security Architecture - Application Risk: Bob Hood
  * IT Business Partner: Brady Tollerud

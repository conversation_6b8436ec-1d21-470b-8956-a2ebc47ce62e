<!-- Parent: Designs -->
<!-- Parent: Marketing Technology -->
<!-- Title: RFC-479 Romify Lead Capture Platform -->

<!-- Include: macros.md -->
# Romify Lead Capture Platform

:isarb:

```text
Author: <PERSON><PERSON>
Publish Date: 2024-03-19
Category: Designs
Subtype: Marketing Technology
Ratified By: iSARB (2025-03-19)
```

## Executive Summary

Implementing Romify's lead event management platform to streamline and accelerate the lead capture process at trade shows and events. This solution will significantly reduce the time between lead capture and follow-up, improving funnel velocity and increasing potential business opportunities. The platform provides real-time lead capture through AI-powered badge scanning, data enrichment, and direct Salesforce integration.

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

## Change Context

* **Organization:** Marketing Technology
* **Purpose:** Accelerate lead capture-to-contact workflow for event-generated leads
* **Users:** Marketing and Sales representatives
* **Integration Points:** Salesforce (Benefits and Corporate Payments orgs)
* **Third-Party Software:** Romify (Pilot/PoC)
* **Impacted Products:**
  * Salesforce CRM (Benefits)
  * Salesforce CRM (Corporate Payments)
* **Design Review Qualities:**
  * 🔲 🔴 Core Tech Stack Changes
  * 🔲 🔴 Large Epics (>= 6 months)
  * 🔲 🔴 HA / DR Strategy Changes
  * 🔲 🟡 New Pattern Adoption
  * ✓ 🟡 New Product or Capability
  * ✓ 🟡 WEX Product Integration
  * 🔲 🟡 Deviation from WEX Standards
  * 🔲 🟡 Accessibility Change (Internal/External)

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| Pilot Event Selection | 2024-03-01 | None |
| Romify Hub Implementation | 2024-04-01 | Salesforce Connector Setup |
| User Training | 2024-04-15 | Hub Implementation |
| Pilot Event | 2024-05-01 | Training Completion |
| Platform Evaluation | 2024-06-01 | Pilot Event Completion |

## Evaluation Criteria

### 🛡️ Security & Compliance

#### Risk Assessment

* ✓ Not Requested
* 🔲 In Progress
* 🔲 Completed without concerns
* 🔲 Completed with concerns

#### Data Classification & Protection

Data Protection Controls:

* ✓ Data is protected at rest using AES-256 encryption
* ✓ HTTPS using TLS 1.3
* ✓ Database Connections using TLS 1.3
* ✓ Access control (permission, role, policy)

Data Retention:

* Data retention details pending vendor documentation

### 💼 Business & Enterprise Impact

#### Design Impact Scope

* ✓ Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* 🔲 Vendor & Product Risk Assessment completed

#### Procurement Details

Software Type:

* ✓ SaaS

Cost Details:

* Pilot fee: $1,000 (one-time)
* Annual fee: $15,000+ USD (Up to 40 Events / Unlimited Users)

Alternatives Considered:

* **Rented Event Apps:** Limited by organizer database, manual data export process
* **Business Card Scanners:** Outdated technology, limited data capture
* **Manual Process:** Current state, significant time delays in lead processing

### 🔄 Operations & Reliability

#### High Availability Controls

* 🔲 Availability Zone Redundancy
* 🔲 Regional Failover
* ✓ Automated Backups
* ✓ Monitoring & Alerting

#### Observability

* System logs access and usage
* Activity monitoring for security incidents
* System disruption tracking

### Data Ingress / Egress

#### WEX Cloud Infrastructure

Not applicable - SaaS solution

#### Vendor Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Vendor Name | Romify |
| Cloud Provider | Google Cloud |
| Primary Region | europe-west1 (Belgium) |

### Authentication & Authorization

Authentication components for WEX Employees:

* ✓ Built-in user management & authentication (Google Identity Platform)
* SSO integration planned for H1 2025

### Secure Coding

#### Technology Stack

| Component | Selection |
|-----------|-----------|
| Client Platforms | web, mobile |
| Database Technologies | Google Firestore |
| Operating Systems | containers |

### Design Patterns

* Event-driven integration with Salesforce
* Mobile-first lead capture
* Real-time data synchronization

### Paved Road Adoption

No significant deviations from standard tools and services identified.

## Diagrams

### System Context Diagram

```mermaid
graph TB
    User[Sales/Marketing Rep]
    Mobile[Romify Mobile App]
    Hub[Romify Hub]
    SF[Salesforce]
    
    User -->|Captures Lead| Mobile
    Mobile -->|Syncs| Hub
    Hub -->|Creates Lead| SF
```

## Risk Analysis & Dependencies

* 🚨 **Key Risks:**
  * Data residency in EU region
  * No SSO integration at launch
  * Limited logging export capabilities

* 📋 **Critical Assumptions:**
  * Salesforce connector will not modify existing data
  * Marketing team will manage user provisioning
  * No sensitive data will be captured at events

## Reference Links

* **Documentation:**
  * [Romify iSARB Presentation](https://docs.google.com/presentation/d/1wM69qWUKWHiMC3ukwE7xXjFpnlmlAk7gPfuhLTz6poY)

<!-- Parent: Designs -->
<!-- Parent: Benefits -->
<!-- Title: RFC-498 Delphix Data Virtualization PoC -->

<!-- Include: macros.md -->
# Delphix Data Virtualization PoC

:isarb:

```text
Author: <PERSON>
Publish Date: 2025-05-06
Category: Designs
Subtype: Benefits
Ratified By: iSARB (2025-05-06)
```

## Executive Summary

This proposal outlines implementing Delphix as a data virtualization platform to improve database refresh processes for test and development environments. The solution will enable developers to work with production-quality data while ensuring sensitive information is properly masked.

This solution drives improvements towards the following Tech Transformation Vectors:

| Vector | Impact |
|--------|--------|
| ☑️ Product Security | Enables proper masking of sensitive data for development use |
| ☑️ Reliability | Improves consistency between environments |
| ☑️ Product Innovation Velocity (PiV) | Accelerates development by providing quality test data |
| 🔲 SaaS Maturity | [Brief impact description if selected] |
| 🔲 AI/ML Maturity | [Brief impact description if selected] |

:box:note::This design document was generated using GitHub Copilot from the iSARB presentation slide deck for this solution.  A cursory check has been completed for accuracy, but the linked presentation should be considered the source-of-truth.:

:toc:

## 💼 Change Context

* **Organization:** Benefits
* **Purpose:** Provide high-quality, masked production data to development and test environments
* **Users:** Developers and QA teams working on Benefits products
* **Integration Points:** CDH UAT database
* **Third-Party Software:** Proof-of-Concept
* **Impacted Products:**
  * Health & Benefits platforms
* **Design Review Qualities:**
  * ☑️ 🟡 New Pattern Adoption
  * ☑️ 🟡 New Product or Capability

### Delivery Timeline

| Milestone | Date | Dependencies |
|-----------|------|--------------|
| PoC Start | 2025-06 | Risk assessment approval |
| PoC Evaluation | 2025-06 (2-3 weeks after start) | Success criteria defined |
| Implementation for H&B | 2025-07 | Successful PoC completion |

## Evaluation Criteria

### 🛡️ Product Security

#### Risk Assessment

The current status of risk assessments for this vendor and product:

* ☑️ In Progress
* 🔲 Completed without concerns
* 🔲 Completed with concerns: `Explain`

#### Data Compliance

The following types of Class 1 Protected Data is transmitted, processed, or stored in this solution:

* ☑️ PCI
* ☑️ PII
* ☑️ PHI

Data Protection Controls:

* ☑️ Data is protected at rest, in transit, and in use per WEX IT policy
* ☑️ HTTPS using TLS 1.2 or higher
* 🔲 Message encryption
* ☑️ Database Connections using TLS 1.2 or higher
* 🔲 Row-level data encryption
* 🔲 Transparent data encryption
* ☑️ De-identification (tokenization, hashing, masking, redaction)
* 🔲 Digital Signatures
* ☑️ Access control (permission, role, policy)

Data Retention:

* Data archived after: `Determined after PoC`
* Data purged after: `Determined after PoC`
* Logs purged after: `Determined after PoC`

#### Data Ingress / Egress

This solution uses the following data flow patterns:

* 🔲 Customer-to-WEX traffic
* 🔲 WEX-to-Customer traffic
* 🔲 Vendor-to-WEX traffic
* 🔲 WEX-to-Vendor traffic
* ☑️ Internal system-to-system traffic

For each data flow, provide:

| Origin System | Destination System | Protocol | Authentication | Data Classification | Encryption Methods |
|---------------|-------------------|----------|---------------|-------------------|-------------------|
| CDH UAT Database | Delphix Virtualization Platform | MSSQL (TCP 1433) | DB Authentication | Class 1 | TLS 1.2+ |
| Delphix Virtualization Platform | Development Environments | MSSQL (TCP 1433) | DB Authentication | Class 1 (Masked) | TLS 1.2+ |

```mermaid
flowchart LR
  SourceDB[CDH UAT Database] -->|DB Backup| Delphix[Delphix Platform]
  Delphix -->|Masked Virtual DB| DevEnv[Development Environments]
  
  subgraph "Data Protection Controls"
    Delphix -->|Data Masking| Delphix
    Delphix -->|Authentication| DevEnv
    Delphix -->|TLS 1.2+| DevEnv
  end
```

#### Authentication & Authorization

WEX Employee Authentication Components:

* 🔲 IdentityIQ SAML
* ☑️ Okta Workforce Identity
* 🔲 Active Directory: `WEXPRODR`
* 🔲 Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* 🔲 Other: `Explain`

WEX Employee automated provisioning & deprovisioning:

* 🔲 Yes
* ☑️ No

WEX Customer Authentication Components:

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory: `DOMAIN FQDN`
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* ☑️ WEX customers do not authenticate
* 🔲 Other: `Explain`

WEX Service Principal Types:

* 🔲 Amazon Managed Identity
* 🔲 Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA: `DOMAIN FQDN`
* ☑️ Active Directory User Service Account: `WEXPRODR`
* 🔲 Local user
* 🔲 Digital certificates
* 🔲 Local administrator
* 🔲 Other: `Explain`

All employee, customer, and service principal credentials require periodic rotation:

* ☑️ Yes
* 🔲 No

Service principal credentials are automatically rotated:

* 🔲 Yes
* ☑️ No

#### Network Traffic Patterns

**Inbound Connectivity:**

* 🔲 Protected by Imperva WAF
* 🔲 Direct inbound (not WAF-protected)
* 🔲 Customer/vendor traffic
* ☑️ WEX business user traffic  
* ☑️ WEX support user traffic

**Outbound Connectivity:**

* 🔲 To customer/vendor systems
* ☑️ From WEX network (support)
* ☑️ Class 1 data transmission
* ☑️ Class 2 data transmission

**Security Controls:**

* 🔲 Bot protection enabled
* 🔲 Automated certificate renewal
* ☑️ TLS 1.2+ enforcement
* ☑️ Network traffic encryption

### 🤖 AI/ML

Indicate if this design includes any new AI/ML components:

* 🔲 Yes
* ☑️ No

Indicate if all AI/ML components have been reviewed for AI/ML compliance:

* 🔲 Yes
* 🔲 No

### 🔄 Reliability

#### High Availability Controls

* 🔲 Availability Zone Redundancy
* 🔲 Regional Failover
* 🔲 Automated Failover
* 🔲 Multi-Region Active-Active
* 🔲 Load Balancing
* 🔲 Content Delivery Network
* ☑️ Monitoring & Alerting
* 🔲 Other: `Explain`

#### Outage Impact Assessment

Business Impact:

* 🔲 Critical customer business impact
* ☑️ Partial loss of non-critical functionality
* 🔲 Customer recovery assistance required
* 🔲 Multiple lines of business affected
* 🔲 Multiple products in LOB affected
* 🔲 Multiple customers affected
* 🔲 External SLA impact
* 🔲 Internal SLA impact
* ☑️ Engineering support required

Recovery Actions:

* ☑️ Infrastructure deployment/patching
* 🔲 Application deployment
* 🔲 Configuration updates (App/LB/DNS/Firewall/Gateway)
* 🔲 Data recovery operations
* 🔲 Other: `Explain`

#### WEX Cloud Infrastructure

| Component | Value |
|-----------|--------|
| Cloud Provider | `Azure` |
| Primary Region | `US East` |
| Failover Region | `N/A for PoC` |
| Production VNet | `To be determined` |
| WEX Fabric ID | `To be determined` |

#### Vendor Cloud Infrastructure (if applicable)

Not applicable - solution will be deployed in WEX environment.

#### Network Connectivity Services

* 🔲 SDWAN
* 🔲 AWS Private Link
* 🔲 Azure Private Link
* 🔲 Netskope Private Access (NPA)
* 🔲 Point-to-Point VPN
* 🔲 Network Peering
* 🔲 Client VPN
* 🔲 DMZ

> [!note]
> All data flows must comply with WEX security standards for encryption, access control, and monitoring.

### ☁️ SaaS

#### Key Components

| Component | Description | Technology | Purpose |
|-----------|-------------|------------|---------|
| Delphix Virtualization Engine | Core data virtualization component | Delphix | Virtualizes database copies |
| Delphix Masking Engine | Data masking and anonymization | Delphix | Identifies and masks sensitive data |
| SQL Staging Target | Temporary staging area | MS SQL | Stages data for masking process |

#### Design Impact Scope

* ☑️ Impacts or used by more than 25 users
* 🔲 Dependent upon legacy or end-of-life software/hardware
* ☑️ Vendor & Product Risk Assessment completed

#### Logging & APM

* ☑️ Splunk Forwarding
* 🔲 DataDog Forwarding
* ☑️ Security Event Logging
* 🔲 Synthetic Testing
* ☑️ Log Archival
* ☑️ Log Purging
* ☑️ Class 1 Data Logging Controls

#### Design Patterns

Design, delivery, or support patterns incorporated in this design:

* **Pattern 1:** Data Virtualization
* **Pattern 2:** Data Masking for Test Environments

#### Standards Adoption

Deviations from standard tools, services, and reference architectures are:

* 🔲 **WEX Fabric:** `Justification`
* 🔲 **SCM (GitHub Enterprise):** `Justification`
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `Justification`
* 🔲 **DFS (Panzura):** `Justification`
* 🔲 **Outbound Email (PowerMTA/Sendgrid):** `Justification`
* 🔲 **SFTP (GoAnywhere):** `Justification`
* 🔲 **WAF (Imperva):** `Justification`
* 🔲 **CDN (Imperva):** `Justification`
* 🔲 **Logging (Splunk):** `Justification`
* 🔲 **APM (DataDog):** `Justification`
* 🔲 **Auth Gateway:** `Justification`
* 🔲 **AI Gateway:** `Justification`
* 🔲 **Eventing Platform:** `Justification`
* 🔲 **WEX Data Platform:** `Justification`
* 🔲 **Tokenizer:** `Justification`
* 🔲 **Notification Hub:** `Justification`

### Relevant Context

The following sections provide relevant context based on the proposed solution:

#### Secure Coding

**Technology Stack:**

| Component | Selection |
|-----------|-----------|
| Languages & Frameworks | `N/A - Database virtualization` |
| Database Technologies | `MS SQL Server` |
| Storage Solutions | `Azure Storage` |
| Operating Systems | `Windows Server` |
| Client Platforms | `web/api` |

**Security Controls:**

Code quality scanning services that are active for all development repositories:

* 🔲 Snyk
* 🔲 Cycode
* 🔲 Mend Renovate
* 🔲 GitHub Copilot
* 🔲 Dependabot
* 🔲 Other: `Service Name`

#### Procurement Details

Third-Party Software Type:

* ☑️ SaaS
* 🔲 IaaS
* 🔲 AWS PaaS
* 🔲 Azure PaaS
* 🔲 Code Dependency Package
* 🔲 Desktop
* 🔲 Mobile App
* 🔲 Browser - Web Browser

Additional Considerations:

* ☑️ Software installation & configuration is automated
* 🔲 Cost exceeds $25,000
* 🔲 Solution is part of Target State Architecture
* 🔲 Temporary solution planned for replacement

Alternatives Considered:

* **Manual database refresh:** Too time-consuming and doesn't allow for proper data masking
* **Custom masking solution:** Would require significant development effort and ongoing maintenance

## Diagrams

### System Context Diagram

```mermaid
graph TB
    Developer[Developer]
    Tester[QA Tester]
    DevOps[DevOps Engineer]
    Admin[Database Admin]
    
    SourceDB[CDH UAT Database]
    Delphix[Delphix Platform]
    subgraph "Virtual Databases"
        VDB1[Dev VDB]
        VDB2[Test VDB]
        VDB3[CI/CD VDB]
    end

    Admin -->|Manages| SourceDB
    Admin -->|Configures| Delphix
    SourceDB -->|Snapshot| Delphix
    Delphix -->|Masks & Virtualizes| VDB1
    Delphix -->|Masks & Virtualizes| VDB2
    Delphix -->|Masks & Virtualizes| VDB3
    
    Developer -->|Uses| VDB1
    Tester -->|Tests with| VDB2
    DevOps -->|Integrates| VDB3

    classDef secure fill:#e6ffe6,stroke:#006600
    classDef external fill:#f9f9f9,stroke:#666666
    class Delphix secure
```

### Cloud Architecture Diagram

```mermaid
flowchart TB
    subgraph "Azure Production Zone"
        direction TB
        SourceDB[CDH UAT SQL Database] -->|TCP 1433| DelphixStaging[Delphix Staging Target]
        DelphixStaging -->|TCP 445| DelphixMask[Delphix Masking Engine]
    end
    
    subgraph "Azure Non-Production Zone"
        direction TB
        DelphixVirt[Delphix Virtualization Engine]
        subgraph "Development Environment"
            DevVDB1[Dev Virtual DB 1]
            DevVDB2[Dev Virtual DB 2]
        end
    end
    
    DelphixMask -->|TCP 8282\nMasked Data| DelphixVirt
    DelphixVirt -->|TCP 1433\nVirtual DB Connection| DevVDB1
    DelphixVirt -->|TCP 1433\nVirtual DB Connection| DevVDB2
    
    classDef class1data fill:#ffebeb,stroke:#990000
    classDef secure fill:#e6ffe6,stroke:#006600
    class SourceDB class1data
    class DelphixMask,DelphixVirt secure
```

## Risk Analysis & Dependencies

### Key Risks

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Performance impact on virtual databases | High | Medium | Ensure low latency connections between Delphix platform and virtual database hosts |
| Incomplete data masking | High | Low | Thoroughly test and validate masking rules before implementation |
| Downtime during POC setup | Low | Medium | Schedule work during non-critical hours and ensure proper communication |

### Critical Assumptions

* **Low Latency Connectivity:** The solution requires low latency connectivity between components (< 1ms latency for TCP connections)
* **Sufficient Storage Capacity:** Adequate storage capacity for database snapshots must be available
* **Database Compatibility:** MS SQL database versions must be compatible with Delphix platform

### Known Issues

* **Latency Sensitivity:** The virtual databases are sensitive to network latency, which may impact performance if network conditions degrade
* **Integration with Existing Tools:** Some CI/CD pipelines may need adjustment to connect to virtual databases

### System Dependencies

| Dependency | Type | Criticality | Contact Team |
|------------|------|-------------|--------------|
| CDH UAT Database | Database | High | Database Administration |
| Azure Network | Network | High | Cloud Operations |
| Firewall Rules | Network | High | Network Engineering |

## Reference Links

* **Documentation:**
  * [iSARB Presentation](https://docs.google.com/presentation/d/16vNg-zKMv1froJo9-zybHIwvJ-lNOcnJhyxB3hULXKQ)

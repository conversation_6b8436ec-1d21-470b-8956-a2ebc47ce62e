<!-- Parent: Designs -->
<!-- Parent: Benefits -->
<!-- Title: RFC321-LambdaTest IP Allowlist Network Change Request -->

<!-- Include: macros.md -->
# LambdaTest IP Allowlist Network Change Request

:isarb:

```text
Author: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> Wu
Publish Date: 2024-10-09
Category: Guardrails
Subtype: Design Specification
Ratified By: iSARB (2025-01-28)
```

## Change Context

### Conditions

Select the type of network design change being proposed (select all that apply):

* ✅ New Inbound/Outbound Route
* 🔲 Standard Service Deviation
* ✅ Network Accessibility Change

Please select all secondary conditions that apply:

* 🔲 Vulnerability Remediation
* 🔲 Authentication / Authorization Changes
* 🔲 Cryptography Changes
* 🔲 Class 1 Data Handling (PII / PCI / PHI)
* 🔲 Class 2 Data Handling (Internal)

### Informal Pre-Review

> An informal pre-review is a casual, often undocumented review process aimed at improving the quality of a document or project before it undergoes a formal review.  Informal reviews are a collaborative effort used to formulate and refine a design for WEX policies, standards, and strategic objectives.

I attest that I have consulted outside my team as due diligence for the following concerns (must select all):

* ✅ Application Security & IT Compliance for compliance with WEX policies and security requirements
* ✅ Cloud Engineering for WEX cloud architecture requirements
* ✅ End User Computing for WEX desktop architecture requirements
* ✅ Operations & Support for significant risk or impact to customers and/or WEX technical support staff
* ✅ Architecture & PaaS Engineering for opportunities for re-use and adoption of standard tools & services

> <!-- Info -->
> Note- Expand for contact information...
>
> * Application Security
>   * Email:
>   * Chat:
>   * Principal SME:
> * IT Compliance
>   * Email:
>   * Chat:
>   * Principal SME:
> * Cloud Engineering
>   * Email: [Dan](mailto:<EMAIL>)
>   * Chat:
>   * Principal SME:
> * End User Computing
>   * Email:
>   * Chat:
>   * Principal SME:
> * Architecture & PaaS Engineering
>   * Ingestion: [Architecture](https://wexinc.atlassian.net/wiki/spaces/PTA/pages/153797001742/Welcome+to+the+Product+Technology+Solution+Architecture+Page) & [PaaS Engineering](https://docs.wexfabric.com/)
>   * Email:
>   * Chat:
>   * Principal SME:

### Scope

Which lines of business are impacted by this change:

* ✅ Mobility
* 🔲 Payments
* ✅ Benefits
* 🔲 WEX Enterprise IT
* 🔲 WEX Enterprise Digital

Which products are impacted by this change:

* ✅ Benefits Mobile Apps (CDH, CDB, MBE)
* ✅ Fleet Mobile OTR Apps
* 🔲 WEX PaaS (Enterprise)
* 🔲 WEX Cloud Engineering (Enterprise)
* 🔲 WEX Support (Enterprise)
* 🔲 WEX Network Security (Enterprise)
* 🔲 WEX End User Computing (Enterprise)
* 🔲 WEX Middleware (Enterprise)

### Impact

Provide the problem statement this change is solving:
_(eg. Building a new service that will facilitate producing physical credit cards.)_

```text

We are using a Tunnel right now for automation and manual testing on LambdaTest and our test and manual testing often fails because of tunnel issues, these are tests that would pass if they had not had issues with the tunnel

```

What is the value proposition for WEX (select all that apply):

* 🔲 Cost Reduction
* 🔲 Customer Retention
* 🔲 Product Competitiveness
* 🔲 Security & Compliance
* 🔲 Delivery Velocity
* ✅ Delivery Quality
* 🔲 Other: `Explain`

Please enumerate all key delivery milestones and tentative delivery dates (including delivery dependencies):

* **Dependency A Delivery:**
* **Phase 1 Delivery:** Establish the service and try out
* **Phase 2 Delivery:** Confirm the service meet our needs
* **Phase 3 Delivery:** Move forward with the solution

Please list any significant risks associated with delivering this change:

* **Risk 1:** Unable to test without purchasing the IP allowlist functionality
* **Risk 2:** If lambdatest has outage, impact development team workflow
* **Risk 3:** Mobile services have splunk and datadog, but lambdatest running mobile app does not have integration with those yet.

Please list any assumptions that this solution is dependent on:

* **Assumption 1:** Imperva WAF supports this lambdatest dedicated IP to be in allow list
* **Assumption 2:** None zero cost, even though small, need finanical approval (done before presenting to the team)
* **Assumption 3:** If lambdatest has outage, no production impact
* **Assumption 3:** This is for testing use only, no real consumer data is being used

Please list any issues that may impact delivery of this solution:

* **Issue 1:**
* **Issue 2:**
* **Issue 3:**

Please list any existing product dependencies impacted by this solution:

* **Dependency 1:**
* **Dependency 2:**
* **Dependency 3:**

### Reference Links

Please provide the Capital Planning Project Number associated with your solution: (`Planview #`)

Please provide the Jira Epic or Feature number associated with this solution: `{Jira #}`

Please provide any Jira Service Management tickets that have been submitted for this solution:

* `{Ticket 1}`
* `{Ticket 2}`
* `{Ticket 3}`

## Concerns

### Availability & Disaster Recovery

> * High Availability (HA): Objective: Ensure continuous operation and minimize downtime.
> * Disaster recovery (DR): is a crucial aspect of business continuity planning. It involves a set of policies, tools, and procedures to enable the recovery or continuation of vital technology infrastructure and systems following a natural or human-induced disaster.

What controls are included in the design to satisfy availability requirements? (select all that apply)

* 🔲 Availability Zone Redundancy (Same Region)
* 🔲 Regional Failover
* 🔲 Automated Failover
* 🔲 Multi-Region Active-Active
* ✅ Load Balancing
* 🔲 Content Delivery Network
* ✅ Monitoring & Alerting
* 🔲 Other: `Explain`

What is the impact of an outage to WEX and our Customers? (select all that apply)

* 🔲 Customer business is significantly impacted by outage
* 🔲 Customer experiences partial loss of functionality not critical to their operations
* 🔲 Customers require assistance to recover from outage (ex. data loss, corruption, etc)
* ✅ Outage may impact multiple lines of business
* ✅ Outage may impact multiple products in a line of business
* 🔲 Outage may impact multiple customers of a product
* 🔲 WEX cannot meet external SLAs
* 🔲 WEX cannot meet internal SLAs
* 🔲 WEX support teams require assistance from engineering teams to respond to outage

What actions may be required by WEX to respond to an outage? (select all that apply)

* 🔲 Deploy or Patch Infrastructure
* 🔲 Deploy Applications
* 🔲 Update Application configuration
* 🔲 Update Load Balancer configuration
* 🔲 Update DNS records
* 🔲 Update Firewall configuration
* 🔲 Update API Gateway configuration
* 🔲 Update WAF configuration
* 🔲 Update Production Data
* 🔲 Other: `Explain`

### AI/ML Compliance

> AI and Machine Learning (ML) are transforming the landscape of regulatory compliance across various industries.  The WEX AI Team must review any potential adoption of new AI/ML capabilities used at WEX.

Does this design include any new AI/ML components?

* 🔲 Yes
* ✅ No

Have all AI/ML components been reviewed for AI/ML compliance?

* 🔲 Yes
* ✅ No

### Logging & APM

> Logging and Application Performance Monitoring (APM) are crucial components for maintaining and optimizing the performance of applications.

What logging and APM capabilities are included in the design? (select all that apply)

* ✅ Splunk forwarding
* ✅ DataDog forwarding
* 🔲 No external storage of log data
* 🔲 Log data is routinely archived
* 🔲 Log data is routinely purged
* ✅ Security events are logged
* ✅ Class 1 data is not logged
* 🔲 Synthetic testing

### Data Ingress & Egress

> * Data ingress refers to the process of data entering a system, network, or application from external sources. This can include user requests, network traffic, or any other form of information sent to a system from outside sources.
> * Egress refers to the process of data leaving a system, network, or application. This can include data being sent to external systems, users, or other networks.

Describe WEX Cloud Architecture:

* **Cloud Provider:** `Azure`
* **Cloud Primary Region:** `US East`
* **Cloud Failover Region:** `US West`
* **Cloud Production Network:** `benefits-product-platform-qa`
* **WEX Fabric ID:** '00000000-0000-0000-0000-000000000000'

What data ingress & egress conditions are present in the design? (select all that apply)

* ✅ Inbound connectivity proxied by Imperva WAF
* 🔲 Inbound connectivity **NOT** proxied by Imperva WAF
* ✅ Inbound connectivity from a customer or vendor
* 🔲 Inbound connectivity from internal WEX business users
* 🔲 Inbound connectivity from internal WEX support users
* ✅ Outbound connectivity to a customer or vendor
* 🔲 Outbound connectivity from the WEX network for support resources
* 🔲 Outbound connectivity proxied by Imperva WAF
* 🔲 Class 1 data is being transmitted
* 🔲 Class 2 data is being transmitted
* ✅ Bot protection enabled
* 🔲 Digital certificate renewal is automated

What network connectivity services are used in the design? (select all that apply)

* ✅ SDWAN
* 🔲 AWS Private Link
* 🔲 Azure Private Link
* 🔲 Netskope Private Access (NPA)
* 🔲 Point-to-Point VPN
* 🔲 Network Peering
* 🔲 Client VPN
* 🔲 DMZ

### Authentication & Authorization

> * Authentication is the process of verifying the identity of a user, device, or system before granting access to resources.
> * User authorization is a crucial aspect of system security. It involves granting specific permissions to users, allowing them to access particular resources or perform certain actions within a system.

What authentication components are used for WEX employees?

* 🔲 IdentityIQ SAML
* 🔲 Okta Workforce Identity
* 🔲 Active Directory: `WEXPRODR`
* ✅ Built-in user management & authentication
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* 🔲 WEX employees do not authenticate
* 🔲 Other: `Explain`

Is access for WEX employees automatically provisioned & deprovisioned?

* 🔲 Yes
* ✅ No

What authentication components are used for WEX customers?

* 🔲 Okta Customer Identity (OWI / Auth0)
* 🔲 Built-in user management & authentication
* 🔲 Active Directory: `DOMAIN FQDN`
* 🔲 WEX custom-built IAM solution (fit-to-purpose)
* ✅ WEX customers do not authenticate
* 🔲 Other: `Explain`

What types of service principals are used? (select all that apply)

* 🔲 Amazon Managed Identity
* 🔲 Microsoft Entra Managed Identity
* 🔲 Active Directory GMSA or MSA: `DOMAIN FQDN`
* 🔲 Active Directory User Service Account: `DOMAIN FQDN`
* ✅ Local user
* 🔲 Digital certificates
* 🔲 Local administrator
* 🔲 Other: `Explain`

Do all employee, customer, and service principle credentials require periodic rotation?

* ✅ Yes
* 🔲 No

Are service principle credentials automatically rotated?

* 🔲 Yes
* ✅ No

### Data Compliance

> Data compliance involves managing and handling personal and sensitive data in accordance with regulatory requirements, industry standards, and internal policies.

Estimate the number of Class 1 Data records that are transmitted, processed, or stored in this solution design:

* PCI Records: `0`
* PII Records: `0`
* PHI Records: `0`

Is this data protected at rest, in transit, and in use according to WEX IT policy?

* ✅ Yes
* 🔲 No

What forms of data protection are used in this solution design? (select all that apply)

* ✅ HTTPS using TLS 1.2 or higher
* 🔲 Message encryption
* ✅ Database Connections using TLS 1.2 or higher
* 🔲 Row-level data encryption
* ✅ Transparent data encryption
* 🔲 De-identification (tokenization, hashing, masking, redaction, generalization, etc)
* 🔲 Digital Signatures
* ✅ Access control (permission, role, policy)

Describe how data is retained.

* Data is archived after `X` Days: 0
* Data is purged after `Y` Days : 0
* Logs are purged after `Z` Days: 0

### Secure Coding

> Secure coding is the practice of writing software in a way that prevents security vulnerabilities. This involves following specific principles and techniques to ensure that the code is robust against attacks and exploits

Describe the Application Technology Stack

* **Application Runtime Framework:** `Robo,python, selenium`
* **Database Technology:** `mssql`
* **Storage Technology:** `Azure Storage, DFS`
* **Operating Systems:** `windows, mac`
* **Application Client Devices:** `automation (cicd), mobile ios, mobile android`

Identify the types of security scans that will be performed with this design. (select all that apply)

* ✅ Secret detection
* 🔲 Security Code Quality
* ✅ Package Dependencies
* ✅ CI-CD Extensions
* 🔲 Container Images
* ✅ Static Application Security Testing (SAST)
* ✅ Dynamic Application Security Testing (DAST)

### Design Patterns

> Design patterns are reusable solutions to common problems in software design.

Please list any design, delivery, or support patterns incorporated in this design:

* **Pattern 1:** `pattern name with documentation url`
* **Pattern 2:** `pattern name with documentation url`
* **Pattern 3:** `pattern name with documentation url`

> <!-- Info -->
> Note
> Refer to [Architecture Outcomes & Patterns for Reuse](https://wexinc.atlassian.net/wiki/spaces/PTA/pages/153788613542/Architecture+Outcomes+Patterns+for+Reuse) for more information on recommended patterns.

### Standard Services, Tools & Reference Architectures

Identify all deviations from standard tools, services, and reference architectures with justification. (select all that apply)

> <!-- Info -->
> Note
> Every [tool/service/reference architecture] should have a published technical document that explains what the software does, when to use it, technical assistance contact, value from standardization, and how to use it.

* 🔲 **WEX Fabric:** `Justification`
* 🔲 **SCM (GitHub Enterprise):** `Justification`
* 🔲 **CI-CD (GitHub Actions | Azure Pipelines):** `Justification`
* 🔲 **DFS (Panzura):** `Justification`
* 🔲 **Outbound Email (PowerMTA/Sendgrid):** `Justification`
* 🔲 **SFTP (GoAnywhere):** `Justification`
* 🔲 **WAF (Imperva):** `Justification`
* 🔲 **CDN (Imperva):** `Justification`
* ✅ **Logging (Splunk):** `Redudent log that is already available from other sources`
* ✅ **APM (DataDog):** `Redudent APM that is already available from other sources`
* 🔲 **Auth Gateway:** `Justification`
* 🔲 **AI Gateway:** `Justification`
* 🔲 **Eventing Platform:** `Justification`
* 🔲 **WEX Data Platform:** `Justification`
* 🔲 **Tokenizer:** `Justification`
* 🔲 **Notification Hub:** `Justification`

## Diagrams

### System Context Diagram

> Context Diagram is a high-level, abstract representation of a system and its interactions with external entities. It defines the boundary between the system and its environment, showing the entities that interact with it.
>
> * Refer to the [C4 Level 1 System Context Diagram](https://c4model.com/diagrams/system-context) for more information

```mermaid
flowchart TB

subgraph healthBenefitConsumer[Health Benefit Consumer]
    h1[-Person-]:::type
    d1[A consumer that has an account in Health Benefit CDH or CDB system]:::description
end
healthBenefitConsumer:::person

subgraph mobileAppSystem[Mobile App and its backend Web Services]
    h2[-Software System-]:::type
    d2[Allows customers to use mobile app to view information about their benefit accounts' balance, file claim, manage debit card and make payments]:::description
end
mobileAppSystem:::internalSystem

subgraph thirdPartyAPIs[Third Party APIs]
    h3[-Software System-]:::type
    d3[Third Party APIs that provides various functionalties, such as user analysis, advanced bot protection,adaptive auth etc]:::description
end
thirdPartyAPIs:::externalSystem

subgraph notificationHubSystem[Push Notification System]
    h4[-Software System-]:::type
    d4[System that sends push notifications to mobile app]:::description
end
notificationHubSystem:::externalSystem

healthBenefitConsumer--Views account balances, manage debit card, and makes payments using-->mobileAppSystem
mobileAppSystem--Gets user analysis, advanced bot protection, adaptive auth etc using-->thirdPartyAPIs
mobileAppSystem--Sends push notification using--> notificationHubSystem
notificationHubSystem--Sends push notification to-->healthBenefitConsumer

%% Element type definitions

classDef person fill:#08427b
classDef internalSystem fill:#1168bd
classDef externalSystem fill:#999999

classDef type stroke-width:0px, color:#fff, fill:transparent, font-size:12px
classDef description stroke-width:0px, color:#fff, fill:transparent, font-size:13px
```

### Cloud Architecture Diagram

> Cloud architecture diagram is a visual representation of the structure and components of a cloud computing environment including virtual network, region, cloud resources, and external software integrations.
>
> * Refer to [AWS Cloud Architecture Diagram](https://github.com/user-attachments/assets/950ffbad-ee91-461e-8f0e-dbe2be600437)
> * Refer to [Lucidchart Architecture Diagrams](https://www.lucidchart.com/pages/architecture-diagram) for more information

![Mobile LambdaTest Cloud Architecture Diagram](cloud-architecture.png)

<!-- Parent: Informational -->
<!-- Parent: Architecture Decision Records -->
<!-- Title: RFC-530 KaaS: Certificate Provisioning GitHub Refactor -->
# KaaS: Certificate Provisioning GitHub Refactor

```text
Author: <PERSON>
Title: KaaS: Certificate Provisioning GitHub Refactor
Group: KaaS Engineering
Publish Date: 2025-05-30
Category: informational
Subtype: Decision Record
```

## Context and Problem Statement

The KaaS team built a certificate provisioning system which is used to manage certificates for various services. The
current implementation has tight coupling of internal modules used to implement the certificate provisioning system,
making testing challenging.

## Executive Summary

At the heart of this proposal is the desire to be [Pragmatic](https://en.wikipedia.org/wiki/Pragmatism), meaning that we want to make changes that are practical,
limited in scope, and understand that the solution will not be perfect. As additional refactoring takes place, more
changes and optimizations might be made.

The recommended approach at refactoring the certificate provisioning system is to decouple the internal modules and
introduce multiple shim layers that allows for abstracting the GitHub connectivity and related components. To do so,
we'll follow three principles:

* Decoupling
* Composition
* Single Responsibility Principle (SRP)

## Overview

The following diagram provides an overview of the current control flow through
the certificate provisioning system.
![ProcessCertificates-Current.png](ProcessCertificates-Current.png)

The items highlighted in grey highlight where GitHub connectivity is initialized or invoked.

While the functional flow is easy to follow, it highlights the challenge of unit testing various components within
the system.

## Proposed Solution

The following diagram outlines an approach for refactoring the certificate provisioning system to decouple the GitHub
connectivity and related components.
![ProcessCertificates-ProposedChanges.png](ProcessCertificates-ProposedChanges.png)

Note: The intent isn't to rewrite the entire system or even some of the smaller components, but instead to reorganize the
working code to allow for easier testing and future refactoring.

## Risk Mitigation

1) New components can be introduced into the system without impacting the existing functionality. A series of
PRs building up the base components and tests, can then be followed with subsequent PRs that

2) Environment variables as feature flags can be used to control the flow of the system.

3) Manual testing of the system after each PR is merged to ensure that the system continues to function as expected.

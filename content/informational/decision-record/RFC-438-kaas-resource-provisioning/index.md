<!-- Parent: Informational -->
<!-- Parent: Architecture Decision Records -->
<!-- Title: RFC-438 KaaS Resource Provisioning: JumpBox -->
# Abstract

```text
Author: <PERSON>
Publish Date: 2025-01-29
Category: Informational
Subtype: Decision Record
```

This document provides a proposal for promoting some experimental elements of eventing tools out of the repository and
into the mainstream of the KaaS Infrastructure project.

## Overview

The [JumpBox Eventing Tool](https://github.com/wexinc/eventing-tools/tree/main/JumpBox) is an experimental code base
which helps provision an infrastructure host within a cloud provider account.  It's been helpful to validate network
connectivity to and from remote resources in Aiven.

## Problem Statement

Similar to our AWS counterparts, the Azure project currently requires a bastion-like host to run support commands. We
need to decide where to build this functionality.

## Proposed Solution

The proposed solution is to extend the Kind system we've been piloting with terraform. This approach can be used as an
abstraction layer for resources while leveraging reasonable defaults.

## Alternatives Considered

1. **Use the existing JumpBox Eventing Tool code base.** While similar code could be produced in the JumpBox Eventing
tools repository, much of the effort will be duplicative across multiple dimensions.  First - The functionality we're
effectively looking for is to idempotently provision resources. Terraform is generally an infrastructure
as code tool built on golang. By reproducing the idempotent functionality that exists in Terraform, we are duplicating
its capability for CRUD operations, polling mechanism, etc..  Second - while we have a specific implementation for a
JumpBox in the JumpBox Eventing tool, we would need to extend a similar functionality for the Azure cloud provider.  
While we'd need to do this anyway, we'd be writing custom code against the Azure SDKs.

## Kind Design

As a support engineer, I would like a simple object to represent a JumpBox in Azure that can be provisioned.

### Jump Box

The following is a specification for a JumpBox kind that can be used to provision a jump box using reasonable defaults.

```yaml
kind: JumpBox
name: ex. support | used in name standard 
description: ex. JumpBox used for research ABC-1234 | description used in yaml for purpose
spec:
   cloud: ex. aws or azure | the cloud where the jump box is provisioned
   region: ex. us-east-1 or eastus | the region where the jump box is provisioned
   account: ex. wexinc-architecture-dev or architecture-dev | the account where the jump box is provisioned
   host:
      type: ex. t3.medium or Standard_D2_v3 | the type of host to provision
      ami: ex. ami-0b69ea66ff8e1a2d1 | AWS only; the ami to use for the host
      image_ref: | Azure only; the image to use for the host
         publisher: ex. Canonical
         offer: ex. UbuntuServer
         sku: ex. 18.04-LTS
         version: ex. latest
   network:
      vpc: ex. vpc-0b69ea66ff8e1a2d1 or vnet-architecture-dev-01-eastus | the vpc to use for the host
      subnet: ex. subnet-0b69ea66ff8e1a2d1 or sub-architecture-dev-private-01 | the subnet to use for the host
```

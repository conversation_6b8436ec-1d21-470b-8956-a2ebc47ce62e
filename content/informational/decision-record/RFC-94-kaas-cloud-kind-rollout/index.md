<!-- Parent: Informational -->
<!-- Parent: Architecture Decision Records -->
<!-- Title: RFC94-KaaS: Cloud Kind Rollout Research -->
# KaaS: Cloud Kind Rollout Research

```text
Author: <PERSON>
Title: KaaS: Cloud Kind Rollout
Group: KaaS Engineering
Publish Date: 2024-08-21
Category: informational
Subtype: Decision Record
```

## Context and Problem Statement

The KaaS team is considering the rollout of a new cloud kind to support expansion into new regions and clouds. The team
needs to evaluate the available options and make an informed decision on the best approach to take.

A list of options explored is at the end of this document.

## Executive Summary

The recommended approach is a multi part shim layer. This approach balances the need for controlled testing and risk
mitigation with the desire for faster rollout and easier management.  The approach provides more control over the
rollout process, making it easier to manage and troubleshoot issues as they arise. It realizes the minimalist, modular
design of storing both the terraform and cloud configurations in the same GitHub repository allowing for testing of
changes to occur in branches with minimal decoupling. This makes for more similified application and testing of
changes.

A risk of this approach for introducing the `kind:cloud` is that there's a one-time adoption cost to the new cloud module.
Almost every solution will require this one-time cost, regardless of approach to introduce some sort of logic. Minimization
of this risk is through the use of multi-part shim layers and testing within the branches as incremental changes are
made.

The cost of the multi-part shim layer is that it's complex, requires lots of pull-requests to affect change, and
requires overhead to manage and then remove special conditionals. This RFC intends to roadmap the rollout of the new
cloud kind and provide a framework for the team to follow.

## Decision Drivers

An ideal solution should meet the following criteria:

### Progression of Features Across Dimensions (Environment and Team)

General Benefits

- Controlled Testing: Environments allow for step-by-step testing and validation of features in increasingly production-like conditions.
- Risk Mitigation: By using staging environments, developers can catch issues before they affect real users in production.
- Continuous Delivery: This progression allows for more frequent and safer deployments, ensuring that new features are tested thoroughly before release.

### A/B Testing

General Benefits

- Data-Driven Decisions: A/B testing provides empirical data that helps you make informed decisions based on actual user behavior rather than assumptions or intuition.
- Optimization: By iteratively testing different variations, you can continually optimize your product, website, or marketing strategy to improve user engagement, conversions, or other key metrics.
- Risk Mitigation: Instead of rolling out a complete change to all users, A/B testing allows you to experiment on a small portion of your audience, minimizing potential negative impacts.

### Small Change Sets

General Benefits

- Reduced Risk: Smaller changes are easier to understand, test, and validate, reducing the likelihood of introducing bugs or regressions.
- Faster Rollout: Smaller changes can be deployed more quickly, allowing you to deliver new features or fixes to users faster.
- Easier Troubleshooting: When issues arise, it's easier to identify the cause of the problem in a smaller change set, speeding up the resolution process.
- Easier Rollback: If a change causes problems, it's easier to roll back a smaller change set than a larger one, minimizing the impact on users.

## Principles

- [Pragmatism](https://en.wikipedia.org/wiki/Pragmatism)
- [Logical Abstractions](https://en.wikipedia.org/wiki/Abstraction_(computer_science))
- [Zen of Python](https://peps.python.org/pep-0020/#the-zen-of-python)
  - Explicit is better than implicit.
  - Simple is better than complex.
  - Complex is better than complicated.
  - Readability counts.
  - Errors should never pass silently.

- [KISS Principle](https://en.wikipedia.org/wiki/KISS_principle)
- [Minimalism](https://en.wikipedia.org/wiki/Minimalism_(computing))
- [Modularity](https://en.wikipedia.org/wiki/Modular_design)

## Risk Mitigation

1) Declarative values will be used to ensure configurations are set explicitly.

2) The shim layer will be introduced to the pipeline to allow for the ability to control the rollout of the new cloud
kind for use with topics, schemas, users, and acls.

3) All regional configurations will be moved to the cloud module for consitency and maintainability.

4) The shim layer will be extended to include sink and source connectors.

5) Most or all of the shim layer will be removed once the rollout is complete to ensure maintainability.

## Outline of Rollout

There's some universal logic that can be initially applied.  The cloud kind can be introduced to the pipeline in a way
that allows for the ability to control the rollout of the new cloud kind.  This provides the ability to ingest cloud
data into the pipeline runs, while minimizing the impact of the new cloud kind.

Like the other Kinds, the cloud kind will need to be validated to ensure valid inputs are provided.

We can safely ingest the cloud kind into the terraform run.  We use map filtering to ensure that kinds are only processed
if they are of the appropriate kind.

Centralization of regional configurations to be moved to the cloud module for consistency and maintainability.

We then can introduce the cloud module to the project.  It can pull in the cloud kind and use it to generate outputs
that will then be introduced to each terraform run. This provides some visibility around the changes introduced and can
be inspected for issues ahead of integration.

Integrate the cloud kind into topics/schemas and users/acls.  This will allow for the cloud kind to be used, but
in a measured approach.

Enable dynamic cloud kind values.  This will allow for the cloud kind to be used when files with `kind:cloud` are added.

At this point, topics, schemas, and users can utilize `kind: cloud`.  We can begin rollout of configuration imported
users. This will involve modifying the migration script to include a `kind: cloud` value so that new teams are created
with the cloud kind.

[Begin Connectors]
Now we need to modify the our connector to handle the cloud kind.
[End Connectors]

### Pull Requests

#### Topics & Schemas, Users & ACLs

1) [Modify pipeline to handle files in team directory](https://github.com/wexinc/kaas-eventing-resources/pull/277)
2) [Add cloud kind for validation](https://github.com/wexinc/kaas-eventing-resources/pull/279)
3) [Create handling for global yaml files](https://github.com/wexinc/kaas-eventing-resources/pull/278)
4) [Updates modules to better receive cloud kind inputs](https://github.com/wexinc/kaas-eventing-resources/pull/282)
5) [Create flags for kind:cloud feature adoption](https://github.com/wexinc/kaas-eventing-resources/pull/300)
6) [Integrate cloud kind into topics/schemas and users/acls](https://github.com/wexinc/kaas-eventing-resources/pull/303)
7) [enable dynamic cloud kind values](https://github.com/wexinc/kaas-eventing-resources/pull/304)

#### Connectors

Links to be provided

1) Add vault provider to cloud

#### Initial Tests

Links to be provided

- kaas-migration: Introduce cloud kind
- Enable cloud kind in stage
- Enable cloud kind in prod

#### Introduce Cloud Kind

- BEComms: Introduce cloud kind
- BEModules: Introduce cloud kind
- EPEC: Introduce cloud kind
- ACS: Introduce cloud kind
- bank-transfer: Introduce cloud kind
- benefits-platform: Introduce cloud kind
- fleet-services: Introduce cloud kind
- tag: Introduce cloud kind

## Option Detail

### Option 1: Rollout to all environments simultaneously

The original thought was to use a data driven approach to generating values for the cloud kind field. This would allow
for configuration files to drive execution for resources built out.

While it works for the most part, it doesn't allow for the ability to easily utilize reasonable defaults, opt-in by
environment, opt-in by team.

Team leadership has expressed a desire to have more control over the rollout process. This approach would not provide
that level of control.

### Option 2: Rollout to one environment at a time, cloud kind environment field

A suggestion was made to use the environment field to control the rollout of the new cloud kind. The intent behind this
approach would allow for multiple cloud configs to be defined, one per environment.

```yml
- kind: cloud
  description: define the default cloud settings for dev
  cloud: aws
  locale: us
  tenancy: shared
  env: dev
```

This approach would require a duplication of configuration files, context, and would conflict with the single cloud kind
requirement. It would open up the possibility for scenarios where non-prod environments could be configured to use
clouds and locales that are not the same a prod.

### Option 3

A suggestion was made to use versioned modules.  This would allow for the ability to control the rollout of
a specific version of modules.

```hcl
module "web_app" {
  source = "git::https://example.com/vpc.git?ref=v1.2.0"
}
```

This approach unfortunately doesn't solve the concern.  The logic within the modules is changing very little so it's not
the modules which need versioning, it's the entire structure under /terraform that there's a desire to version.

### Option 4: Use something (like terragrunt) to control version of repository used

A prototype branch can be found here: <https://github.com/wexinc/kaas-eventing-resources/pull/286>

Significant overhead would be required to take this approach, though appears to be the potentially viable.

### Option 5: Alternative modules

A suggestion was made to use alternative modules to control the flow of logic. This would mean calling a different module to
persist resources.

```hcl
module "topics-alternative" {
  for_each = module.cloud.cloud_config
  source   = "./topics"
  providers = {
    aiven = aiven
  }

  topics = try(local.all_objects_by_kind["topic"], [])

  project           = each.value.project
  service_prefix    = each.value.service_prefix
  region_short_name = each.value.region_short_name
  primary_regions   = each.value.primary_regions
}

module "topics" {
  for_each = local.service_regions
  source   = "./topics"
  providers = {
    aiven = aiven
  }

  topics = try(local.all_objects_by_kind["topic"], [])

  project           = local.project
  service_prefix    = local.service_prefix
  region_short_name = local.region_short_name
  primary_regions   = local.primary_regions
}
```

The challenge with this approach is that it would require movement of resources to a new module for each team, for each
environment.  This would complicate adoption and increase the overhead required to manage the rollout.

<!-- Parent: Informational -->
<!-- Parent: Architecture Decision Records -->
<!-- Title: RFC-159 KaaS: Schema Compatibility Validation -->
# KaaS: Schema Compatibility Validation

```text
Author: <PERSON>
Title: KaaS: Schema Compatibility Validation
Group: KaaS Engineering
Publish Date: 2024-09-05
Category: informational
Subtype: Decision Record
```

## Context and Problem Statement

Recently, the team worked to adopt Enterprise Standards for [Schema Compatibility settings](https://github.com/wexinc/fabric-standards-eventing/commit/df0ed7e08734865f1340d7680f94a0825126d1be).

Now the team would like to ensure adoption of the standard by creating some sort of validation which is applied to
resources provisioned using the [Kaas Eventing Resources repository](https://github.com/wexinc/kaas-eventing-resources).

## Executive Summary

Given there is already a validation routine written in Golang, we'll leverage the existing solution.  Attempts to explore
other validators are included below.

## Decision Drivers

An ideal solution will ensure that PRs will not allow values other than FULL_TRANSITIVE or FORWARD_TRANSITIVE for
topics of type Application or Canonical.

## Rollout

1) Change default compatibility to FULL_TRANSITIVE.  Fortunately, the default compatibility mode is already set to
FULL_TRANSITIVE. <https://github.com/wexinc/kaas-eventing-resources/blob/v0.2.80/terraform/topics/variables.tf#L50>

2) Introduce a validation routine to ensure that the compatibility mode.

## Option Detail

### Option 1: Spectral Lint

The following spectral lint ruleset was generated using AI.  However, AI seemed to have hallucinated parts of the solution.
It may be that some sort of custom validator could be created using spectral lint, but is beyond the scope for right
now.

A prototype PR with the attempt can be found here: <https://github.com/wexinc/kaas-eventing-resources/pull/356>

```yaml
rules:
  schema-compatibility-check:
    description: "Ensure that non-raw topics have schema compatibility mode either FULL_TRANSITIVE or FORWARD_TRANSITIVE."
    given: "$.schemas[*]"
    then:
      field: "compatibility"
      function: enumeration
      functionOptions:
        values:
          - FULL_TRANSITIVE
          - FORWARD_TRANSITIVE
    message: "Schemas for non-raw topics must have a compatibility mode of FULL_TRANSITIVE or FORWARD_TRANSITIVE."
    severity: error

overrides:
  # Override rule to apply only if:
  # 1. kind is "topic"
  # 2. description is not "raw"
  - rules: ["schema-compatibility-check"]
    if:
      and:
        - field: "$.kind"
          function: pattern
          functionOptions:
            match: "^topic$"  # Matches when kind is exactly "topic"
        - field: "$.description"
          function: pattern
          functionOptions:
            match: "^((?!raw).)*$"  # Ensures description does not contain "raw"
```

Unfortunately, spectral lint also doesn't have a great way to create exceptions for certain resources.  While it wasn't
explored in depth, it seems that it would be difficult to create exceptions for certain resources, especially where there's
so many.

### Option 2: Golang Validator

The Golang validator approach is the most straightforward at this time since it works the same as other validators.

The idea is that the validator will traverse each set of resources and ensure that the compatibility mode is not set
explicitly, using the default of FULL_TRANSITIVE or is set explicitly. If it is set explicitly, it will ensure that the
value is either FULL_TRANSITIVE or FORWARD_TRANSITIVE.

Lastly, because the validator is custom code, it is fairly straight forward to keep an exception list of resources that
should be exempt from this validation.

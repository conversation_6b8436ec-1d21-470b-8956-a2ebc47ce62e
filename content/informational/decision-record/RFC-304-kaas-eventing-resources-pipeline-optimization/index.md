<!-- Parent: Informational -->
<!-- Parent: Architecture Decision Records -->
<!-- Title: RFC-304 KaaS: Eventing Resources Pipeline Optimization -->
# KaaS: Eventing Resources Pipeline Optimization

```text
Author: <PERSON>
Title: KaaS: Eventing Resources Pipeline Optimization
Group: KaaS Engineering
Publish Date: 2024-10-11
Category: informational
Subtype: Decision Record
```

## Context and Problem Statement

The KaaS team is responsible for the development and maintenance of the Kafka as a Service (KaaS) offering backed by
Aiven. The team is currently considering optimizations for the eventing resources pipeline to improve the efficiency of
the pipeline and reduce overall time and resources utilizing Github Action Runners.

## Executive Summary

Right now, there are two main areas of focus for optimization:

1. Queue Time: The greatest wait time appears to be tasks waiting for runners with the average being ~4.5m of in queue time. This is calculated by taking the total runtime and subtracting out the maximum unparallelized time. Jobs with a negative calculated number (Total runtime greater than the unparallized run time) mean that there's wait time in the system. To remediate this, we should look to increase the number of runners available to the pipeline.

2. Modularity/Duplication: The second-greatest wait time appears to be the degree of modularity and duplication in the test, validate-duplicates, and terraform-apply jobs. We should look to break these jobs into smaller, more modular jobs or removing duplicitive steps. Smaller more modular jobs will allow for greater parallelism by holding on to processors for shorter periods of time.

## Out of Scope

Caching: Caching is out of scope for this optimization. Caching can be a tool to reduce the time it takes to run a pipeline, but it's not the focus of this optimization exercise. Caching could be considered when all other areas have been optimized.

## Proposal

1. Increase the number of runners available by 2 to 14 total ($60 increase per month) to the dev pipeline.

2. Terraform-apply, separate out the create and close standard change steps.

3. Remove report generation from the validation code.

4. Remove duplicate test step.

## Impact

1. The cost of adding 2 (~17%) runners to the dev pipeline is $60 per month.

2. The impact of separating out the create and close standard change steps should yield the ability to better measure these steps and increase paralellism.

3. The impact of removing report generation from the validation code can be measured as a 70.55% runtime improvement and 46.56% unparallelized runtime improvement.

4. The impact of removing the duplicate test step can be measured as a 50% reduction in testing time, translating to ~75s less unparallized time per job run.

## Resources

* [[RFC] Matrixed team and environment evaluation](https://docs.google.com/document/d/1orfsku0Gv78YkuNT1qx2z23ONM9v-GT4EiLmVS0BO-4/edit#heading=h.jg71u3hr7tkx)
* [Introducing KaaS v0.2.0](https://wexinc.atlassian.net/wiki/spaces/KAAS/pages/154497417221/Introducing+KaaS+v0.2.0)
* [Optimizing your CI/CD GitHub Actions: A Comprehensive Guide](https://medium.com/@george_bakas/optimizing-your-ci-cd-github-actions-a-comprehensive-guide-f25ea95fd494)
* [Developer experience: What is it and why should you care?](https://github.blog/enterprise-software/collaboration/developer-experience-what-is-it-and-why-should-you-care/)

## Decision Drivers

An ideal solution should meet the following criteria:

### Measure Parallelism

Multiple tasks or subtasks run simultaneously on a hardware with multiple computing resources, such as a multi-core
processor or in our case multiple GitHub Runners. Parallelism involves running multiple instruction sequences at the
same time.

Fabric team, of which KaaS is a tenant, has 12, 2, and 4 runners available for use in dev, stage, and prod.

### Measure Cost

It's been stated that runners cost about $30 USD per month.

### Measure Developer Experience

DevEx refers to the systems, technology, process, and culture that influence the effectiveness of software development. It
looks at all components of a developer’s ecosystem—from environment to workflows to tools—and asks how they are contributing
to developer productivity, satisfaction, and operational impact.

Productivity: how quickly or simply a change can be made to a codebase
Impact: how frictionless it is to move from idea to production
Satisfaction: how the environment, workflows, and tools affect developer happiness

For our purposes, we'll measure a component of DevEx by looking at the time it takes for 1 pipeline iteration to run.

## Principles

* [Pragmatism](https://en.wikipedia.org/wiki/Pragmatism)
* [KISS Principle](https://en.wikipedia.org/wiki/KISS_principle)
* [Modularity](https://en.wikipedia.org/wiki/Modular_design)

## Supporting Data

### Current State

There are 8 segments of the pipeline which run in a linear fashion. 5 of these segments are matrixed jobs which scale out as more resources are added to each environment.

#### Example Pipeline

![Pipeline Example](pipeline_example.png)

#### Pipeline Runtime Sample Data - unmodified

| Total | Unparallized Total (Step) | Approx Queue (Total-Step) | step 1             | step 2               | step 3 | step 4                 | step 5        | step 6           | step 7                | step 7                         | step 8              | step 9              | step 10        | step 11         | step 12 | source                                                                             |
|-------|---------------------------|---------------------------|--------------------|----------------------|--------|------------------------|---------------|------------------|-----------------------|--------------------------------|---------------------|---------------------|----------------|-----------------|---------|------------------------------------------------------------------------------------|
| Total | Unparallized Total        | Approx Queue              | terraform-validate | validate-json-schema | test   | identify-changed-files | validate-json | validate-schemas | validate-avro-schemas | validate-schemas-spectral-lint | check-avro-encoding | validate-duplicates | terraform-plan | terraform-apply | result  | source                                                                             |
| 790   | 488                       | -302                      | 17                 | 6                    | 118    | 7                      | 6             | 5                | 5                     | 27                             | 4                   | 184                 | 47             | 0               | 2       | [link](https://github.com/wexinc/kaas-eventing-resources/actions/runs/11292307660) |
| 540   | 515                       | -25                       | 19                 | 5                    | 182    | 7                      | 5             | 5                | 5                     | 33                             | 5                   | 172                 | 35             | 40              | 2       | [link](https://github.com/wexinc/kaas-eventing-resources/actions/runs/11298432390) |
| 634   | 505                       | -129                      | 18                 | 5                    | 178    | 6                      | 5             | 5                | 5                     | 28                             | 6                   | 186                 | 27             | 34              | 2       | [link](https://github.com/wexinc/kaas-eventing-resources/actions/runs/11292824862) |
| 1258  | 512                       | -746                      | 17                 | 5                    | 187    | 6                      | 5             | 5                | 5                     | 36                             | 4                   | 181                 | 31             | 28              | 2       | [link](https://github.com/wexinc/kaas-eventing-resources/actions/runs/11292131724) |
| 679   | 513                       | -166                      | 17                 | 4                    | 180    | 5                      | 5             | 5                | 5                     | 34                             | 4                   | 170                 | 33             | 39              | 2       | [link](https://github.com/wexinc/kaas-eventing-resources/actions/runs/11277320996) |
| 613   | 792                       | 179                       | 17                 | 5                    | 174    | 5                      | 5 (5, 4)      | 5 (5, 4)         | 5 (5, 4)              | 33 (29, 27)                    | 5 (5, 4)            | 174                 | 31 (28, 25)    | 128 (30, 27)    | 2       | [link](https://github.com/wexinc/kaas-eventing-resources/actions/runs/11274139754) |
| 442   | 667                       | 107                       | 17                 | 5                    | 180    | 7                      | 5 (3)         | 5 (5)            | 5 (5)                 | 30 (27)                        | 5 (5)               | 173                 | 25 (24)        | 90 (28)         | 2       | [link](https://github.com/wexinc/kaas-eventing-resources/actions/runs/11256387202) |
| 399   | 668                       | 269                       | 17                 | 4                    | 188    | 4                      | 5 (4)         | 4 (4)            | 5 (5)                 | 27 (25)                        | 4 (4)               | 179                 | 26 (24)        | 106 (32)        | 2       | [link](https://github.com/wexinc/kaas-eventing-resources/actions/runs/11242715547) |

Note: A negative queue time means that the pipeline waits for runners to be available. The average wait time is ~4.5m.
Note: Pipelines with dev and prod matrixed jobs have a higher unparallized total than jobs with only a single environment.
Mean of Total: ~669
Mean of Unparallelized: ~582

#### Pipeline Runtime Sample Data - Remove reports

| Total | Unparallized Total (Step) | Approx Queue (Total-Step) | step 1             | step 2               | step 3 | step 4                 | step 5        | step 6           | step 7                | step 7                         | step 8              | step 9              | step 10        | step 11         | step 12 | source                                                                             |
|-------|---------------------------|---------------------------|--------------------|----------------------|--------|------------------------|---------------|------------------|-----------------------|--------------------------------|---------------------|---------------------|----------------|-----------------|---------|------------------------------------------------------------------------------------|
| Total | Unparallized Total        | Approx Queue              | terraform-validate | validate-json-schema | test   | identify-changed-files | validate-json | validate-schemas | validate-avro-schemas | validate-schemas-spectral-lint | check-avro-encoding | validate-duplicates | terraform-plan | terraform-apply | result  | source                                                                             |
| 197   | 311                       | -114                      | 16                 | 4                    | 75     | 6                      | 3             | 4                | 4                     | 28                             | 5                   | 66                  | 47             | 66 (calculated) | 2       | [link](https://github.com/wexinc/kaas-eventing-resources/actions/runs/11292307660) |

Note: terraform-apply has been calculated using the mean of other runs.
Note: A negative queue time means that the pipeline waits for runners to be available. The average wait time is ~4.5m.
Estimated Improvement of total: 70.55% or 472s faster
Estimated Improvement of unparallelized: 46.56% or 271s faster

#### Modularize Standard Change Actions

![pipeline_with_standard_change](./pipeline_with_standard_change.png)

Note: improvement here is the ability to measure distinct steps and potential for increased parallelism due to shorter steps.

#### Remove duplicate test steps

![pipeline_sans_test](pipeline_sans_test.png)

| Total | Unparallized Total (Step) | Approx Queue (Total-Step) | step 1             | step 2               | step 3 | step 4                 | step 5        | step 6           | step 7                | step 7                         | step 8              | step 9              | step 10        | step 11         | step 12 | source                                                                                    |
|-------|---------------------------|---------------------------|--------------------|----------------------|--------|------------------------|---------------|------------------|-----------------------|--------------------------------|---------------------|---------------------|----------------|-----------------|---------|-------------------------------------------------------------------------------------------|
| Total | Unparallized Total        | Approx Queue              | terraform-validate | validate-json-schema | test   | identify-changed-files | validate-json | validate-schemas | validate-avro-schemas | validate-schemas-spectral-lint | check-avro-encoding | validate-duplicates | terraform-plan | terraform-apply | result  | source                                                                                    |
| 211   | 358                       | 147                       | 16                 | 5                    | 0      | 6                      | 4 (4)         | 5(3)             | 6 (5)                 | 29 (22)                        | 4 (4)               | 75                  | 27 (23)        | 66 (calculated) | 2       | [link](https://github.com/wexinc/kaas-eventing-resources/actions/runs/11344950973?pr=569) |

Note: terraform-apply has been calculated using the mean of other runs.
Note: The unparallized time includes extra time for a prod and dev matrixed jobs. Prod-only unparallized value is 245s.  

#### Observations

* Total runtime includes the time it takes to queue the job with a runner.  
* The validate-duplicates job is the longest running job at 184s.  It should be queued as early in the pipeline as possible.
* Jobs without resources changes run faster due to no matrixed jobs being run.
* Create standard change and close standard change jobs took [44s, 28s] and [57s, 52s] as part of Terraform-apply respectively.
* Most recent pipeline runs become constrained by the number of available runners, with an average of approx ~4.5m of time waiting for runners.
* Its unclear which environment is the most constrained by runners.
* The longest jobs are test, validate-duplicates, and terraform-apply.
* Removal of reporting from the validation code is approx 2 hours of effort or much less as it's already been done in a branch. <https://github.com/wexinc/kaas-eventing-resources/tree/feature/johncosta/ARC-3903>

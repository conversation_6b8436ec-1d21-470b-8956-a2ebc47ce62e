<!-- Parent: Informational -->
<!-- Parent: White Paper -->
<!-- Title: RFC-492 GitHub Actions Array Input Best Practices -->

<!-- Include: macros.md -->
# GitHub Actions Array Input Best Practices

```text
Author: <PERSON> / <PERSON>
Title: GitHub Actions Array Input Best Practices
Publish Date: 2025-04-24
Category: Informational
Subtype: White Paper
```

## Overview

GitHub Actions lacks native support for array inputs, but workflows often need to process multiple values. This RFC provides a practical guide for implementing array-like inputs in GitHub Actions with a focus on security and reliability. Key recommendations:

- Use CSV format for simple lists of items (environments, regions, etc.)
- Use Multi-line inputs for structured configuration data
- Use JSON only when complex nested data structures are required
- Always validate inputs and use environment variables to prevent injection attacks
- Apply proper error handling for edge cases like empty values and malformed inputs

The recommendations in this document balance simplicity with security to help teams create more maintainable and reliable CI/CD pipelines.

## Introduction

This document describes best practices for handling arrays as inputs in GitHub Actions workflows, specifically focusing on processing them with Bash. GitHub Actions workflows often need to handle multiple values for configuration, deployment targets, or other operations. Since GitHub Actions doesn't natively support array inputs, this RFC outlines practical approaches for implementing and processing array-like data in your workflows, with emphasis on methods that work well in simple pipeline scripts.

## Background

GitHub Actions defines workflow inputs in YAML, but has limitations when it comes to complex data types. For reusable workflows, there are two main entry points with slightly different behaviors:

- **on.workflow_call**: Used when a workflow calls another reusable workflow
- **on.workflow_dispatch**: Used for manual or API-triggered workflow execution

Neither of these methods natively supports arrays, requiring developers to use string-based workarounds that are then parsed in Bash. Since most pipeline scripts don't handle complex data types well, this RFC focuses on simple, reliable methods (CSV and Multi-line inputs) that work consistently across different environments.

## Array Input Methods

Here's a comparison of methods for handling array inputs in GitHub Actions, with emphasis on approaches suitable for pipeline scripts:

| Method | Complexity | Use Case | Pros | Cons | Pipeline Compatibility |
|--------|------------|----------|------|------|------------------------|
| CSV Strings | Low | Simple lists | Easy to read and implement, works in all shells | No support for complex types | High |
| Multi-line Input | Low | Configuration | Very readable, natural for configs | Whitespace sensitivity | High |
| JSON Strings | Medium | Complex data | Preserves types, supports nesting | Requires `jq` for parsing, more error-prone | Medium |

### Method 1: Comma-separated Values (Recommended for Simple Lists)

CSV is the most straightforward method for passing simple lists and works reliably in pipeline scripts:

```yaml
inputs:
  environments:
    description: 'Comma-separated list of environments'
    required: true
    default: 'dev,test,stage'
```

### Method 2: Multi-line Input (Recommended for Configuration)

Multi-line input is excellent for configuration-style data and readable in pipeline scripts:

```yaml
inputs:
  server_list:
    description: 'List of servers to configure (one per line)'
    required: true
    default: |
      app-server-1.example.com
      db-server-1.example.com
      cache-server-1.example.com
```

### Method 3: JSON Strings (For Complex Data When Needed)

While less ideal for simple pipeline scripts, JSON can be used when complex nested data is required:

```yaml
inputs:
  config:
    description: 'Configuration as JSON'
    required: true
    default: '{"servers": ["server1", "server2"], "options": {"timeout": 30}}'
```

## Best Practices

### 1. Validate Inputs Before Processing

Always validate array inputs to avoid runtime errors:

```bash
# For CSV inputs
if [[ -z "$INPUT_CSV" ]]; then
  echo "::error::Empty input provided"
  exit 1
fi

# For multi-line inputs
if [[ -z "$(echo "$CONFIG" | tr -d '[:space:]')" ]]; then
  echo "::warning::Empty configuration provided"
fi
```

### 2. Use an intermediate environment variable to store inputs

When using inputs in scripts, especially when they are untrusted, it's crucial to avoid directly using them in the script. Instead, store them in an environment variable to prevent injection attacks.

The following example uses Bash to process the inputs.title value:

```yaml
# UNSAFE: Example of a script vulnerable to injection attack
- name: Check PR title
  run: |
    title="${{ inputs.title }}"
    if [[ $title =~ ^octocat ]]; then
    echo "Title starts with 'octocat'"
    exit 0
    else
    echo "Title did not start with 'octocat'"
    exit 1
    fi
```

To inject commands into this workflow, the attacker could call this workflow with a title of: `a"; ls $GITHUB_WORKSPACE":`, allowing the `ls` command to be executed on the runner.

The safe approach using environment variables prevents this attack vector:

```yaml
# SAFE: Using an intermediate environment variable
- name: Check PR title
  env:
    TITLE: ${{ inputs.title }}
  run: |
    if [[ "$TITLE" =~ ^octocat ]]; then
    echo "Title starts with 'octocat'"
    exit 0
    else
    echo "Title did not start with 'octocat'"
    exit 1
    fi
```

### 3. Handle Edge Cases in CSV Inputs

Account for special characters, empty values, and delimiters in CSV inputs:

```bash
# For CSV inputs with potential empty values
IFS=',' read -ra ITEMS <<< "$INPUT_CSV"
for item in "${ITEMS[@]}"; do
  # Trim whitespace
  item=$(echo "$item" | xargs)
  
  if [ -n "$item" ]; then
    echo "Processing: $item"
  else
    echo "Skipping empty item"
  fi
done
```

### 4. Process Multi-line Inputs Efficiently

When handling multi-line inputs, consider comments, empty lines, and section markers:

```bash
while IFS= read -r line || [ -n "$line" ]; do
  # Skip empty lines
  if [ -z "$(echo "$line" | tr -d '[:space:]')" ]; then
    continue
  fi
  
  # Skip comments
  if [[ "$line" == \#* ]]; then
    continue
  fi
  
  # Process valid lines
  echo "Processing: $line"
done <<< "$MULTI_LINE_INPUT"
```

### 5. Provide Sensible Defaults

Define default values to handle optional inputs:

```yaml
inputs:
  environments:
    description: 'Target environments (comma-separated)'
    required: false
    default: 'dev'
```

```bash
# Use default if input is empty
ENVIRONMENTS="${ENVIRONMENTS_INPUT:-dev}"
```

## Example Implementations

### CSV Input Example

Simple example using comma-separated values:

```yaml
name: Deploy Application
on:
  workflow_dispatch:
    inputs:
      environments:
        description: 'Environments to deploy to'
        required: true
        default: 'dev,test'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to environments
        env:
          ENVIRONMENTS: ${{ inputs.environments }}
        run: |
          # Convert CSV to array
          IFS=',' read -ra ENV_ARRAY <<< "$ENVIRONMENTS"
          
          # Process each environment
          for env in "${ENV_ARRAY[@]}"; do
            env=$(echo "$env" | xargs)  # Trim whitespace
            echo "Deploying to $env environment"
          done
```

### Multi-line Input Example

Simple example using multi-line input:

```yaml
name: Configure Application
on:
  workflow_dispatch:
    inputs:
      config:
        description: 'Configuration settings'
        required: true
        default: |
          # Database
          DB_HOST=localhost
          DB_PORT=5432
          
          # API Settings
          API_TIMEOUT=30

jobs:
  configure:
    runs-on: ubuntu-latest
    steps:
      - name: Apply configuration
        env:
          CONFIG: ${{ inputs.config }}
        run: |
          # Process each line
          while IFS= read -r line; do
            # Skip empty lines and comments
            if [[ -z "$line" || "$line" == \#* ]]; then
              continue
            fi
            
            # Extract key and value
            key="${line%%=*}"
            value="${line#*=}"
            echo "Setting $key to $value"
          done <<< "$CONFIG"
```

### JSON Input Example

```yaml
name: Process JSON Configuration
on:
  workflow_dispatch:
    inputs:
      config_json:
        description: 'Application configuration as JSON'
        required: true
        default: '{"regions": ["us-east-1", "eu-west-1"], "options": {"timeout": 30, "retries": 3}}'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Install jq
        run: sudo apt-get install jq
      
      - name: Process JSON configuration
        env:
          CONFIG: ${{ inputs.config_json }}
        run: |
          # Validate JSON format
          if ! echo "$CONFIG" | jq -e . > /dev/null 2>&1; then
            echo "::error::Invalid JSON configuration"
            exit 1
          fi
          
          # Extract and process regions
          echo "Deploying to regions:"
          echo "$CONFIG" | jq -r '.regions[]' | while read -r region; do
            echo "- $region"
          done
          
          # Extract configuration options
          TIMEOUT=$(echo "$CONFIG" | jq -r '.options.timeout')
          RETRIES=$(echo "$CONFIG" | jq -r '.options.retries')
          
          echo "Using timeout: $TIMEOUT seconds"
          echo "Using retries: $RETRIES"
```

## Conclusion

This document has outlined practical approaches for handling array inputs in GitHub Actions workflows using Bash, with a focus on methods that work reliably in pipeline scripts:

1. **CSV input** is recommended for simple lists of items due to its simplicity and wide compatibility with all shell environments.

2. **Multi-line input** is ideal for configuration-style data where readability and structure are important.

3. **JSON input**, while more powerful for complex data, is recommended when nested structures are  necessary, as it introduces additional complexity and dependencies.

Key takeaways for implementing array inputs in GitHub Actions:

- **Choose the simplest method** that meets your requirements - CSV and Multi-line inputs are preferable for most pipeline scripts
- **Always validate and sanitize inputs** before processing to avoid runtime errors
- **Use environment variables** to store input data and prevent script injection attacks
- **Handle edge cases** like empty values and special characters with careful validation
- **Provide clear defaults** for optional inputs to improve usability

By following these guidelines, teams can create more reliable, maintainable, and secure GitHub Actions workflows that effectively process array-like inputs across different environments while minimizing complexity and potential security risks.

## References

- [GitHub Actions Workflow Syntax](https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions)
- [Security hardening for GitHub Actions](https://docs.github.com/en/actions/security-guides/security-hardening-for-github-actions)
- [GitHub Actions Contexts](https://docs.github.com/en/actions/learn-github-actions/contexts)
- [Bash Reference Manual](https://www.gnu.org/software/bash/manual/bash.html)
- [jq Manual](https://stedolan.github.io/jq/manual/)

<!-- Parent: Informational -->
<!-- Parent: White Paper -->
<!-- Title: RFC-422 Managing Engineering Work in Jira -->
# Managing Engineering Work in Jira

```text
Author: <PERSON>
Title: Managing Engineering Work in Jira
Publish Date: 2025-01-21
Category: Informational
Subtype: Whitepaper
```

## Introduction

This document provides opinionated guidance for managing engineering work in Jira, focusing on the distinction between capital expenditure (CapEx) and operational expenditure (OpEx) work. It aims to streamline processes, ensure accurate financial reporting, and improve overall efficiency. The guidance provided here is aligned with Jira best practices at WEX, ensuring that teams can leverage Jira's capabilities to their fullest potential while adhering to organizational standards and workflows.

## Time-Tracking Requirements

Issues should ensure the following properties are set for accurate time tracking:

- Parent Epic
- Agile Team
- Aha Project Code
- Story Points

## Key Concepts

### Capital Expenditure (CapEx)

CapEx refers to funds used by an organization to acquire, upgrade, and maintain physical assets such as property, industrial buildings, or equipment. In the context of software engineering, CapEx includes activities that create or enhance software products, which can be capitalized and depreciated over time.

### Operational Expenditure (OpEx)

OpEx refers to the ongoing costs for running a product, business, or system. This includes expenses such as maintenance, support, and administrative activities that are necessary for day-to-day operations but do not create long-term value.

## Jira Issue Types

### Epic

An Epic is a large body of work that can be broken down into smaller tasks or user stories. It serves as an umbrella for related work items and is essential for tracking high-level initiatives.

### User Story

A User Story represents a feature or functionality that delivers value to a stakeholder. It is typically used for work that results in a tangible outcome, such as a new screen or user-facing feature.

### Task

A Task is used for work that does not directly deliver a new feature but is necessary for project completion. Tasks are often associated with OpEx activities, such as administrative work or coordination efforts.

### Technical Enhancement

A Technical Enhancement is used for backend or non-user-facing improvements, such as creating new APIs or endpoints. It is similar to a User Story but focuses on technical aspects.

### Spike

A Spike is used for research or proof-of-concept work that is necessary before starting a User Story or Technical Enhancement. Spikes are always considered OpEx.

### Bug

A Bug represents an issue or defect that needs to be fixed. Bugs should only be created to resolve issues with prior completed work that has already been delivered to production. If a problem is discovered before an issue has been delivered to production, the status should be changed back to Coding. Depending on the context, a Bug can be categorized as either CapEx or OpEx.

## Jira Issue Statuses

> <!-- Info -->
> Note
> Different teams may customize Jira with additional statuses or work states that are applicable to their specific delivery practices. Ensure that any custom statuses align with the overall workflow and reporting requirements.

### ToDo

Work item is identified and ready to be started.

### Coding

Work item is actively being developed.

### Code Complete

Development is finished, and the code is ready for review.

### Ready for QA

Work item is ready for quality assurance testing.

### Testing

Work item is undergoing testing to ensure it meets requirements.

### Pending Prod Deployment

Work item is approved and waiting to be deployed to production.

### Done

Work item is completed and successfully deployed.

## Process Steps

### Creating Epics

1. **Define the Epic**: Create an Epic for each high-level initiative. Ensure it is abstract and value-driven.
2. **Assign Project Code**: Link the Epic to a project code for financial tracking.
3. **Populate Fields**: Fill in necessary fields such as Agile Team, Funding Source, Project Code, and Initiative.

### Managing Work Items

1. **User Stories and Technical Enhancements**: Use these for CapEx work that delivers new features or technical improvements.
2. **Tasks**: Use for OpEx activities that support project completion but do not deliver new features.
3. **Spikes**: Use for research activities. Always categorized as OpEx.
4. **Bugs**: Categorize based on the nature of the work (CapEx or OpEx).

### Financial Reporting

1. **CapEx vs. OpEx**: Ensure child issues are correctly categorized to maintain accurate financial reporting.
2. **Project Codes**: Use project codes to link work items to financial buckets.
3. **Automation**: Utilize Jira automation to populate fields and track time efficiently.

### Tracking Time

1. **Active Minutes**: Move work items status from ToDo to Coding to begin time tracking.
2. **Status Transitions**: Ensure work items are moved to the appropriate status in real-time to accurately track time spent in each phase of delivery. This allows us to target further process improvements.
3. **Completion**: Move items to Done once completed to pause time tracking.

### Blocked Items

A blocked item represents work that cannot proceed due to external dependencies, technical limitations, or other impediments. It's crucial to properly flag blocked items to maintain transparency and facilitate quick resolution of blockers.

#### Marking an Item as Blocked

To indicate that a work item is blocked:

1. Right-click the item on the Jira board
2. Click "Add Flag"
3. Enter the reason for the blockage in the flag description

## Best Practices

1. **Collaboration**: Use tags, mentions, and comments to facilitate collaboration and knowledge sharing.
2. **Consistency**: Maintain consistent use of issue types and statuses to ensure accurate tracking and reporting.
3. **Training**: Invest in training to ensure team members understand the processes and tools.

## Conclusion

By following these guidelines, engineering teams can effectively manage their CapEx and OpEx work in Jira, ensuring accurate financial reporting and improved operational efficiency.

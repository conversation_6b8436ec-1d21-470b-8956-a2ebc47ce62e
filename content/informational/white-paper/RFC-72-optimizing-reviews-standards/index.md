<!-- Parent: Informational -->
<!-- Parent: White Paper -->
<!-- Title: RFC72-Optimizing Technical Reviews & Standards -->
# Optimizing Technical Reviews & Standards

```text
Author: <PERSON>
Title: Optimizing Technical Reviews
Publish Date: 2024-08-23
Category: Informational
Subtype: Whitepaper
```

This whitepaper is an overview of the methodology, analysis, and decisions used to optimize technical reviews and standards at WEX.  This information is being provided as additional context and allows the community to understand and question any assumptions, biases, logical fallacies, etc.

The following RFCs are the work product of this project:

- [RFC13-Technical Review Taxonomy](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593689766)
- [RFC14-Technical Standards Taxonomy](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593657230)
- [RFC15-Design Collaboration](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593984673)
- [RFC12-Formal Review Process](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154594017461)

## Problem Domain Analysis

### Commonalities

Technical standards and technical reviews have a symbiotic relationship.

- Technical standards must be reviewed before being adopted
- Technical reviews examine a solution for adherence to standards

Because of this both systems are impacted if either is changed.  The problem domain for optimization must include both systems.

Both systems are often described inconsistently or in vague terms by different teams and governing bodies. A shared terminology and understanding of concepts is crucial in optimizing how these systems operate.

Changes to both systems have a large impact radius across WEX.  Usage of existing tools & services must be prioritized for effective delivery and implementation lead times.  There must also be close collaboration with governance bodies to ensure all needs are met and enable full adoption.

### Governance Scopes

There is significant overlap in the use cases and concerns examined by the current governance bodies.  However the governance bodies have historically not published concrete conditions that necessitate review & approval or what concerns need to be satisfied to gain approval.

While there is overlap in the set of evaluated concerns, the scope of accountability for each body performing technical reviews is currently:

- iSARB is accountable for Infrastructure & Network Security governance
- SARB is accountable for Product Architecture design governance
- AppSec is accountable for Application Security governance
- Architecture org is accountable for guiding and advising development teams on cross-cutting concerns

The current set of use cases requiring a review ***appear*** to be distributed as follows:

![Use Cases](use-cases.png)

The current set of concerns evaluated in the reviews ***appear*** to be distributed as follows:

![Concerns](concerns.png)

### Desired Outcomes for Teams

Engineering teams need to be able to deliver without approval except for specific conditions identified by WEX governance bodies.  This means that we will need to distinguish between technical reviews that require approval and technical reviews which are collaborative or additive to a design.  This distinction can be re-enforced by categorizing technical reviews as:

- **Formal Reviews:** that act as a gating condition and require approval before moving forward with a design
- **Informal Reviews:** that help to form and refine a high-level design and ensure that any required formal reviews are identified

Currently all three governance bodies have their own intake process and templates.  Security Architecture is represented in all three as a defense-in-depth measure to ensure compliance with WEX policies.

Due to the overlap in concerns and conditions, teams may be overwhelmed and slowed down by the gauntlet of questions that occurs today:
![Source Questions](source-questions.png)

## Design Considerations

The analysis identified the following prioritized process design considerations for both formal technical reviews and standards:

- a cohesive and effective governance process for all formal technical reviews
- extensible and adaptable solution for the current needs of all governance bodies performing technical reviews
- a shared taxonomy for reviews and standards with some basis on industry standards to increase awareness
- broad collaboration across WEX IT for refinement and approval of work product
- optimized for delivery and implementation lead times by leveraging existing WEX standard tools & services

### Informal Reviews

Informal technical reviews are a valuable practice that can ensure a solution is meeting all non-functional requirements.  Informal technical reviews are highly encouraged to occur early during discovery and design phases with technical SMEs from:

- Architecture
- PaaS Engineering
- Security Architecture
- Cloud Engineering
- Database Engineering
- LoB Support Resources

## Technical Reviews

### Current Review Pain

- lots of repetition caused by overlapping concerns of multiple governance bodies
- lack of coordination between different governance bodies
- lack of clarity about the conditions which should trigger a review (rule-of-thumb vs of explicit)
- significant time spent focusing on immaterial detail
- inconsistent responses on quiz-style intake using Google Forms and Slides
- increased implementation lead times caused by review prep complexity

### Review Recommendations

The following recommendations are based on the common design considerations and current pain associated with technical reviews:

- deliver an opinionated taxonomy for technical reviews based on IEEE 1028-2008 that is consistent with existing types of reviews at WEX
- leverage Global Service Portal and Jira Service Management for intake and orchestration of technical reviews
- leverage RFC platform as repository for portable technical documents which can be reviewed by all applicable governing bodies
- recommend practices that teams can adopt which foster informal reviews early in the design phase
- formalizing and publishing the triggering conditions and acceptance criteria for technical reviews performed by iSARB, SARB, and Security Architecture
- deliver RFC templates and/or JSM intake process that can be used for review by governance bodies
- adoption of a new technical review process should use itself as a pilot test case
- SARB/CAD needs to be more inclusive of technical leaders outside of Architecture: Cloud, PaaS, InfoSec, Middleware, EUC, etc
- SARB/CAD needs separate meetings for formal reviews vs informal walkthroughs and inspections
- SARB/CAD needs to enumerate concrete conditions and requirements for approving formal technical review
- iSARB needs to enumerate concrete conditions and requirements for approving formal technical review
- InfoSec needs to enumerate the concrete conditions and requirements for Security Inspections of security components not covered by SARB & iSARB

## Technical Standards

### Current Standards Pain

- existing standards are difficult to discover or have unreasonable lead times restricting adoption
- enforcement level is unclear for some standards
- lack of clarity about what can be standardized
- inconsistent evaluation of standards by governance bodies
- lack of clear path for extension or replacement of standards
- lack of consistency in evaluating solutions for adherence to standards
- lack of clarity about how to resolve conflicts between standards from different scopes

### Standards Recommendations

The following recommendations are based on the common design considerations and current pain associated with technical standards:

- deliver an opinionated taxonomy for technical standards based on IETF RFC 2119
- leverage RFC platform for open commentary by development community about proposed standards
- deliver RFC templates for different types of technical standards at WEX

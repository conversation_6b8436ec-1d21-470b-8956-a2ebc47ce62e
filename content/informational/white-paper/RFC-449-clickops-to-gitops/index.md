<!-- Parent: Informational -->
<!-- Parent: White Paper -->
<!-- Title: RFC-449 Transitioning from ClickOps to GitOps -->
# Transitioning from ClickOps to GitOps

```text
Author: <PERSON>, <PERSON>, <PERSON>
Title: # Transitioning from ClickOps to GitOps
Publish Date: 2025-02-26
Category: Informational
Subtype: Whitepaper
```

## Introduction

This whitepaper aims to educate and encourage teams to transition from ClickOps to GitOps.

> **ClickOps** is the practice of manually managing infrastructure through user interfaces and can be inefficient, error-prone, and difficult to scale.
>
> **GitOps** is a modern approach that leverages Git as a single source of truth for infrastructure and application code.

This paper will explore the benefits of GitOps, the technologies and practices involved, and strategies for a successful transition.

## When to Use GitOps

**Development teams** should consider a GitOps strategy in the following scenarios:

* **Consistent Environments**: When development teams need consistent environments across different stages (development, testing, production), GitOps ensures that the same configurations are applied everywhere.
* **Frequent Changes**: If your team frequently updates applications or infrastructure, GitOps automates deployments, reducing manual effort and errors.
* **Collaboration**: When multiple developers work on the same project, GitOps facilitates collaboration through version control, code reviews, and automated testing.
* **Rollback Capabilities**: If your team requires the ability to quickly revert changes, GitOps provides easy rollback to previous states using Git history.

**Operational teams** should consider a GitOps strategy in the following scenarios:

* **Infrastructure as Code (IaC)**: When managing infrastructure as code, GitOps provides a structured and auditable approach, ensuring all changes are tracked and reviewed.
* **Scalability**: If your organization needs to scale infrastructure rapidly, GitOps automates deployments, making it easier to handle large-scale changes.
* **Compliance and Auditing**: For teams that need to comply with regulatory requirements, GitOps offers a clear audit trail of all changes, enhancing security and compliance.
* **Disaster Recovery**: When disaster recovery is a priority, GitOps allows for quick restoration of infrastructure by applying the desired state stored in Git.
* **Developer Self-Service and Operational Focus**: GitOps empowers developers to manage their own infrastructure changes, reducing dependency on operational roles. This self-service capability allows operational teams to concentrate on quality improvements, ensuring actions are consistent, traceable, and aligned with the desired state defined by the development team.

## When to Avoid GitOps

**Development teams** should avoid a GitOps strategy in the following scenarios:

* **Small Projects**: For small projects with infrequent changes, the overhead of setting up GitOps may not be justified.
* **Lack of Git Proficiency**: If the team lacks proficiency in Git and version control practices, the transition to GitOps can be challenging and counterproductive.

**Operational teams** should avoid a GitOps strategy in the following scenarios:

* **Highly Dynamic Environments**: In environments where infrastructure changes are highly dynamic and unpredictable, the declarative nature of GitOps may not be flexible enough.
* **Legacy Systems**: For legacy systems that do not support infrastructure as code or are difficult to integrate with GitOps tools, the transition may be complex and risky.
* **Resource Constraints**: If the team lacks the resources to invest in the necessary tooling and training, adopting GitOps may not be feasible.

## Value Propositions

GitOps makes infrastructure management more reliable, efficient, scalable, and auditable by applying the principles of version control and automation.

### Quality Improvements

* **Increased testing and reduced user error**: GitOps promotes a structured and auditable approach to infrastructure management.
 All changes are tracked in Git, allowing for thorough code reviews and automated testing.
 This reduces the risk of human error and ensures that only validated changes are deployed.

* **Automated testing throughout the development pipeline**: With GitOps, infrastructure changes can be subjected to the same rigorous testing processes as application code.
 This includes unit tests, integration tests, and end-to-end tests, which can be automated throughout the CI/CD pipeline.

* **Improved traceability of changes**: Git provides a complete history of all changes made to infrastructure, including who made the change, when it was made, and why.
 This improved traceability simplifies debugging, auditing, and compliance.

### Self-Service Capabilities

* **Empower developers to make changes independently**: GitOps enables developers to manage their own infrastructure using Git, reducing the need to rely on operations teams for every change.
 This empowers developers and accelerates the development process.

* **Reduce the burden on operations teams**: By enabling self-service, GitOps reduces the workload on operations teams, allowing them to focus on more strategic initiatives.

### Scalability

* **Enable rapid scaling of change delivery**: GitOps allows for the automated deployment of infrastructure changes, enabling organizations to scale their change delivery processes quickly and efficiently.

* **Handle large-scale deployments efficiently**: GitOps is designed to handle large-scale deployments by leveraging Git's distributed nature and automation tools.

### Release Process Integration

* **Deploy changes as part of a controlled and managed release process**: GitOps integrates with existing release processes, enabling organizations to deploy infrastructure changes in a controlled and managed manner.

* **Ensure consistent and reliable deployments**: By using Git as the single source of truth, GitOps ensures that deployments are consistent and reliable.

### Delivery Velocity

* **Reduce the time it takes to deliver changes to production**: GitOps automates the deployment process, significantly reducing the time it takes to deliver changes to production.

* **Improve overall agility and responsiveness**: By enabling faster and more efficient change delivery, GitOps improves an organization's overall agility and responsiveness to changing business needs.

## GitOps Maturity Levels

GitOps maturity can be categorized into different levels, each representing the extent to which GitOps practices are adopted and integrated within an organization. These levels help teams understand their current state and identify areas for improvement.

### Level 1: Manual GitOps

At this initial level, teams start by manually applying GitOps principles. Infrastructure and application configurations are stored in Git, but deployments and updates are triggered manually. This level helps teams get familiar with version control and the basic concepts of GitOps.

* **Characteristics**:
  * Configurations stored in Git.
  * Manual deployment processes.
  * Basic version control and collaboration.

* **Challenges**:
  * Potential for human error.
  * Limited automation.
  * Inconsistent deployments.

### Level 2: Automated GitOps

In this level, teams begin to automate the deployment process using CI/CD pipelines. Changes to the Git repository automatically trigger deployments, reducing the need for manual intervention and increasing consistency.

* **Characteristics**:
  * Automated deployment pipelines.
  * Integration with CI/CD tools.
  * Improved consistency and reliability.

* **Challenges**:
  * Initial setup complexity.
  * Requires investment in CI/CD tooling.
  * Need for robust testing practices.

### Level 3: Self-Healing GitOps

At this maturity level, the system can automatically detect and correct drift between the desired state in Git and the actual state of the infrastructure. Tools continuously monitor the environment and reconcile any discrepancies, ensuring that the infrastructure always matches the desired state.

* **Characteristics**:
  * Continuous monitoring and reconciliation.
  * Automated drift detection and correction.
  * High reliability and consistency.

* **Challenges**:
  * Requires advanced tooling and monitoring.
  * Complex setup and maintenance.
  * Need for comprehensive alerting and logging.

### Level 4: Policy-Driven GitOps

In this advanced level, policies and governance are integrated into the GitOps workflow. Policies define what changes are allowed and enforce compliance with organizational standards. This level ensures that all changes adhere to security, compliance, and operational policies.

* **Characteristics**:
  * Policy enforcement integrated into workflows.
  * Automated compliance checks.
  * Enhanced security and governance.

* **Challenges**:
  * Requires policy management tools.
  * Complex policy definitions and enforcement.
  * Continuous policy updates and maintenance.

### Level 5: Adaptive GitOps

The highest maturity level involves adaptive and intelligent GitOps practices. The system leverages machine learning and advanced analytics to optimize deployments, predict issues, and proactively manage infrastructure. This level represents a fully autonomous and self-optimizing GitOps implementation.

* **Characteristics**:
  * Machine learning and analytics integration.
  * Proactive issue detection and resolution.
  * Continuous optimization of deployments.

* **Challenges**:
  * High complexity and resource requirements.
  * Need for advanced data analytics capabilities.
  * Continuous learning and adaptation.

## Technical Up-skilling & Cultural Change

The transition to GitOps requires a blend of technical up-skilling and cultural change.
 Teams need to learn new tools and practices, but also fundamentally change how they think about and manage infrastructure.

### Technical Skills

* **Git Proficiency**: Deep understanding of Git for version control, branching strategies, and collaboration workflows.
 This includes how to create, review, and merge changes effectively.

* **Infrastructure as Code (IaC) Tools**: Familiarity with tools like Terraform for provisioning and managing infrastructure resources programmatically.

* **CI/CD Pipelines**: Knowledge of how to build, automate, and manage continuous integration and continuous delivery pipelines, including testing and deployment automation.
 Tools like GitHub Actions are essential here.

* **GitOps Tools**: Learning specific GitOps tools like ArgoCD for deploying and managing applications on Kubernetes or similar platforms.

* **Containerization and Orchestration**: Understanding of container technologies (e.g., Docker) and orchestration systems (e.g., Kubernetes), as these often form the foundation of modern GitOps implementations.

* **Testing and Validation**: How to write and automate tests for infrastructure changes and deployments.

### Methodologies

* **Code-First Mentality**: Moving from managing infrastructure through GUIs (ClickOps) to managing everything as code in Git.
 This requires a shift towards thinking of infrastructure as software.

* **Automation Focus**: Embracing automation for all aspects of infrastructure management, from provisioning to deployment.
 This means trusting automated processes and relying less on manual intervention.

* **Collaboration and Transparency**: GitOps encourages collaboration through code reviews and shared ownership of infrastructure.
 A shift towards more transparent and collaborative workflows is crucial.

* **Version Control as Single Source of Truth**: Accepting Git as the single source of truth for the entire system's desired state.
 Any change must go through Git, and the system should reconcile itself with what's in Git.

* **Embracing Failure and Recovery**: Because GitOps enables fast and reliable rollbacks, there's less fear of making mistakes.
 Teams need to embrace experimentation and be prepared to recover quickly when things go wrong.

* **Empowerment and Ownership**: Developers and other team members need to take ownership of the infrastructure they manage.
 This involves empowering them to make changes and being accountable for those changes.

### Technologies

* **GitHub Enterprise**: A platform for hosting and managing Git repositories.

* **GitHub Action Runners**: Tools for automating CI/CD workflows.

* **Terraform**: An infrastructure-as-code tool for provisioning and managing cloud resources.

* **ArgoCD**: A GitOps tool for deploying and managing applications on Kubernetes.

* **Helm**: A package manager for Kubernetes.

* **Artifactory**: A repository manager for storing and managing software artifacts.

* **LaunchDarkly**: A feature flag management platform.

### Patterns

* **Continuous integration/continuous delivery (CI/CD)**: A set of practices for automating the software development lifecycle.

* **Infrastructure as code (IaC)**: The practice of managing infrastructure using code.

* **GitOps**: A pattern for managing infrastructure and applications using Git as the single source of truth.

### Practices

* **Version control**: Tracking all changes to code and infrastructure in Git.

* **Code reviews**: Reviewing code changes before they are merged into the main branch.

* **Automated testing**: Automating the testing of code and infrastructure changes.

* **Continuous deployment**: Automatically deploying changes to production after they have been tested.

## Transition Strategies

* **Pilot a GitOps approach on a small scale**: Start by implementing GitOps for a small, non-critical application or infrastructure component.

* **Gradually migrate existing applications to GitOps**: Once the pilot project is successful, gradually migrate other applications and infrastructure components to GitOps.

* **Train and up-skill system administrators and analysts in GitOps practices**: Provide training and resources to help system administrators and analysts learn GitOps practices and technologies.

## Frequently Asked Questions

**Why should we transition to GitOps when it's faster to use the UI for quick changes?**

* While UI changes can be fast, they lack version control, audit trails, and repeatability.
 This leads to configuration drift, inconsistencies, and difficulty in rolling back changes.

* GitOps provides a reliable history of every change, enabling easy rollbacks and improved auditability.

* Automation through GitOps reduces human error and ensures consistent deployments, which ultimately saves time and reduces risk in the long run.

* Essentially, short term speed, turns into long term instability.

**How does GitOps improve security?**

* GitOps centralizes configuration management, making it easier to enforce security policies.

* Version control provides a clear audit trail, allowing you to track who made changes and when.

* Secrets management tools can be integrated into GitOps workflows to protect sensitive data.

* GitOps also allows for a clear review process of all changes before they are implemented.

**How do we handle rollbacks in GitOps?**

* Rollbacks are simple in GitOps.
 You revert to a previous commit in your Git repository, and the GitOps tool automatically updates the infrastructure to match that state.

**What are the challenges of transitioning to GitOps?**

* It requires a shift in mindset and workflow.

* Teams need to learn new tools and practices.

* Migrating existing infrastructure to a declarative model can be complex.

**How do we manage secrets in a GitOps workflow?**

* Never commit secrets directly to Git.
 Use dedicated secrets management tools like HashiCorp Vault or Azure Key Vault.
 These tools can integrate with your GitOps workflow to inject secrets during deployment.

**How does GitOps help with disaster recovery?**

* Because your entire infrastructure configuration is stored in Git, you can quickly rebuild your environment in case of a disaster.

**What are the benefits of declarative configuration?**

* Declarative configuration defines the desired state of your infrastructure, not the steps to achieve it.
 This makes it easier to automate and maintain consistent configurations.

## Further Reading

* [GitOps Explained: Why It's the Future of Infrastructure Management](https://ekantmate.medium.com/gitops-explained-why-its-the-future-of-infrastructure-management-e9caf33f02d3)
* [Bridging ClickOps and GitOps](https://www.qovery.com/blog/bridging-clickops-and-gitops/)
* [Guide to GitOps](https://www.datacamp.com/tutorial/guide-to-gitops)

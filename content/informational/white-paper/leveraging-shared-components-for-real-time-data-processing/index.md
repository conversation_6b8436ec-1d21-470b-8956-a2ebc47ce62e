<!-- Parent: Informational -->
<!-- Parent: White Paper -->
<!-- Title: RFC-526 Leveraging Shared Components for Real-time Data Processing -->
# Leveraging Shared Components for Real-time Data Processing

## A Strategic Analysis of Event Hub and Notification Hub Reuse Opportunities

```text
Author: <PERSON><PERSON>wain
Title: Leveraging Shared Components for Real-time Data Processing
Publish Date: 2025-06-26
Category: Informational
Subtype: White paper
```

:toc:

## Reference Documents

The following documentation provides additional context and supporting materials for this whitepaper:

- [Common Pattern - real-time data processing and external communication](https://docs.google.com/document/d/1gXz9rUy7lg5pST7HAnC0sJbbQzJo53pJpRxHnxjva_8/edit?usp=drive_link)

## Executive Summary

Both the "Consumer Realtime Card Transaction alerts from FIS" (CDH) and "Transactional Activity Streaming" (TAS) Candidate Architecture Designs (CADs) demonstrate a clear adoption of modern, event-driven architectures and shared services, particularly centered around real-time data processing and external communication.

This whitepaper identifies significant opportunities to leverage existing shared components rather than creating duplicative implementations. By utilizing common services like the Event Hub (Kafka) and Notification Hub, WEX can:

- **Reduce Development Time**: Reduce development effort by reusing existing components
- **Improve Consistency**: Standardize on proven patterns and technologies across business units
- **Enhance Operational Efficiency**: Consolidate monitoring, support, and expertise around fewer components
- **Accelerate Time-to-Market**: Deploy new capabilities faster by building on established foundations

**TAS CAD:** [https://wexinc.atlassian.net/wiki/x/AQBPHCQ](https://wexinc.atlassian.net/wiki/x/AQBPHCQ)  
**CDH CAD:** [https://wexinc.atlassian.net/wiki/x/rABOGyQ](https://wexinc.atlassian.net/wiki/x/rABOGyQ)

## Summary of Findings and Recommendations

The following table summarizes the key findings and recommendations from our analysis:

| Area | Current State | Opportunity | Recommendation | Priority |
|------|--------------|-------------|----------------|----------|
| **Event Hub (Kafka)** | **Existing shared component** already used by TAS. CDH plans to use Kafka Aiven but hasn't explicitly referenced the existing Event Hub. | Leverage the existing Event Hub rather than implementing a separate Kafka solution. | Standardize on the existing Event Hub with shared client libraries and implement a centralized schema registry for event standardization. | High |
| **Notification Hub** | **Existing shared component** already used by TAS with HMAC authentication. CDH has an undefined "Send notification to consumer" requirement. | Utilize the existing Notification Hub for CDH's consumer alerts. | Integrate CDH's consumer notification requirements with the existing Notification Hub shared component. | High |
| **Microservice Patterns** | Both use microservice architecture. TAS uses "Creatio" template for .NET microservices. CDH implements webhook receiver API with outbox pattern. | Standardize development patterns across teams. | Adopt the Creatio template for new .NET microservices and implement common patterns like outbox pattern for guaranteed message delivery. | Medium |
| **Webhook Security** | CDH uses OAuth2.0 and IP allowlisting for inbound webhooks. TAS uses HMAC for outbound webhooks. | Create a unified security approach for both inbound and outbound webhooks. | Develop common webhook security libraries and standards that support both OAuth2.0/IP allowlisting for inbound and HMAC for outbound communications. | High |
| **Observability** | Both use Datadog/Splunk. TAS additionally uses OpenTelemetry (OTEL) for instrumentation. | Create a unified observability approach with end-to-end tracing. | Adopt OpenTelemetry across all services with shared dashboards and standardized metrics for webhook delivery performance. | Medium |
| **Secrets Management** | CDH uses Delinea for Kafka credentials. TAS secrets management is unspecified. | Centralize secrets management across both platforms. | Use Delinea as the standard for secrets management for all credentials, including HMAC secrets for webhooks. | High |
| **Developer Experience** | TAS Notification Hub has manual onboarding for MVP. | Create unified customer experience for notifications with self-service capabilities. | Develop a "UI Once" developer portal with event catalog, webhook configuration, and delivery monitoring features. | Medium |

## 1. Common Solution Patterns Identified

A technical deep-dive into both CADs reveals several common solution patterns:

### 1.1 Event-Driven Architecture with Kafka

Both solutions heavily rely on Kafka for real-time data streaming and communication between internal components. Kafka is highlighted for its high throughput, low latency, high availability, and ability to handle high volumes. In the TAS CAD, Kafka is explicitly identified as a "Shared Component" (referred to as Event Hub) for real-time transaction streams. **The Event Hub is an existing shared component already in use by WEX services.**

```mermaid
flowchart TB
    subgraph "Common Event-Driven Architecture"
        kafka["Event Hub / Kafka<br>(Existing Shared Component)"]
        
        subgraph "CDH"
            webhook_api["Webhook Receiver API"] --> kafka
            kafka --> auth_consumer["Authorization Event Consumer"]
            auth_consumer --> notification["Consumer Notification"]
        end
        
        subgraph "TAS"
            encompass["EnCompass UDF Producer"] --> kafka
            kafka --> txn_assembler["Transaction Assembler"]
            txn_assembler --> kafka
            kafka --> notification_hub["Notification Hub<br>(Existing Shared Component)"]
        end
    end
```

### 1.2 Microservice-Based Design

Both CADs propose solutions built upon microservices. The CDH design includes a webhook receiver API and an Authorization Event Consumer service. The TAS design introduces a new Transaction Assembler microservice and an EnCompass UDF Producer microservice. This indicates a shared architectural paradigm of breaking down functionalities into smaller, independently deployable services.

### 1.3 Webhook for External Integration

Both systems utilize webhooks for communication with external parties. CDH's solution involves FIS pushing authorization and decline notifications to a webhook receiver API. The TAS Notification Hub uses webhooks to notify customers about data availability. **The Notification Hub is an existing shared component already used by WEX services for external communications.**

```mermaid
flowchart LR
    subgraph "External Integration Pattern"
        subgraph "CDH Inbound"
            fis["FIS"] -->|Webhook| cdh_webhook["Webhook Receiver API"]
        end
        
        subgraph "TAS Outbound"
            notification_hub["Notification Hub<br>(Existing Shared Component)"] -->|Webhook| customers["Customers"]
        end
    end
```

### 1.4 Robust Security Measures

Both CADs emphasize strong security. They mention OAuth2.0 Client Credentials Flow and IP allowedlist for webhook authentication, and HMAC authentication for webhooks (in TAS). Both process sensitive information (PHI, PII, PAN) and ensure encryption (e.g., when stored in Kafka Broker in CDH). Adherence to WEX Security Standards and Global Information Classification and Handling Policy is referenced in both.

### 1.5 Comprehensive Monitoring

Both solutions integrate monitoring using Splunk and Datadog for various components. Kafka Aiven also provides its own monitoring. TAS additionally mentions OTEL for instrumentation. This indicates a shared approach to observability.

```mermaid
flowchart TB
    subgraph "Monitoring & Observability"
        services["WEX Services"]
        kafka["Kafka Aiven"]
        
        services -->|Logs & Metrics| splunk["Splunk"]
        services -->|Metrics & Traces| datadog["Datadog"]
        kafka -->|Built-in Metrics| kafka_monitoring["Kafka Monitoring"]
        
        subgraph "TAS-Specific"
            tas_services["TAS Services"] -->|Instrumentation| otel["OpenTelemetry (OTEL)"]
            otel --> datadog
        end
    end
```

### 1.6 Standardized Non-Functional Requirements (NFRs)

Both documents categorize NFRs similarly, focusing on Reliability, Flexibility, and Performance Efficiency, and detail how their proposed solutions address these.

### 1.7 Shared Cloud Infrastructure

Both projects appear to operate within existing Azure Subscriptions, indicating a shared underlying cloud environment.

## 2. Shared Asset Opportunities

This section provides a detailed examination of the specific assets from both CADs that present significant opportunities for reuse.

### 2.1 Event Hub (Kafka)

**Current State:**

- **Event Hub:** An existing shared component at WEX, providing Kafka as a service for real-time event streaming
- **TAS:** Already leverages this shared component for transaction streams
- **CDH:** Plans to use Kafka Aiven but hasn't explicitly referenced the existing Event Hub

**Opportunity:** CDH should directly integrate with the existing Event Hub rather than implementing a separate Kafka solution.

### 2.2 Notification Hub

**Current State:**

- **Notification Hub:** An existing shared component for webhook-based customer notifications
- **TAS:** Already uses this component with HMAC authentication for secure webhooks
- **CDH:** Has an undefined "Send notification to consumer" requirement

**Opportunity:** CDH should leverage the established Notification Hub for its consumer alerts.

```mermaid
flowchart TB
    subgraph "Notification Hub Integration Opportunity"
        subgraph "Current State"
            tas_services["TAS Services"] --> notification_hub["Notification Hub<br>(Existing Shared Component)"]
            notification_hub -->|Webhook| tas_customers["TAS Customers"]
            
            cdh_services["CDH Services"] --> custom_notification["Planned Custom Notification<br>Mechanism"]
            custom_notification -->|Undefined| cdh_customers["CDH Customers"]
        end
        
        subgraph "Recommended State"
            tas_services2["TAS Services"] --> notification_hub2["Notification Hub<br>(Existing Shared Component)"]
            cdh_services2["CDH Services"] --> notification_hub2
            notification_hub2 -->|Webhook| all_customers["All WEX Customers"]
        end
    end
```

### 2.3 Transaction Assembler Microservice

**Current State:**

- **TAS:** A NEW microservice for "merging the 'Auth Stream' with the 'User Defined Fields Stream'". Built using Wex's .NET Kafka library.
- **CDH:** No equivalent functionality required.

**Opportunity:** While the business logic isn't directly reusable, the development pattern of building a .NET microservice that consumes from and produces to Kafka can be standardized across teams.

### 2.4 EnCompass UDF Producer Microservice

**Current State:**

- **TAS:** A new microservice to "produce the User Defined Field stream from the encompass database to kafka".
- **CDH:** Uses FIS as data source via a webhook API, not EnCompass.

**Opportunity:** The pattern of creating dedicated producer microservices for specific data sources can be standardized, even if the specific implementation is not directly reusable.

## 3. Technical Deep Dive

This section examines key technical areas where standardization and shared components would provide the most value.

### 3.1 Event-Driven Architecture and Kafka as a Shared Event Hub

**Technical Details:**

- The CDH solution explicitly states it uses "Kafka to provide high throughput, low latency real time data feeds", noting its support for high availability, automagical recovery, and multi-regions. The webhook API encrypts sensitive data and "publish to Kafka", and the "Authorization Event Consumer Service will consume the events from Kafka". Authentication to "Kafka Aiven will follow the current standard", with credentials stored in Delinea.
- The TAS solution lists "Event / Kafka for communication between WEX components" as a pattern adopted. It defines the "Event Hub" as an existing "Shared Component" for "Use of Kafka for real-time transaction stream". Specific microservices like the Transaction Assembler consume from and produce to Kafka, and a new EnCompass UDF Producer microservice will also produce to Kafka.

**Shared Asset Opportunities:**

- **Standardized Kafka Client Libraries:** TAS mentions leveraging "Wex's .NET Kafka library". If CDH also uses .NET, this library is a direct candidate for code reuse, ensuring consistent interaction with Kafka, including handling authentication, serialization/deserialization, and error handling.
- **Event Schema Governance:** Implementing a centralized schema registry (e.g., Confluent Schema Registry, compatible with Aiven) and defining common event schemas (e.g., using Avro or Protobuf) for domain events (like authorization or transaction activity) ensures interoperability and data consistency.
- **Shared Monitoring for Kafka:** Standardizing Kafka observability metrics, dashboards, and alerting configurations would provide a consolidated view of the health of the entire event streaming platform.

```mermaid
flowchart TB
    subgraph "Event Hub - Existing Shared Infrastructure"
        kafka["Event Hub / Kafka Aiven<br>(Existing Shared Component)"]
        schema_registry["Schema Registry"]
        
        subgraph "CDH Components"
            webhook_api["Webhook Receiver API"] -->|Produces| kafka
            kafka -->|Consumes| auth_consumer["Authorization Event Consumer"]
        end
        
        subgraph "TAS Components"
            encompass["EnCompass UDF Producer"] -->|Produces| kafka
            kafka -->|Consumes| txn_assembler["Transaction Assembler"]
            txn_assembler -->|Produces| kafka
        end
        
        subgraph "Shared Libraries & Patterns"
            kafka_lib[".NET Kafka Library"]
            outbox["Outbox Pattern"]
            
            webhook_api -.-> kafka_lib
            auth_consumer -.-> kafka_lib
            encompass -.-> kafka_lib
            txn_assembler -.-> kafka_lib
            
            webhook_api -.-> outbox
        end
        
        subgraph "Shared Monitoring"
            kafka --> metrics["Kafka Metrics"]
            metrics --> datadog["Datadog Dashboards"]
            metrics --> splunk["Splunk Alerts"]
        end
        
        kafka <--> schema_registry
    end
```

### 3.2 Webhook for External Ingress/Egress and Notification Hub

**Technical Details:**

- CDH proposes creating a "webhook receiver API" to receive "Authorization and Decline notification in near real-time" from FIS. This API will be authorized using "OAuth2.0 Client Credentials Flow" and protected by an "IP allowedlist". It will also encrypt sensitive data before publishing to Kafka.
- TAS explicitly adopts the "Webhook for communication between WEX and external parties / Customers" pattern. It identifies the "Notification Hub" as an existing "Shared Component" responsible "To notify Customers about the data availability". This Notification Hub utilizes webhooks and supports "HMAC authentication for Webhooks".

**Shared Asset Opportunities:**

- **Standardized Inbound Webhook Receiver Component:** The "webhook receiver API" in CDH could be designed as a reusable template or library for other inbound webhook integrations across WEX, encapsulating common concerns like security, payload validation, and integration with an outbox pattern.
- **Common Security Standards for Webhooks:** A unified security framework or set of guidelines for webhook authentication and authorization could be applied to both inbound and outbound webhook integrations.

```mermaid
flowchart LR
    subgraph "External Integration Framework"
        subgraph "Inbound Webhooks"
            fis["FIS"] -->|OAuth2.0 + IP Allowlist| webhook_receiver["Webhook Receiver API<br>(Reusable Template)"]
            webhook_receiver -->|Outbox Pattern| db[(Database)]
            db -->|Async| kafka["Event Hub<br>(Existing Shared Component)"]
        end
        
        subgraph "Outbound Webhooks"
            kafka2["Event Hub<br>(Existing Shared Component)"] --> notification_hub["Notification Hub<br>(Existing Shared Component)"]
            notification_hub -->|HMAC Authentication| customers["WEX Customers"]
        end
        
        security_framework["Common Webhook Security Framework<br>(OAuth2.0, IP Allowlist, HMAC)"]
        security_framework -.->|Governs| webhook_receiver
        security_framework -.->|Governs| notification_hub
    end
```

## 4. The "UI Once" Concept: A Shared Developer Portal

The "UI Once" concept envisions a unified customer-facing developer portal for managing webhook subscriptions across all WEX real-time data streams, leveraging the existing Notification Hub.

### 4.1 Vision and Benefits

- **Unified Experience**: Customers interact with a single portal regardless of which WEX product they use
- **Self-Service**: Customers can discover, configure, and manage their own webhook subscriptions
- **Standardized Security**: Consistent authentication and authorization mechanisms
- **Reduced Development Effort**: Built once, leveraged by multiple WEX teams

### 4.2 Key Components

**The Notification Hub**:

- Serves as the central egress point for all customer-facing webhooks
- Already exists as an established "Shared Component" in use by TAS
- Supports HMAC authentication for secure webhook delivery
- Currently employs manual onboarding for the TAS MVP, with plans to enable self-service in the future

**Customer-Facing Portal Features**:

- Customer authentication and onboarding
- Event catalog for discovering available data streams
- Webhook endpoint configuration
- Subscription management
- Developer resources (documentation, SDKs)

```mermaid
flowchart TB
    subgraph "UI Once Developer Portal"
        portal["Shared Developer Portal<br>(UI Once)"]
        
        subgraph "Portal Features"
            auth["Customer Authentication"]
            catalog["Event Catalog"]
            config["Webhook Configuration"]
            subscriptions["Subscription Management"]
            docs["Developer Resources"]
        end
        
        portal --> auth
        portal --> catalog
        portal --> config
        portal --> subscriptions
        portal --> docs
    end
    
    subgraph "Unified Backend"
        notification_hub["Notification Hub<br>(Existing Shared Component)"]
        
        subgraph "Event Sources"
            cdh_events["CDH Events<br>(Card Transaction Alerts)"]
            tas_events["TAS Events<br>(Payment Data)"]
            future_events["Future WEX Events"]
        end
        
        cdh_events --> notification_hub
        tas_events --> notification_hub
        future_events -.-> notification_hub
    end
    
    portal <-->|"Configuration API"| notification_hub
    notification_hub -->|"HMAC-secured Webhooks"| customers["WEX Customers"]
```

### 4.3 Data Flow Architecture

To achieve this unified experience, the internal WEX systems need to feed into the Notification Hub in a standardized way:

```mermaid
flowchart TB
    subgraph "CDH Data Flow"
        fis["FIS"] -->|"Webhooks<br>(Auth & Decline Notifications)"| webhook_api["Webhook Receiver API"]
        webhook_api -->|"Encrypted Events"| kafka_cdh["Kafka"]
        kafka_cdh --> auth_consumer["Authorization Event Consumer"]
        auth_consumer -->|"Consumer Notifications"| notification_hub["Notification Hub"]
    end
    
    subgraph "TAS Data Flow"
        encompass["EnCompass"] --> udf_producer["UDF Producer Microservice"]
        udf_producer -->|"UDF Stream"| kafka_tas["Kafka"]
        kafka_tas --> txn_assembler["Transaction Assembler"]
        txn_assembler -->|"Merged Stream"| kafka_tas
        kafka_tas --> notification_hub
    end
    
    subgraph "Notification Hub"
        event_router["Event Router<br>(Based on Customer Subscriptions)"]
        delivery_manager["Webhook Delivery Manager<br>(with Retry Logic)"]
        
        notification_hub --> event_router
        event_router --> delivery_manager
    end
    
    delivery_manager -->|"HMAC-secured Webhooks"| customers["Customer Systems"]
    
    subgraph "Developer Portal"
        portal["UI Once Portal"]
        subscription_db[(Subscription Database)]
        
        portal <--> subscription_db
    end
    
    subscription_db <-->|"Subscription Configuration"| event_router
```

### 4.4 Technical Implementation Details

**API Gateway:**

- Handles rate limiting, API key management, and security checks for the Dev Portal's calls to the Notification Hub

**Subscription Data Storage:**

- Robust database to store webhook configurations, endpoints, event types, HMAC secrets, and preferences

**Reliable Delivery:**

- Message queue with retry logic and dead-letter queues for reliable webhook delivery

**Security:**

- Customer authentication for portal access
- HMAC authentication for outgoing webhooks
- Optional IP whitelisting for additional security

**Monitoring:**

- End-to-end distributed tracing
- Delivery performance metrics and alerts
- Customer-facing delivery status visibility

### 4.5 Customer Experience Details

**Customer Onboarding & Authentication:**

- Self-service signup and account management
- Standard authentication mechanisms
- Personalized dashboard of subscribed services

**Event Discovery & Subscription:**

- Catalog of available event types from both CDH and TAS
- Filtering and detailed event descriptions
- Granular subscription options

**Webhook Configuration:**

- Endpoint URL validation
- Multiple endpoint support
- Testing tools
- Self-service credential management

**Developer Resources:**

- Comprehensive documentation
- Sample code and SDKs
- Integration guides

**Event Delivery Monitoring:**

- Delivery logs and status tracking
- Health dashboards
- Notification preferences

### 4.6 Standardization Requirements

**Event Schema Standardization:**

- Common schema format across domains
- Consistent field naming conventions
- Versioning and backward compatibility

**Security Standards:**

- Consistent authentication mechanisms
- Standardized HMAC implementation
- Unified security documentation

**Monitoring Standards:**

- OpenTelemetry instrumentation
- Standard performance metrics
- Unified logging format

## 5. Conclusion and Recommendations

This analysis reveals significant opportunities for leveraging existing shared components between the CDH and TAS solutions. By utilizing the existing Event Hub and Notification Hub, WEX can accelerate development, improve consistency, and reduce operational overhead across its platforms.

### Key Recommendations

1. **Standardize on Kafka (Event Hub) as the Central Messaging Platform:**
   - Use Kafka Aiven as the shared managed service across all event-driven solutions
   - Implement a centralized schema registry for event standardization
   - Develop shared Kafka client libraries and best practices

2. **Leverage the Notification Hub as a Shared Service:**
   - Integrate CDH's consumer notification requirements with the existing TAS Notification Hub
   - Develop the "UI Once" developer portal for self-service webhook management
   - Standardize on HMAC for webhook authentication across all outbound notifications

3. **Adopt Common Microservice Development Standards:**
   - Use the Creatio template for new .NET microservices across both platforms
   - Implement shared patterns like the outbox pattern for guaranteed message delivery
   - Standardize API design, error handling, and documentation

4. **Unify the Observability Stack:**
   - Adopt OpenTelemetry (OTEL) for instrumentation across all services
   - Create shared dashboards and alerting templates in Datadog/Grafana
   - Implement a common secure logging framework that enforces WEX standards

5. **Implement Shared Security Practices:**
   - Use Delinea for centralized secrets management across all services
   - Develop common security libraries for authentication, authorization, and encryption
   - Standardize infrastructure security configurations in cloud environments

The most immediate opportunities are for CDH to integrate with these existing shared services. Longer-term benefits will come from unified development standards, common security practices, and the "UI Once" developer portal that builds upon these established shared components.

By implementing these recommendations, WEX can build a more cohesive technical ecosystem where new capabilities can be developed more rapidly by leveraging existing shared components and established patterns.

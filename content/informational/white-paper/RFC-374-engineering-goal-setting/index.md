<!-- Parent: Informational -->
<!-- Parent: White Paper -->
<!-- Title: RFC-374 Engineering Goal Setting -->
# Engineering Goal Setting

```text
Author: <PERSON>
Title: Engineering Goal Setting
Publish Date: 2024-11-26
Category: Informational
Subtype: Whitepaper
```

This document contains opinionated guidance for engineers on setting annual performance goals. It outlines the relationship between projects and goals, the importance of measurable value, and how to articulate goals effectively.  Not all details or examples may be relevant to all teams or roles at WEX.

> <!-- Info -->
> Info
> This document is intended to help engineers set and achieve their annual performance goals effectively.
>
> For further assistance, please reach out to your manager or refer to [Coaching & Goal Setting](https://wexchange.wexinc.com/home/<USER>/content/6072472477630464/coaching-goal-setting) on wexchange.

## Key Concepts

![Use Cases](goal-concepts.png)

### Projects vs. Goals

- **Projects**: Specific tasks or initiatives tracked in tools like Jira or Azure Boards.
- **Goals**: Higher-level objectives that deliver measurable value. Goals can be personal or organizational and are tracked in Workday.

## Setting Goals

Goals should deliver value to:

- Yourself (e.g., personal skill development)
- The team (e.g., improving team velocity)
- The organization (e.g., drafting reference architectures)

Goals should follow the SMART criteria:

1. **Specific**: Clearly define what you want to achieve.
2. **Measurable**: Establish criteria for measuring progress and success.
3. **Achievable**: Set realistic goals that are attainable.
4. **Relevant**: Ensure the goals are pertinent to your role and objectives.
5. **Time-bound**: Set a deadline for achieving the goals.

### Personal Goals

Personal goals focus on your own development and contributions to the team. Examples include:

- Enhance personal skill development by obtaining at least one cloud certification, increasing proficiency in three specific tools (e.g., Docker, Kubernetes, Jenkins), and learning two new technologies (e.g., machine learning, blockchain) through online courses, practical projects, and workshops by the end of the year.
- Take at least 15 days of vacation throughout the year to avoid burnout and maintain productivity.
- Mentor at least two junior team members by providing regular feedback, conducting code reviews, and organizing monthly knowledge-sharing sessions to enhance their skills and contribute to the team's overall performance by the end of Q3.

### Organizational Goals

Organizational goals are set by your manager and align with broader team or company objectives. Examples include:

- Improve continuous delivery (CD) processes by reducing deployment time by 20% through automation and process optimization by the end of Q4.
- Increase code review efficiency by implementing a standardized review checklist and reducing review time by 15% by the end of Q2.
- Publish two reference architectures by Q2 to assist team members in understanding best practices and increasing velocity.

## Self-Evaluation

When completing the self-evaluation of your annual performance:

- Ensure your goals clearly state the value proposition and are measurable.
- Connect projects to goals, showing how each project contributes to achieving the goal.
- Use specific examples and metrics to quantify success.

## Conclusion

Setting effective performance goals involves understanding the distinction between projects and goals, focusing on measurable value, and articulating goals clearly. By following this guidance, engineers can set meaningful goals that contribute to personal growth, team success, and organizational value.

> <!-- Info -->
> Note
> This content was converted from a Google Meet transcript to a general guidance document using GitHub Copilot.  Suggested content was then manually adjusted for the resulting first draft.

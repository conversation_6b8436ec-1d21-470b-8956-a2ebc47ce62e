<!-- Parent: Informational -->
<!-- Parent: Instructions -->
<!-- Title: RFC-506 RFC Ratification Process -->
# RFC Ratification Process

```text
Author: <PERSON><PERSON><PERSON>
Publish Date: 2025-05-09
Category: Informational
Subtype: Instructions
```

## Overview

This document describes the process for ratifying RFC in the repository. As an iSARB board member, you can officially mark an RFC as ratified to indicate that it has been approved. Ratified RFCs are protected from further changes to maintain their integrity and ensure the content remains exactly as approved.

## Ratification Workflow

### How to Ratify an RFC

1. Navigate to the GitHub Actions tab in the repository <https://github.com/wexinc/arch-rfc-shared/actions>
2. Select the "Ratify RFC Content" workflow
3. Click "Run workflow"
4. Enter the required information:
   - **RFC number**: Just the number part of the RFC identifier (e.g., For `RFC-41` it should be`41`)
   - **ID Prefix**: The prefix used in the RFC identifiers (default: "RFC")
   - **Repository**: Specify a different repository if the RFC isn't in the current repository (default: "wexinc/arch-rfc-content")
   - **Content folder**: Specify the folder where RFCs are located in the repository (default: "content")
   - **Comment**: (Optional) Add any notes about the ratification
5. Click "Run workflow" to execute the ratification process

The workflow will:

- Search for files containing the RFC number in their header comment with the specified prefix (e.g., `Title: RFC-41 RFC Example`)
- If the document contains a `:draft:` tag, it will be replaced with the `:isarb:` marker
- If no `:draft:` tag exists, the `:isarb:` marker will be added after the title
- Commit the changes to the repository in the main branch

> <!-- Info -->
> Note
> Only users listed in the ISARB_RATIFIERS repository variable are authorized to ratify content. Unauthorized users will receive an error message.
> If you need to be added to the list of ratifiers, please contact the [repository administrator](mailto:<EMAIL>).

### Using Different RFC ID Formats

The workflow supports different RFC ID formats:

- If your RFC IDs use the format "RFC-41", use the default settings
- If your RFC IDs use a different prefix (e.g., "DOCID-41"), specify that prefix in the ID Prefix field
- The workflow handles both hyphenated (RFC-41) and non-hyphenated (RFC41) formats automatically

### Cross-Repository Ratification

The workflow supports ratifying RFCs located in different repositories:

- The default repository is set to "wexinc/arch-rfc-content"
- If the RFC is in a different repository, specify the full repository name (e.g., wexinc/arch-rfc-shared)
- If the RFCs are stored in a different folder than "content", specify the folder path in the Content folder field
- Ensure that the workflow has appropriate permissions to access and modify the target repository

## Example of a Ratified Document

A ratified document will contain a marker like this after the title:

```markdown
# Document Title

:isarb:

Content of the document...
```

### From Draft to Ratified

If an RFC was previously in draft status:

```markdown
# Document Title

:draft:

Content of the document...
```

After ratification, it will be updated to:

```markdown
# Document Title

:isarb:

Content of the document...
```

## Protection Mechanism

Once an RFC is ratified:

- The system automatically detects changes to ratified documents during pull requests
- Pull requests that attempt to modify ratified content will require manual approval
- A notification will be added to the PR indicating that the content is ratified and cannot be changed

### Exemption for Informational Content

Content in the informational category is exempt from ratification validation. This means:

- Informational content can still be marked as ratified for informational purposes
- However, changes to informational content will not be blocked even if it is ratified

## Best Practices

- Only ratify content that has been formally approved by the iSARB board
- Ensure the content is in its final state before ratification
- Document the rationale for ratification in the comment field for future reference
- Communicate ratification status to relevant stakeholders after completing the process

<!-- Parent: Informational -->
<!-- Parent: Instructions -->
<!-- Title: RFC-491 REST API Standards Audit using GitHub Copilot -->

<!-- Include: macros.md -->
# REST API Standards Audit using GitHub Copilot

```text
Author: <PERSON>
Publish Date: 2025-04-22
Category: Informational
Subtype: Instructions
```

## Overview

This document provides step-by-step instructions for using GitHub Copilot to perform adhoc compliance audits of REST API code repositories against WEX architectural standards. By leveraging GitHub Copilot's AI capabilities with structured prompts, architects and engineers can efficiently evaluate REST API implementations against established company standards without requiring specialized tools or extensive manual review.

:box:tip:Other Uses:While this document specifically addresses REST API audits, the same technique can be applied to audit any type of solution against markdown-based requirements stored in GitHub repositories. Consider adapting this approach for reviewing microservice architectures, security implementations, database designs, or other solution components against relevant organizational standards.:

---

:box:warning:Interpreting Results:The scan results should be treated as a valuable starting point but not as a comprehensive or authoritative assessment. Manual verification by architects or subject matter experts is still necessary for critical compliance requirements.:

## Prerequisites

Before beginning the REST API standards audit process, ensure you have:

- **GitHub Account with <PERSON><PERSON>lot Access**: Requires a valid GitHub account with [access to GitHub Copilot](https://wexinc.atlassian.net/wiki/spaces/EPS/pages/************/GitHub+Copilot+Project)
- **Repository Access**: Read access to the target REST API code repository

:box:warning:Repository Indexing:The first time a repository is used as context in GitHub Copilot Chat, that triggers a process where the repository is scanned and indexed into a vector database.  Basic straightforward questions may work initially, but you have to wait for the indexing to complete (20-40 minutes) and re-run the prompt before a complex prompt like the audit will respond correctly.:

### Value Proposition

Implementing this audit process offers several key benefits:

- **Consistency**: Ensures uniform evaluation of REST APIs across the organization
- **Efficiency**: Reduces manual review time from days to hours
- **Actionability**: Provides specific, prioritized recommendations for remediation
- **Documentation**: Creates standardized audit reports that can be shared with stakeholders
- **Education**: Helps engineering teams understand architectural standards through practical application

This audit process evaluates APIs against both the REST API Design Standard and Application Logging Standard, providing a comprehensive view of implementation quality and compliance.

<!-- Info -->
> Info
> While these manual steps provide a structured approach for conducting standards audits, the Architecture team is currently exploring the development of a re-usable GitHub App that could automate compliance audits against any published standard. This would potentially streamline the process for engineers and architects across the organization.

## Preparing Your Repository for Scanning

> This preparation is optional, **BUT** performing these steps can significantly improve the accuracy of the audit report for your REST API.

Make the following changes to the `README.md` in the root of your repository:

- Document all current known violations of standards and include any exception justification statements or remediation plans
- Document concerns that may not be observable from your code repository
  - (e.g. cloud architecture, upstream & downstream dependencies, transport security, etc)

Ensure the OpenAPI JSON definition for your REST API is published to a GitHub repository.

## Assessment Steps

1. Navigate to the [GitHub Copilot Immersive Chat](https://github.com/copilot)
2. Select `GPT-4o` as the language model
3. Attach your REST API GitHub Repository
4. **(Optional)** Attach your REST API's OpenAPI JSON definition (if it exists in a GitHub repository)
5. Attach RFC Standards and Audit Response Template
    - Repository: `wexinc/arch-rfc-content`
    - REST API Design Standard RFC ([source](https://github.com/wexinc/arch-rfc-content/blob/main/content/guardrails/design-specification/rest-api-design-standard/index.md)|[doc](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155342569626/RFC-489+REST+API+Design+Standard)): `content/guardrails/design-specification/rest-api-design-standard/index.md`
    - Application Logging Standard RFC ([source](https://github.com/wexinc/arch-rfc-content/blob/main/content/guardrails/design-specification/application-logging-standard/index.md)|[doc](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155343061285/RFC-490+Application+Logging+Standard)): `content/guardrails/design-specification/application-logging-standard/index.md`
    - Audit Response Template ([source](https://github.com/wexinc/arch-rfc-content/blob/main/templates/standards-audit.md)): `templates/standards-audit.md`
6. Submit the following prompt:

```text
You are a senior architect auditing a REST API for compliance with standard requirements.
Examine source code in repository and OpenAPI JSON definition (if supplied) for compliance to MUST and SHOULD requirements in: 

- Application Logging Standard
- REST API Design Standard

Standard services include:

- Splunk (Logging Platform)
- DataDog (APM/Observability Platform)

Determine a compliance score (1 to 5) for each standard using the scoring system below:

- Score 1: More than 5 violations of MUST requirements
- Score 2: Between 1 and 5 violations of MUST requirements
- Score 3: No violations of MUST requirements, Less than 5 violations of SHOULD requirements with most exceptions documented
- Score 4: No violations of MUST requirements, No undocumented violations of SHOULD requirements, advanced capabilities are not leveraged
- Score 5: No violations of MUST requirements, No undocumented violations of SHOULD requirements, advanced capabilities are leveraged

Additional requirements for the compliance score include:
- Business logic should be evaluated for compliance with REST API requirement `M-038`

Generate a markdown file containing the Compliance Audit Report for the audit.  The requirements for the report are:
- Response should conform to the Standards Audit Template `standards-audit.md`.
- Author of report should be: `WEX AI Auditor`
- The subtype of the report should be a placeholder of the organization responsible for the REST API source code repository
- Include violations citing specific examples with actionable recommendations prioritizing easy and impactful changes.
- Violations must reference the requirement identifiers that triggered the violation.
- Include up to 7 violations for each standard
- Include unsatisfied MAY requirements in recommendations when standard compliance score is between 3 and 5.

Respond back with the markdown file and a verbal summary of the results.
```

### Audit Workflow Diagram

```mermaid
flowchart TB
    subgraph "Preparation and Data Collection"
        direction TB
        A[Prepare Repository] --> B[Navigate to GitHub Copilot]
        B --> C[Select GPT-4o Model]
        C --> D[Attach REST API Repository]
        D --> E{OpenAPI Available?}
        E -->|Yes| F[Attach OpenAPI Definition]
        E -->|No| G[Skip OpenAPI Attachment]
        F & G --> H[Attach Standards Documents]
    end
    
    subgraph "Audit Execution and Remediation"
        direction TB
        H --> I[Attach Audit Template]
        I --> J[Submit Audit Prompt]
        J --> K[Review Compliance Report]
        K --> L[Implement Recommendations]
        L --> M[Re-audit Repository]
    end
    
    style A fill:#d4f1f9,stroke:#05a,stroke-width:2px
    style J fill:#ffe6cc,stroke:#d79b00,stroke-width:2px
    style K fill:#d5e8d4,stroke:#82b366,stroke-width:2px
    style L fill:#d5e8d4,stroke:#82b366,stroke-width:2px
```

## Next Steps

After receiving your audit report, consider the following actions:

### Remediating Violations

1. **Prioritize MUST Requirements**: Address any violations of MUST requirements first as these are mandatory for compliance.
2. **Document Exceptions**: For any SHOULD requirements that cannot be met, document justifications in your repository README.
3. **Create Remediation Plan**: Develop a timeline for addressing violations based on their impact and effort required.
4. **Rerun Audit**: After making changes, rerun the audit to verify improvements in compliance scores.
5. **Share Findings**: Publish the report as an RFC for your stakeholders to your [organization's design folder](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155174174942/Designs)

### Integration with Development Process

1. **Sprint Planning Integration**: Convert high-priority findings into technical debt stories for upcoming sprints
2. **Technical Debt Backlog**: Create a dedicated technical debt board in your project management tool to track standards compliance work
3. **Compliance Roadmap**: Develop a 3-6 month roadmap for achieving full compliance with MUST requirements and addressing SHOULD requirements
4. **Knowledge Sharing**: Schedule a team review session to discuss audit findings and ensure understanding of standards requirements
5. **Standards Feedback**: If you identify potential issues with the standards themselves, submit feedback through the RFC process

### Measuring Progress

Track the following metrics to demonstrate improvement in your API's standards compliance:

- **Compliance Score Trend**: Track increases in your compliance scores over time (target: Score 4+)
- **MUST Violation Reduction**: Measure the percentage reduction in MUST requirement violations (target: 100%)
- **Documentation Coverage**: Track percentage of remaining SHOULD requirement exceptions that are properly documented (target: 100%)
- **Technical Debt Reduction**: Monitor the closure rate of compliance-related stories in your backlog
- **Regression Prevention**: Implement standards compliance checks in your CI/CD pipeline to prevent new violations

### Additional Prompts for Further Analysis

Use these follow-up prompts with GitHub Copilot for deeper insights:

```text
Analyze the most severe REST API standard violations identified in the audit and provide specific code examples for how to remediate them.
```

```text
Generate unit tests that would verify compliance with the logging requirements identified as violations in the audit report.
```

```text
Review our error handling patterns and suggest improvements based on the REST API Design Standard requirements for consistent error responses.
```

```text
Analyze our API versioning approach and provide recommendations to align with the versioning requirements in the REST API Design Standard.
```

```text
Create a checklist of implementation tasks to address the top 5 priority violations identified in the audit report.
```

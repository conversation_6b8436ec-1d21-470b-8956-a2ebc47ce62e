<!-- Parent: Informational -->
<!-- Parent: Instructions -->
<!-- Title: RFC-364 Scoreable Survey Questions -->
# Scoreable Survey Questions

```text
Author: <PERSON>
Title: Scoreable Survey Questions
Publish Date: 2024-11-22
Category: Informational
Subtype: Instructions
```

Scoreable survey questions are designed to be evaluated on a predefined scale, allowing for objective measurement of responses. Unlike questions aimed at informational discovery, which focus on gathering qualitative data about the current state of a system, scoreable questions provide a quantifiable assessment of specific components or practices.

Drafting questions with scoring in mind offers several benefits:

- Reduces subjective bias by providing clear, predefined response options.
- Allows for precise and consistent scoring across different respondents.
- Facilitates meaningful insights through quantifiable data.
- Enhances the ability to track progress and identify areas for improvement over time.

By focusing on structured, scoreable questions, assessments become more reliable and actionable.

## Taxonomy

Vectors and components are the building blocks of a structured assessment.

### Vectors

Vectors represent the broad categories or dimensions that you want to assess. Each vector should capture a distinct aspect of the system or process being evaluated. For example, in a software development context, vectors might include code quality, team collaboration, and deployment frequency.

### Components

Components are the specific elements within each vector that you will measure. Each vector is broken down into multiple components to provide a more granular assessment. For instance, within the vector of code quality, components might include readability, maintainability, and test coverage.

By defining clear vectors and components, you can create a comprehensive and detailed assessment framework that allows for precise scoring and meaningful insights.

## Value Driven Assessments

Value driven assessment questions are designed to evaluate the maturity or quality of specific components within a vector by using a predefined scale. These questions provide respondents with a set of options that reflect different levels of maturity or quality, allowing for a more nuanced and objective assessment.

Question are scored using a scale (e.g., 1 to 5) with the levels reflecting the progression from a lower to higher level of maturity or quality. The scale progression should be based on one or more desired value propositions. The value propositions should be clearly defined and reflect a collective state representative of the progression from a lower to a higher level of maturity or quality. Respondents select the option that best describes their current state, and the responses are then used to calculate an average score for each component.

> <!-- Info -->
> Note
> By focusing on value propositions, value driven assessment questions help to minimize subjective bias and provide a clear, quantifiable measure of the assessed component's maturity or quality.

## Percentage Driven Assessments

Percentage driven assessment questions are designed to measure the extent to which certain practices or attributes are present within a system or process. These questions typically provide a list of options, and respondents are asked to select all that apply. The responses are then quantified to provide a score that reflects the percentage of applicable options chosen.

This type of assessment helps to objectively evaluate the prevalence or adoption of specific practices by calculating the proportion of selected options relative to the total available options. The resulting score offers a clear indication of how well the assessed component aligns with the desired practices or attributes.

Percentage driven questions can be scored using the formula: `([total selected] / [total available]) * [upper bound of scale]`

For example, if the scoring scale is 1 to 5 and a question lists 10 communication practices, a respondent selecting six of them would result in a score calculated as `(6/10) * 5 = 3`. This score indicates a moderate level of adoption of the listed practices.

> <!-- Info -->
> Note
> By using this method, you can quantify the extent to which the respondent's practices align with the prioritized options, providing a clear and objective measure of the assessed component.

## Example Questions

Here are some example questions to illustrate how to create scoreable survey questions.

> <!-- Info -->
> Info
> Examples of real-world questions drafted with this methodology can be found in [RFC-351 SaaS Maturity Vector](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154867007780/RFC-351+SaaS+Maturity+Vector).

### Value Driven Example

**Vector:** Code Quality  
**Component:** Readability

**Question:** How would you rate the readability of the codebase?

1. The code is very difficult to read and understand.
2. The code is somewhat difficult to read and understand.
3. The code is readable but could be improved.
4. The code is mostly readable and easy to understand.
5. The code is very readable and easy to understand.

**Scoring:** The score is directly associated with the selected option, with higher numbers indicating a higher score.

### Percentage Driven Example

**Vector:** Team Collaboration  
**Component:** Communication

**Question:** Which of the following communication practices does your team regularly use? (Select all that apply)

- Daily stand-up meetings
- Weekly planning sessions
- Code review discussions
- Pair programming
- Retrospective meetings

**Scoring:** If a respondent selects 3 out of 5 options, the score would be calculated as follows: `(3/5) * 5 = 3`

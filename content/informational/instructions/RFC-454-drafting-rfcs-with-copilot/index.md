<!-- Parent: Informational -->
<!-- Parent: Instructions -->
<!-- Title: RFC-454 Drafting RFCs with <PERSON><PERSON><PERSON> -->

<!-- Include: macros.md -->
# Drafting RFCs with Co<PERSON>lot

```text
Author: <PERSON> Jenkins
Title: Drafting RFCs with Copilot
Publish Date: 2025-06-17
Category: Informational
Subtype: Instructions
```

This document provides instructions for using GitHub Copilot to accelerate RFC authoring and updates.

> **Tip**
>
> - **Don't** accept AI-generated content without verification
> - Remove detailed implementation specifics that belong in other docs

## Prerequisites

- Latest version of Visual Studio Code installed
- All VS Code extensions updated to latest versions
- GitHub Copilot extension installed and configured
- Access to the RFC repository

:box:warning:Preview Functionality:These instructions rely on preview functionality in Visual Studio Code. The user experience is subject to change. If the instructions appear out-of-date, notify `<EMAIL>` and refer to the [official VS Code documentation](https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-prompt-file-in-chat) for re-usable prompts.:

### Workspace VS Code Settings

The workspace settings.json is tracked by version control to ensure the necessary IDE settings are enabled.

```json
{
    "chat.promptFiles": "true",
    "github.copilot.chat.codesearch.enabled": true
}
```

## Generating a New Canonical System Profile

1. Start a new Copilot edit session:
    - Select "Claude" as the language model if available
    - Select "Agent" mode
2. Submit the `new-canonical-profile` re-usable prompt along with a description of the system, for example: `/new-canonical-profile Create a new profile for {system name} which is used for {purpose}`
3. Copilot will generate the RFC using the canonical profile template
4. Review and validate the generated content:
   - Update the author field with the system owner
   - Verify accuracy of system description
   - Update onboarding instructions and user guide links
   - Add appropriate contact information

## Generating a New Reference Architecture

1. Start a new Copilot edit session:
    - Select "Claude" as the language model if available
    - Select "Agent" mode
2. Submit the `new-reference-architecture` re-usable prompt along with a description of the system, for example: `/new-reference-architecture Create a new reference architecture for {purpose} in [AWS | Azure] using components: {A, B, C, D}`
3. Copilot will generate the RFC using the reference architecture template
4. Review and validate the generated content:
   - Update the author field with the system owner
   - Create or update the git repository for the reference architecture
   - Verify accuracy of the content

## Generating a New High-level Solution Design

1. Start a new Copilot edit session:
    - Select "Claude" as the language model if available
    - Select "Agent" mode
2. Submit the `new-isarb-design-draft` re-usable prompt, for example: `/new-isarb-design-draft`
3. Answer series of questions about your design
4. Make adjustments to the design based on AI recommendations
5. When ready, prompt to proceed with generating the design document
6. Copilot will generate the RFC using the iSARB high-level solution design template
7. Review and validate the generated content:
   - Update the author field with the system owner
   - Verify accuracy of the content

> **Tip**
> You may respond to the design questions in different ways...
>
> - A direct answer
> - A large set of design qualities and decisions
> - Ask for a recommendation
> - Ask for a relevant design consultation contact
>
> You may also supply context with the initial re-usable prompt

## Generating a New Candidate Architecture Design

1. Start a new Copilot edit session:
    - Select "Claude" as the language model if available
    - Select "Agent" mode
2. Submit the `new-cad-design-draft` re-usable prompt, for example: `/new-cad-design-draft Create a candidate architecture for {system name} to address {business problem}`
3. Provide the following key inputs when prompted:
   - Business problem statement and context
   - Functional and non-functional requirements
   - Technology constraints and preferences
   - Integration points with existing systems
   - Performance and security considerations
4. Review and refine the initial architectural concepts proposed by Copilot
5. Copilot will generate the CAD document using the standard template
6. Review and validate the generated content:
   - Ensure architectural diagrams accurately represent the solution
   - Verify all requirements are addressed
   - Confirm feasibility of implementation approach
   - Check alignment with enterprise architecture standards

> **Tip**
>
> - Include clear success criteria for the architecture
> - Focus on business outcomes rather than just technical implementation
> - Explicitly address cross-cutting concerns (security, performance, observability)
> - Provide trade-off analysis for major architectural decisions
> - Use architecture diagrams at different levels of abstraction (conceptual, logical, physical)
> - Include a phased implementation approach if applicable

```mermaid
mindmap
  root((CAD Document<br>Key Elements))
    (Problem Statement)
      [Business Context]
      [Current Limitations]
      [Success Criteria]
    (Solution Architecture)
      [Component Diagram]
      [Interaction Flows]
      [Data Model]
    (Technology Selection)
      [Justification]
      [Alternatives Considered]
    (Implementation Approach)
      [Phases]
      [Dependencies]
      [Timeline]
    (Cross-cutting Concerns)
      [Security]
      [Performance]
      [Scalability]
      [Observability]
```

## Updating Existing RFCs

To update an existing RFC to match the latest template:

1. Open the RFC file
2. Start a new Copilot edit session
3. Add the appropriate markdown template to Copilot Edit context
4. Use the prompt: "update RFC [number] to use the [template name] markdown template"
5. Review suggested changes

### Moving Detailed Content

When Copilot suggests removing content, consider moving it to a different RFC document such as:

- Design specifications
- Informational instructions
- Reference architecture documentation
- Training materials

## Using Codesearch with Copilot Chat

To leverage the codebase for answering questions:

1. Start a new Copilot Chat session
2. Prefix your question with `#codebase`
3. Ask specific questions about standards, components, or requirements

Example:

```text
#codebase What components should I use for a new containerized microservices API in AWS?
```

The chat will search RFC content to provide guidance based on established standards and patterns.

### Best Practices

- Be specific in your questions
- Verify responses against authoritative sources
- Use chat results as guidance, not absolute truth
- Consider the context when evaluating suggestions

## Additional Notes

- Copilot is a tool to accelerate work, not replace human judgment
- Always review and validate generated content
- The technology and capabilities are rapidly evolving
- Suggest improvements to prompts and templates with pull requests

<!-- Parent: Informational -->
<!-- Parent: Instructions -->
<!-- Title: RFC77-How to use LaunchDarkly GitOps -->
#

```text
Author: <PERSON><PERSON>
Publish Date: 2024-08-23
Category: Informational
Subtype: General Information
```

## Table of Contents

- [Overview](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/How+to+use+LaunchDarkly+GitOps#Overview)
- [Folder Structure](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/How+to+use+LaunchDarkly+GitOps#Folder-Structure)
- [GitHub Actions Workflows](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/How+to+use+LaunchDarkly+GitOps#GitHub-Actions-Workflows)
- [Usage Guidance](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/How+to+use+LaunchDarkly+GitOps#Usage-Guidance)
  - [Flag Naming Conventions](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/How+to+use+LaunchDarkly+GitOps#Flag-Naming-Conventions)
  - [Create a New LD Project](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/How+to+use+LaunchDarkly+GitOps#Create-a-New-LD-Project)
  - [Create or update a Feature Flag](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/How+to+use+LaunchDarkly+GitOps#Create-or-update-a-Feature-Flag)
  - [Import a Feature Flag](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/How+to+use+LaunchDarkly+GitOps#Import-a-Feature-Flag)
  - [Delete a Feature Flag](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/How+to+use+LaunchDarkly+GitOps#Delete-a-Feature-Flag)
  - [Delete a Project](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/How+to+use+LaunchDarkly+GitOps#Delete-a-Project)
  - [Run 'Terraform Apply' Manually](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154618003539/How+to+use+LaunchDarkly+GitOps#Run-'Terraform-Apply'-Manually)

## Overview

A GitHub repository has been created for holding LaunchDarkly (LD) projects and feature flags. Once new projects and flags are created within the repository, a GitHub action builds them. If there import blocks specified the code, it will import the flags and generate pull requests for a reviewer to approve those changes. At the time of merging the code into Main, a merge action goes through each project containing changes and syncs up with LD automatically. GitHub actions employ PowerShell scripts and terraform configurations to perform these activities. Hence, there are specific steps that needs to be followed for the creation of projects and feature flags in LD.

## Folder Structure

Since the repository contains terraform scripts for all of the LD projects, a base folder, '\terraform' has been introduced. Each folder under this folder represents an LD project with the name proj-*[LD project key]*.

*[LD project key]* represents actual name of the project as defined within LD. The name is all lowercase characters. Example: proj-wex-benefits, where wex-benefits is the actual project key within LD.

> <!-- Note -->
> Warning
> While running automated tasks through GitHub actions workflows, PowerShell scripts interpret each LD project by reading the trailing characters after 'proj-' from the name of the project folder. Incorrectly named folders will result in the project not being able to sync up with LD.

A project folder contains fixed set of files each with a specific purpose. They are described in the following diagram:

![image image-20240605-193846.png](image-20240605-193846.png)

## GitHub Actions Workflows

Each GitHub action performs a specific set of tasks contributing to the overall automation. Following is a list of all actions with a high level summary of their intended purpose:

|**Action**|**Trigger Event**|**Activity Summary**|**Outcome**|
| :-: | :-: | :-: | :-: |
|Sync Changes from LD into LD repo|Run once daily on main branch|For each LD project in the repo, it looks for newly created flags in the LD system that are not in the repo and attempts to add those to the end of FeatureFlags.tf file.|It generates a pull request containing imported flags that can be merged into Main after approval.|
|Checkin Actions - Import Feature Flags|on code push|For each LD project in the repo, it looks for new entries into import.tf and attempts to pull those new flags. If successful, those are added to the end of Featureflags.tf file.|It generates a pull request containing imported flags that can be merged into the topic branch after approval.|
|Merge Actions - Terraform Apply|on merge|It will run Terraform Apply for each LD project in the repo that have changes.|This action will sync with LD and the changes are immediately reflected within all flag evaluation calls to LD.|
|PR Actions - Run Terraform Plan|on generation of pull request|For each LD project in the repo that has changes, it generates a plan|Plan output is generated. It can be seen in the (actions) run log.  Requires an approval for the changes to be merged.|
|* Manually Run Terraform Apply|manual, when necessary|For the specified LD project (input), it can generates a Terraform Plan by default. Additionally, if 'Run Terraform Apply' is set to true, it will perform Terraform Apply.|This can be run manually (as necessary) to inspect plan or to do a quick sync up with LD. The changes are immediately reflected within all flag evaluation calls to LD.|

> <!-- Note -->
> Warning
>
> - *GitHub actions workflow for manual invocation of Terraform Apply is provided as a way to fix occasional inconsistencies, if any, between the code and the LD system. Running this action from any branch would cause a sync up with LD with the changes in that branch that has not yet been merged to Main branch and hence it must be run only from Main branch. It's use is not intended to bypass the CI/CD workflows.

## Usage Guidance

Managing Feature Flags using terraform configurations provide all the benefits of Infrastructure-as-code and a high degree of automation. But, improper use would potentially put the LD project and feature flags in an inconsistent state. Following scenarios provide guidance on the best practices for creating and maintaining terraform scripts for LD projects and Feature Flags.

### Flag Naming Conventions

Flags naming is standardized across WEX. More details can be found [here](https://wexinc.atlassian.net/wiki/spaces/WF/pages/153828524161/Feature+Flags#MUST%3A-Conform-to-flag-naming-conventions).

### Create a New LD Project

1.Create a new folder under /terraform with proj-*[LD project key]*.

> <!-- Info -->
> Note
> *[LD project key]* needs to be replaced with the actual key of the project as defined within LD. Keep all lowercase characters.
>
> Example: proj-wex-benefits, where wex-benefits is the actual project key within LD.

2.Within the folder created under step1, create a file main.tf with the following contents and update as specified in the note below:

```Terraform Provider
terraform {
  required_providers {
  launchdarkly = {
    source  = "launchdarkly/launchdarkly"
    version = "2.12.2"
    }
  }
  
  backend "s3" {
    region               = "us-east-1"
    bucket               = "wexinc-fabric-ue1-tfstate"
    workspace_key_prefix = "launchdarkly-[LD project key]"
    key                  = "[LD project key]/prod/terraform.tfstate"
    dynamodb_table       = "wexinc-fabric-ue1-tflock"
    encrypt              = true
    assume_role = {
      role_arn = "arn:aws:iam::211125478244:role/dev-fabric-tf"
     }
  }
}

# Configure the LaunchDarkly provider

provider "launchdarkly" {
  access_token = var.launchdarkly_access_token
}
```

> <!-- Info -->
> Note
>
> 1. Set the version number for LaunchDarkly Provider appropriately.
> 2. Within the values for workspace_key_prefix and key, [LD project key] needs to be replaced with the actual key of the project as defined within LD.

```Example
Example:
    workspace_key_prefix = "launchdarkly-benefits"
    key                  = "launchdarkly-benefits.tfstate"
  ...
```

> <!-- Note -->
> Warning
> The values for workspace_key_prefix and key MUST be unique across all terraform projects. Violation of this rule will result with accidental destruction of resources within the LD system.

3.Create the file variables.tf with the following contents:

```Access Token
variable "launchdarkly_access_token" {
  description = "LaunchDarkly access token"
  type        = string
  sensitive   = true
}
```

4.Create the file Project.tf and create the resource definition for the LD project to be created. Following sample code may be used as a starting point as it includes the project definition along with a few environments:

> <!-- Info -->
> Note
> Environments may be added or removed based on the specific needs of projects involved

```Project
resource "launchdarkly_project" "[LD project key]" {
  key  = "[LD project key]"
  name = "[LD project name]"
  tags = ["terraform"]
  default_client_side_availability {
    using_environment_id = false
    using_mobile_key     = false
  }
  environments {
    color                = "d9f9eb"
    confirm_changes      = false
    default_track_events = false
    default_ttl          = 0
    key                  = "qa-env-2"
    name                 = "QA2"
    require_comments     = false
    secure_mode          = false
    tags                 = []
    approval_settings {
      can_apply_declined_changes = true
      can_review_own_request     = false
      min_num_approvals          = 1
      required                   = false
      required_approval_tags     = []
    }
  }
  environments {
    color                = "ffe1e9"
    confirm_changes      = false
    default_track_events = false
    default_ttl          = 0
    key                  = "test-env"
    name                 = "TestEnv"
    require_comments     = false
    secure_mode          = false
    tags                 = []
    approval_settings {
      can_apply_declined_changes = true
      can_review_own_request     = false
      min_num_approvals          = 1
      required                   = false
      required_approval_tags     = []
    }
  }
  environments {
    color                = "ffe1e9"
    confirm_changes      = false
    default_track_events = false
    default_ttl          = 0
    key                  = "staging"
    name                 = "Staging"
    require_comments     = false
    secure_mode          = false
    tags                 = []
    approval_settings {
      can_apply_declined_changes = true
      can_review_own_request     = false
      min_num_approvals          = 1
      required                   = false
      required_approval_tags     = []
    }
  }
  environments {
    color                = "d9f9eb"
    confirm_changes      = false
    default_track_events = false
    default_ttl          = 0
    key                  = "integration"
    name                 = "Integration"
    require_comments     = false
    secure_mode          = false
    tags                 = ["development"]
    approval_settings {
      can_apply_declined_changes = true
      can_review_own_request     = false
      min_num_approvals          = 1
      required                   = false
      required_approval_tags     = []
    }
  }
  environments {
    color                = "EBFF38"
    confirm_changes      = false
    default_track_events = false
    default_ttl          = 0
    key                  = "test"
    name                 = "QA"
    require_comments     = false
    secure_mode          = false
    tags                 = []
    approval_settings {
      can_apply_declined_changes = true
      can_review_own_request     = false
      min_num_approvals          = 1
      required                   = false
      required_approval_tags     = []
    }
  }
  environments {
    color                = "ff386b"
    confirm_changes      = true
    default_track_events = false
    default_ttl          = 0
    key                  = "production"
    name                 = "Production"
    require_comments     = true
    secure_mode          = false
    tags                 = []
    approval_settings {
      can_apply_declined_changes = true
      can_review_own_request     = false
      min_num_approvals          = 1
      required                   = false
      required_approval_tags     = []
    }
  }
}

```

5.Create an empty file called FeatureFlags.tf

6.Create an empty file called import.tf.

7.Push the code and merge to main.

### Create or update a Feature Flag

Feature flags are created within a given LD project and hence the project should be existing before the flag is created. Since Terraform config consists of all the definitions of the resources to be created, the definitions for feature flags can be included at the same time a definition for project is added to the repository. There are times where Terraform could not infer the flag's dependency on the project and it fails at the time it attempts to create the flag. In order to fix this situation, each definition of a flag needs to include 'depends_on' tag to ensure that the project is created before the creation of the flag.

1.Create a file FeatureFlags.tf if it's not already available under /terraform/proj-*[LD project key]*.

> <!-- Info -->
> Note
> *[LD project key]* is the key of the project defined within LD.

2.Create the resource definition for the flag to be created. Sample code is provided below towards the definition of a feature flag that can be used as a starting point:

```Feature Flag
resource "launchdarkly_feature_flag" "cycle123-Feature1" {
  depends_on     = "[LD project key]"
  archived       = false
  description    = "Test for Release Feature 1"
  key            = "cycle123-Feature1"
  maintainer_id  = null
  name           = "cycle123-Feature1"
  project_key    = "[LD project key]"
  tags           = []
  temporary      = true
  variation_type = "boolean"
  client_side_availability {
    using_environment_id = false
    using_mobile_key     = false
  }
  defaults {
    off_variation = 1
    on_variation  = 1
  }
  variations {
    description = null
    name        = "Option1"
    value       = "true"
  }
  variations {
    description = null
    name        = "Option2"
    value       = "false"
  }
}
```

3.Push the code and merge to main.

### Import a Feature Flag

If a Feature Flag has been created in the LD system without adding it to the code, it can be imported into the repo by the use of a simple definition of the flag in the import.tf file. The GitHub actions workflow will import the feature flag, move it into FeatureFlags.tf file, and generate a PR for review.

1.Create a file import.tf if it's not already available under /terraform/proj-*[LD project key]*.

> <!-- Info -->
> Note
> [LD project key] is the key of the project defined within LD.

2.Create the import definition for the flag to be imported. Sample code is provided below towards the definition of a feature flag that can be used as a starting point:

```Import
import {
    id = "[LD project key]/cycle123-Feature1"
    to = launchdarkly_feature_flag.cycle123-Feature1
    }

```

3.Push the code to the topic branch.

4.GitHub Actions will import the resource definition from LD and generate a pull request for merging the imported code into the topic branch. Approve the PR and merge into topic branch.

If the PR is not generated, most probably, there is a naming error for the feature flag. Check the logs generated by the 'Checkin Actions - Import Feature Flags' action.

5.Merge the code into Main.

### Delete a Feature Flag

1. Remove the entire definition of the flag from FreatureFlags.tf file

2. Push the code and merge to main.

### Delete a Project

This is a two step process.

First, delete all the contents within Project.tf, FeatureFlag.tf and Import.tf and push the code. After the GitHub Actions are run, the LD project gets deleted upon merging the code into Main.

Second, the entire terraform folder corresponding to the deleted project should be deleted and merged into Main.

### Run 'Terraform Apply' Manually

There could be times where a specific LD project gets into an inconsistent state. This is caused by one or more errors during a prior terraform apply. As long as the project and the entire set of feature flags are in the code repository, this GitHub Action can quickly sync up the LD system with those in the repository.

<!-- Parent: Informational -->
<!-- Parent: Instructions -->
<!-- Title: RFC409-Managing Apps using ArgoCD (Azure) -->

# Managing Apps using ArgoCD (Azure)

```text
Author: <PERSON><PERSON>
Publish Date: 2025-01-10
Category: Informational
Subtype: Instructions
```

## Overview

ArgoCD is WEX's standardized GitOps continuous delivery platform for Kubernetes applications. It automates the deployment and lifecycle management of applications by monitoring Git repositories and ensuring cluster states match the desired configuration defined in Git. WEX maintains separate ArgoCD instances for AWS and Azure environments, with this document focusing specifically on the Azure instance.

Key capabilities include:

- Automated deployment synchronization
- Git-based configuration management
- Drift detection and remediation
- Multi-cluster and multi-environment support
- Kubernetes resource visualization
- Role-based access control

## Value of Standardization

### Business Benefits

- **Reduced Deployment Risk**: Automated consistency checks ensure reliable deployments
- **Faster Recovery**: Automated rollbacks quickly restore stability when issues occur
- **Improved Compliance**: Git-based audit trails track all deployment changes
- **Reduced Operational Costs**: Automation reduces manual intervention needs
- **Accelerated Delivery**: Streamlined deployment process speeds time-to-market

### Technical Benefits

- **Consistent Deployment Model**: Same patterns work across all environments
- **Infrastructure as Code**: All configurations stored as version-controlled code
- **Self-Healing Applications**: Automated drift detection and correction
- **Simplified Multi-Environment Management**: App-of-Apps pattern streamlines management
- **Enhanced Security**: Reduced need for direct cluster access

## Architecture and Workflow

```mermaid
architecture-beta
    container "dev" [
        Developers
        Person: developers
        Technology: Code Changes
    ]
    
    container "git" [
        Git Repositories
        Container: git-repos
        Technology: Version Control
    ]
    
    container "argocd" [
        ArgoCD
        Container: argocd
        Technology: GitOps Controller
    ]
    
    container "k8s" [
        Azure AKS
        Container: kubernetes
        Technology: Container Orchestration
    ]
    
    container "apps" [
        Applications
        Container: apps
        Technology: Containerized Services
    ]
    
    rel "dev" -> "git": Push manifests
    rel "git" -> "argocd": Pull changes
    rel "argocd" -> "k8s": Apply changes
    rel "k8s" -> "apps": Run workloads
    rel "argocd" -> "argocd": Monitor & reconcile
```

## App of Apps Pattern

ArgoCD uses the **App of Apps** pattern for managing applications and their environments. This hierarchical approach allows centralized management of multiple applications across environments. The pattern works by:

1. Creating a root application that points to a Git repository containing child application definitions
2. Each child application represents a deployable component or environment
3. Changes to the child applications automatically propagate through the hierarchy

> <!-- Info -->
> Note
> Benefits applications are standardized to reside within a separate App-of-Apps [repository](https://github.com/wexinc/benefits-app-of-apps/tree/main/apps).

```mermaid
mindmap
    root((ArgoCD))
        App-of-Apps Root
            Application 1
                Dev Environment
                Stage Environment
                Production Environment
            Application 2
                Dev Environment
                Stage Environment
                Production Environment
```

Following is an image of apps running inside ArgoCD:

![image azureargocdrunningapps.png](./azureargocdrunningapps.png)

## Onboarding Process

### Prerequisites

- ☑️ Kubernetes application manifests or Helm charts
- ☑️ Access to WEX GitHub repositories
- ☑️ Azure subscription access
- ☑️ ArgoCD access credentials

### Step-by-Step Onboarding

```Step 1
Step 1: Ensure the helm chart and the values are setup appropriately for the application in the application repo.
```

Organize your application code repository with the following structure:

```text
my-application/
├── charts/                  # Helm charts
│   └── my-app/
│       ├── templates/
│       ├── values.yaml      # Default values
│       └── Chart.yaml       # Chart metadata
└── overlays/                # Environment-specific values
    ├── dev/
    │   └── values.yaml      # Dev values
    ├── stage/
    │   └── values.yaml      # Stage values
    └── prod/
        └── values.yaml      # Production values
```

```Step 2
Step 2: Create the application manifest using YAML in the App-of-Apps repository. For Architecture related applications, use [fabric-aks-configuration](https://github.com/wexinc/fabric-aks-configuration) repository for storing the manifests.
```

Following is a sample manifest for LaunchDarkly Relay Proxy:

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ld-relay-proxy
  namespace: argocd
  labels:
    name: ld-relay-proxy
spec:
  project: fabric-prod

  source:
    repoURL: https://github.com/wexinc/fabric-ld-relay-helm.git
    targetRevision: main
    path: charts/ld-relay-azure
    helm:
      valueFiles:
      - ./overlays/stage/values.yaml

  destination:
    server: https://kubernetes.default.svc
    namespace: ld-relay-proxy

  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
```

Following is a sample configuration of a running application within ArgoCD:

![image argocdsampleappconfig.png](./argocdsampleappconfig.png)

```Step 3
Step 3: Credentials to the repository containing the application manifest needs to be configured so that ArgoCD would be able to connect. This can be done by placing the keys in the KeyVault and adding entries to the yaml maintained for the Secrets Provider. For Architecture related applications, [kv-secretprovider.yaml](https://github.com/wexinc/fabric-aks-configuration/blob/main/helm/argocd/templates/kv-secretprovider.yaml) is the file used.
```

![image azureargocdrepositories.png](./azureargocdrepositories.png)

Once the application is correctly configured, ArgoCD automatically reads the manifest and proceed with the deployment.

![image argocdsamplerunningapp.png](./argocdsamplerunningapp.png)

## Best Practices

- Use declarative configurations stored in Git
- Do not use ArgoCD UI for creating applications and use the declarative configurations only.
- Implement proper RBAC controls
- Configure automated sync policies with appropriate sync windows
- Set up health checks for reliable monitoring
- Use Helm or Kustomize for templating
- Implement proper secret management
- Follow the App-of-Apps pattern for complex deployments

## Deployment Workflow

```mermaid
sequence
  box
  participant Dev as "Developer"
  participant Git as "Git Repository"
  participant Argo as "ArgoCD"
  participant K8s as "Kubernetes"
  end
  Dev->>Git: Push application manifests
  Git->>Argo: Webhook notification
  Argo->>Git: Pull changes
  Argo->>Argo: Validate manifests
  Argo->>K8s: Apply changes
  K8s-->>Argo: Status update
  Argo-->>Dev: Deployment status
```

## Support and Resources

### Primary Support Teams

- Platform Engineering Team (<<EMAIL>>)
- Cloud Engineering Team (<<EMAIL>>)

### Documentation Resources

- [WEX Fabric - AWS ArgoCD](https://docs.wexfabric.com/tools_and_platforms/argocd/)
- [Benefits - Wex Fabric Journey](https://wexinc.atlassian.net/wiki/spaces/WH/pages/154090340954/Wex+Fabric+Journey)
- [ArgoCD - App of Apps](https://argo-cd.readthedocs.io/en/stable/operator-manual/cluster-bootstrapping/#app-of-apps-pattern)
- [ArgoCD Official Documentation](https://argo-cd.readthedocs.io/)
- [GitOps Best Practices](https://wexinc.atlassian.net/wiki/spaces/Platform/GitOps)

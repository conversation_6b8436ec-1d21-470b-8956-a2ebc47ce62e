<!-- Parent: Informational -->
<!-- Parent: Instructions -->
<!-- Title: RFC1-Using RFCs -->
# Using RFCs

```text
Author: <PERSON><PERSON><PERSON>
Publish Date: 2024-05-15
Category: Informational
Subtype: Instructions
```

## Summary

This proposal introduces the use of RFCs in engineering. The "Request for Comments" system, used by several successful open-source groups, provides a framework for managing complex changes and their documentation. While not everything needs an RFC, as this can slow down a company, the proposal itself deserves an RFC to explain the rationale and reasoning.

## Motivation

Larger changes, while still reversible, require documentation explaining why the decisions were made and the technical climate at the time the idea was proposed. Historically, these documents reside in wikis or other documentation systems. These locations are often difficult to search, too far removed from the code, or rarely seen as a source of truth.

This solution proposes a documentation system that lives in code, uses plain text for authoring, and takes advantage of existing Git collaboration tools to edit, maintain, and build consensus on code ideas that impact all of engineering. It is based on the RFC process designed by the [IETF]("https://www.ietf.org/process/rfcs/") and later adapted for open-source projects. For feedback and comments, this RFC will also be added to Confluence.

## Guide Implementation

To ensure you've covered all major parts of an RFC, this RFC can serve as a reference model.

## Manually Creating a new RFC

1. **Determine Necessity**: Start creating a new RFC as soon as the work feels substantial and worth getting additional opinions on.
2. **Choose Category and Subcategory**: Before creating your RFC, think about the category and subcategory it would best fit into (e.g., Informational, Paved Roads, etc.). If you need more information about the categories and subcategories please check our read me file: <https://github.com/wexinc/arch-rfc-content/blob/main/README.md#content-categories-and-sub-types>.
3. **Branch the Repository**: Branch the repository: <https://github.com/wexinc/arch-rfc-content/> to begin your RFC.
4. **Copy the Template**: Copy a template file from the template folder: `RFCs/templates` to a new file in the `RFCs/[Category]/[Subcategory]` directory. Replace `[Category]` and `[Subcategory]` with the appropriate ones you chose in step 2
5. **Rename to `index.md`**: Rename the template file to `index.md` to ensure integration with Confluence works correctly.
6. **Edit the Template**: You'll be given a simple markdown document, which you can edit to start your proposal.
7. **Check the header**: The header of the file contains important information; make sure to fill in the fields appropriately.
8. **Push the changes to your branch**: Commiting the changes to your branch will also trigger a linting automation. This could even fix some minor fixable issues.
9. **Create PR to main**: Create a PR to get some insights from your peers on your RFC.
10. **Merge the PR back to main**: After a breaf check with your peers, we recommend you to merge your PR to main. This will trigger automations and will assign a number to your RFC. Also, your RFC will be posted to [Confluence RFC Space](https://wexinc.atlassian.net/wiki/spaces/ITRFC/overview?homepageId=154384499250).
11. **Share your RFC**: Now it time to share your ideas with Wexers. Share the link to your RFC with anyone that could help. If you need to update your RFC, just start from step 3.
12. **Finn integration**: Your new RFC will be indexed and available for users asking Finn in about 4 hours.

## Creating an RFC using Github Copilot

Follow instructions in [RFC-454](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/************/RFC-454+Drafting+RFCs+with+Copilot) to use AI to help you draft your RFCs

## Confluence integration

This repository uses a GitHub Actions workflow to automatically publish your RFC file to Confluence. Once you submit a pull request and it is mergemd into the ain branch, a Confluence page will be created with your RFC, and the process can begin. The Confluence pages will be created under a service account, not the user submitting the RFC.

## Getting Feedback

The beauty of having the Confluence integration is that you can have your RFC at an accessible URL, which you can share with others to get feedback. While we won't be prescriptive on who you should reach out to at this stage, testing your ideas and assumptions with several senior engineers can help work out any large issues. If you don't have great solutions yet, that's okay! Just mark the areas as still open and needing input. The RFC is a public document, so use this to your benefit. Share your RFC with everyone you feel is appropriate within Wex!

## Manage Feedback

As more eyes review your RFC, they'll share their valuable thoughts and opinions. As the creator of the RFC, it's generally good to address feedback and, if necessary, make changes to the RFC. If you have a meeting in which the RFC is discussed, be sure to capture the notes and share them in the RFC Confluence Page or via a Pull Request. After some time, the folks closest to the domain at the company will ask for all final comments to be submitted.

## Integration with Finn

Finn, our next-generation AI enterprise co-pilot, enhances your workplace experience with a conversational interface. It automatically indexes all RFCs published to Confluence, enabling quick access and searchability.

With Finn:

- **Efficient Information Retrieval**: Ask about specific RFCs or topics, and Finn will search Confluence for relevant information.
- **Intelligent Assistance**: Obtain concise summaries and answers to queries.
- **Time Savings**: Instant access to information saves valuable time.

Finn optimizes access, productivity, and efficiency across your engineering teams by leveraging its integration with Confluence.

## Drawbacks

Time: A commonly cited problem for an RFC process is the time investment asked of every participating engineer. This is in error, as the time required to create an RFC is ultimately spent in several other ways stemming from a lack of consensus or documentation. A proposal for an RFC has the highest ROI at the beginning of the process, not the end. In situations where there is already substantial buy-in from domain owners, the RFC is a lightweight process of recording what was already agreed upon. In situations where there is disagreement, then this time spent in the RFC process becomes time well spent.

Owners: Ownership is something difficult for engineering organizations to talk about. It requires the engineering organization to agree outright that one or more individuals are operating in the best interest of a domain and will invest the time ensuring their domain remains well maintained. However, these owners already exist; they are the folks engineers turn to with questions. To offset the obligation of being an owner, ownership can be added to an individual's engineering expectations and factored into any leverage/impact criteria for their role.

## Rationale / Alternatives

### Decision Bodies

RFCs are a better solution than forming an Architecture Council. The move to create an Architecture Council forms when there is no longer a way to create consensus around a topic. Unfortunately, AC meetings are often reduced to debating semantics and rarely making decisions regarding proposed changes to the domain. Another common problem with ACs is they often lack a holistic view of the domains in engineering. Without the right individuals representing the needs of the domain, the best decisions cannot be made. RFCs avoid this process. Each RFC naturally drives towards a decision (and a decision is required on every RFC). Every RFC can be handled asynchronously, avoiding the challenges associated with running a regular meeting with senior engineers.

RFCs are a more comprehensive solution than [Architecture Decision Records (ADRs)](https://www.thoughtworks.com/radar/techniques/lightweight-architecture-decision-records) and [whydocs](https://web.archive.org/web/20161119073912/https://medium.com/@jakob/a-better-framework-for-status-5c3dde887aa5). While ADRs and whydocs can effectively capture decisions being made during the development phase of software, they fail to capture ideas and intent. Why we **don't** do things is equally important to why we **do**, necessitating a more complete process.

### Documentation Systems

RFCs are superior to wikis. While wikis often have better search facilities, wiki pages do not easily foster the discussion needed around major engineering topics. Wikis lie outside of an engineer's normal workflow and tools, making them less visible to the engineering organization. RFCs are a naturally better fit for the engineering workflow, leveraging GitHub and its native review and discussion tools. Additionally, we have an integration that automatically publishes RFCs to Confluence, further enhancing visibility and collaboration.

RFCs are a better alternative than document cloud options. While document clouds have better discussion tools than wikis, the ability to locate documents is restricted to the search facilities and sharing permissions available in the document cloud. For example, items shared with a limited subset of engineers are unlikely to receive holistic review. Further, when the document author leaves the company, the decisions risk being lost unless the owner is migrated. RFCs avoid this by living as code in a git repository, owned by the engineering organization as a whole.

## Unresolved Questions

Unresolved Questions are marked inline in the following format:

> **Open Question:** topic

These questions remain open and require either textual amendment to define the operating rules of your unique RFC process, or a supplemental RFC proposing adaptations that build upon this idea.

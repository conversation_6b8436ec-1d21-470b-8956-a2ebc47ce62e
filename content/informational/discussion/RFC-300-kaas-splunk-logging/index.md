<!-- Parent: Informational -->
<!-- Parent: Discussion -->
<!-- Title: RFC-300 KaaS: Splunk Logging -->
# Kaas: Splunk Logging

```text
Author: <PERSON>
Publish Date: 2024-10-16
Category: Informational
Subtype: General Information
```

## Related Documentation

* <https://wexinc.atlassian.net/wiki/spaces/AEAE/pages/************/RFC+Splunk+Logging+of+Kafka+using+HTTP+Event+Collector+HEC>

## Splunk Learnings

WEX got security approval to remove the IP allow list on Splunk Cloud, shortly after we got Aiven logs routing through Core (HF).  Now <PERSON>ven can reach Splunk directly over the internet, *if* <PERSON>ven can whitelist the traffic outbound [currently not possible unless we receive IPs/CIDRs for Splunk], and the monitoring team provides us a new HEC token [DONE].  The current HF implementation will still work but we could save a few hops.

IPs/CIDRs: Splunk doesn’t publish that and they change too frequently, so guidance is to do nslookup on fqdn.  No proxy or forwarding internally for Datadog, that is straight to the internet, and they publish their ip's - <https://ip-ranges.datadoghq.com/>

<PERSON><PERSON> does have the ability to route through TGW to Core Services, and out to Splunk Cloud via Heavy Forwarder.  That is a single (private) IP address from the Monitoring Team, and would align with the HEC.  Firewall rules need to allow it [DONE?  We already allow inbound 443 but HF security group might need additions].

In the new TGW VPC's we can use the private ip which is the Heavy Forwarder in Core.  in the old PL VPC, Aiven hits a public ALB which resolves to the same Forwarder.  Note: all roads lead to 1 Heavy Forwarder IP in core services us-east-1, and all core services regions can hit it.

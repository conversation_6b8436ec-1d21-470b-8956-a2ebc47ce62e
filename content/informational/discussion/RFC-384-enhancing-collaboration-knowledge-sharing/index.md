<!-- Parent: Informational -->
<!-- Parent: Discussion -->
<!-- Title: RFC-384 Enhancing Collaboration and Knowledge Sharing -->
# Enhancing Collaboration and Knowledge Sharing

```text
Author: <PERSON><PERSON><PERSON>
Publish Date: 2024-12-17
Category: Informational
Subtype: Discussion
```

## 1. Introduction

This RFC proposes a revitalized approach to collaboration and knowledge sharing within WEX. We aim to address the challenges of current initiatives and foster a more engaged and effective exchange of knowledge and best practices.

## 2. Problem Statement

Currently, Wex faces challenges in fostering a robust knowledge-sharing culture. This is evidenced by:

* Limited engagement and ownership in existing knowledge-sharing initiatives.
* Difficulties in identifying and disseminating relevant content.
* A perceived lack of awareness and understanding of enterprise standards among different teams.

### 3. Proposed Solution: A Collaborative Ecosystem

This RFC proposes a multi-faceted approach to address these challenges:

#### 3.1 Distinct Collaboration Channels

Make a clear separation about the informal and formal reviews.

* **Informal Reviews (Design Discovery):** Explore ways to facilitate ad-hoc cross-functional consultations in a fluid and dynamic manner. This could involve:
  * Office hours
  * Brain storm sessions  

* **Formal Reviews (Design Approvals):** Reform the SARB (Solution Architecture Review Board) process to ensure clarity, efficiency, and adherence to architectural standards. This might include:
  * Use the RFC platform and the ratification feature to formal needed reviews
  * Clarifying submission guidelines and documentation requirements.
  * Defining clear timelines for review and feedback.
  * Streamlining the decision-making process.

* **Walkthroughs (Demos):** Have sessions to showcase new technologies, patterns, and best practices.
  * Scheduling regular walkthrough sessions clearly communicating the topics and target audience for each session.
  * Recording and archiving sessions for later access and reference.
  * Create RFC for each walkthrough

#### 3.2 Knowledge Sharing Focus Areas

* **Coordination:** Ensure alignment with TISO (Technical Information Sharing Office) activities.
  * Schedule recurrent content alignment sessions. Possible topics:
    * Cup o Tech
    * Minimum Knowledge Inventory presentations
    * IE Transformation Case Studies
    * Broaden Audience for Walkthroughs
    * Walkthroughs vs Cup o Tech  

* **Expand Knowledge Resources:**
  * Continue and expand Minimum Knowledge Inventory presentations.
  * Develop and share WEX Transformation Case Studies, documenting them in a knowledge base or shared repository.
  * Document all walkthrough topics as RFCs for future reference.

* **Walkthroughs:**
  * **Broaden Audience:**  All IE Coaches and IE Tech Leads should attend or have representation present to encourage active participation.
  * **Topics:**
    * **A walkthrough of the IT and Digital orgs with emphasis on scopes of responsibilities and contact channels.**
    * Patterns
    * Practices
    * Reference architectures
    * Tools

  * **Solicit Topics:**
    * New RFCs published.
    * RFCs with most recent comments.
    * Most viewed RFCs in the last month

### 4. Metrics/Goals

* Increase active participation in knowledge-sharing initiatives (e.g., workshops, forums, presentations) by 20% for Q2 2025
  * We can measure this using attendance data from Q1
  
* Set a goal for clarity and effectiveness of knowledge-sharing initiatives. We can measure this with surveys about user satisfaction, send an initial one on Q1 and repeat it in Q2

* Increase documented and shared knowledge resourced by 30% for Q2 2025.
  * We can measure this using data (RFC?) from Q1

<!-- Parent: Informational -->
<!-- Parent: Discussion -->
<!-- Title: RFC-142 KaaS: Schema Compatibility Standards -->
# KaaS: Schema Compatibility Standards

```text
Author: <PERSON>
Title: KaaS: Schema Compatibility Standards
Group: KaaS Engineering
Publish Date: 2024-08-15
Category: informational
Subtype: discussion
```

## Abstract

The following memoizes the discussion within the eventing_working_group regarding [Schema Compatibility Standards](https://chat.google.com/room/AAAAj3c1bbI/MjpiUQPhB9U/MjpiUQPhB9U?cls=10).

Credit should be given to <PERSON> for all thoughts and ideas here.

## Summary

The following is supporting information to establish a standard around schema compatibility for non-raw topics. Ultimately,
this document begins to build a case to standardize on non-raw topics which are either "FULL_TRANSITIVE" or "FORWARD_TRANSITIVE"
compatibility modes.

## Background

To establish a foundational understanding of what compatibility modes should be supported and when, we first need to establish a common understanding of the different types of topics that we support.

### Topic Types

There are three topic types defined: "Canonical", "Application", "Raw".  For more on this, see [fabric-eventing-standards](https://github.com/wexinc/fabric-standards-eventing/blob/main/spec/1_eventing.md#topic-types)

#### What are some examples of Raw Topics?

- something coming from CDC/Debezium connector that could change and the tool manages the schema itself
- data platform snowflake connectors that take any data from a topic
- prototyping or exploratory topics

### Non-Raw Topics (Canonical and Application)

#### What compatibility types are available?

![compatibility_types](./compatibility_types.png)

[Source](https://docs.confluent.io/platform/current/schema-registry/fundamentals/schema-evolution.html#compatibility-types)

#### Should non-raw topics support "NONE"?

One use case for "NONE" is for initial development when you might adjust a schema as the development evolves and you
understand the domain/solution more.  We've established that this activity is better suited for development and
prototying activities. Therefore, we must not support "NONE" compatibility mode for non-raw topics.

#### Should non-raw topics support "BACKWARDS" or "BACKWARDS_TRANSITIVE" compatibility?

Backwards compatibility is useful when consumers are upgraded before producers.

#### Should non-raw topics support "FORWARD" or "FORWARD_TRANSITIVE" compatibility?

Forwards compatibility is useful when producers are upgraded before consumers.

In practice, some teams have found that it is the producer that owns the schema.  As the producer evolves, it makes sense
that the schema would evolve as well.

#### Should non-raw topics support "FULL" or "FULL_TRANSITIVE" compatibility?

Either the producer or the consumer can be upgraded first. This is the most flexible compatibility mode, however
it is also the most strict.

#### Should we require TRANSITIVE types or allow either?

Using the non-transitive types would mean that producers need to be in communication with consumers (distributed teams
across timezones, reporting lines, etc) to make sure they are up-to-date with the most recent schema version before you
update, in order to be confident of not causing any compatibility issues.

A benefit to using TRANSITIVE types is that new producers or consumers that come along later can consume all messages on
the topic for that given schema.

TRANSITIVE is important in an enterprise standard - it allows for consumers to evolve at a different pace to the
producer, otherwise the producer would need to make sure the consumer comes up to the most recent version to prevent
potential of breaking changes.

#### "Broken Schema"

A "Broken" schema is a schema that won't be able to be brought back into it's compatibility mode.

## Open Questions

- How do we handle data/analysis processing layers backing snowflake connectors that take any data from a topic?
- How do we support manual creation of "in development" schemas?
- What other reasons why "NONE" compatibility mode should be supported?

## Resources

- <https://github.com/wexinc/fabric-standards-eventing/blob/main/spec/1_eventing.md#topic-types>
- <https://docs.confluent.io/platform/current/schema-registry/fundamentals/schema-evolution.html#compatibility-types>

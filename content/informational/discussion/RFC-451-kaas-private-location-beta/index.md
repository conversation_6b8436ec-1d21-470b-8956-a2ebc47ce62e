<!-- Parent: Informational -->
<!-- Parent: Discussion -->
<!-- Title: RFC-142 KaaS: Private Location Beta -->
# KaaS: Private Location Beta

```text
Author: <PERSON>
Title: KaaS: Private Location Beta
Group: KaaS Engineering
Publish Date: 2024-08-15
Category: informational
Subtype: discussion
```

## Abstract

The following is a proposal for a v2 iteration of the KaaS private locations feature. Private Locations are a way to
schedule synthetic tests from various locations within the WEX private network. KaaS uses private locations to monitor
existence and health of Aiven hosted services connected to WEX owned VPCs.

## Summary

Private locations can be extended into Azure cloud with some rework of the original design.  This proposal
outlines the changes needed to support Azure cloud regions as a private locations.  

### Limitations of the current design

The current design has a couple limitations of note:

* Couples the private location configuration (2) provisioning with the tcp checks provisioning (5), which are not directly related.

* Decouples the private location configuration provisioning (2) from the utilization of the configuration (7).

* When using the terraform AWS provider, the private location module doesn't have an option to specify the region for the
ecs clusters used to host the private locations. [^1]

* Duplicates the private location configuration in the terraform state as well as in vault.

## Background

The original RFC/design can be found documented here: <https://wexinc.atlassian.net/wiki/spaces/KAAS/pages/154828472618/DataDog+Health+Checks+Infrastructure>

The current design and implementation uses the following process to persist private locations:

1a User creates an Aiven project configuration file specifying services to provision

1b User sets healthchecks_enabled to true in the project configuration file

2 Private location module invoked

3 Private location module persists private location config

4 Private location config is persisted to vault

5 Service configurations are passed to the TCP module for provisioning

6 A periodic job invokes the containerized golang code which persists a polling location

7 The polling location runs any configured health checks.

![private_location_current.png](private_location_current.png)
[Source](https://app.diagrams.net/#G1RUZ8HAaw1qnulU-e_m4zNbzY8C_albh_#%7B%22pageId%22%3A%22TQdCoNh7Se2MV9oSXFSr%22%7D)

## Proposal

The following proposal outlines the changes needed to support Azure cloud regions as a private locations.

### Design Goals

This design aims to address the challenges of the current design by:

* decoupling the private location configuration provisioning from the provisioning of the tcp checks.

* coupling the private location configuration provisioning with the utilization of the configuration.

* reduces duplication of the private location configuration in the terraform state by avoiding vault storage.

### Outline

A new module, private_locations, will be created to manage the instantiation of private locations.

1) Private locations will need to include an azure implementation.  

2) The output from this module will be the private location ids which have been persisted.

Using the outputs from the private_locations module, we can then supply the private location ids to the altered projects module
to accept the private location ids as an input.

__Note__: As an aside, its believed there could be a parallel implementation for aws private locations, if the limitations could be bridged. [^2]

![private_location_proposal.png](private_location_proposal.png)
[Source](https://app.diagrams.net/#G1RUZ8HAaw1qnulU-e_m4zNbzY8C_albh_#%7B%22pageId%22%3A%22TQdCoNh7Se2MV9oSXFSr%22%7D)

A semi-functional prototype for this approach can be found in the PR here: <https://github.com/wexinc/kaas-infrastructure-pilot/pull/71>

## Resources

[^1]: <https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecs_cluster>
[^2]: <https://github.com/terraform-aws-modules/terraform-aws-ecs/blob/v5.12.0/examples/complete/main.tf#L1-L3>

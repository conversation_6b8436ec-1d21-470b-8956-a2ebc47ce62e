<!-- Parent: Informational -->
<!-- Parent: Discussion -->
<!-- Title: RFC-302 KaaS: Subject Name Strategy Standards -->
# KaaS: Subject Name Strategy Standards

```text
Author: <PERSON>
Title: KaaS: Subject Name Strategy Standards
Group: KaaS Engineering
Publish Date: 2024-09-10
Category: informational
Subtype: discussion
```

## Abstract

There is a need for a more robust and flexible approach to managing schemas and topics in Kafka. This document outlines the three schema naming strategies in Kafka and
supports the proposal to use the `RecordNameStrategy` as the default strategy for schema naming in Kafka.

## Overview

The three schema naming strategies in Kafka are:

| Strategy                | Description                                                                           |
|-------------------------|---------------------------------------------------------------------------------------|
| TopicNameStrategy       | The default strategy that limits a topic to one record type.                          |
| RecordNameStrategy      | Uses the record's fully qualified class name to create the subject name.              |
| TopicRecordNameStrategy | A combination of the first two strategies that scopes the schema to a specific topic. |

### TopicNameStrategy

![TopicNameStrategy.png](TopicNameStrategy.png)

TopicNameStrategy is the default setting. If `auto.register.schema` is set to true, schemas that are automatically registered by producers will be registered under topic-name-(key or value).

When you register a schema, you provide the topic name appended with key or value, e.g. topicA-value.

Using TopicNameStrategy effectively limits the topic to one record type, since all records in the topic must adhere to the same schema. Trying to register a schema for a different type would break the compatibility checks.

### RecordNameStrategy

![RecordNameStrategy.png](RecordNameStrategy.png)

To get around the limitation of one record type for a given topic, we can use RecordNameStrategy. It creates the subject name using the fully qualified name of the record, e.g. `{namespace}.{name}`.

RecordNameStrategy allows for different schemas in a topic since the individual records only need to comply with a schema that has the subject name that matches its fully qualified name.

But you have to use the same schema across all the topics in a cluster for that particular record type, since there’s no way to tell which topic the record belongs to.

### TopicRecordNameStrategy

![TopicRecordNameStrategy.png](TopicRecordNameStrategy.png)

TopicRecordNameStrategy is a combination of the first two strategies.

Subject names are created using [topic name]-[fully qualified record name].

Since you’ve now scoped the schema to a particular topic you have multiple schemas that will evolve independently.

## Proposal

1) `Raw` topics and participating schemas can use whatever strategy is most appropriate for the given domain and use case.
2) For `Application` and `Canonical` topic schemas, we propose using the `RecordNameStrategy` as the default strategy for schema naming in Kafka.

### Rationale

| Strategy                | Pros                                                                       | Cons                                                                                                                                                                                                    | Example                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
|-------------------------|----------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| TopicNameStrategy       | This strategy is good when grouping messages by topic name is appropriate. | The need to register the schema under multiple subjects when using mirror maker.   Inability to make a breaking change to a schema without creating a new topic, therefore the proliferation of topics. | When mirror maker is part of the solution and you use TopicNameStrategy, the schema needs to be registered under 3 subjects and kept in sync across all 3. "{topic-name}-value", "source-ue1.{topic-name}-value", "source-uw2.{topic-name}-value". In a distributed architecture, if we need to make a breaking change to a schema, we can't roll a "V2" schema, and do an incremental cutover on that topic. We would have to create a topic and cutover to that. |
| TopicRecordNameStrategy | This strategy allows a topic to have different schemas.               | Introduce multiple version histories for a record (tied to the topic it is being produced in).                                                                                                          | If you have a topic that sends messages to third party clients (or tenants) and you split the tenant by topic, this would allow the event schema to evolve as required for the tenant.                                                                                                                                                                                                                                                                             |
| RecordNameStrategy      | This strategy allows a topic to have different schemas.                    |                                                                                                                                                                                                         |                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |

### What about MirrorMaker?

MirrorMaker copies byte to byte from one topic to another.

## Open Questions

n/a

## Resources

* [Eventing Working Group Discussion](https://mail.google.com/chat/u/0/#chat/space/AAAAj3c1bbI)
* [Understanding Schema Subjects](https://developer.confluent.io/courses/schema-registry/schema-subjects/)

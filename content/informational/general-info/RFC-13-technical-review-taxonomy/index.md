<!-- Parent: Informational -->
<!-- Parent: General Information -->
<!-- Title: RFC13-Technical Review Taxonomy -->
# Technical Review Taxonomy

```text
Author: <PERSON>
Title: Technical Review Taxonomy
Publish Date: 2024-08-19
Category: Informational
Subtype: General Information
```

This document outlines a standardized framework for organizing the concepts related to technical reviews.  This document is just one possible way of talking about technical reviews.  RFCs that follow the framework described here should explicitly reference this document.

> <!-- Info -->
> Info
> Refer to [RFC15-Design Collaboration](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593984673/RFC15-Design+Collaboration) for detailed practice information.

## Formal Reviews

Formal reviews are required evaluations by a governing body that must be completed successfully when specific conditions exist before a solution can be released.

A governing body may be a representative group of qualified technical leaders or an organizational team with governance responsibilities of a particular area.

### Governance Design Review

> A **high-level** independent evaluation of a solution described in a technical document and used as a gating condition for delivery

- should occur after the design consultations are completed and public commentary starts
- intended to ensure all applicable WEX standards, policies, and hard requirements are met

> <!-- Info -->
> Info
> Integrated Solutions and Architecture Review Board (iSARB) reviews and approves solution designs for adherence to WEX IT policy and alignment to the WEX Technology Strategy. This includes the ratification and adherence to standard tools, services, and reference architectures which enable WEX to fulfill its mission and vision
>
> Refer to [RFC12-Formal Review Process](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593689766) and the [iSARB Home](https://wexchange.wexinc.com/home/<USER>

### Security Inspection

> A **detailed** evaluation by security experts of one or more security components in a solution

- may be originated by a team introducing a change
- may be initiated by Security and Solution Architects who audit work that a team has prioritized for delivery

> <!-- Info -->
> Info
> For more information, refer to [When to Engage Security Architecture for a Design Review](https://wexinc.atlassian.net/wiki/spaces/ESA/pages/154290520067)

## Informal Reviews

Informal reviews are initiated based on subjective analysis and are used to provide feedback, share knowledge, and identify potential issues early in the development process.

### Design Consultations

> Quick, informal, and ad-hoc technical reviews focused on a portion of a solution design. These types of engagements should occur early in the design phase during discovery.

Design consultations are NOT a delivery gate. They are a collaborative effort for the formulation and refinement of a complete high-level design that incorporates both functional and non-functional concerns.

Design consultations may be needed with a variety of coaches depending on what changes are being delivered with a solution.

The current list of coaches available for design consultations can be found in [RFC-420 Design Consultation Contacts](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/155035730254/RFC-420+Design+Consultation+Contacts).

### Technical Walkthroughs

> Structured sessions where a solution or a component of a solution is demonstrated to stakeholders. The primary goals of these sessions are to share knowledge and gather feedback for further enhancement.

The key goals of technical walkthroughs are:

- **Knowledge Sharing**: Walkthroughs provide an opportunity for team members to gain a deeper understanding of the solution, fostering a culture of continuous learning and collaboration.
- **Feedback Collection**: By presenting the solution to a diverse group of stakeholders, teams can gather valuable insights and suggestions that can lead to future improvements.

<!-- Parent: Informational -->
<!-- Parent: General Information -->
<!-- Title: RFC-471 Repository meta-data guidelines -->

# Repository meta-data guidelines

```text
Author: <PERSON>
Publish Date: 2025-13-03
Category: Informational
Subtype: General Information
```

There are primarily two ways to add meta-data to GitHub repositories: topics and custom properties.  This document provides guidelines regarding which medium to use for different types of meta-data that may be associated with GitHub repositories.

## Custom Properties

The general rule is meta-data that is required across all WEX repositories will be added as a custom property.  Although in most cases repository users will be able to set the values of the custom properties, the creation of custom properties is controlled at the organization level by organization admins.  

**Examples:**

- fabricId
- Line of Business
- Test/PoC code

Finally, to the greatest extent possible, we will leverage Fabric data to populate these values.  This, however, will not always be possible, so development teams should take it upon themselves to set the values appropriately and, if needed, reach out to the Fabric team for the creation of a fabricId.

## Topics

Topics, on the other hand, may be created by repository admins.  These should be used for meta-data that may be more specific to a particular team's or products' way of working.

**Examples:**

- Team name
- framework
- application layer (such as frontend or backend)

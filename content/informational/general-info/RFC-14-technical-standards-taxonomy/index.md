<!-- Parent: Informational -->
<!-- Parent: General Information -->
<!-- Title: RFC14-Technical Standards Taxonomy -->
# Technical Standards Taxonomy

```text
Author: <PERSON>
Title: Technical Standards Taxonomy
Publish Date: 2024-08-19
Category: Informational
Subtype: General Information
```

This document outlines a standardized framework for organizing concepts related to technical standards. It provides one possible way of discussing technical standards. RFCs that follow the framework described here should explicitly reference this document.

`Technical Standards` are guidelines, specifications, or criteria that define how a particular technology or process should be implemented. They provide a common framework for developers, engineers, and organizations to ensure compatibility, interoperability, and quality in their products or services.

A `Governing Body` may be a representative group of qualified technical leaders or an organizational team with governance responsibilities in a particular area.

## Adoption

Standards must be adopted by a governing body and should be described in an authoritative technical document. Technical documentation for standards must include:

- Governing body that approved the standard
- Approval date of the standard
- Instructions for adopting the standard

The documentation should include (when applicable):

- Exception process when enforcement level is `MUST`, `MUST NOT`, `SHOULD`, or `SHOULD NOT`
- Retirement date of the standard if it is no longer followed
- Links to extensions or replacements of the standard as they are adopted
- Links to applicable policies in PolicyTech

> <!-- Info -->
> Info
> The Integrated Solutions and Architecture Review Board (iSARB) reviews and approves solution designs for adherence to WEX IT policy and alignment with the WEX Technology Strategy. This includes the ratification and adherence to standard tools, services, and reference architectures which enable WEX to fulfill its mission and vision.
>
> Refer to [RFC12-Formal Review Process](https://wexinc.atlassian.net/wiki/spaces/ITRFC/pages/154593689766) and the [iSARB Home](https://wexchange.wexinc.com/home/<USER>

## Categorization

Technical standards vary widely by when they are used, how, and by whom. Technical standards are categorized as follows:

### Tools

Tools are software applications or platforms **used by WEX employees** for design, delivery, and support tasks. They are essential for enhancing productivity, ensuring accuracy, and streamlining workflows.

> <!-- Info -->
> Note
> Tools should have a Canonical System Profile published.

**Examples:**

- **JIRA**: A project management tool used for tracking issues, bugs, and project progress.
- **Confluence**: A collaboration tool used for documentation and knowledge sharing.
- **GitHub Enterprise**: A source-code management solution using Git.
- **Visual Studio Code**: A source-code editor used for writing and debugging code.

### Services

Services are backend applications or systems that provide functionality to other applications or systems. They are **runtime dependencies** for WEX products, enabling interoperability, scalability, and reliability in software ecosystems.

Applications that fit both definitions of tools and services should be considered as services.

> <!-- Info -->
> Note
> Services should have a Canonical System Profile published.

**Examples:**

- **Okta**: An identity and access management service for secure user authentication.
- **Imperva**: A cybersecurity service providing protection against web attacks and data breaches and POP services.
- **AWS S3**: A scalable object storage service for data backup, archiving, and analytics.

### Reference Architectures

Standardized templates or **blueprints** that guide the design and implementation of systems within a specific domain, ensuring consistency, interoperability, and best practices across projects. Reference architectures provide a high-level view of the system, including its components, their interactions, and the principles governing their design and evolution. They serve as a foundation for developing specific solutions and help in aligning technical decisions with organizational goals.

**Examples:**

- **Multi-Region Active-Active Availability**: A reference architecture that ensures high availability and disaster recovery by deploying applications across multiple geographic regions, allowing for seamless failover and load balancing.
- **CDN Hosting in AWS**: A reference architecture for using AWS CloudFront to distribute content globally with low latency and high transfer speeds, leveraging edge locations for caching and delivery.
- **Event Streaming with Aiven Kafka**: A reference architecture for implementing real-time data streaming and processing using Aiven Kafka, enabling scalable and reliable event-driven applications.

### Design Specifications

Detailed requirements that outline the **necessary criteria and constraints** for the design and implementation of a technical solution. Design specifications provide a clear and comprehensive description of the system's functionality, performance, and constraints, ensuring that all stakeholders have a common understanding of the project's goals and requirements. They serve as a contract between the stakeholders and the development team, guiding the development process and ensuring that the final product meets the specified requirements.

**Examples:**

- **REST API Specification**: A detailed description of the endpoints, request/response formats, authentication methods, and error handling for an API.
- **Application Logging Specification**: A detailed description of the logging requirements, including log levels, formats, destinations, types of events to be logged, structure of log messages, and mechanisms for log aggregation and analysis.

### Design Patterns

Reusable **abstract solutions** to commonly occurring problems in software design. Design patterns provide a proven approach to solving specific design challenges, promoting best practices and improving code maintainability, scalability, and flexibility. They capture the experience and knowledge of seasoned developers, enabling others to apply these solutions to similar problems in their own projects.

**Examples:**

- **Microservices Architecture**: A design pattern that structures an application as a collection of loosely coupled services, each implementing a specific business capability.
- **Event-Driven Architecture**: A pattern where decoupled components communicate through events, enabling real-time processing and scalability.
- **Serverless Architecture**: A design approach where applications are built using third-party services or cloud functions, reducing the need for managing infrastructure.

### Design Practices

A systematic approach that guides developers in **creating** well-structured, maintainable, and efficient software systems. This includes defining the roles and responsibilities of actors involved in the design process, the artifacts produced during the design phase, and the process flow that ensures all design activities are carried out in a coordinated and effective manner.

**Examples:**

- **Design Collaboration Practice**: A process for collaboration across organizations when developing changes for new and existing WEX systems.
- **Production Readiness Checklist**: A process for verifying that a high-level design has solved for a required concerns before going to production.

### Delivery Practices

A systematic approach that guides teams in **planning, executing, and delivering** software projects. This includes defining the roles and responsibilities of actors involved in the delivery process, the artifacts produced during the delivery phase, and the process flow that ensures all delivery activities are carried out in a coordinated and effective manner. Practices include agile methodologies, continuous integration and delivery (CI/CD), project management techniques, and quality assurance processes.

**Examples:**

- **Experiments with LaunchDarkly**: A process for using LaunchDarkly's feature flag management to conduct controlled experiments and rollouts. This practice involves defining feature flags, setting up experiments, and analyzing results to make data-driven decisions on feature releases.
- **Continuous Integration with GitHub Actions**: A process for automating the integration of code changes from multiple contributors into a shared repository, using GitHub Actions to build, test, and validate the code.
- **Agile Sprint Planning**: A process for organizing and planning work in iterative cycles, including defining sprint goals, selecting backlog items, and estimating effort.

### Support Practices

A systematic approach that guides teams in **maintaining, troubleshooting, and enhancing** software systems post-deployment. This includes defining the roles and responsibilities of actors involved in the support process, the artifacts produced during the support phase, and the process flow that ensures all support activities are carried out in a coordinated and effective manner. Practices include incident management, problem management, change management, and continuous monitoring.

**Examples:**

- **Diagnostics and Troubleshooting**: A process for identifying and resolving issues in software systems. This practice involves collecting diagnostic data, analyzing logs, and using debugging tools to pinpoint the root cause of problems.
- **Alerting and Escalation**: A process for monitoring system health and performance, generating alerts for anomalies, and escalating issues to the appropriate teams for timely resolution. This practice ensures that critical issues are addressed promptly to minimize downtime and impact.
- **Root Cause Analysis (RCA)**: A process for investigating and determining the underlying causes of incidents or problems. This practice involves conducting thorough analyses, documenting findings, and implementing corrective actions to prevent recurrence.

### WEX Fabric

WEX Fabric is a comprehensive framework that provides a "golden path" for development, incorporating multiple "paved roads" to streamline and standardize processes. It aligns teams with WEX's standards, policies, priorities, and strategic objectives, ensuring consistency and efficiency across projects. WEX Fabric facilitates the adoption of best practices and tools, enabling teams to deliver high-quality solutions that meet organizational goals.

## Enforcement Levels

Standards adopted by a governing body may be applied in different ways. This document follows the methodology outlined in [IETF RFC 2119 - RFC Requirement Levels](https://datatracker.ietf.org/doc/html/rfc2119).

The enforcement level of a complete standard or individual requirements is categorized using the terms `MUST`, `MUST NOT`, `SHOULD`, `SHOULD NOT`, and `MAY`.

If a standard does not explicitly define an enforcement level, it should be interpreted with a `SHOULD` enforcement level.

For example:

- Multiple standard tools may exist with similar capabilities but target different nuanced use cases for efficiency, risk mitigation, and technical feasibility. Standards relating to "approved tools" would have a `MAY` enforcement level.
- Hard design requirements that need to be followed based on WEX policies and standard services would use a `MUST` or `MUST NOT` enforcement level.
- Standard practices that promote proven techniques for advancing prioritized concerns would use a `SHOULD` or `SHOULD NOT` enforcement level.

## Scopes

Standards may be maintained by different scopes of responsibility.

- **WEX Enterprise Standards**
  - Example: All applications with a user interface should use the Phoenix UX Design Components (`MUST` enforcement level).

- **Line of Business (LoB) Standards**
  - Example: The Payments LoB must use the Payments Gateway API for processing transactions (`SHOULD` enforcement level).

- **Product Standards**
  - Example: The Fleet product should use the Fleet Data Integration API for synchronizing vehicle data (`SHOULD` enforcement level).

- **Application Standards**
  - Example: The Expense Management application should use the Expense Reporting microservice for generating reports (`SHOULD` enforcement level).

Competing standards from different scopes should be evaluated based on enforcement level and scope. A conflict may occur if standards at different scopes have incongruent enforcement levels. Deference between conflicting standards should be given to the standard with the highest enforcement level at the broadest scope.
For example:

- A WEX-wide standard with a `MUST` enforcement level should be prioritized over any competing standard.
- A WEX-wide standard with a `SHOULD` enforcement level should be prioritized over a product-scoped standard with a `SHOULD` enforcement level.
- An application-scoped standard with a `MUST` enforcement level may be prioritized over a competing WEX-wide standard with a `SHOULD` or `MAY` enforcement level.

Teams should consider and prioritize the work needed to adopt broader standards when deference between conflicting standards has been given to a more localized standard.

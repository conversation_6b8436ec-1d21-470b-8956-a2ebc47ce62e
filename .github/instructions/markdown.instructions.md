---
applyTo: '**/*.md'
---
- Headers must be surrounded by blank lines
- Use mermaid diagrams in code blocks when generating diagrams
- Use callouts, icons, diagrams, code blocks, and panels to break up long sections of text
- Use ☑️ and 🔲 characters for checkboxes
- Use clear and concise titles that accurately describe the content
- Use `architecture-beta` mermaid diagrams for cloud architecture diagrams
- Use `sequence` mermaid diagrams to describe business and system process flows
- Use `mindmap` mermaid diagrams to describe a collection of related topics or categories
- Markdown should pass linting rule `MD047`: "Files should end with a single newline character"
- Markdown should pass linting rule `MD009`: "Lines should not end with trailing spaces"
- Markdown should pass linting rule `MD032`: "Lists should be surrounded by blank lines"
- Mark<PERSON> should pass linting rule `MD030`: "Single space after list markers"
- Mark<PERSON> should pass linting rule `MD007`: "Unordered list indentation is 2 spaces"

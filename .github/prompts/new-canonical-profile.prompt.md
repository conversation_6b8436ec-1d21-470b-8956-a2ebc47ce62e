Create a new high-level documentation profile for a canonical system.

Ask for the name of the system and and section contents if not provided.

Requirements for the canonical system profile:
- Generate `index.md` in a new folder under `/content/paved-roads/canonical`
- Title should be the name of the canonical system
- Folder name should be concise form of the title using lowercase and dash word separators
- Conform to RFC layout requirements: [instructions/rfc-layout.md](../../instructions/rfc-layout.md)
- Conform to Canonical System Profile markdown template: [templates/canonical-profile.md](../../templates/canonical-profile.md)

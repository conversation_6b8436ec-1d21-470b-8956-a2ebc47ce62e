#codebase 

Find all organizations that are missing subtype folders under `/content/designs/`

Create new design subtypes for all missing organizations. 

Requirements for design subtypes are:

- organizations are defined in: [RFC-421](../../content/guardrails/governance/RFC-421-isarb-tech-partners/index.md)
- create new subfolders under `/content/designs/`
- new folder names should be clear, concise, and all lowercase with dash word separators
- add `index.md` for each new subtype folder that explains the scope of the organization
- update the Designs folder `index.md` to include new subtypes
- update `content_root_folders` in `on-pullrequest.yml` to include new subfolders
- update `content_root_folders` and `content_folders_to_import` in `on-push-to-main.yml` to include new subfolders
name: "Ratify RFC Content"
on:
  workflow_dispatch:
    inputs:
      rfc_numbers:
        description: "RFC numbers to ratify (e.g. 41,42,43)"
        required: true
        type: string
      id_prefix:
        description: "Prefix used in RFC identifiers (e.g. RFC, DOCID)"
        required: true
        default: "RFC"
        type: string
      repository:
        description: "Repository containing the RFC (e.g. wexinc/arch-rfc-shared)"
        required: true
        default: "wexinc/arch-rfc-content"
        type: string
      content_folder:
        description: "Folder where RFCs are located in the repository"
        required: true
        default: "content"
        type: string
      comment:
        description: "Optional comment about ratification"
        required: false
        type: string

jobs:
  extract-rfc-numbers:
    runs-on: wex-sandbox
    if: ${{ contains(vars.ISARB_RATIFIERS, github.actor) }}
    outputs:
      rfc-matrix: ${{ steps.create-matrix.outputs.result }}
    steps:
      - name: Input validation - only numbers allowed
        id: validate-inputs
        shell: bash
        run: |
          rfc_numbers="${{ inputs.rfc_numbers }}"
          rfc_numbers=$(echo "$rfc_numbers" | xargs) # Trim leading and trailing whitespace
          if [[ ! "$rfc_numbers" =~ ^[0-9,]+$ ]]; then
            echo "::error::Invalid RFC numbers format. Only numbers and commas are allowed."
            exit 1
          fi
      - name: Create RFC Matrix
        id: create-matrix
        uses: actions/github-script@v7
        with:
          script: |
            const rfcList = '${{ inputs.rfc_numbers }}'.split(',').map(item => item.trim());
            const matrix = { rfc_number: rfcList };
            return matrix;

  ratify-content:
    needs: extract-rfc-numbers
    runs-on: wex-sandbox
    if: ${{ contains(vars.ISARB_RATIFIERS, github.actor) }}
    strategy:
      matrix: ${{ fromJSON(needs.extract-rfc-numbers.outputs.rfc-matrix) }}
      fail-fast: false
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          repository: ${{ inputs.repository }}
          token: ${{ secrets.GITHUBPAT }}
          ref: feature/SARCH-248-test
          
      - name: Find RFC
        id: mark-ratified
        shell: bash
        run: |
          # Set variables from inputs
          RFC_NUMBER="${{ matrix.rfc_number }}"
          ID_PREFIX="${{ inputs.id_prefix }}"
          ROOT_FOLDER="${{ inputs.content_folder }}"
          RATIFICATION_MARKER=":isarb:"
          REPOSITORY="${{ inputs.repository }}"

          echo "Looking for RFC number: ${ID_PREFIX}-${RFC_NUMBER} in repository: $REPOSITORY, folder: $ROOT_FOLDER"

          # Find all index.md files in the content directory
          INDEX_FILES=$(find "$ROOT_FOLDER" -name "index.md" -type f)

          # Search each index file for the RFC number in the header with flexible hyphen format
          for file in $INDEX_FILES; do
            # Look for the exact identifier with or without hyphen
            if grep -q "<!-- Title: ${ID_PREFIX}-${RFC_NUMBER}[ ]" "$file" || \
               grep -q "<!-- Title: ${ID_PREFIX}${RFC_NUMBER}[ ]" "$file"; then
              echo "Found match in file header: $file"
              FILE_PATH="$file"
              break
            fi
          done

          # Check if file is in informational folder
          if [[ "$file" == *"$ROOT_FOLDER/informational/"* ]]; then
            echo "::warning::${ID_PREFIX}-${RFC_NUMBER} found in the informational folder: $file"
            echo "::notice::This RFC is in the informational category and will not be subject to ratification validation."
            exit 0
          fi

          # Check if file is already ratified
          if grep -q "$RATIFICATION_MARKER" "$file"; then
            echo "::warning::RFC is already ratified: $file"
            exit 0
          fi

          if [ -z "$FILE_PATH" ]; then
            echo "::error::No RFC found with identifier ${ID_PREFIX}-${RFC_NUMBER} in the repository"
            exit 1
          fi
          echo "Found RFC file: $FILE_PATH"
          echo "file_path=$FILE_PATH" >> $GITHUB_OUTPUT

      - name: Ratify RFC
        if: steps.mark-ratified.outputs.file_path != ''
        run: |
          RATIFICATION_MARKER=":isarb:"
          DRAFT_MARKER=":draft:"
          REPOSITORY="${{ inputs.repository }}"
          FILE_PATH="${{ steps.mark-ratified.outputs.file_path }}"

          # Check if document has :draft: tag and replace it
          if grep -q "$DRAFT_MARKER" "$FILE_PATH"; then
            # Replace :draft: with :isarb:
            sed -i "s/$DRAFT_MARKER/$RATIFICATION_MARKER/g" "$FILE_PATH"
            echo "Replaced :draft: tag with ratification marker"
          else
            # Add :isarb: after the title line
            sed -i "/^# /a $RATIFICATION_MARKER" "$FILE_PATH"
            # Add a blank line before the ratification marker
            sed -i $"s,$RATIFICATION_MARKER,\\n$RATIFICATION_MARKER," "$FILE_PATH"
            echo "Added ratification marker after title"
          fi

          echo "Successfully marked RFC as ratified: $FILE_PATH"
          # Commit the changes
          git config --global user.email "<EMAIL>"
          git config --global user.name "rfcconfluence-bot"

          COMMENT="${{ inputs.comment }}"
          if [ -z "$COMMENT" ]; then
            COMMENT="No additional comments provided."
          fi
          git add ${{ steps.mark-ratified.outputs.file_path }}
          git commit -m "Ratify ${{ inputs.id_prefix }}-${{ matrix.rfc_number }}" -m "Ratified by: ${{ github.actor }}" -m "$COMMENT"
          git push

          echo "Successfully ratified RFC ${{ inputs.id_prefix }}-${{ matrix.rfc_number }} in repository ${{ inputs.repository }}"

  ratify-content_not_allowed_user:
    runs-on: wex-sandbox
    if: ${{ !contains(vars.ISARB_RATIFIERS, github.actor) }}
    steps:
      - name: Notify user
        run: |
          echo "You are not allowed to ratify RFCs in this repository."
          echo "Please contact the repository administrator for assistance."
          exit 1


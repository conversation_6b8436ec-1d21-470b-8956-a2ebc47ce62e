on:
  workflow_call:
    inputs:
      content_root_folders:
        required: true
        type: string
      tag_name:
        required: false
        type: string
      filter_content_pattern:
        required: false
        type: string
        default: "DOCID"
      fetch_depth:
        required: false
        type: number
        default: 2
    secrets:
      GH_PAT:
        required: true
  workflow_dispatch:
    inputs:
      content_root_folders:
        description: "List of content root folders"
        required: true
        type: string
      filter_content_pattern:
        description: "Pattern to filter content"
        type: string
        default: "DOCID"
      fetch_depth:
        description: "Fetch depth"
        required: false
        type: number
        default: 0

concurrency:
  group: assign-and-deploy

name: "Assign Missing Content Identifiers"
jobs:
  assign-id:
    runs-on: wex-sandbox
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: ${{ inputs.fetch_depth }}
          token: ${{ secrets.GH_PAT }}

      - name: Evaluate changed files
        id: evaluate-files
        uses: wexinc/arch-rfc-shared/actions/evaluate-changed-files@main
        with:
          content_root_folders: ${{ inputs.content_root_folders }}
          common_root: "./content"

      - name: Filter changed .md files only
        id: filter-md-files
        if: ${{ steps.evaluate-files.outputs.content != '' }}
        run: |
          # Process the files and store in a variable
          FILTERED_FILES=$(echo "${{ steps.evaluate-files.outputs.content }}" | tr ' ' '\n' | grep -E "index\.md$" || echo " ")

          # Use delimiter syntax for multiline output
          EOF=$(dd if=/dev/urandom bs=15 count=1 status=none | base64)
          echo "changed_md_files<<$EOF" >> $GITHUB_OUTPUT
          echo "$FILTERED_FILES" >> $GITHUB_OUTPUT
          echo "$EOF" >> $GITHUB_OUTPUT
      - name: Assigning Content Identifiers
        id: id-assign
        if: ${{ steps.evaluate-files.outputs.content != '' }}
        uses: wexinc/arch-rfc-shared/actions/assign-content-id@main
        with:
          changed_files: ${{ steps.filter-md-files.outputs.changed_md_files }}
          filter_content_pattern: ${{ inputs.filter_content_pattern }}
          tag_name: ${{ inputs.tag_name }}
      - name: Commit New Identifiers
        id: commit-normalization
        if: ${{ steps.id-assign.outputs.has_new_assigned_ids == 'true' }}
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "rfcconfluence-bot"
          git add .
          git commit -m "Assign content ID"
          git push --force

name: Build Custom Mermaid Renderer Docker Image

on:
  push:
    paths:
      - 'actions/publish-mark/custom_mermaid_renderer.py'
      - 'actions/publish-mark/Dockerfile'
      - '.github/workflows/build-mermaid-renderer.yml'
    branches:
      - main
  pull_request:
    paths:
      - 'actions/publish-mark/custom_mermaid_renderer.py'
      - 'actions/publish-mark/Dockerfile'
      - '.github/workflows/build-mermaid-renderer.yml'
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/mermaid-renderer

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: actions/publish-mark
          file: actions/publish-mark/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

      - name: Test the built image
        run: |
          echo "Testing the custom Mermaid renderer..."
          docker run --rm ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest sh -c "
            echo 'graph TD; A[Test] --> B[Success]' | python3 /app/custom_mermaid_renderer.py > /dev/null && 
            echo 'Custom Mermaid renderer test passed!'
          "

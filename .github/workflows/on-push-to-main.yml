name: "On push test - Custom Mermaid Icons"
on:
    push:
        branches:
            - feature/custom-mermaid-renderer

jobs:
    handle-rfc-assign-id:
        uses: wexinc/arch-rfc-shared/.github/workflows/content-assign-identifier.yml@main
        with:
            content_root_folders: content/category-b/subtype-b1 content/category-a/subtype-a1 content/category-a/subtype-a2
            tag_name: rfc-prod
        secrets:
            GH_PAT: ${{ secrets.GITHUBPAT }}
    handle-deploy:
        if: ${{ always() }} # Always runs after job handle-rfc-assign-id have completed, regardless of whether it were successful
        needs: handle-rfc-assign-id
        uses: wexinc/arch-rfc-shared/.github/workflows/content-build-deploy.yml@feature/custom-mermaid-renderer
        with:
            filter_content_pattern: DOCID
            content_folders_to_import: content/category-b/subtype-b1 content/category-a/subtype-a1 content/category-a/subtype-a2
            root_content_folder: content
            site_space_id: ~712020f93207a80b2f4d3e88e86eaf0d4ef6d2
            site_root_page: https://wexinc.atlassian.net/wiki
            include_path: include
        secrets:
            ATLASSIAN_SERVICE_ACCOUNT: ${{ secrets.ATLASSIAN_USERNAME }}
            ATLASSIAN_API_TOKEN_SA: ${{ secrets.ATLASSIAN_API_TOKEN }}
            GITHUBPAT: ${{ secrets.GITHUBPAT }}

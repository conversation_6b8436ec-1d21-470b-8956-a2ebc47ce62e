name: "On push to main"
on: 
    push:
        branches:
            - main
jobs:
    handle-rfc-assign-id:
        uses: wexinc/arch-rfc-shared/.github/workflows/content-assign-identifier.yml@main
        with:
            content_root_folders: content/designs content/designs/ai content/designs/architecture content/designs/benefits content/designs/corporate-payments content/designs/credit-fraud content/designs/customer-service content/designs/data-analytics content/designs/end-user content/designs/end-user-desktop content/designs/enterprise content/designs/enterprise-web content/designs/finance content/designs/gts content/designs/hr-legal content/designs/marketing content/designs/mobility content/designs/paas content/designs/payments-platform content/designs/project-mgmt content/designs/security content/designs/wex-select content/guardrails content/guardrails/design-pattern content/guardrails/design-specification content/guardrails/governance content/informational content/informational/decision-record content/informational/discussion content/informational/general-info content/informational/instructions content/informational/white-paper content/informational/case-study content/paved-roads content/paved-roads/canonical content/paved-roads/golden-path content/paved-roads/reference-architecture content/practices content/practices/delivery content/practices/design content/practices/initiatives content/practices/support
            filter_content_pattern: RFC
            tag_name: build-number
        secrets:
            GH_PAT: ${{ secrets.GITHUBPAT }}
    handle-deploy:
      if: ${{ always() }} # Always runs after job handle-rfc-assign-id have completed, regardless of whether it were successful
      needs: handle-rfc-assign-id    
      uses: wexinc/arch-rfc-shared/.github/workflows/content-build-deploy.yml@main
      with:
        filter_content_pattern: RFC
        content_folders_to_import: content/designs content/designs/ai content/designs/architecture content/designs/benefits content/designs/corporate-payments content/designs/credit-fraud content/designs/customer-service content/designs/data-analytics content/designs/end-user content/designs/end-user-desktop content/designs/enterprise content/designs/enterprise-web content/designs/finance content/designs/gts content/designs/hr-legal content/designs/marketing content/designs/mobility content/designs/paas content/designs/payments-platform content/designs/project-mgmt content/designs/security content/designs/wex-select content/guardrails content/guardrails/design-pattern content/guardrails/design-specification content/guardrails/governance content/informational content/informational/decision-record content/informational/discussion content/informational/general-info content/informational/instructions content/informational/white-paper content/informational/case-study content/paved-roads content/paved-roads/canonical content/paved-roads/golden-path content/paved-roads/reference-architecture content/practices content/practices/delivery content/practices/design content/practices/initiatives content/practices/support
        root_content_folder: content
        include_path: include
        site_space_id: ITRFC
        site_root_page: https://wexinc.atlassian.net/wiki
        deploy_all_content: false
      secrets:
        ATLASSIAN_SERVICE_ACCOUNT: ${{ secrets.ATLASSIAN_SERVICE_ACCOUNT }}
        ATLASSIAN_API_TOKEN_SA: ${{ secrets.ATLASSIAN_API_TOKEN_SA }}
        GITHUBPAT: ${{ secrets.GITHUBPAT }}

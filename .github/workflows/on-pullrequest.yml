name: "On pull request test"
on:
    pull_request:

jobs:
    handle-pull-request:
        uses: wexinc/arch-rfc-shared/.github/workflows/content-pull-request.yml@main
        with:
            content_root_folders: content/category-b/subtype-b1 content/category-a/subtype-a1 content/category-a/subtype-a2
            common_root: "./content"
            config_file_path: ".markdownlint-cli2.yaml"
            editor_user: ${{ github.actor }}
        secrets:
            GH_PAT: ${{ secrets.GITHUBPAT }}

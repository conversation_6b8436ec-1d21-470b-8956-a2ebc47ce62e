name: "On pull request"
on: 
    pull_request:
        
jobs:
    handle-pull-request:
        uses: wexinc/arch-rfc-shared/.github/workflows/content-pull-request.yml@main
        with:
            content_root_folders: content/designs content/designs/ai content/designs/architecture content/designs/benefits content/designs/corporate-payments content/designs/credit-fraud content/designs/customer-service content/designs/data-analytics content/designs/end-user content/designs/end-user-desktop content/designs/enterprise content/designs/enterprise-web content/designs/finance content/designs/gts content/designs/hr-legal content/designs/marketing content/designs/mobility content/designs/paas content/designs/payments-platform content/designs/project-mgmt content/designs/security content/designs/wex-select content/guardrails content/guardrails/design-pattern content/guardrails/design-specification content/guardrails/governance content/informational content/informational/decision-record content/informational/discussion content/informational/general-info content/informational/instructions content/informational/white-paper content/informational/case-study content/paved-roads content/paved-roads/canonical content/paved-roads/golden-path content/paved-roads/reference-architecture content/practices content/practices/delivery content/practices/design content/practices/initiatives content/practices/support
            common_root: './content'
            config_file_path: './.markdownlint-cli2.yaml'
            editor_user: ${{ github.actor }}
        secrets:
            GH_PAT: ${{ secrets.GITHUBPAT }}
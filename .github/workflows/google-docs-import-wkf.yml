name: Google Docs to Markdown

on:
    workflow_dispatch:
        inputs:
            FILE_URL:
                description: 'Google docs file URL'
                required: true        
            RFC_CATEGORY:
                description: 'RFC Category'
                required: true
                type: choice
                options:
                    - content/guardrails/design-pattern/
                    - content/guardrails/design-specification/
                    - content/guardrails/governance/
            
                    - content/informational/decision-record/
                    - content/informational/discussion/
                    - content/informational/general-info/
                    - content/informational/instructions/
                    - content/informational/white-paper/
                    - content/informational/case-study/
            
                    - content/paved-roads/canonical/
                    - content/paved-roads/golden-path/
                    - content/paved-roads/reference-architecture/
            
                    - content/practices/delivery/
                    - content/practices/design/
                    - content/practices/initiatives/
                    - content/practices/support/
                    - content/practices/initiatives/
            RFC_NAME:
                description: 'RFC Name'
                required: true

jobs:
    convert:
        runs-on: ubuntu-latest
        outputs: 
            extemsion:  ${{ steps.fileexport.outputs.extension}}
            filename: ${{ steps.fileexport.outputs.filename }}

        env:
          CI_COMMIT_AUTHOR: Continuous Integration
          CI_USER_EMAIL: <EMAIL>
        steps:
        - name: Checkout repository
          uses: actions/checkout@v4

        - name: Set up Python
          uses: actions/setup-python@v5
          with:
            python-version: '3.x'

        - name: Configure github
          run: |
            git config --global user.name "${{ env.CI_COMMIT_AUTHOR }}"
            git config --global user.email "${{ env.CI_USER_EMAIL }}"

        - name: Install dependencies
          run: |
            python -m pip install --upgrade pip
            pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client markdownify

        - name: Create branch
          run: |
            branchName="feature/google-docs-import-${{ github.run_number }}-doc-id"
            git checkout -b $branchName

        - name: File export
          id: fileexport
          run:
            python scripts/google-docs-import.py
          env:
              REFRESH_TOKEN: ${{ secrets.GDOCS_REFRESH_TOKEN }}
              CLIENT_ID: ${{ secrets.GDOCS_CLIENT_ID }}
              CLIENT_SECRET: ${{ secrets.GDOCS_CLIENT_SECRET }}
              FILE_URL: ${{ github.event.inputs.FILE_URL }}
              RFC_CATEGORY: ${{ github.event.inputs.RFC_CATEGORY }}
              RFC_NAME: ${{ github.event.inputs.RFC_NAME }}

              
        - name: Commit and push changes
          run: |
            git add .
            git status
            git commit -m 'Google doc ${{ github.event.inputs.FILE_URL }} converted into Markdown'
            git push origin feature/google-docs-import-${{ github.run_number }}-doc-id

          env:
            GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

        - name: Create a PR
          run: |      
            gh pr create -B main -H "feature/google-docs-import-${{ github.run_number }}-doc-id" --title 'Bot: Pulling ${{ github.ref }} into main' --body ':crown: *Bot: RFC Github action bot changes. Google docs import to markdown.'
          
          env:
            GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}   
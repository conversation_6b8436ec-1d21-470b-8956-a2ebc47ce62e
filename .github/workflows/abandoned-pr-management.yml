on:
  workflow_call:
    inputs:
      warn_after_days:
        required: true
        type: number
        default: 25
      close_after_days:
        required: true
        type: number
        default: 30
    
name: Daily Abandoned PR Management
jobs:
  abandoned-pr-check:
    runs-on: wex-sandbox
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Run Abandoned PR Check
        id: scan-abandoned-pr
        uses: wexinc/arch-rfc-shared/actions/abandoned_pr_management@main
        with:
          warn_after_days: ${{ inputs.warn_after_days }}
          close_after_days: ${{ inputs.close_after_days }}

on:
  workflow_call:
    inputs:
      config_file_path:
        required: true
        type: string
      common_root:
        required: true
        type: string
        default: ./content
      content_root_folders:
        required: true
        type: string
      fetch_depth:
        required: false
        type: number
        default: 2
      editor_user:
        required: true
        type: string
    secrets:
      GH_PAT:
        required: true
  workflow_dispatch:
    inputs:
      config_file_path:
        description: "Path to the markdownlint configuration file"
        required: true
        type: string
        default: .markdownlint-cli2.yaml
      common_root:
        description: "Common parent shared by all content files"
        required: true
        type: string
        default: ./content
      content_root_folders:
        description: "Root folders containing content to validate"
        required: true
        type: string
      fetch_depth:
        description: "Number of commits to fetch"
        required: false
        type: number
        default: 2
      editor_user:
        description: "Username of the editor"
        required: true
        type: string

name: "Content PR Validator"
jobs:
  pr-validation:
    runs-on: wex-sandbox
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: ${{ inputs.fetch_depth }}
          ref: ${{ github.head_ref }}
          token: ${{ secrets.GH_PAT }}
      - name: Evaluate changed files
        id: evaluate-files
        uses: wexinc/arch-rfc-shared/actions/evaluate-changed-files@main
        with:
          content_root_folders: ${{ inputs.content_root_folders }}
          common_root: ${{ inputs.common_root }}
      - name: Check metadata
        id: check-metadata
        if: ${{ steps.evaluate-files.outputs.content != '' }}
        uses: wexinc/arch-rfc-shared/actions/check-metadata@main
        with:
          file_path: ${{ steps.evaluate-files.outputs.content }}
      - name: Lint Changed Content
        id: lint-content
        if: ${{ steps.evaluate-files.outputs.content != '' }}
        uses: wexinc/arch-rfc-shared/actions/lint-markdown@main
        with:
          config_file_path: ${{ inputs.config_file_path }}
          changed_content: ${{ steps.evaluate-files.outputs.content }}
      - name: Lint Changed Root Files
        id: lint-root
        if: ${{ steps.evaluate-files.outputs.root_files != '' }}
        uses: wexinc/arch-rfc-shared/actions/lint-markdown@main
        with:
          config_file_path: ${{ inputs.config_file_path }}
          changed_content: ${{ steps.evaluate-files.outputs.root_files }}
      - name: Commit linting fixes
        id: commit-normalization
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "rfcconfluence-bot"
          git add .
          git commit -m "Linting fixes" || exit 0
          git push || exit 0
      - name: Scan for modified ratified content
        id: scan-ratify
        if: ${{ steps.evaluate-files.outputs.content != '' }}
        uses: wexinc/arch-rfc-shared/actions/scan-ratified@main
        with:
          changed_content: ${{ steps.evaluate-files.outputs.content }}
      - name: Check if content publishing is needed
        id: check-publish-needed
        run: |
          # Check if there are any content files or root files that need publishing
          if [[ -n "${{ steps.evaluate-files.outputs.content }}" || -n "${{ steps.evaluate-files.outputs.root_files }}" ]]; then
            echo "publish_needed=true" >> $GITHUB_OUTPUT
            echo "✅ Content changes detected - publishing will be triggered"
            echo "Content files: ${{ steps.evaluate-files.outputs.content }}"
            echo "Root files: ${{ steps.evaluate-files.outputs.root_files }}"
          else
            echo "publish_needed=false" >> $GITHUB_OUTPUT
            echo "ℹ️ No content changes detected - skipping publish step"
          fi

      - name: Publish Content (Only if content modified)
        if: ${{ steps.check-publish-needed.outputs.publish_needed == 'true' }}
        run: |
          echo "🚀 Publishing content changes..."
          echo "This step would trigger the actual publishing workflow"
          echo "Content files to publish: ${{ steps.evaluate-files.outputs.content }}"
          echo "Root files to publish: ${{ steps.evaluate-files.outputs.root_files }}"
          # Add your actual publish logic here, for example:
          # uses: wexinc/arch-rfc-shared/.github/workflows/content-build-deploy.yml@main
          # with:
          #   content_folders_to_import: ${{ inputs.content_root_folders }}
          #   changed_files: "${{ steps.evaluate-files.outputs.content }} ${{ steps.evaluate-files.outputs.root_files }}"

      - name: Approve Pull Request
        if: ${{ steps.evaluate-files.outputs.other_files == '' &&
          steps.evaluate-files.outputs.root_files == '' &&
          steps.evaluate-files.outputs.deleted_files == '' &&
          github.event_name == 'pull_request' }}
        uses: hmarr/auto-approve-action@v4
        with:
          review-message: "Auto approved automated PR from bot."

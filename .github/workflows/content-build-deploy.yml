on:
  workflow_call:
    inputs:
      content_folders_to_import:
        required: true
        type: string
      filter_content_pattern:
        required: false
        type: string
        default: "DOCID"
      root_content_folder:
        required: true
        type: string
      site_space_id:
        type: string
      site_root_page:
        required: true
        type: string
      include_path:
        required: false
        type: string
        default: ""
      deploy_all_content:
        description: "When set to true, deploys all content instead of only changed files"
        required: false
        default: false
        type: boolean
    secrets:
      ATLASSIAN_SERVICE_ACCOUNT:
        required: true
      ATLASSIAN_API_TOKEN_SA:
        required: true
      GITHUBPAT:
        required: true
  workflow_dispatch:
    inputs:
      filter_content_pattern:
        description: "Pattern to filter content"
        type: string
        default: "DOCID"
      root_content_folder:
        description: "Root content folder to import to Confluence"
        required: true
        type: string
      site_space_id:
        description: "Confluence space ID"
        type: string
      site_root_page:
        description: "Confluence page default root url"
        required: true
        default: "https://wexinc.atlassian.net/wiki"
        type: string
      include_path:
        description: "Include path for Mark templates"
        required: false
        default: "include"
        type: string
      compile_only:
        description: "Show resulting HTML and don't update Confluence page content."
        required: false
        default: false
        type: boolean
      deploy_all_content:
        description: "Deploy all content files."
        required: false
        default: false
        type: boolean
      repository:
        description: "Repository to use for the workflow. Example: wexinc/arch-rfc-content"
        required: true
        type: string

name: "Content Build and Deploy"
jobs:
  build-deploy:
    runs-on: wex-sandbox
    steps:
      - name: Debug info
        run: |
          echo "Event name: ${{ github.event_name }}"
          echo "Repository: ${{ github.repository }}"
          echo "Input repository: ${{ inputs.repository }}"
          echo "Final repository to use: ${{ github.event_name == 'workflow_dispatch' && inputs.repository != '' && inputs.repository || github.repository }}"
          echo "Actor: ${{ github.actor }}"

      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUBPAT }}
          repository: ${{ github.event_name == 'workflow_dispatch' && inputs.repository != '' && inputs.repository || github.repository }}
          ref: ${{ github.ref }}

      - name: Evaluate changed files
        if: ${{ inputs.deploy_all_content == false }}
        id: evaluate-files
        uses: wexinc/arch-rfc-shared/actions/evaluate-changed-files@main
        with:
          content_root_folders: ${{ inputs.content_folders_to_import }}
          common_root: "./content"

      - name: Verify content changes before publishing
        id: verify-content-changes
        run: |
          # Check if we should proceed with publishing
          if [[ "${{ inputs.deploy_all_content }}" == "true" ]]; then
            echo "content_changes_detected=true" >> $GITHUB_OUTPUT
            echo "✅ Deploy all content mode - proceeding with publish"
          elif [[ -n "${{ steps.evaluate-files.outputs.content }}" || -n "${{ steps.evaluate-files.outputs.root_files }}" ]]; then
            echo "content_changes_detected=true" >> $GITHUB_OUTPUT
            echo "✅ Content changes detected in content folder:"
            echo "  - Content files: ${{ steps.evaluate-files.outputs.content }}"
            echo "  - Root files: ${{ steps.evaluate-files.outputs.root_files }}"
            echo "  - Proceeding with publish step"
          else
            echo "content_changes_detected=false" >> $GITHUB_OUTPUT
            echo "ℹ️ No content changes detected in content folder"
            echo "  - Content files: empty"
            echo "  - Root files: empty"
            echo "  - Skipping publish step to save resources"
          fi

      - name: Preserve existing comments
        if: >-
          ${{ steps.verify-content-changes.outputs.content_changes_detected == 'true' &&
          inputs.compile_only != 'true' }}
        id: preserve-comments
        uses: wexinc/arch-rfc-shared/actions/manage-comments@main
        with:
          operation: preserve
          confluence_url: ${{ inputs.site_root_page }}
          username: ${{ secrets.ATLASSIAN_SERVICE_ACCOUNT }}
          api_token: ${{ secrets.ATLASSIAN_API_TOKEN_SA }}
          space_key: ${{ inputs.site_space_id }}
          changed_files: "${{ steps.evaluate-files.outputs.content }} ${{ steps.evaluate-files.outputs.root_files }}"

      - name: Publish Markdown
        if: ${{ steps.verify-content-changes.outputs.content_changes_detected == 'true' }}
        uses: wexinc/arch-rfc-shared/actions/publish-mark@feature/custom-mermaid-renderer
        id: publish-markdown
        with:
          content_root_folders: ${{ inputs.content_folders_to_import }}
          filter_content_pattern: ${{ inputs.filter_content_pattern }}
          root_content_folder: ${{ inputs.root_content_folder }}
          site_space_id: ${{ inputs.site_space_id }}
          site_root_page: ${{ inputs.site_root_page }}
          # Fix Atlassian credentials for workflow_dispatch
          atlassian_user_name: ${{ secrets.ATLASSIAN_SERVICE_ACCOUNT }}
          atlassian_api_token: ${{ secrets.ATLASSIAN_API_TOKEN_SA }}
          compile_only: ${{ inputs.compile_only }}
          include_path: ${{ inputs.include_path }}
          changed_files: ${{ steps.evaluate-files.outputs.content }} ${{ steps.evaluate-files.outputs.root_files }}
          deploy_all_content: ${{ inputs.deploy_all_content }}

      - name: Restore comments after publishing
        if: >-
          ${{ steps.evaluate-files.outputs.content != '' ||
          steps.evaluate-files.outputs.root_files != '' ||
          github.event_name == 'workflow_dispatch' ||
          inputs.deploy_all_content == true }}
        uses: wexinc/arch-rfc-shared/actions/manage-comments@main
        with:
          operation: restore
          confluence_url: ${{ inputs.site_root_page }}
          username: ${{ secrets.ATLASSIAN_SERVICE_ACCOUNT }}
          api_token: ${{ secrets.ATLASSIAN_API_TOKEN_SA }}
          page_ids: ${{ steps.preserve-comments.outputs.page_ids }}
